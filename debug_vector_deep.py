#!/usr/bin/env python3
"""
深度调试向量检索问题
"""

import numpy as np
import pickle
import faiss
from pathlib import Path
from sentence_transformers import SentenceTransformer

def deep_debug_vector():
    """深度调试向量检索"""
    print("🔍 深度调试向量检索...")
    
    # 1. 加载数据
    db_path = Path('./ultimate_final_vector_db')
    metadata_file = db_path / 'metadata.pkl'
    index_file = db_path / 'index.faiss'
    
    with open(metadata_file, 'rb') as f:
        metadata = pickle.load(f)
    
    index = faiss.read_index(str(index_file))
    model = SentenceTransformer('./models/m3e-base')
    
    print(f"数据库信息: {len(metadata)} 条记录, {index.ntotal} 个向量")
    
    # 2. 测试查询
    query = "肾虚脾虚怎么治疗"
    print(f"查询: {query}")
    
    # 生成查询向量
    query_embedding = model.encode([query])
    query_embedding_normalized = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
    
    print(f"查询向量形状: {query_embedding.shape}")
    print(f"查询向量范数: {np.linalg.norm(query_embedding)}")
    
    # 3. 搜索所有向量
    distances, indices = index.search(query_embedding_normalized.astype('float32'), index.ntotal)
    
    print(f"搜索返回: {len(distances[0])} 个结果")
    
    # 4. 分析所有距离
    all_distances = distances[0]
    all_similarities = [1 - (d ** 2 / 2) for d in all_distances]
    
    print(f"距离范围: {min(all_distances):.6f} - {max(all_distances):.6f}")
    print(f"相似度范围: {min(all_similarities):.6f} - {max(all_similarities):.6f}")
    
    # 5. 检查前10个结果
    print("\n前10个结果:")
    for i in range(min(10, len(distances[0]))):
        distance = distances[0][i]
        idx = indices[0][i]
        similarity = 1 - (distance ** 2 / 2)
        
        if idx < len(metadata):
            source = metadata[idx].get('source', 'unknown')
            content = metadata[idx].get('content', '')
            
            print(f"{i+1}. 距离: {distance:.6f}, 相似度: {similarity:.6f}")
            print(f"   来源: {source}")
            print(f"   内容: {content[:80]}...")
            print()
    
    # 6. 检查是否有任何结果通过极低阈值
    thresholds = [0.001, 0.01, 0.05, 0.1, 0.2]
    for threshold in thresholds:
        passed = sum(1 for sim in all_similarities if sim >= threshold)
        print(f"阈值 {threshold}: {passed} 个结果通过")
    
    # 7. 检查向量数据是否正确
    print(f"\n向量数据检查:")
    print(f"索引类型: {type(index)}")
    print(f"向量维度: {index.d}")
    print(f"向量数量: {index.ntotal}")
    
    # 8. 尝试重新计算相似度
    print(f"\n重新计算相似度:")
    
    # 获取第一个向量进行测试
    if index.ntotal > 0:
        # 重构第一个向量
        first_vector = index.reconstruct(0)
        
        # 计算与查询向量的余弦相似度
        dot_product = np.dot(query_embedding_normalized[0], first_vector)
        manual_similarity = dot_product  # 因为都是归一化的
        
        print(f"手动计算相似度: {manual_similarity:.6f}")
        print(f"FAISS距离: {distances[0][0]:.6f}")
        print(f"FAISS相似度: {1 - (distances[0][0] ** 2 / 2):.6f}")

def fix_vector_search_completely():
    """完全修复向量搜索"""
    print("\n🔧 完全修复向量搜索...")
    
    # 生成修复代码
    fix_code = '''
# 在 ultimate_final_tcm_system.py 的 search 方法中添加强制返回逻辑

def search(self, query: str, top_k: int = None) -> List[Dict]:
    """搜索相关文档 - 强制返回结果"""
    if not self.initialized or self.vector_index is None:
        return []

    if top_k is None:
        top_k = CONFIG['TOP_K']

    try:
        # 生成查询向量
        query_embedding = self.embedding_model.encode([query])
        query_embedding_normalized = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)

        # 搜索所有向量
        distances, indices = self.vector_index.search(
            query_embedding_normalized.astype('float32'),
            min(top_k * 2, self.vector_index.ntotal)
        )

        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx < len(self.metadata) and idx >= 0:
                # 计算相似度
                cosine_similarity = 1 - (distance ** 2 / 2)
                cosine_similarity = max(0.0, min(1.0, cosine_similarity))

                # 强制返回结果，不过滤阈值
                result = self.metadata[idx].copy()
                result['similarity'] = float(cosine_similarity)
                result['distance'] = float(distance)
                results.append(result)

        # 按相似度排序
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]

    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return []
'''
    
    print("修复代码已生成，需要应用到主文件中")
    return fix_code

if __name__ == "__main__":
    deep_debug_vector()
    fix_vector_search_completely()
