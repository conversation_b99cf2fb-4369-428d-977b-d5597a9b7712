#!/usr/bin/env python3
"""
MCP客户端 - 连接FastMCP Elasticsearch服务
"""

import requests
import json
import logging
from typing import Dict, List, Any, Optional
import streamlit as st
import time

logger = logging.getLogger(__name__)

class MCPClient:
    """MCP协议客户端"""
    
    def __init__(self, service_url: str = "http://localhost:8006"):
        self.service_url = service_url
        self.mcp_endpoint = f"{service_url}/mcp"
        self.health_endpoint = f"{service_url}/health"
        self.info_endpoint = f"{service_url}/info"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.initialized = False
        self.capabilities = []
        self.supported_domains = []
    
    def initialize(self) -> bool:
        """初始化MCP客户端"""
        try:
            st.info("🔧 初始化MCP Elasticsearch服务...")
            
            # 检查服务健康状态
            if not self._check_health():
                st.error("❌ MCP服务不可用")
                return False
            
            # 获取服务能力
            if not self._get_capabilities():
                st.error("❌ 无法获取MCP服务能力")
                return False
            
            self.initialized = True
            st.success("✅ MCP Elasticsearch服务初始化成功")
            st.info(f"📊 支持领域: {', '.join(self.supported_domains)}")
            
            return True
            
        except Exception as e:
            st.error(f"❌ MCP客户端初始化失败: {e}")
            logger.error(f"MCP客户端初始化失败: {e}")
            return False
    
    def _check_health(self) -> bool:
        """检查服务健康状态"""
        try:
            response = self.session.get(self.health_endpoint, timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                return health_data.get('status') == 'healthy'
            return False
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
    
    def _get_capabilities(self) -> bool:
        """获取服务能力"""
        try:
            mcp_request = {
                "method": "get_capabilities",
                "params": {},
                "id": "cap_001"
            }
            
            response = self.session.post(
                self.mcp_endpoint,
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                mcp_response = response.json()
                if 'result' in mcp_response:
                    result = mcp_response['result']
                    self.capabilities = result.get('capabilities', [])
                    self.supported_domains = result.get('domains', [])
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"获取能力失败: {e}")
            return False
    
    def search_knowledge(self, query: str, domain: str = "medical", 
                        max_results: int = 10, search_type: str = "comprehensive") -> List[Dict]:
        """通过MCP协议搜索知识"""
        if not self.initialized:
            logger.warning("MCP客户端未初始化")
            return []
        
        try:
            st.info(f"🌐 MCP检索: {query} | 领域: {domain}")
            
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": domain,
                    "max_results": max_results,
                    "search_type": search_type
                },
                "id": f"search_{int(time.time())}"
            }
            
            response = self.session.post(
                self.mcp_endpoint,
                json=mcp_request,
                timeout=30
            )
            
            if response.status_code == 200:
                mcp_response = response.json()
                
                if 'result' in mcp_response:
                    result = mcp_response['result']
                    results = result.get('results', [])
                    total = result.get('total', 0)
                    
                    st.success(f"✅ MCP检索成功: 找到 {total} 个结果")
                    return results
                
                elif 'error' in mcp_response:
                    error = mcp_response['error']
                    st.error(f"❌ MCP检索错误: {error.get('message', '未知错误')}")
                    return []
            
            else:
                st.error(f"❌ MCP服务响应错误: {response.status_code}")
                return []
                
        except requests.exceptions.Timeout:
            st.error("❌ MCP检索超时")
            return []
        except Exception as e:
            st.error(f"❌ MCP检索失败: {e}")
            logger.error(f"MCP检索失败: {e}")
            return []
    
    def get_service_info(self) -> Dict:
        """获取服务信息"""
        try:
            response = self.session.get(self.info_endpoint, timeout=5)
            if response.status_code == 200:
                return response.json()
            return {}
        except Exception as e:
            logger.error(f"获取服务信息失败: {e}")
            return {}
    
    def get_stats(self) -> str:
        """获取统计信息"""
        if not self.initialized:
            return "MCP客户端未初始化"
        
        return f"MCP Elasticsearch服务 - 支持{len(self.supported_domains)}个领域，{len(self.capabilities)}种能力"

class MCPElasticsearchRetriever:
    """基于MCP的Elasticsearch检索器 - 替代原有的在线检索器"""
    
    def __init__(self):
        self.mcp_client = MCPClient()
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化检索器"""
        try:
            st.info("🚀 启动MCP Elasticsearch检索服务...")
            
            # 启动MCP服务
            if not self._start_mcp_service():
                st.warning("⚠️ 无法启动MCP服务，尝试连接现有服务...")
            
            # 等待服务启动
            time.sleep(3)
            
            # 初始化MCP客户端
            if self.mcp_client.initialize():
                self.initialized = True
                return True
            else:
                st.error("❌ MCP客户端初始化失败")
                return False
                
        except Exception as e:
            st.error(f"❌ MCP检索器初始化失败: {e}")
            return False
    
    def _start_mcp_service(self) -> bool:
        """启动智能MCP服务"""
        try:
            import subprocess
            import sys

            # 在后台启动智能MCP服务
            subprocess.Popen(
                [sys.executable, "intelligent_mcp_service.py"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
            )

            st.info("✅ 智能MCP服务启动命令已执行")
            return True

        except Exception as e:
            st.error(f"❌ 启动智能MCP服务失败: {e}")
            return False
    
    def search_by_domain(self, query: str, domain: str, max_results: int = 10) -> List[Dict]:
        """根据领域搜索知识"""
        if not self.initialized:
            return []
        
        # 使用MCP客户端搜索
        results = self.mcp_client.search_knowledge(
            query=query,
            domain=domain,
            max_results=max_results,
            search_type="comprehensive"
        )
        
        return results
    
    def quick_search(self, query: str, domain: str, max_results: int = 5) -> List[Dict]:
        """快速搜索"""
        if not self.initialized:
            return []
        
        results = self.mcp_client.search_knowledge(
            query=query,
            domain=domain,
            max_results=max_results,
            search_type="quick"
        )
        
        return results
    
    def deep_search(self, query: str, domain: str, max_results: int = 20) -> List[Dict]:
        """深度搜索"""
        if not self.initialized:
            return []
        
        results = self.mcp_client.search_knowledge(
            query=query,
            domain=domain,
            max_results=max_results,
            search_type="deep"
        )
        
        return results
    
    def get_stats(self) -> str:
        """获取统计信息"""
        if not self.initialized:
            return "MCP检索器未初始化"
        
        return self.mcp_client.get_stats()
    
    def get_service_info(self) -> Dict:
        """获取服务信息"""
        if not self.initialized:
            return {}
        
        return self.mcp_client.get_service_info()

# 测试函数
def test_mcp_client():
    """测试MCP客户端"""
    print("🧪 测试MCP客户端")
    print("=" * 40)
    
    # 创建检索器
    retriever = MCPElasticsearchRetriever()
    
    # 初始化
    if retriever.initialize():
        print("✅ 初始化成功")
        print(retriever.get_stats())
        
        # 测试搜索
        test_queries = [
            ("气血不足怎么调理", "medical"),
            ("合同违约处理", "legal"),
            ("Python编程基础", "technology")
        ]
        
        for query, domain in test_queries:
            print(f"\n🔍 搜索: {query} (领域: {domain})")
            results = retriever.search_by_domain(query, domain, max_results=3)
            print(f"找到 {len(results)} 个结果")
            
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result.get('title', '无标题')}")
                print(f"     评分: {result.get('score', 0):.2f}")
                print(f"     内容: {result.get('content', '')[:100]}...")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    test_mcp_client()
