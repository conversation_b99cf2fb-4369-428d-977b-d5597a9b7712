"""
快速文档处理器 - 优化PDF处理速度
"""
import PyPDF2
import numpy as np
import faiss
import pickle
import json
import time
import gc
from pathlib import Path
from typing import List, Dict
import config

class FastDocumentProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
    def extract_text_from_pdf_fast(self, pdf_path: str) -> str:
        """快速PDF文本提取 - 优化版"""
        print(f"📄 开始提取PDF: {pdf_path}")
        start_time = time.time()
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                print(f"📖 总页数: {total_pages}")
                
                text = ""
                # 分批处理页面，避免内存问题
                batch_size = 10  # 每次处理10页
                
                for i in range(0, total_pages, batch_size):
                    batch_end = min(i + batch_size, total_pages)
                    print(f"   处理页面 {i+1}-{batch_end}...")
                    
                    batch_text = ""
                    for page_num in range(i, batch_end):
                        try:
                            page = pdf_reader.pages[page_num]
                            page_text = page.extract_text()
                            batch_text += page_text + "\n"
                        except Exception as e:
                            print(f"   页面 {page_num+1} 提取失败: {e}")
                            continue
                    
                    text += batch_text
                    
                    # 每批处理后清理内存
                    if i % 50 == 0:  # 每50页清理一次
                        gc.collect()
                
                extraction_time = time.time() - start_time
                print(f"✅ PDF提取完成，耗时: {extraction_time:.2f}秒")
                print(f"📝 提取文本长度: {len(text)} 字符")
                
                return text
                
        except Exception as e:
            print(f"❌ PDF提取失败: {e}")
            return ""
    
    def split_text_fast(self, text: str) -> List[str]:
        """快速文本分割 - 优化版"""
        print("🔪 开始文本分割...")
        start_time = time.time()
        
        # 使用更小的块大小以加快处理
        chunk_size = 200  # 减小到200字符
        overlap = 20      # 减小重叠
        
        chunks = []
        text_length = len(text)
        
        # 预处理：移除多余空白字符
        text = ' '.join(text.split())
        
        start = 0
        chunk_count = 0
        
        while start < text_length:
            end = start + chunk_size
            if end > text_length:
                end = text_length
            
            chunk = text[start:end]
            
            # 在句号处分割，避免截断
            if end < text_length and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = text[start:end]
            
            if len(chunk.strip()) > 10:  # 过滤太短的块
                chunks.append(chunk.strip())
                chunk_count += 1
                
                # 每100个块显示进度
                if chunk_count % 100 == 0:
                    print(f"   已分割: {chunk_count} 块")
            
            start = end - overlap
            
            if start >= text_length:
                break
        
        split_time = time.time() - start_time
        print(f"✅ 文本分割完成，耗时: {split_time:.2f}秒")
        print(f"📊 总块数: {len(chunks)}")
        
        return chunks
    
    def create_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """批量创建嵌入 - 优化版"""
        print("🔄 开始批量向量化...")
        start_time = time.time()
        
        from models.model_manager import model_manager
        
        embeddings = []
        batch_size = 32  # 批处理大小
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            print(f"   处理批次 {batch_num}/{total_batches} ({len(batch_texts)} 个文本)")
            
            try:
                # 批量处理
                batch_embeddings = []
                for text in batch_texts:
                    embedding = model_manager.get_embedding(text)
                    batch_embeddings.append(embedding)
                
                embeddings.extend(batch_embeddings)
                
                # 每10个批次清理内存
                if batch_num % 10 == 0:
                    gc.collect()
                    
            except Exception as e:
                print(f"   批次 {batch_num} 处理失败: {e}")
                # 单个处理作为备用
                for text in batch_texts:
                    try:
                        embedding = model_manager.get_embedding(text)
                        embeddings.append(embedding)
                    except:
                        # 如果单个也失败，使用零向量
                        embeddings.append(np.zeros(768))
        
        embeddings_array = np.array(embeddings).astype('float32')
        
        embedding_time = time.time() - start_time
        print(f"✅ 向量化完成，耗时: {embedding_time:.2f}秒")
        print(f"📊 向量维度: {embeddings_array.shape}")
        
        return embeddings_array
    
    def create_index_fast(self, embeddings: np.ndarray) -> bool:
        """快速创建索引"""
        print("🏗️ 创建FAISS索引...")
        start_time = time.time()
        
        try:
            dimension = embeddings.shape[1]
            
            # 使用更简单的索引类型
            self.vector_index = faiss.IndexFlatIP(dimension)
            
            # 归一化向量
            faiss.normalize_L2(embeddings)
            
            # 添加向量到索引
            self.vector_index.add(embeddings)
            
            index_time = time.time() - start_time
            print(f"✅ 索引创建完成，耗时: {index_time:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def process_pdf_fast(self, pdf_path: str) -> bool:
        """快速处理PDF文档"""
        print(f"🚀 开始快速处理PDF: {pdf_path}")
        total_start_time = time.time()
        
        try:
            # 1. 提取文本
            text = self.extract_text_from_pdf_fast(pdf_path)
            if not text:
                print("❌ 文本提取失败")
                return False
            
            # 2. 分割文本
            chunks = self.split_text_fast(text)
            if not chunks:
                print("❌ 文本分割失败")
                return False
            
            # 限制块数量以提高速度
            max_chunks = 500  # 最多处理500个块
            if len(chunks) > max_chunks:
                print(f"⚠️ 块数量过多({len(chunks)})，限制为{max_chunks}个")
                chunks = chunks[:max_chunks]
            
            # 3. 创建元数据
            metadata = []
            for i, chunk in enumerate(chunks):
                metadata.append({
                    "source": pdf_path,
                    "chunk_id": i,
                    "chunk_index": i,
                    "content": chunk
                })
            
            # 4. 创建嵌入
            embeddings = self.create_embeddings_batch(chunks)
            
            # 5. 创建索引
            if not self.create_index_fast(embeddings):
                return False
            
            # 6. 保存数据
            self.document_chunks = chunks
            self.chunk_metadata = metadata
            
            # 7. 保存到磁盘
            self.save_index_fast()
            
            total_time = time.time() - total_start_time
            print(f"🎉 PDF处理完成！总耗时: {total_time:.2f}秒")
            print(f"📊 处理了 {len(chunks)} 个文档块")
            
            return True
            
        except Exception as e:
            print(f"❌ PDF处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_index_fast(self):
        """快速保存索引"""
        print("💾 保存索引...")
        
        try:
            index_path = Path(config.VECTOR_DB_PATH)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            # 保存文档块
            with open(index_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # 保存元数据
            with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            print("✅ 索引保存完成")
            
        except Exception as e:
            print(f"❌ 索引保存失败: {e}")

# 全局快速处理器实例
fast_processor = FastDocumentProcessor()

def process_pdf_file(pdf_path: str) -> bool:
    """处理单个PDF文件的便捷函数"""
    return fast_processor.process_pdf_fast(pdf_path)

if __name__ == "__main__":
    # 测试处理
    import sys
    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
        success = process_pdf_file(pdf_file)
        if success:
            print("✅ 处理成功")
        else:
            print("❌ 处理失败")
    else:
        print("用法: python fast_document_processor.py <pdf文件路径>")
