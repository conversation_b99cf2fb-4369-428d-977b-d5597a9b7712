#!/usr/bin/env python3
"""
修复文档处理问题的脚本
"""
import os
import sys
import traceback
from pathlib import Path

def test_model_loading():
    """测试模型加载"""
    print("=== 测试模型加载 ===")
    try:
        from models.model_manager import model_manager
        
        print("1. 测试嵌入模型加载...")
        embedding_success = model_manager.load_embedding_model()
        print(f"嵌入模型加载: {'成功' if embedding_success else '失败'}")
        
        if embedding_success:
            print("2. 测试嵌入功能...")
            test_embedding = model_manager.get_embedding("测试文本")
            print(f"嵌入向量维度: {len(test_embedding)}")
            print("嵌入功能正常")
        
        return embedding_success
        
    except Exception as e:
        print(f"模型加载测试失败: {e}")
        traceback.print_exc()
        return False

def test_pdf_processing():
    """测试PDF处理"""
    print("\n=== 测试PDF处理 ===")
    try:
        from document_processor import doc_processor
        
        # 查找PDF文件
        documents_dir = Path("documents")
        pdf_files = list(documents_dir.glob("*.pdf"))
        
        if not pdf_files:
            print("未找到PDF文件")
            return False
        
        print(f"找到 {len(pdf_files)} 个PDF文件:")
        for pdf in pdf_files:
            print(f"  - {pdf.name}")
        
        # 测试单个PDF处理
        test_pdf = pdf_files[0]
        print(f"\n测试处理: {test_pdf.name}")
        
        # 提取文本
        text = doc_processor.extract_text_from_pdf(str(test_pdf))
        if text:
            print(f"提取文本长度: {len(text)} 字符")
            print(f"文本预览: {text[:200]}...")
            
            # 测试文本分块
            chunks = doc_processor.split_text_into_chunks(text)
            print(f"分块数量: {len(chunks)}")
            
            if chunks:
                print(f"第一块预览: {chunks[0][:100]}...")
                return True
        else:
            print("文本提取失败")
            return False
            
    except Exception as e:
        print(f"PDF处理测试失败: {e}")
        traceback.print_exc()
        return False

def process_documents_step_by_step():
    """逐步处理文档"""
    print("\n=== 逐步处理文档 ===")
    try:
        from document_processor import doc_processor
        from models.model_manager import model_manager
        
        # 确保模型已加载
        if not model_manager.embedding_model:
            print("嵌入模型未加载，尝试加载...")
            if not model_manager.load_embedding_model():
                print("嵌入模型加载失败，无法继续")
                return False
        
        # 查找PDF文件
        documents_dir = Path("documents")
        pdf_files = [str(f) for f in documents_dir.glob("*.pdf")]
        
        if not pdf_files:
            print("未找到PDF文件")
            return False
        
        print(f"开始处理 {len(pdf_files)} 个PDF文件...")
        
        # 逐个处理文件
        all_chunks = []
        all_metadata = []
        
        for i, pdf_file in enumerate(pdf_files):
            print(f"\n处理文件 {i+1}/{len(pdf_files)}: {Path(pdf_file).name}")
            
            # 提取文本
            text = doc_processor.extract_text_from_pdf(pdf_file)
            if not text:
                print(f"  跳过: 无法提取文本")
                continue
            
            print(f"  提取文本: {len(text)} 字符")
            
            # 分割文本
            chunks = doc_processor.split_text_into_chunks(text)
            print(f"  分割块数: {len(chunks)}")
            
            # 创建元数据
            for j, chunk in enumerate(chunks):
                metadata = {
                    "source": pdf_file,
                    "chunk_id": len(all_chunks) + j,
                    "chunk_index": j,
                    "content": chunk
                }
                all_metadata.append(metadata)
            
            all_chunks.extend(chunks)
            
            # 限制处理数量以避免内存问题
            if len(all_chunks) > 200:
                print(f"  达到块数限制 (200)，停止处理")
                break
        
        if not all_chunks:
            print("没有提取到任何文本块")
            return False
        
        print(f"\n总共提取 {len(all_chunks)} 个文本块")
        
        # 创建向量索引
        print("创建向量索引...")
        success = doc_processor.create_vector_index(all_chunks, all_metadata)
        
        if success:
            print("文档处理完成！")
            
            # 测试搜索
            print("\n测试搜索功能...")
            results = doc_processor.search_similar_chunks("栀子甘草豉汤", top_k=3)
            print(f"搜索结果数量: {len(results)}")
            
            for i, result in enumerate(results):
                print(f"结果 {i+1}:")
                print(f"  相似度: {result.get('similarity_score', 0):.3f}")
                print(f"  来源: {Path(result.get('source', '')).name}")
                print(f"  内容: {result.get('content', '')[:100]}...")
                print()
            
            return True
        else:
            print("向量索引创建失败")
            return False
            
    except Exception as e:
        print(f"文档处理失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 文档处理修复工具")
    print("=" * 50)
    
    # 1. 测试模型加载
    model_ok = test_model_loading()
    
    if not model_ok:
        print("\n❌ 模型加载失败，无法继续")
        return
    
    # 2. 测试PDF处理
    pdf_ok = test_pdf_processing()
    
    if not pdf_ok:
        print("\n❌ PDF处理失败")
        return
    
    # 3. 完整处理文档
    process_ok = process_documents_step_by_step()
    
    if process_ok:
        print("\n✅ 文档处理修复完成！")
        print("现在可以正常使用RAG系统进行问答了")
    else:
        print("\n❌ 文档处理修复失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 修复过程已取消")
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        traceback.print_exc()
