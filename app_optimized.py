"""
内存优化版RAG系统界面
"""
import streamlit as st
import psutil
import gc
import time
from pathlib import Path

# 页面配置
st.set_page_config(
    page_title="中医经典RAG问答系统 (优化版)",
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 内存监控
def show_memory_usage():
    """显示内存使用情况"""
    memory = psutil.virtual_memory()
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总内存", f"{memory.total / (1024**3):.1f} GB")
    with col2:
        st.metric("可用内存", f"{memory.available / (1024**3):.1f} GB")
    with col3:
        color = "red" if memory.percent > 90 else "orange" if memory.percent > 80 else "green"
        st.metric("使用率", f"{memory.percent:.1f}%")
        
    if memory.percent > 90:
        st.error("⚠️ 内存使用率过高，建议关闭其他应用程序")

def initialize_system_optimized():
    """优化版系统初始化"""
    if 'system_initialized' not in st.session_state:
        with st.spinner("正在初始化优化版RAG系统..."):
            try:
                # 强制垃圾回收
                gc.collect()
                
                # 检查内存
                memory = psutil.virtual_memory()
                if memory.percent > 95:
                    st.error("内存不足，无法启动系统。请关闭其他应用程序后重试。")
                    return False
                
                # 延迟导入以节省内存
                from rag_system import rag_system
                
                # 初始化系统
                success = rag_system.initialize()
                st.session_state.system_initialized = success
                
                if success:
                    st.success("✅ 优化版系统初始化成功！")
                else:
                    st.error("❌ 系统初始化失败")
                    
            except Exception as e:
                st.error(f"初始化错误: {str(e)}")
                st.session_state.system_initialized = False
                
    return st.session_state.get('system_initialized', False)

def sidebar_optimized():
    """优化版侧边栏"""
    st.sidebar.title("📚 RAG系统 (优化版)")
    
    # 内存监控
    st.sidebar.subheader("💾 系统监控")
    show_memory_usage()
    
    # 清理内存按钮
    if st.sidebar.button("🧹 清理内存"):
        gc.collect()
        st.sidebar.success("内存清理完成")
        time.sleep(1)
        st.rerun()
    
    st.sidebar.divider()
    
    # 系统状态
    st.sidebar.subheader("📊 系统状态")
    if st.session_state.get('system_initialized', False):
        st.sidebar.success("✅ 系统已就绪")
        
        try:
            from rag_system import rag_system
            status = rag_system.get_system_status()
            st.sidebar.write(f"📄 已索引: {status['documents_indexed']} 块")
            st.sidebar.write(f"🖥️ 设备: {status['device']}")
        except:
            st.sidebar.warning("⚠️ 状态获取失败")
    else:
        st.sidebar.warning("⚠️ 系统未初始化")
    
    st.sidebar.divider()
    
    # 文档管理
    st.sidebar.subheader("📁 文档管理")
    
    uploaded_files = st.sidebar.file_uploader(
        "上传PDF文档",
        type=['pdf'],
        accept_multiple_files=False,  # 限制单文件上传
        help="建议文件大小不超过10MB"
    )
    
    if uploaded_files and st.sidebar.button("处理文档"):
        process_document_optimized(uploaded_files)

def process_document_optimized(uploaded_file):
    """优化版文档处理"""
    if not st.session_state.get('system_initialized', False):
        st.error("请先初始化系统")
        return
    
    # 检查文件大小
    if uploaded_file.size > 10 * 1024 * 1024:  # 10MB限制
        st.error("文件过大，请选择小于10MB的文件")
        return
    
    # 检查内存
    memory = psutil.virtual_memory()
    if memory.percent > 90:
        st.error("内存不足，无法处理文档。请先清理内存。")
        return
    
    # 保存文件
    import config
    file_path = config.DOCUMENTS_DIR / uploaded_file.name
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    
    # 处理文档
    with st.spinner("正在处理文档，请稍候..."):
        try:
            from rag_system import rag_system
            success = rag_system.process_documents([str(file_path)])
            
            if success:
                st.success(f"✅ 文档 {uploaded_file.name} 处理完成！")
                # 清理内存
                gc.collect()
            else:
                st.error("❌ 文档处理失败")
                
        except Exception as e:
            st.error(f"处理错误: {str(e)}")

def main():
    """主界面"""
    st.title("📚 中医经典RAG问答系统 (内存优化版)")
    
    # 显示优化说明
    with st.expander("💡 优化版说明", expanded=False):
        st.write("""
        **内存优化版特点:**
        - 🔧 减少内存使用量
        - 📊 实时内存监控
        - 🧹 内存清理功能
        - 📁 限制文件大小
        - ⚡ 更快的响应速度
        
        **使用建议:**
        - 关闭其他大型应用程序
        - 单次上传小文件（<10MB）
        - 定期清理内存
        """)
    
    # 初始化系统
    if not initialize_system_optimized():
        st.stop()
    
    # 侧边栏
    sidebar_optimized()
    
    # 主要内容
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("💬 智能问答")
        
        # 简化的问答界面
        question = st.text_input(
            "请输入您的问题:",
            placeholder="例如：阴阳学说的基本概念是什么？",
            key="question_input"
        )
        
        if st.button("🔍 提问", type="primary") and question:
            handle_question_optimized(question)
    
    with col2:
        st.subheader("📊 性能监控")
        show_memory_usage()
        
        # 显示处理建议
        memory = psutil.virtual_memory()
        if memory.percent > 80:
            st.warning("💡 内存使用较高，建议:")
            st.write("- 关闭其他应用")
            st.write("- 点击清理内存")
            st.write("- 重启系统")

def handle_question_optimized(question):
    """优化版问题处理"""
    # 检查内存
    memory = psutil.virtual_memory()
    if memory.percent > 95:
        st.error("内存不足，无法处理问题")
        return
    
    with st.spinner("正在思考中..."):
        try:
            from rag_system import rag_system
            from session_manager import session_manager
            
            # 创建或获取会话
            if 'current_session_id' not in st.session_state:
                st.session_state.current_session_id = session_manager.create_session()
            
            # 生成回答
            result = rag_system.retrieve_and_generate(
                question, 
                st.session_state.current_session_id
            )
            
            if 'error' in result:
                st.error(f"错误: {result['error']}")
                return
            
            # 显示结果
            st.write("**问题:**", question)
            st.write("**回答:**", result['answer'])
            
            # 显示来源（简化版）
            if result.get('sources'):
                with st.expander("📖 参考来源"):
                    for i, source in enumerate(result['sources'][:2], 1):  # 只显示前2个来源
                        st.write(f"**来源 {i}:** {Path(source['source']).name}")
                        st.write(f"**内容:** {source['content'][:100]}...")
            
            # 清理内存
            gc.collect()
            
        except Exception as e:
            st.error(f"处理错误: {str(e)}")

if __name__ == "__main__":
    main()
