#!/usr/bin/env python3
"""
智能Ngrok启动器 - 自动检测端口并创建隧道
"""

import subprocess
import time
import requests
import json
import os
import sys
import webbrowser
from datetime import datetime

class SmartNgrokManager:
    """智能Ngrok管理器"""

    def __init__(self):
        self.ngrok_process = None
        self.public_url = None
        self.current_port = None
        self.info_file = "ngrok_分享信息.txt"

    def find_streamlit_port(self):
        """查找Streamlit运行的端口"""
        common_ports = [8501, 8502, 8503, 8504, 8505]

        for port in common_ports:
            try:
                response = requests.get(f'http://localhost:{port}', timeout=2)
                if response.status_code == 200:
                    print(f"✅ 发现Streamlit服务运行在端口 {port}")
                    return port
            except:
                continue

        print("⚠️ 未发现运行中的Streamlit服务，使用默认端口8501")
        return 8501

    def kill_existing_ngrok(self):
        """终止现有的ngrok进程"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/f', '/im', 'ngrok.exe'],
                             capture_output=True, check=False)
            else:  # Linux/Mac
                subprocess.run(['pkill', 'ngrok'],
                             capture_output=True, check=False)
            print("🔄 已清理现有ngrok进程")
        except:
            pass

    def start_ngrok_tunnel(self, port=None):
        """启动ngrok隧道"""
        try:
            # 自动检测端口
            if port is None:
                port = self.find_streamlit_port()

            self.current_port = port

            # 检查ngrok是否可用
            result = subprocess.run(['ngrok', 'version'],
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Ngrok未安装或不可用")
                return False

            # 清理现有进程
            self.kill_existing_ngrok()
            time.sleep(2)

            print(f"🚀 启动ngrok隧道，端口: {port}")

            # 启动ngrok
            self.ngrok_process = subprocess.Popen([
                'ngrok', 'http', str(port),
                '--log=stdout'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            # 等待隧道建立
            print("⏳ 等待隧道建立...")
            time.sleep(8)

            # 获取公网URL
            tunnel_url = self.get_tunnel_url()
            if tunnel_url:
                self.public_url = tunnel_url
                self.save_share_info(tunnel_url, port)
                print(f"✅ 隧道创建成功!")
                print(f"🌐 公网地址: {tunnel_url}")
                print(f"📱 本地端口: {port}")
                return True
            else:
                print("❌ 无法获取隧道URL")
                return False

        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False

    def get_tunnel_url(self):
        """获取隧道URL"""
        try:
            # 从ngrok API获取
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get('tunnels', [])
                if tunnels:
                    return tunnels[0]['public_url']
        except Exception as e:
            print(f"⚠️ API获取失败: {e}")

        return None

    def save_share_info(self, url, port):
        """保存分享信息"""
        try:
            info = f"""🏥 家庭私人医生小帮手 - 远程访问信息

🌐 公网访问地址: {url}
📱 本地端口: {port}
🔐 访问密码: MVP168918

📋 分享给朋友:
地址: {url}
密码: MVP168918

⏰ 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 使用说明:
1. 朋友打开上面的地址
2. 输入密码: MVP168918
3. 即可使用中医问诊系统

⚠️ 注意事项:
- 每次重启系统地址会变化
- 请及时分享新地址给朋友
- 系统关闭后地址失效
"""

            with open(self.info_file, 'w', encoding='utf-8') as f:
                f.write(info)

            print(f"📄 分享信息已保存到: {self.info_file}")

        except Exception as e:
            print(f"⚠️ 保存信息失败: {e}")

    def stop_tunnel(self):
        """停止隧道"""
        if self.ngrok_process:
            self.ngrok_process.terminate()
            self.ngrok_process = None
        self.kill_existing_ngrok()
        print("⏹️ Ngrok隧道已停止")

def main():
    """主函数"""
    print("🏥 智能Ngrok启动器")
    print("🎯 自动检测端口并创建远程访问隧道")
    print("=" * 50)

    manager = SmartNgrokManager()

    try:
        # 启动隧道
        if manager.start_ngrok_tunnel():
            print("\n" + "=" * 50)
            print("🎉 隧道创建成功!")
            print(f"📄 分享信息文件: {manager.info_file}")

            # 询问是否打开分享文件
            try:
                choice = input("\n是否打开分享信息文件? (y/n): ").lower().strip()
                if choice in ['y', 'yes', '是']:
                    if os.name == 'nt':  # Windows
                        os.startfile(manager.info_file)
                    else:  # Linux/Mac
                        subprocess.run(['open', manager.info_file])
            except:
                pass

            print("\n💡 隧道将保持运行，按 Ctrl+C 停止")

            # 保持运行
            try:
                while True:
                    time.sleep(10)
                    # 检查隧道状态
                    if manager.ngrok_process and manager.ngrok_process.poll() is not None:
                        print("⚠️ Ngrok进程意外退出")
                        break
            except KeyboardInterrupt:
                print("\n🛑 用户中断")
            finally:
                manager.stop_tunnel()
        else:
            print("❌ 隧道创建失败")
            return False

    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ 完成' if success else '❌ 失败'}")
    input("按回车键退出...")