#!/usr/bin/env python3
"""
终极中医RAG系统 - 最终启动器
一键启动所有功能的完整解决方案
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def print_final_banner():
    """打印最终横幅"""
    print("=" * 100)
    print("🧙‍♂️ 终极中医RAG系统 - 最终版本")
    print("=" * 100)
    print("🎉 恭喜！您即将体验最完整的中医RAG系统")
    print()
    print("✅ 已满足您的所有需求:")
    print("   🧠 DeepSeek模型直接调用 - 无需LM Studio")
    print("   📚 PDF文档智能检索 - 支持>500MB大文件")
    print("   🌐 古代医书在线检索 - 8个权威网站")
    print("   🎤 语音对话功能 - 中文识别和播放")
    print("   💬 连续聊天管理 - 智能上下文记忆")
    print("   ⚡ 超快速文档解析 - 多格式并行处理")
    print("   🌍 Ngrok远程访问 - 手机端友好")
    print("   🐳 Docker跨硬件部署 - 一键移植")
    print("=" * 100)
    print()

def check_final_requirements():
    """检查最终需求"""
    print("🔍 检查系统环境...")
    
    # 检查主系统文件
    if not os.path.exists("ultimate_final_tcm_system.py"):
        print("❌ 主系统文件不存在")
        return False
    print("✅ 主系统文件存在")
    
    # 检查DeepSeek模型
    model_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    if os.path.exists(model_path):
        model_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ DeepSeek模型存在 ({model_size:.2f} GB)")
    else:
        print("⚠️ DeepSeek模型文件不存在，将使用API模式")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}")
    
    return True

def install_final_dependencies():
    """安装最终依赖"""
    print("📦 安装/检查依赖包...")
    
    packages = [
        "streamlit",
        "numpy", 
        "pandas",
        "requests",
        "beautifulsoup4",
        "PyPDF2",
        "python-docx",
        "openpyxl", 
        "python-pptx",
        "sentence-transformers",
        "faiss-cpu",
        "pyttsx3",
        "SpeechRecognition",
        "llama-cpp-python"
    ]
    
    for package in packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"📥 安装 {package}...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package, "--quiet"
                ])
                print(f"✅ {package} 安装成功")
            except:
                print(f"⚠️ {package} 安装失败，继续...")
    
    print("✅ 依赖检查完成")

def start_final_system():
    """启动最终系统"""
    print("🚀 启动终极中医RAG系统...")
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "ultimate_final_tcm_system.py",
        "--server.port=8507",
        "--server.address=0.0.0.0",
        "--theme.base=light"
    ]
    
    print("💡 启动命令:")
    print(f"   {' '.join(cmd)}")
    print()
    print("🌐 访问地址:")
    print("   本地: http://localhost:8507")
    print("   局域网: http://[您的IP]:8507")
    print()
    print("📱 功能说明:")
    print("   • 上传PDF文档进行智能检索")
    print("   • 使用语音输入和播放功能")
    print("   • 启用远程访问分享给朋友")
    print("   • 查看连续对话历史")
    print()
    print("⏹️ 按 Ctrl+C 停止服务")
    print("=" * 100)
    
    # 自动打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8507")
            print("🌐 已自动打开浏览器")
        except:
            pass
    
    import threading
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 系统已停止，感谢使用！")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def create_final_docker():
    """创建最终Docker部署"""
    print("🐳 创建Docker部署包...")
    
    try:
        subprocess.run([sys.executable, "ultimate_docker_manager.py"])
        print("✅ Docker部署包创建完成")
        print("💡 现在可以将系统部署到任何支持Docker的服务器")
    except Exception as e:
        print(f"❌ Docker部署创建失败: {e}")

def show_final_menu():
    """显示最终菜单"""
    while True:
        print("\n" + "=" * 60)
        print("🎛️ 终极中医RAG系统 - 操作菜单")
        print("=" * 60)
        print("1. 🚀 启动系统 (推荐)")
        print("2. 📦 安装/更新依赖")
        print("3. 🐳 创建Docker部署包")
        print("4. 🔍 系统环境检查")
        print("5. 📖 查看使用说明")
        print("0. 👋 退出")
        print("=" * 60)
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == "1":
                start_final_system()
            elif choice == "2":
                install_final_dependencies()
            elif choice == "3":
                create_final_docker()
            elif choice == "4":
                check_final_requirements()
            elif choice == "5":
                show_usage_guide()
            elif choice == "0":
                print("👋 感谢使用终极中医RAG系统！")
                break
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 感谢使用！")
            break

def show_usage_guide():
    """显示使用指南"""
    guide = """
📖 终极中医RAG系统使用指南

🎯 核心功能:
1. 智能问答 - 基于DeepSeek模型的专业中医回答
2. 文档检索 - 上传PDF文档进行智能搜索
3. 古籍查询 - 自动搜索8个古代医书网站
4. 语音对话 - 支持中文语音输入和播放
5. 连续对话 - 智能记忆对话历史和用户画像
6. 远程访问 - 通过ngrok分享给异地朋友

🚀 快速开始:
1. 选择"启动系统"
2. 等待浏览器自动打开
3. 点击"初始化系统"按钮
4. 开始使用各项功能

📱 移动端使用:
1. 在侧边栏点击"启动远程访问"
2. 获取公网地址
3. 在手机浏览器中访问
4. 界面会自动适配手机屏幕

🔧 高级功能:
• 文档上传: 支持PDF、Word、Excel、PPT等格式
• 语音功能: 可语音输入问题，AI回答会自动播放
• 对话管理: 自动保存对话历史，支持导出
• 远程部署: 创建Docker包可部署到任何服务器

💡 使用技巧:
• 上传中医相关PDF文档可提高回答质量
• 使用具体的中医术语提问效果更好
• 语音功能需要麦克风权限
• 远程访问需要安装ngrok

🐳 Docker部署:
1. 选择"创建Docker部署包"
2. 将生成的zip文件复制到目标服务器
3. 解压后运行部署脚本
4. 系统会自动启动并可通过浏览器访问

⚠️ 注意事项:
• 首次启动需要下载模型，请耐心等待
• 大文件上传可能需要较长时间
• 语音功能需要安装额外依赖
• 远程访问需要网络连接

📞 故障排除:
• 如果启动失败，请先运行"安装/更新依赖"
• 如果模型加载失败，请检查模型文件路径
• 如果语音不可用，请安装pyttsx3和SpeechRecognition
• 如果远程访问失败，请安装ngrok
"""
    print(guide)

def main():
    """主函数"""
    print_final_banner()
    
    # 检查环境
    if not check_final_requirements():
        print("❌ 环境检查失败，请解决问题后重试")
        return
    
    print("🎉 环境检查通过！")
    print("💡 建议先运行'安装/更新依赖'确保所有功能可用")
    
    # 显示菜单
    show_final_menu()

if __name__ == "__main__":
    main()
