#!/usr/bin/env python3
"""
启动MCP+API+RAG集成系统
"""

import asyncio
import streamlit as st
import json
import time
from pathlib import Path
import requests
from integrated_mcp_rag_system import integrated_system

# 页面配置
st.set_page_config(
    page_title="🚀 MCP+API+RAG集成系统",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    text-align: center;
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}
.status-good {
    background-color: #E8F5E8;
    border: 2px solid #4CAF50;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}
.status-bad {
    background-color: #FFEBEE;
    border: 2px solid #F44336;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}
.architecture-box {
    background-color: #F5F5F5;
    border: 2px solid #2196F3;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

async def initialize_system():
    """初始化系统"""
    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = False
    
    if not st.session_state.system_initialized:
        with st.spinner("🚀 正在初始化MCP+API+RAG集成系统..."):
            try:
                success = await integrated_system.initialize()
                if success:
                    st.session_state.system_initialized = True
                    st.success("✅ 系统初始化成功！")
                    return True
                else:
                    st.error("❌ 系统初始化失败")
                    return False
            except Exception as e:
                st.error(f"❌ 初始化异常: {e}")
                return False
    return True

async def get_system_status():
    """获取系统状态"""
    try:
        status = await integrated_system.get_system_status()
        return status
    except Exception as e:
        st.error(f"获取系统状态失败: {e}")
        return {}

def display_architecture():
    """显示系统架构"""
    st.markdown("""
    <div class="architecture-box">
        <h3>🏗️ 系统架构</h3>
        <pre>
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   本地模型API   │◄──►│  MCP服务        │◄──►│  RAG向量数据库  │
│                 │    │ (Elasticsearch) │    │                 │
│ • DeepSeek-R1   │    │ • 智能检索      │    │ • FAISS索引     │
│ • M3E嵌入       │    │ • 文档索引      │    │ • 文档块存储    │
│ • API兼容       │    │ • MCP协议       │    │ • 元数据管理    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        └────────────────────────┼────────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   集成控制器    │
                    │                 │
                    │ • 查询路由      │
                    │ • 结果融合      │
                    │ • 智能回答      │
                    └─────────────────┘
        </pre>
    </div>
    """, unsafe_allow_html=True)

def main():
    """主界面"""
    # 标题
    st.markdown("""
    <div class="main-header">
        <h1>🚀 MCP+API+RAG集成系统</h1>
        <h3>本地模型API + Elasticsearch MCP服务 + RAG向量数据库</h3>
        <p>真正的MCP协议 + API集成 + 向量检索的完美组合</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 显示架构
    display_architecture()
    
    # 侧边栏
    with st.sidebar:
        st.header("🎛️ 系统控制")
        
        # 初始化按钮
        if st.button("🚀 初始化系统"):
            asyncio.run(initialize_system())
        
        # 系统状态
        st.subheader("📊 系统状态")
        
        if st.button("🔄 刷新状态"):
            status = asyncio.run(get_system_status())
            st.session_state.system_status = status
        
        # 显示状态
        if 'system_status' in st.session_state:
            status = st.session_state.system_status
            
            # API服务器状态
            if status.get('api_server', False):
                st.markdown('<div class="status-good">✅ API服务器: 运行中</div>', unsafe_allow_html=True)
                if status.get('available_models'):
                    st.write(f"可用模型: {', '.join(status['available_models'])}")
            else:
                st.markdown('<div class="status-bad">❌ API服务器: 未运行</div>', unsafe_allow_html=True)
            
            # MCP服务器状态
            if status.get('mcp_server', False):
                st.markdown('<div class="status-good">✅ MCP服务器: 运行中</div>', unsafe_allow_html=True)
            else:
                st.markdown('<div class="status-bad">❌ MCP服务器: 未运行</div>', unsafe_allow_html=True)
            
            # Elasticsearch状态
            if status.get('elasticsearch', False):
                st.markdown('<div class="status-good">✅ Elasticsearch: 连接正常</div>', unsafe_allow_html=True)
                es_stats = status.get('elasticsearch_stats', {})
                if es_stats:
                    st.write(f"文档数量: {es_stats.get('document_count', 0)}")
                    st.write(f"索引大小: {es_stats.get('index_size', 0)} bytes")
            else:
                st.markdown('<div class="status-bad">❌ Elasticsearch: 连接失败</div>', unsafe_allow_html=True)
        
        st.markdown("---")
        
        # 配置选项
        st.subheader("⚙️ 配置选项")
        
        chat_model = st.selectbox(
            "聊天模型",
            ["deepseek-r1", "qwen-chat"],
            index=0
        )
        
        embedding_model = st.selectbox(
            "嵌入模型",
            ["m3e-base"],
            index=0
        )
        
        max_results = st.slider(
            "最大检索结果",
            min_value=1,
            max_value=20,
            value=10
        )
        
        # 更新配置
        if st.button("💾 保存配置"):
            integrated_system.config.update({
                'default_chat_model': chat_model,
                'default_embedding_model': embedding_model,
                'max_search_results': max_results
            })
            st.success("配置已保存")
    
    # 主要内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["💬 智能问答", "📊 系统监控", "📚 文档管理", "🧪 系统测试"])
    
    with tab1:
        st.header("💬 MCP+API+RAG智能问答")
        
        # 问题输入
        user_question = st.text_area(
            "请输入您的问题：",
            height=100,
            placeholder="例如：肾虚脾虚怎么治疗？栀子甘草豉汤的功效是什么？"
        )
        
        # 搜索选项
        col1, col2 = st.columns(2)
        with col1:
            use_mcp = st.checkbox("使用MCP检索", value=True)
        with col2:
            use_api = st.checkbox("使用API嵌入", value=True)
        
        # 提交按钮
        if st.button("🔍 智能搜索", type="primary"):
            if user_question.strip():
                if not st.session_state.get('system_initialized', False):
                    st.error("请先初始化系统")
                else:
                    with st.spinner("🧠 正在进行智能搜索和回答生成..."):
                        try:
                            # 执行搜索
                            search_results = asyncio.run(
                                integrated_system.intelligent_search(
                                    user_question, use_mcp, use_api
                                )
                            )
                            
                            # 生成回答
                            answer = asyncio.run(
                                integrated_system.generate_intelligent_response(
                                    user_question, search_results
                                )
                            )
                            
                            # 显示结果
                            st.markdown("### 🎯 智能回答")
                            st.markdown(answer)
                            
                            # 显示搜索详情
                            with st.expander("📊 搜索详情", expanded=False):
                                st.json(search_results['summary'])
                                
                                if search_results['mcp_results']:
                                    st.subheader("🔍 MCP检索结果")
                                    for i, result in enumerate(search_results['mcp_results'][:3], 1):
                                        st.write(f"**结果 {i}**")
                                        st.write(f"评分: {result.get('score', 0):.3f}")
                                        st.write(f"内容: {result.get('source', {}).get('content', '')[:200]}...")
                                        st.write("---")
                                
                                if search_results['api_embeddings']:
                                    st.subheader("🧮 API嵌入信息")
                                    embedding = search_results['api_embeddings'][0]
                                    st.write(f"维度: {len(embedding['embedding'])}")
                                    st.write(f"前10个值: {embedding['embedding'][:10]}")
                            
                        except Exception as e:
                            st.error(f"搜索失败: {e}")
            else:
                st.warning("请输入问题")
    
    with tab2:
        st.header("📊 系统监控")
        
        if st.button("🔄 刷新监控数据"):
            status = asyncio.run(get_system_status())
            st.session_state.monitoring_data = status
        
        if 'monitoring_data' in st.session_state:
            data = st.session_state.monitoring_data
            
            # 系统概览
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                api_status = "🟢 正常" if data.get('api_server') else "🔴 异常"
                st.metric("API服务器", api_status)
            
            with col2:
                mcp_status = "🟢 正常" if data.get('mcp_server') else "🔴 异常"
                st.metric("MCP服务器", mcp_status)
            
            with col3:
                es_status = "🟢 正常" if data.get('elasticsearch') else "🔴 异常"
                st.metric("Elasticsearch", es_status)
            
            with col4:
                overall_status = "🟢 健康" if data.get('initialized') else "🔴 未初始化"
                st.metric("整体状态", overall_status)
            
            # 详细信息
            st.subheader("📋 详细信息")
            st.json(data)
    
    with tab3:
        st.header("📚 文档管理")
        
        # 文档上传
        uploaded_files = st.file_uploader(
            "上传文档到Elasticsearch",
            type=['txt', 'json'],
            accept_multiple_files=True,
            help="支持文本和JSON格式文档"
        )
        
        if uploaded_files:
            if st.button("📥 索引文档"):
                if not st.session_state.get('system_initialized', False):
                    st.error("请先初始化系统")
                else:
                    with st.spinner("正在索引文档..."):
                        try:
                            documents = []
                            for file in uploaded_files:
                                content = file.read().decode('utf-8')
                                doc = {
                                    'title': file.name,
                                    'content': content,
                                    'source': f'upload_{file.name}',
                                    'category': 'user_upload'
                                }
                                documents.append(doc)
                            
                            result = asyncio.run(
                                integrated_system.index_documents_to_elasticsearch(documents)
                            )
                            
                            st.success(f"✅ 索引完成: 成功 {result['successful_indexes']}, 失败 {result['failed_indexes']}")
                            
                            if result['errors']:
                                with st.expander("查看错误"):
                                    for error in result['errors']:
                                        st.error(error)
                        
                        except Exception as e:
                            st.error(f"索引失败: {e}")
        
        # 索引统计
        st.subheader("📊 索引统计")
        if st.button("📈 获取统计信息"):
            if st.session_state.get('system_initialized', False):
                try:
                    status = asyncio.run(get_system_status())
                    es_stats = status.get('elasticsearch_stats', {})
                    if es_stats:
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("文档数量", es_stats.get('document_count', 0))
                        with col2:
                            st.metric("索引大小", f"{es_stats.get('index_size', 0)} bytes")
                except Exception as e:
                    st.error(f"获取统计失败: {e}")
    
    with tab4:
        st.header("🧪 系统测试")
        
        # 预设测试用例
        test_cases = [
            "肾虚脾虚怎么治疗",
            "栀子甘草豉汤的功效",
            "湿气重的症状",
            "气血不足如何调理"
        ]
        
        selected_test = st.selectbox("选择测试用例", test_cases)
        
        if st.button("🚀 运行测试"):
            if not st.session_state.get('system_initialized', False):
                st.error("请先初始化系统")
            else:
                with st.spinner("运行系统测试..."):
                    try:
                        # 测试搜索
                        search_results = asyncio.run(
                            integrated_system.intelligent_search(selected_test)
                        )
                        
                        # 测试回答生成
                        answer = asyncio.run(
                            integrated_system.generate_intelligent_response(
                                selected_test, search_results
                            )
                        )
                        
                        # 显示测试结果
                        st.success("✅ 测试通过")
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.subheader("🔍 搜索结果")
                            st.json(search_results['summary'])
                        
                        with col2:
                            st.subheader("🧠 生成回答")
                            st.write(answer[:200] + "..." if len(answer) > 200 else answer)
                        
                    except Exception as e:
                        st.error(f"❌ 测试失败: {e}")
        
        # 性能测试
        st.subheader("⚡ 性能测试")
        if st.button("📊 运行性能测试"):
            if st.session_state.get('system_initialized', False):
                with st.spinner("运行性能测试..."):
                    try:
                        start_time = time.time()
                        
                        # 简单性能测试
                        for test_query in test_cases[:2]:  # 测试前2个
                            search_results = asyncio.run(
                                integrated_system.intelligent_search(test_query)
                            )
                        
                        end_time = time.time()
                        duration = end_time - start_time
                        
                        st.success(f"✅ 性能测试完成")
                        st.metric("平均响应时间", f"{duration/2:.2f} 秒")
                        
                    except Exception as e:
                        st.error(f"❌ 性能测试失败: {e}")

if __name__ == "__main__":
    main()
