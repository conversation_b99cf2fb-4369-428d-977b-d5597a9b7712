
// 中医智能助手 Service Worker
const CACHE_NAME = 'tcm-assistant-v1.0';
const urlsToCache = [
    '/',
    '/static/css/index.css',
    '/static/js/index.js',
    '/static/icon-192.png',
    '/static/icon-512.png'
];

// 安装事件
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// 获取事件
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // 如果缓存中有，返回缓存版本
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

// 激活事件
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
