"""
紧急内存清理脚本
"""
import gc
import psutil
import subprocess
import sys

def show_memory_status():
    """显示内存状态"""
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"   总内存: {memory.total / (1024**3):.1f} GB")
    print(f"   已使用: {memory.used / (1024**3):.1f} GB")
    print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"   使用率: {memory.percent:.1f}%")
    return memory.percent

def cleanup_python_processes():
    """清理Python进程"""
    print("\n🧹 清理Python内存...")
    
    # 强制垃圾回收
    collected = gc.collect()
    print(f"   清理了 {collected} 个对象")
    
    # 清理缓存
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("   清理了CUDA缓存")
    except:
        pass

def kill_heavy_processes():
    """终止占用内存较多的进程"""
    print("\n🔍 查找占用内存较多的Python进程...")
    
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_percent', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                if proc.info['memory_percent'] > 5:  # 占用超过5%内存的进程
                    python_processes.append(proc)
        except:
            continue
    
    if python_processes:
        print("发现以下占用内存较多的Python进程:")
        for i, proc in enumerate(python_processes):
            try:
                cmdline = ' '.join(proc.info['cmdline'][:3]) if proc.info['cmdline'] else 'Unknown'
                print(f"   {i+1}. PID: {proc.info['pid']}, 内存: {proc.info['memory_percent']:.1f}%, 命令: {cmdline}")
            except:
                continue
        
        response = input("\n是否终止这些进程？(y/n): ").lower().strip()
        if response == 'y':
            for proc in python_processes:
                try:
                    proc.terminate()
                    print(f"   终止进程 PID: {proc.info['pid']}")
                except:
                    continue

def main():
    print("🚨 紧急内存清理工具")
    print("=" * 40)
    
    # 显示初始状态
    initial_usage = show_memory_status()
    
    if initial_usage < 80:
        print("\n✅ 内存使用正常，无需清理")
        return
    
    print(f"\n⚠️ 内存使用率过高: {initial_usage:.1f}%")
    print("开始清理...")
    
    # 清理Python内存
    cleanup_python_processes()
    
    # 显示清理后状态
    print("\n" + "="*40)
    after_cleanup = show_memory_status()
    
    improvement = initial_usage - after_cleanup
    if improvement > 0:
        print(f"\n✅ 内存清理完成，释放了 {improvement:.1f}% 内存")
    else:
        print(f"\n⚠️ 内存清理效果有限")
    
    if after_cleanup > 90:
        print("\n💡 建议采取以下措施:")
        print("   1. 关闭浏览器多余标签页")
        print("   2. 关闭其他应用程序")
        print("   3. 重启计算机")
        print("   4. 使用更轻量的模型")
        
        # 询问是否终止占用内存的进程
        kill_heavy_processes()
    
    # 最终状态
    print("\n" + "="*40)
    final_usage = show_memory_status()
    
    if final_usage < 85:
        print("\n🎉 内存状态已改善，可以尝试启动RAG系统")
        print("建议使用: python start_optimized.py")
    else:
        print("\n⚠️ 内存仍然不足，建议重启计算机后再试")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 清理已取消")
    except Exception as e:
        print(f"\n❌ 清理过程中出错: {e}")
