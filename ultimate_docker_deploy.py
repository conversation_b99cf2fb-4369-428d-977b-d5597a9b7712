#!/usr/bin/env python3
"""
终极中医RAG系统 - Docker容器化部署
支持完整打包、一键部署、跨硬件移植
"""

import subprocess
import sys
import os
import json
import shutil
from pathlib import Path
import time

def print_docker_banner():
    """打印Docker横幅"""
    print("=" * 100)
    print("🐳 终极中医RAG系统 - Docker容器化部署")
    print("=" * 100)
    print("🎯 容器化特色:")
    print("")
    print("✅ 完整打包:")
    print("   🧠 DeepSeek模型 + 推理引擎")
    print("   📚 古代医书检索系统")
    print("   📄 大文件处理引擎 (>200MB)")
    print("   🔊 语音对话功能")
    print("   💬 连续对话记忆")
    print("")
    print("✅ 便携部署:")
    print("   📦 一键构建镜像")
    print("   🚚 导出/导入镜像文件")
    print("   💾 数据持久化")
    print("   🔄 跨硬件移植")
    print("")
    print("✅ 生产就绪:")
    print("   🌐 Web服务自动启动")
    print("   📊 健康检查")
    print("   🔧 环境变量配置")
    print("   📝 日志管理")
    print("=" * 100)

def check_docker():
    """检查Docker是否安装"""
    print("🔍 检查Docker环境...")
    
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Docker已安装: {version}")
            
            # 检查Docker是否运行
            result = subprocess.run(["docker", "info"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Docker服务正在运行")
                return True
            else:
                print("❌ Docker服务未运行，请启动Docker")
                return False
        else:
            print("❌ Docker未正确安装")
            return False
    except FileNotFoundError:
        print("❌ Docker未找到")
        print_docker_installation_guide()
        return False

def print_docker_installation_guide():
    """打印Docker安装指南"""
    print("\n💡 Docker安装指南:")
    print("   Windows: https://docs.docker.com/desktop/windows/install/")
    print("   Mac: https://docs.docker.com/desktop/mac/install/")
    print("   Linux: https://docs.docker.com/engine/install/")

def create_dockerfile():
    """创建Dockerfile"""
    print("📝 创建Dockerfile...")
    
    dockerfile_content = '''# 终极中医RAG系统 Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    cmake \\
    build-essential \\
    portaudio19-dev \\
    python3-pyaudio \\
    espeak \\
    espeak-data \\
    libespeak1 \\
    libespeak-dev \\
    festival \\
    festvox-kallpc16k \\
    curl \\
    wget \\
    git \\
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV TOKENIZERS_PARALLELISM=false
ENV HF_HUB_DISABLE_SYMLINKS_WARNING=1

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/ultimate_vector_db \\
    /app/documents \\
    /app/conversations \\
    /app/logs \\
    /app/models

# 设置权限
RUN chmod +x *.py

# 暴露端口
EXPOSE 8507

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost:8507/_stcore/health || exit 1

# 启动命令
CMD ["python", "-m", "streamlit", "run", "ultimate_working_tcm_system.py", \\
     "--server.port=8507", \\
     "--server.address=0.0.0.0", \\
     "--theme.base=light", \\
     "--server.headless=true", \\
     "--browser.gatherUsageStats=false"]
'''
    
    with open("Dockerfile", "w", encoding="utf-8") as f:
        f.write(dockerfile_content)
    
    print("✅ Dockerfile创建成功")

def create_requirements():
    """创建requirements.txt"""
    print("📝 创建requirements.txt...")
    
    requirements_content = '''# 终极中医RAG系统依赖
streamlit>=1.28.0
numpy>=1.21.0
pandas>=1.3.0
requests>=2.25.0
beautifulsoup4>=4.9.0
aiohttp>=3.8.0

# 文档处理
PyPDF2>=3.0.0
python-docx>=0.8.11
python-pptx>=0.6.21
openpyxl>=3.0.9
markdown>=3.3.0

# 向量搜索
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
scikit-learn>=1.0.0

# DeepSeek模型
llama-cpp-python>=0.2.0

# 语音功能
pyttsx3>=2.90
SpeechRecognition>=3.8.1
PyAudio>=0.2.11

# 其他工具
pathlib2>=2.3.0
python-multipart>=0.0.5
'''
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(requirements_content)
    
    print("✅ requirements.txt创建成功")

def create_docker_compose():
    """创建docker-compose.yml"""
    print("📝 创建docker-compose.yml...")
    
    compose_content = '''version: '3.8'

services:
  ultimate-tcm-rag:
    build: .
    container_name: ultimate-tcm-rag
    ports:
      - "8507:8507"
    volumes:
      - ./ultimate_vector_db:/app/ultimate_vector_db
      - ./documents:/app/documents
      - ./conversations:/app/conversations
      - ./logs:/app/logs
      - ./models:/app/models
    environment:
      - PYTHONUNBUFFERED=1
      - TOKENIZERS_PARALLELISM=false
      - HF_HUB_DISABLE_SYMLINKS_WARNING=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8507/_stcore/health"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
'''
    
    with open("docker-compose.yml", "w", encoding="utf-8") as f:
        f.write(compose_content)
    
    print("✅ docker-compose.yml创建成功")

def create_dockerignore():
    """创建.dockerignore"""
    print("📝 创建.dockerignore...")
    
    dockerignore_content = '''# 忽略文件
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/
.coverage
.env
.venv
env/
venv/

# 忽略大文件
*.tar
*.tar.gz
*.zip
*.rar

# 忽略日志
*.log
logs/

# 忽略临时文件
.DS_Store
Thumbs.db
.idea/
.vscode/

# 忽略模型文件（太大）
*.gguf
*.bin
*.safetensors

# 忽略数据库文件
*.db
*.sqlite
*.sqlite3

# 忽略Git
.git/
.gitignore
'''
    
    with open(".dockerignore", "w", encoding="utf-8") as f:
        f.write(dockerignore_content)
    
    print("✅ .dockerignore创建成功")

def build_docker_image():
    """构建Docker镜像"""
    print("\n🔨 构建Docker镜像...")
    
    try:
        # 构建镜像
        cmd = [
            "docker", "build", 
            "-t", "ultimate-tcm-rag:latest",
            "-f", "Dockerfile",
            "."
        ]
        
        print("⏳ 正在构建镜像，这可能需要几分钟...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        
        if result.returncode == 0:
            print("✅ Docker镜像构建成功")
            
            # 显示镜像信息
            result = subprocess.run(["docker", "images", "ultimate-tcm-rag"], 
                                  capture_output=True, text=True)
            print("📊 镜像信息:")
            print(result.stdout)
            
            return True
        else:
            print("❌ Docker镜像构建失败:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def run_docker_container():
    """运行Docker容器"""
    print("\n🚀 启动Docker容器...")
    
    try:
        # 停止现有容器
        subprocess.run(["docker", "stop", "ultimate-tcm-rag"], 
                      capture_output=True, text=True)
        subprocess.run(["docker", "rm", "ultimate-tcm-rag"], 
                      capture_output=True, text=True)
        
        # 启动新容器
        cmd = [
            "docker", "run", "-d",
            "--name", "ultimate-tcm-rag",
            "-p", "8507:8507",
            "-v", f"{os.getcwd()}/ultimate_vector_db:/app/ultimate_vector_db",
            "-v", f"{os.getcwd()}/documents:/app/documents",
            "-v", f"{os.getcwd()}/conversations:/app/conversations",
            "-v", f"{os.getcwd()}/logs:/app/logs",
            "ultimate-tcm-rag:latest"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            print(f"✅ 容器启动成功: {container_id[:12]}")
            
            # 等待容器启动
            print("⏳ 等待服务启动...")
            time.sleep(30)
            
            # 检查容器状态
            result = subprocess.run(["docker", "ps", "-f", "name=ultimate-tcm-rag"], 
                                  capture_output=True, text=True)
            print("📊 容器状态:")
            print(result.stdout)
            
            print("🌐 访问地址: http://localhost:8507")
            return True
        else:
            print("❌ 容器启动失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 启动过程出错: {e}")
        return False

def export_docker_image():
    """导出Docker镜像"""
    print("\n📦 导出Docker镜像...")
    
    try:
        export_file = f"ultimate-tcm-rag-{int(time.time())}.tar"
        
        cmd = ["docker", "save", "-o", export_file, "ultimate-tcm-rag:latest"]
        
        print("⏳ 正在导出镜像...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            file_size = os.path.getsize(export_file) / (1024 * 1024 * 1024)  # GB
            print(f"✅ 镜像导出成功: {export_file}")
            print(f"📊 文件大小: {file_size:.2f} GB")
            
            # 创建导入说明
            import_guide = f'''# 终极中医RAG系统 - 镜像导入指南

## 导入镜像
```bash
docker load -i {export_file}
```

## 运行容器
```bash
docker run -d \\
  --name ultimate-tcm-rag \\
  -p 8507:8507 \\
  -v ./ultimate_vector_db:/app/ultimate_vector_db \\
  -v ./documents:/app/documents \\
  -v ./conversations:/app/conversations \\
  -v ./logs:/app/logs \\
  ultimate-tcm-rag:latest
```

## 访问系统
http://localhost:8507

## 系统功能
- 🧠 DeepSeek模型推理
- 📚 古代医书检索
- 📄 大文件处理 (>200MB)
- 🔊 语音对话功能
- 💬 连续对话记忆
- 📱 移动端适配
'''
            
            with open(f"import_guide_{int(time.time())}.md", "w", encoding="utf-8") as f:
                f.write(import_guide)
            
            return export_file
        else:
            print("❌ 镜像导出失败:")
            print(result.stderr)
            return None
            
    except subprocess.TimeoutExpired:
        print("⏰ 导出超时")
        return None
    except Exception as e:
        print(f"❌ 导出过程出错: {e}")
        return None

def show_container_logs():
    """显示容器日志"""
    print("\n📝 显示容器日志...")
    
    try:
        result = subprocess.run(["docker", "logs", "--tail", "50", "ultimate-tcm-rag"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📊 最近50行日志:")
            print(result.stdout)
        else:
            print("❌ 获取日志失败")
            
    except Exception as e:
        print(f"❌ 获取日志出错: {e}")

def main():
    """主函数"""
    print_docker_banner()
    
    # 检查Docker
    if not check_docker():
        input("按回车键退出...")
        return
    
    print("\n🎯 选择操作:")
    print("1. 构建并运行容器")
    print("2. 仅构建镜像")
    print("3. 仅运行容器")
    print("4. 导出镜像文件")
    print("5. 查看容器日志")
    print("6. 停止容器")
    
    choice = input("\n请选择操作 (1-6): ").strip()
    
    if choice == "1":
        # 构建并运行
        print("\n🚀 开始构建并运行终极系统...")
        
        # 创建必要文件
        create_dockerfile()
        create_requirements()
        create_docker_compose()
        create_dockerignore()
        
        # 构建镜像
        if build_docker_image():
            # 运行容器
            if run_docker_container():
                print("\n🎉 终极中医RAG系统Docker部署完成！")
                print("🌐 访问地址: http://localhost:8507")
                
                # 询问是否导出
                export_choice = input("\n是否导出镜像文件用于移植? (y/n): ").lower().strip()
                if export_choice == 'y':
                    export_file = export_docker_image()
                    if export_file:
                        print(f"📦 镜像已导出: {export_file}")
                        print("💡 可以将此文件复制到其他电脑上导入使用")
            else:
                print("❌ 容器运行失败")
        else:
            print("❌ 镜像构建失败")
    
    elif choice == "2":
        # 仅构建
        create_dockerfile()
        create_requirements()
        create_docker_compose()
        create_dockerignore()
        build_docker_image()
    
    elif choice == "3":
        # 仅运行
        run_docker_container()
    
    elif choice == "4":
        # 导出镜像
        export_file = export_docker_image()
        if export_file:
            print(f"📦 镜像已导出: {export_file}")
    
    elif choice == "5":
        # 查看日志
        show_container_logs()
    
    elif choice == "6":
        # 停止容器
        print("🛑 停止容器...")
        subprocess.run(["docker", "stop", "ultimate-tcm-rag"])
        subprocess.run(["docker", "rm", "ultimate-tcm-rag"])
        print("✅ 容器已停止并删除")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
