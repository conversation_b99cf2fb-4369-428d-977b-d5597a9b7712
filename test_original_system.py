#!/usr/bin/env python3
"""
测试原始系统
"""
from family_tcm_system import family_tcm_system

print('🔍 测试家庭中医系统初始化...')
try:
    success = family_tcm_system.initialize_system()
    print(f'初始化结果: {success}')
    
    if success:
        print('✅ 系统初始化成功')
        
        # 测试查询功能
        print('\n🔍 测试查询功能...')
        result = family_tcm_system.query_tcm_knowledge('栀子甘草豉汤方', 'test_user')
        
        if 'error' in result:
            print(f'❌ 查询失败: {result["error"]}')
        else:
            print('✅ 查询成功')
            if 'answer' in result:
                print(f'回答长度: {len(result["answer"])} 字符')
                print(f'回答预览: {result["answer"][:200]}...')
    else:
        print('❌ 系统初始化失败')
        
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
