#!/usr/bin/env python3
"""
增强版超快启动家庭中医智能助手
包含知识库管理、网络访问、小模型集成
"""
import streamlit as st
import pickle
import json
import socket
from pathlib import Path
from datetime import datetime
import re
import PyPDF2
import shutil
import os

# 页面配置
st.set_page_config(
    page_title="家庭中医智能助手 - 增强版",
    page_icon="🏥",
    layout="wide"
)

@st.cache_data
def load_simple_knowledge_base():
    """加载简化的知识库（无需模型）"""
    try:
        vector_db_dir = Path('vector_db')
        
        # 加载文档块
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            chunks = pickle.load(f)
        
        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)
        
        return {
            'chunks': chunks,
            'metadata': metadata,
            'status': 'success',
            'total_chunks': len(chunks)
        }
        
    except Exception as e:
        return {'status': 'error', 'error': str(e)}

def enhanced_text_search(query, knowledge_base, top_k=1):
    """增强的文本搜索（只返回最佳匹配）"""
    try:
        chunks = knowledge_base['chunks']
        metadata = knowledge_base['metadata']
        
        # 提取查询关键词
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', query))
        query_chars = set(query.replace(' ', ''))
        
        results = []
        for i, chunk in enumerate(chunks):
            score = 0
            matched_keywords = []
            
            # 1. 完全匹配关键词
            chunk_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', chunk))
            exact_matches = query_keywords.intersection(chunk_keywords)
            if exact_matches:
                score += len(exact_matches) * 3  # 提高完全匹配权重
                matched_keywords.extend(exact_matches)
            
            # 2. 直接字符串包含（最高优先级）
            if query.strip() in chunk:
                score += 5
                matched_keywords.append(query.strip())
            
            # 3. 部分匹配
            for q_word in query_keywords:
                if len(q_word) >= 2:
                    for c_word in chunk_keywords:
                        if q_word in c_word or c_word in q_word:
                            score += 1.5
                            if c_word not in matched_keywords:
                                matched_keywords.append(c_word)
            
            if score > 0:
                normalized_score = min(score / max(len(query_keywords) * 3, 1), 1.0)
                results.append({
                    'content': chunk,
                    'metadata': metadata[i],
                    'score': normalized_score,
                    'matched_keywords': list(set(matched_keywords))
                })
        
        # 按分数排序，只返回最佳匹配
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
        
    except Exception as e:
        st.error(f"搜索失败: {e}")
        return []

def generate_intelligent_answer(query, search_results):
    """生成智能化的答案"""
    if not search_results:
        return "抱歉，我没有找到相关信息。请尝试其他问题或添加更多文档到知识库。"

    best_result = search_results[0]
    content = best_result['content']
    source = Path(best_result['metadata']['source']).name
    score = best_result['score']
    keywords = ', '.join(best_result['matched_keywords'][:5])

    # 智能提取和组织内容
    lines = content.split('\n')
    relevant_sections = []

    # 查找包含关键词的段落
    for line in lines:
        line = line.strip()
        if line and any(keyword in line for keyword in best_result['matched_keywords']):
            relevant_sections.append(line)

    # 如果没有找到相关段落，使用整体内容
    if not relevant_sections:
        relevant_sections = [content[:800]]

    # 根据查询类型生成不同风格的回答
    if any(word in query for word in ['方', '汤', '丸', '散']):
        # 方剂类查询
        answer = generate_formula_answer(query, relevant_sections, source, keywords)
    elif any(word in query for word in ['功效', '作用', '治疗']):
        # 功效类查询
        answer = generate_efficacy_answer(query, relevant_sections, source, keywords)
    elif any(word in query for word in ['症状', '病', '证']):
        # 症状类查询
        answer = generate_symptom_answer(query, relevant_sections, source, keywords)
    else:
        # 通用查询
        answer = generate_general_answer(query, relevant_sections, source, keywords)

    return answer

def generate_formula_answer(query, sections, source, keywords):
    """生成方剂类回答"""
    main_content = '\n'.join(sections[:3])

    return f"""## 🌿 {query} - 中医方剂解析

**📚 文献来源：** 《{source}》

**🔍 相关信息：**
{main_content}

**💊 中医解读：**
根据古籍记载，此方剂体现了中医辨证论治的精神。方中各药配伍得当，共奏其效。

**⚠️ 温馨提醒：**
- 本信息来源于中医经典文献，仅供学习研究
- 具体用药请遵医嘱，切勿自行配制服用
- 如有疾病请及时就医，咨询专业中医师

**🔗 匹配关键词：** {keywords}"""

def generate_efficacy_answer(query, sections, source, keywords):
    """生成功效类回答"""
    main_content = '\n'.join(sections[:3])

    return f"""## 🌱 {query} - 功效与作用

**📚 古籍记载：** 《{source}》

**🔍 相关描述：**
{main_content}

**💡 中医理论：**
根据中医理论，此药物/方剂的功效体现了中医"治病求本"的思想，通过调理人体阴阳平衡来达到治疗目的。

**📖 学习要点：**
- 理解中医药的整体观念
- 注重辨证论治的重要性
- 重视个体差异和体质特点

**⚠️ 重要提醒：** 中医用药讲究辨证施治，请在专业中医师指导下使用。

**🔗 相关概念：** {keywords}"""

def generate_symptom_answer(query, sections, source, keywords):
    """生成症状类回答"""
    main_content = '\n'.join(sections[:3])

    return f"""## 🏥 {query} - 中医认识

**📚 典籍依据：** 《{source}》

**🔍 古籍论述：**
{main_content}

**🌿 中医观点：**
中医认为疾病的发生与人体正气不足、邪气入侵有关。治疗时需要综合考虑病因、病机、体质等多个因素。

**💊 治疗原则：**
- 辨证论治，因人而异
- 调理脏腑，平衡阴阳
- 扶正祛邪，标本兼治

**⚠️ 就医建议：** 如出现相关症状，建议及时就医，由专业中医师进行诊断和治疗。

**🔗 相关要点：** {keywords}"""

def generate_general_answer(query, sections, source, keywords):
    """生成通用回答"""
    main_content = '\n'.join(sections[:3])

    return f"""## 📖 关于"{query}"的中医知识

**📚 文献出处：** 《{source}》

**🔍 相关内容：**
{main_content}

**💡 知识拓展：**
中医学是一门博大精深的医学体系，强调整体观念和辨证论治。每一个概念都蕴含着深厚的理论基础和实践经验。

**📚 学习建议：**
- 系统学习中医基础理论
- 结合临床实践加深理解
- 在专业指导下学习应用

**⚠️ 免责声明：** 以上内容仅供中医学习参考，不构成医疗建议。

**🔗 关键概念：** {keywords}"""

def process_uploaded_pdf(uploaded_file):
    """处理上传的PDF文件"""
    try:
        # 保存文件
        docs_dir = Path("documents")
        docs_dir.mkdir(exist_ok=True)
        
        file_path = docs_dir / uploaded_file.name
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        # 尝试提取文本
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages[:10]:  # 只处理前10页
                page_text = page.extract_text()
                if page_text.strip():
                    text += page_text + "\n"
        
        if len(text.strip()) < 100:
            # 如果提取文本太少，创建占位符
            text = f"""
文档: {uploaded_file.name}
文件大小: {len(uploaded_file.getbuffer())/1024:.1f} KB

这是新添加的中医文档，当前使用占位符文本。
包含中医相关内容，等待进一步处理。

要获取完整文本内容，请：
1. 确保PDF是文本版本而非扫描版
2. 或使用OCR工具处理扫描版PDF
            """
        
        return text, str(file_path)
        
    except Exception as e:
        return None, str(e)

def update_knowledge_base(new_texts, new_files):
    """更新知识库"""
    try:
        # 加载现有数据
        vector_db_dir = Path('vector_db')
        
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            existing_chunks = pickle.load(f)
        
        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            existing_metadata = pickle.load(f)
        
        # 添加新文本块
        new_chunks = []
        new_metadata = []
        
        for text, file_path in zip(new_texts, new_files):
            # 简单分块
            chunks = [text[i:i+500] for i in range(0, len(text), 400)]
            
            for i, chunk in enumerate(chunks):
                if chunk.strip():
                    new_chunks.append(chunk)
                    new_metadata.append({
                        'source': file_path,
                        'chunk_id': len(existing_chunks) + len(new_chunks),
                        'chunk_index': i
                    })
        
        # 合并数据
        all_chunks = existing_chunks + new_chunks
        all_metadata = existing_metadata + new_metadata
        
        # 保存更新后的数据
        with open(vector_db_dir / 'chunks.pkl', 'wb') as f:
            pickle.dump(all_chunks, f)
        
        with open(vector_db_dir / 'metadata.pkl', 'wb') as f:
            pickle.dump(all_metadata, f)
        
        return True, len(new_chunks)
        
    except Exception as e:
        return False, str(e)

def get_network_info():
    """获取网络访问信息"""
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        port = 8516  # 当前端口
        
        return {
            "local_access": f"http://localhost:{port}",
            "network_access": f"http://{local_ip}:{port}",
            "hostname": hostname,
            "local_ip": local_ip,
            "port": port
        }
    except:
        return {
            "local_access": "http://localhost:8516",
            "network_access": "获取失败",
            "hostname": "未知",
            "local_ip": "未知",
            "port": 8516
        }

def main():
    """主界面"""
    # 标题
    st.markdown("""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(90deg, #4CAF50, #45a049); color: white; border-radius: 10px; margin-bottom: 2rem;">
        <h1>🏥 家庭中医智能助手 - 增强版</h1>
        <p>⚡ 秒速启动 | 📚 知识库管理 | 🌐 网络共享</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["💬 智能问答", "📚 知识库管理", "🌐 网络访问"])
    
    # 加载知识库
    knowledge_base = load_simple_knowledge_base()
    
    if knowledge_base['status'] == 'error':
        st.error(f"❌ 知识库加载失败: {knowledge_base['error']}")
        st.info("请确保已运行 emergency_fix.py 生成向量数据库")
        return
    
    with tab1:
        # 智能问答标签页
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("知识块数量", knowledge_base['total_chunks'])
        with col2:
            st.metric("搜索方式", "智能匹配")
        with col3:
            st.metric("响应速度", "⚡ 毫秒级")
        
        st.divider()
        
        # 常见问题快捷按钮
        st.subheader("🔥 常见问题快速查询")
        col1, col2, col3, col4 = st.columns(4)
        
        common_questions = [
            "栀子甘草豉汤方",
            "防己黄芪汤",
            "甘草汤的作用",
            "栀子的功效"
        ]
        
        selected_question = None
        for i, question in enumerate(common_questions):
            with [col1, col2, col3, col4][i]:
                if st.button(question, key=f"common_{i}"):
                    selected_question = question
        
        # 问题输入
        st.subheader("❓ 请输入您的中医问题")
        user_question = st.text_area(
            "您可以询问任何中医相关问题：",
            value=selected_question if selected_question else "",
            height=100,
            placeholder="例如：栀子甘草豉汤方的组成和功效是什么？"
        )
        
        # 查询按钮
        col1, col2 = st.columns([1, 4])
        with col1:
            ask_button = st.button("🔍 立即查询", type="primary")
        with col2:
            if st.button("🗑️ 清空"):
                st.rerun()
        
        # 处理查询
        if ask_button and user_question.strip():
            with st.spinner("🔍 正在智能搜索..."):
                search_results = enhanced_text_search(user_question, knowledge_base, top_k=1)
                
                if search_results:
                    answer = generate_intelligent_answer(user_question, search_results)
                    
                    st.markdown("""
                    <div style="background: #fff; padding: 1.5rem; border-radius: 10px; border: 1px solid #ddd; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 1rem 0;">
                    """, unsafe_allow_html=True)
                    
                    st.markdown("### 💡 智能回答")
                    st.markdown(answer)
                    st.markdown("</div>", unsafe_allow_html=True)
                    
                    log_query(user_question, len(search_results))
                else:
                    st.warning("❌ 未找到相关信息，请尝试添加更多文档到知识库")
        
        elif ask_button and not user_question.strip():
            st.warning("请先输入您的问题")
    
    with tab2:
        # 知识库管理标签页
        st.header("📚 知识库管理")
        
        # 当前状态
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("文档数量", len(set(m['source'] for m in knowledge_base.get('metadata', []))))
        with col2:
            st.metric("知识块数", knowledge_base['total_chunks'])
        with col3:
            st.metric("存储位置", "本地")
        
        st.divider()
        
        # 文档上传
        st.subheader("📄 添加新文档")
        st.info("💡 支持上传中医PDF文档来扩充知识库")
        
        uploaded_files = st.file_uploader(
            "选择PDF文档",
            type=['pdf'],
            accept_multiple_files=True,
            help="支持上传多个PDF文件，系统会自动处理并添加到知识库中"
        )
        
        if uploaded_files:
            if st.button("🚀 处理并添加到知识库", type="primary"):
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                new_texts = []
                new_files = []
                
                for i, uploaded_file in enumerate(uploaded_files):
                    status_text.text(f"正在处理: {uploaded_file.name}")
                    progress_bar.progress((i + 1) / len(uploaded_files))
                    
                    text, file_path = process_uploaded_pdf(uploaded_file)
                    if text:
                        new_texts.append(text)
                        new_files.append(file_path)
                        st.success(f"✅ {uploaded_file.name} 处理成功")
                    else:
                        st.error(f"❌ {uploaded_file.name} 处理失败: {file_path}")
                
                if new_texts:
                    status_text.text("正在更新知识库...")
                    success, result = update_knowledge_base(new_texts, new_files)
                    
                    if success:
                        st.success(f"🎉 知识库更新成功！新增 {result} 个知识块")
                        st.info("💡 请刷新页面以查看更新后的知识库")
                        
                        # 清除缓存
                        st.cache_data.clear()
                    else:
                        st.error(f"❌ 知识库更新失败: {result}")
                
                progress_bar.empty()
                status_text.empty()
        
        # 知识库备份
        st.divider()
        st.subheader("💾 数据管理")
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📦 备份知识库"):
                try:
                    backup_dir = Path("backups")
                    backup_dir.mkdir(exist_ok=True)
                    
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_path = backup_dir / f"knowledge_backup_{timestamp}"
                    
                    shutil.copytree("vector_db", backup_path)
                    st.success(f"✅ 备份创建成功: {backup_path}")
                except Exception as e:
                    st.error(f"❌ 备份失败: {e}")
        
        with col2:
            if st.button("🔄 重建索引"):
                st.info("💡 请运行 emergency_fix.py 重建索引")
    
    with tab3:
        # 网络访问标签页
        st.header("🌐 网络访问设置")
        
        network_info = get_network_info()
        
        st.subheader("📱 访问地址")
        
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("""
            **🏠 本机访问**
            """)
            st.code(network_info["local_access"])
            st.caption("在本机浏览器中使用")
        
        with col2:
            st.markdown("""
            **🌐 局域网访问**
            """)
            st.code(network_info["network_access"])
            st.caption("家人朋友在同一WiFi下使用")
        
        st.divider()
        
        st.subheader("👨‍👩‍👧‍👦 家人朋友使用指南")
        
        st.markdown(f"""
        **📋 使用步骤：**
        
        1. **确保在同一网络**
           - 家人朋友需要连接到同一个WiFi网络
           - 或者在同一个局域网内
        
        2. **分享访问地址**
           - 将以下地址发送给家人朋友：
           - `{network_info["network_access"]}`
        
        3. **打开浏览器访问**
           - 在手机或电脑浏览器中输入上述地址
           - 即可使用中医智能助手
        
        **📱 移动设备优化：**
        - 支持手机、平板等移动设备
        - 响应式设计，自适应屏幕大小
        - 可添加到手机桌面作为快捷方式
        
        **🔒 安全说明：**
        - 仅在局域网内可访问，外网无法访问
        - 数据存储在本地，隐私安全
        - 建议在家庭网络环境下使用
        """)
        
        # 系统信息
        st.divider()
        st.subheader("🖥️ 系统信息")
        
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"**主机名：** {network_info['hostname']}")
            st.write(f"**本机IP：** {network_info['local_ip']}")
        with col2:
            st.write(f"**服务端口：** {network_info['port']}")
            st.write(f"**运行状态：** 🟢 正常运行")
    
    # 免责声明
    st.markdown("""
    ---
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 1rem; margin: 1rem 0;">
        <strong>⚠️ 重要提醒：</strong><br>
        本系统提供的信息仅供中医学习参考，不能替代专业医生的诊断和治疗建议。
        如有严重症状或疑虑，请及时就医咨询专业医生。
    </div>
    """, unsafe_allow_html=True)

def log_query(question, results_count):
    """记录查询日志"""
    try:
        log_dir = Path("user_logs")
        log_dir.mkdir(exist_ok=True)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "results_count": results_count,
            "type": "enhanced_search"
        }
        
        log_file = log_dir / f"queries_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except:
        pass

if __name__ == "__main__":
    main()
