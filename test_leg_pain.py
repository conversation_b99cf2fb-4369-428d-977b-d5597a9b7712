#!/usr/bin/env python3
"""
测试腿疼问题的智能回答
"""
import asyncio
import sys
import time
from simple_ultimate_tcm import SimpleResponseGenerator, SimpleDocumentProcessor, SimpleOnlineCrawler

async def test_leg_pain_query():
    """测试腿疼怎么办的查询"""
    print("🧪 测试腿疼问题的智能回答")
    print("=" * 60)
    
    # 初始化系统组件
    print("🚀 初始化中医RAG系统...")
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    tcm_system = SimpleResponseGenerator(doc_processor, crawler)
    
    # 测试查询
    query = "腿疼怎么办"
    print(f"📝 用户问题: {query}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 生成回答
        response, sources, processing_time = await tcm_system.generate_response(query)
        
        print(f"✅ 处理成功 (耗时: {processing_time:.2f}秒)")
        print(f"📚 来源数量: {len(sources)}条")
        
        # 分析来源类型
        online_sources = [s for s in sources if s.get('type') == 'online']
        local_sources = [s for s in sources if s.get('type') == 'pdf']
        
        print(f"🌐 在线资源: {len(online_sources)}条")
        print(f"📁 本地资源: {len(local_sources)}条")
        print()
        
        print("🤖 智能回答:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
        # 分析回答质量
        print("\n📊 回答质量分析:")
        print(f"• 回答长度: {len(response)} 字符")
        print(f"• 是否包含具体建议: {'是' if '建议' in response or '治疗' in response else '否'}")
        print(f"• 是否包含中医理论: {'是' if '中医' in response or '辨证' in response else '否'}")
        print(f"• 是否包含注意事项: {'是' if '注意' in response or '提醒' in response else '否'}")
        
        # 检查是否针对腿疼问题
        leg_pain_keywords = ['腿', '膝', '下肢', '腿部', '膝盖', '腿痛', '肝肾', '寒湿']
        relevant_keywords = [kw for kw in leg_pain_keywords if kw in response]
        print(f"• 腿疼相关关键词: {relevant_keywords}")
        
        if len(relevant_keywords) >= 2:
            print("✅ 回答针对性强，包含腿疼相关内容")
        else:
            print("❌ 回答针对性不足，缺乏腿疼专门内容")
        
        # 显示来源信息
        if sources:
            print("\n📚 参考来源详情:")
            for i, source in enumerate(sources[:5], 1):
                source_type = "🌐 在线" if source.get('type') == 'online' else "📁 本地"
                score = source.get('score', 0)
                print(f"{i}. {source_type} {source.get('source', '未知来源')} (相关度: {score:.2f})")
                content_preview = source.get('content', '')[:100]
                print(f"   内容预览: {content_preview}...")
                print()
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

async def test_multiple_questions():
    """测试多个不同问题"""
    print("\n" + "=" * 60)
    print("🧪 测试多个不同问题的智能回答")
    print("=" * 60)
    
    # 初始化系统
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    tcm_system = SimpleResponseGenerator(doc_processor, crawler)
    
    test_questions = [
        "腿疼怎么办",
        "膝盖疼痛如何治疗",
        "手臂酸痛怎么缓解",
        "胸闷气短的原因",
        "头晕头痛怎么调理"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试问题 {i}: {question}")
        print("-" * 40)
        
        try:
            response, sources, processing_time = await tcm_system.generate_response(question)
            
            # 简要分析
            print(f"⏱️ 处理时间: {processing_time:.2f}秒")
            print(f"📚 来源数量: {len(sources)}条")
            print(f"📝 回答长度: {len(response)}字符")
            
            # 检查是否包含问题相关关键词
            question_keywords = question.replace('怎么办', '').replace('如何', '').replace('治疗', '').replace('的', '').replace('原因', '')
            if question_keywords in response:
                print("✅ 回答针对性强")
            else:
                print("❌ 回答针对性不足")
                
            # 显示回答摘要
            response_summary = response[:150] + "..." if len(response) > 150 else response
            print(f"💬 回答摘要: {response_summary}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
        
        print()

if __name__ == "__main__":
    print("🚀 开始智能回答系统测试...")
    
    # 测试单个问题
    asyncio.run(test_leg_pain_query())
    
    # 测试多个问题
    asyncio.run(test_multiple_questions())
    
    print("\n🎉 测试完成！")
