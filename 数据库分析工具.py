#!/usr/bin/env python3
"""
数据库分析工具 - 分析当前知识库状况
"""

import os
import sqlite3
import json
from pathlib import Path
import numpy as np

def analyze_vector_database():
    """分析向量数据库"""
    print("📊 向量数据库分析")
    print("=" * 50)
    
    db_path = "vector_store.db"
    if not os.path.exists(db_path):
        print("❌ 向量数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 数据库表: {[table[0] for table in tables]}")
        
        # 分析documents表
        if ('documents',) in tables:
            cursor.execute("SELECT COUNT(*) FROM documents")
            doc_count = cursor.fetchone()[0]
            print(f"📄 文档数量: {doc_count}")
            
            cursor.execute("SELECT filename, COUNT(*) as chunks FROM documents GROUP BY filename")
            files = cursor.fetchall()
            print(f"📁 文件分布:")
            for filename, chunks in files:
                print(f"   - {filename}: {chunks} 个文本块")
            
            # 分析文本块大小
            cursor.execute("SELECT LENGTH(content) FROM documents")
            lengths = [row[0] for row in cursor.fetchall()]
            if lengths:
                print(f"📏 文本块统计:")
                print(f"   - 平均长度: {np.mean(lengths):.0f} 字符")
                print(f"   - 最大长度: {max(lengths)} 字符")
                print(f"   - 最小长度: {min(lengths)} 字符")
        
        # 分析embeddings表
        if ('embeddings',) in tables:
            cursor.execute("SELECT COUNT(*) FROM embeddings")
            embedding_count = cursor.fetchone()[0]
            print(f"🧠 向量数量: {embedding_count}")
            
            # 获取向量维度
            cursor.execute("SELECT embedding FROM embeddings LIMIT 1")
            sample = cursor.fetchone()
            if sample:
                embedding = json.loads(sample[0])
                print(f"📐 向量维度: {len(embedding)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库分析失败: {e}")

def analyze_knowledge_sources():
    """分析知识来源"""
    print("\n🔍 知识来源分析")
    print("=" * 50)
    
    # 检查PDF文件
    pdf_dir = Path(".")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    print(f"📚 本地PDF文件: {len(pdf_files)}")
    for pdf in pdf_files:
        size_mb = pdf.stat().st_size / (1024 * 1024)
        print(f"   - {pdf.name}: {size_mb:.1f} MB")
    
    # 检查上传目录
    upload_dirs = ["uploads", "documents", "data"]
    for upload_dir in upload_dirs:
        if os.path.exists(upload_dir):
            files = list(Path(upload_dir).glob("*.*"))
            if files:
                print(f"📁 {upload_dir} 目录: {len(files)} 个文件")
                total_size = sum(f.stat().st_size for f in files) / (1024 * 1024)
                print(f"   总大小: {total_size:.1f} MB")

def estimate_capacity():
    """估算数据库容量"""
    print("\n💾 数据库容量估算")
    print("=" * 50)
    
    # SQLite理论限制
    print("📊 SQLite理论限制:")
    print("   - 最大数据库大小: 281 TB")
    print("   - 最大表大小: 268 GB")
    print("   - 最大行数: 2^64")
    
    # 实际估算
    print("\n🎯 实际容量估算 (基于m3e-base 768维向量):")
    
    # 每个文档块的存储需求
    text_size = 500  # 平均文本长度
    vector_size = 768 * 4  # 768维 float32
    metadata_size = 200  # 元数据
    total_per_chunk = text_size + vector_size + metadata_size
    
    print(f"   - 每个文本块: ~{total_per_chunk/1024:.1f} KB")
    
    # 不同规模的估算
    scales = [
        (1000, "小型知识库"),
        (10000, "中型知识库"), 
        (100000, "大型知识库"),
        (1000000, "超大型知识库")
    ]
    
    for chunks, desc in scales:
        total_size_mb = (chunks * total_per_chunk) / (1024 * 1024)
        docs_estimate = chunks // 20  # 假设每个文档20个块
        print(f"   - {desc}: {chunks:,} 块 (~{docs_estimate:,} 文档) = {total_size_mb:.0f} MB")

def recommend_optimization():
    """推荐优化方案"""
    print("\n🚀 优化建议")
    print("=" * 50)
    
    print("📈 扩展知识库建议:")
    print("1. 📚 医学文献:")
    print("   - 中医经典: 黄帝内经、伤寒论、金匮要略等")
    print("   - 现代中医: 中药学、方剂学、诊断学")
    print("   - 临床案例: 各科室病例分析")
    
    print("\n2. 🌐 在线资源:")
    print("   - 中医药数据库")
    print("   - 医学期刊文章")
    print("   - 临床指南和标准")
    
    print("\n3. 📊 数据质量优化:")
    print("   - 文本预处理: 去除噪声、标准化术语")
    print("   - 分块策略: 按章节、病症分类")
    print("   - 向量质量: 使用医学专用embedding模型")
    
    print("\n4. 🔧 性能优化:")
    print("   - 索引优化: 创建合适的数据库索引")
    print("   - 缓存策略: 常用查询结果缓存")
    print("   - 分布式存储: 大规模数据分片存储")

def main():
    """主函数"""
    print("🏥 TCM知识库分析报告")
    print("🎯 分析当前数据库状况和扩展建议")
    print()
    
    # 分析现有数据库
    analyze_vector_database()
    
    # 分析知识来源
    analyze_knowledge_sources()
    
    # 容量估算
    estimate_capacity()
    
    # 优化建议
    recommend_optimization()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("✅ 当前系统支持大规模知识库扩展")
    print("💡 建议优先添加高质量中医文献")
    print("🎯 重点关注文本质量和相关性")
    print("🔧 可根据需要进行性能优化")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
