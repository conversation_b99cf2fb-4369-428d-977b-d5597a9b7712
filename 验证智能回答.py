#!/usr/bin/env python3
"""
验证智能回答系统 - 确保不再输出模板化回答
"""

import requests
import time

def test_intelligent_responses():
    """测试智能回答系统"""
    
    print("🧠 智能回答系统验证")
    print("=" * 60)
    
    # 测试多种不同类型的问题
    test_cases = [
        {
            'query': '肚子疼，湿气重怎么治疗，男性',
            'expected_keywords': ['腹痛', '湿气', '健脾', '祛湿', '男性'],
            'avoid_keywords': ['失眠', '甘麦大枣汤', '心神不安']
        },
        {
            'query': '失眠多梦，心烦意乱怎么办',
            'expected_keywords': ['失眠', '多梦', '心烦', '安神', '养心'],
            'avoid_keywords': ['湿气', '腹痛', '祛湿']
        },
        {
            'query': '头痛头晕，女性，30岁',
            'expected_keywords': ['头痛', '头晕', '女性', '清热'],
            'avoid_keywords': ['腹痛', '失眠', '湿气']
        },
        {
            'query': '腰膝酸软，肾虚怎么调理',
            'expected_keywords': ['腰膝', '肾虚', '补肾', '调理'],
            'avoid_keywords': ['失眠', '腹痛', '湿气']
        },
        {
            'query': '咳嗽有痰，胸闷气短',
            'expected_keywords': ['咳嗽', '痰', '胸闷', '气短', '肺'],
            'avoid_keywords': ['腹痛', '失眠', '肾虚']
        }
    ]
    
    # 检查MCP服务是否运行
    try:
        response = requests.get('http://localhost:8006/health', timeout=5)
        if response.status_code != 200:
            print("❌ 智能MCP服务未运行，请先启动")
            return False
        print("✅ 智能MCP服务运行正常")
    except:
        print("❌ 无法连接智能MCP服务")
        return False
    
    print("\n开始测试不同类型问题...")
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case['query']
        expected = test_case['expected_keywords']
        avoid = test_case['avoid_keywords']
        
        print(f"\n{i}. 测试问题: {query}")
        print("-" * 40)
        
        try:
            # 调用MCP服务
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": "medical",
                    "max_results": 3
                },
                "id": f"test_{i}"
            }
            
            response = requests.post(
                'http://localhost:8006/mcp',
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result:
                    results = result['result'].get('results', [])
                    
                    if results:
                        # 检查第一个结果的相关性
                        first_result = results[0]
                        title = first_result.get('title', '')
                        content = first_result.get('content', '')
                        score = first_result.get('score', 0)
                        
                        print(f"✅ 返回 {len(results)} 个结果")
                        print(f"最佳匹配: {title} (评分: {score:.3f})")
                        print(f"内容摘要: {content[:150]}...")
                        
                        # 检查相关性
                        relevant_count = 0
                        irrelevant_count = 0
                        
                        combined_text = title + ' ' + content
                        
                        for keyword in expected:
                            if keyword in combined_text:
                                relevant_count += 1
                                print(f"  ✅ 包含期望关键词: {keyword}")
                        
                        for keyword in avoid:
                            if keyword in combined_text:
                                irrelevant_count += 1
                                print(f"  ❌ 包含无关关键词: {keyword}")
                        
                        # 评估测试结果
                        if relevant_count >= len(expected) // 2 and irrelevant_count == 0:
                            print(f"  🎉 测试通过: 相关性高，无无关内容")
                            passed_tests += 1
                        elif relevant_count > 0 and irrelevant_count <= 1:
                            print(f"  ⚠️ 测试部分通过: 有相关内容但可能有少量无关内容")
                            passed_tests += 0.5
                        else:
                            print(f"  ❌ 测试失败: 相关性低或包含过多无关内容")
                    else:
                        print("❌ 无返回结果")
                else:
                    print(f"❌ MCP错误: {result.get('error', 'unknown')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    success_rate = passed_tests / total_tests
    if success_rate >= 0.8:
        print("🎉 优秀！智能回答系统工作良好，能够针对不同问题给出相关回答")
        return True
    elif success_rate >= 0.6:
        print("✅ 良好！智能回答系统基本工作正常，但仍有改进空间")
        return True
    else:
        print("⚠️ 需要改进！智能回答系统仍存在问题")
        return False

if __name__ == "__main__":
    print("🎯 目标：验证系统不再输出模板化、无关的回答")
    print("🔍 方法：测试多种不同问题，检查回答的针对性")
    print()
    
    success = test_intelligent_responses()
    
    if success:
        print("\n💡 建议：现在可以启动完整系统测试")
        print("运行: python 启动TCM系统.py")
    else:
        print("\n🔧 需要进一步调试智能回答系统")
