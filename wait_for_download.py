#!/usr/bin/env python3
"""
等待模型下载完成的脚本
"""

import requests
import subprocess
import time
import sys

def check_models():
    """检查模型是否下载完成"""
    try:
        # 检查API
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            
            # 检查是否有DeepSeek模型
            for model in models:
                if 'deepseek' in model.lower():
                    return True, model
        
        return False, None
    except:
        return False, None

def wait_for_download():
    """等待下载完成"""
    print("⏳ 等待DeepSeek模型下载完成...")
    print("💡 这可能需要几分钟，请耐心等待")
    print("=" * 50)
    
    start_time = time.time()
    check_count = 0
    
    while True:
        check_count += 1
        elapsed = int(time.time() - start_time)
        
        print(f"\r🔍 检查 #{check_count} - 已等待 {elapsed//60}分{elapsed%60}秒", end='', flush=True)
        
        # 检查模型
        success, model_name = check_models()
        if success:
            print(f"\n\n🎉 模型下载完成: {model_name}")
            return True, model_name
        
        # 每30秒显示一次详细信息
        if check_count % 30 == 0:
            print(f"\n📊 状态更新 (第{check_count//30}次):")
            print(f"   ⏰ 已等待: {elapsed//60}分{elapsed%60}秒")
            print(f"   🔄 继续监控中...")
        
        time.sleep(1)

def test_model():
    """测试模型是否可用"""
    try:
        print("🧪 测试模型生成...")
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "deepseek-r1:8b",
                "prompt": "你好",
                "stream": False,
                "options": {"num_predict": 5}
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('response', '').strip()
            print(f"✅ 模型测试成功")
            print(f"🎯 测试回答: {content}")
            return True
        else:
            print(f"❌ 模型测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🤖 DeepSeek模型下载等待器")
    print("=" * 40)
    
    try:
        # 等待下载完成
        success, model_name = wait_for_download()
        
        if success:
            print(f"✅ 模型 {model_name} 下载完成")
            
            # 测试模型
            if test_model():
                print("\n🎊 API完全就绪！")
                print("📋 现在可以启动RAG系统了")
                print("🚀 运行命令: streamlit run ultimate_final_tcm_system.py --server.port=8507")
                return True
            else:
                print("\n⚠️ 模型下载完成但测试失败")
                return False
        else:
            print("\n❌ 等待超时或中断")
            return False
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断等待")
        return False
    except Exception as e:
        print(f"\n❌ 等待过程异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
