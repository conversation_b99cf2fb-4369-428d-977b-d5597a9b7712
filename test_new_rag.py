#!/usr/bin/env python3
"""
测试新建的RAG系统
"""
import pickle
import faiss
import numpy as np
from pathlib import Path
from sklearn.feature_extraction.text import TfidfVectorizer

def load_vector_db():
    """加载向量数据库"""
    try:
        vector_db_dir = Path('vector_db')
        
        # 加载数据
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            chunks = pickle.load(f)
        
        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)
        
        index = faiss.read_index(str(vector_db_dir / 'index.faiss'))
        
        print(f"✅ 成功加载向量数据库")
        print(f"   文档块数量: {len(chunks)}")
        print(f"   索引向量数量: {index.ntotal}")
        print(f"   索引维度: {index.d}")
        
        return chunks, metadata, index
        
    except Exception as e:
        print(f"❌ 加载向量数据库失败: {e}")
        return None, None, None

def create_tfidf_model(chunks):
    """重新创建TF-IDF模型"""
    try:
        vectorizer = TfidfVectorizer(max_features=384, stop_words=None)
        vectorizer.fit(chunks)
        print("✅ TF-IDF模型创建成功")
        return vectorizer
    except Exception as e:
        print(f"❌ TF-IDF模型创建失败: {e}")
        return None

def search_documents(query, index, vectorizer, chunks, metadata, top_k=5):
    """搜索文档"""
    try:
        # 获取查询嵌入
        query_embedding = vectorizer.transform([query]).toarray()
        query_embedding = np.array(query_embedding, dtype=np.float32)
        
        # 归一化查询向量
        faiss.normalize_L2(query_embedding)
        
        # 搜索
        distances, indices = index.search(query_embedding, top_k)
        
        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx < len(chunks) and idx >= 0:
                similarity = 1.0 / (1.0 + distance)
                results.append({
                    'content': chunks[idx],
                    'metadata': metadata[idx],
                    'score': float(similarity),
                    'distance': float(distance)
                })
        
        return results
    except Exception as e:
        print(f"搜索失败: {e}")
        return []

def generate_answer(query, search_results):
    """生成答案（简单版本）"""
    if not search_results:
        return "抱歉，我没有找到相关信息。"
    
    # 收集相关内容
    relevant_content = []
    for result in search_results[:3]:  # 使用前3个结果
        content = result['content']
        source = Path(result['metadata']['source']).name
        relevant_content.append(f"来源《{source}》: {content}")
    
    # 简单的答案生成
    answer = f"""根据中医典籍，关于"{query}"的相关信息如下：

{chr(10).join(relevant_content)}

注意：以上信息来自中医经典文献，仅供学习参考。如需医疗建议，请咨询专业中医师。"""
    
    return answer

def main():
    """主函数"""
    print("🔍 测试新建的RAG系统")
    print("=" * 50)
    
    # 1. 加载向量数据库
    chunks, metadata, index = load_vector_db()
    if chunks is None:
        return
    
    # 2. 创建TF-IDF模型
    vectorizer = create_tfidf_model(chunks)
    if vectorizer is None:
        return
    
    # 3. 测试搜索
    test_queries = [
        "栀子甘草豉汤方的组成和功效是什么？",
        "栀子甘草豉汤",
        "防己黄芪汤",
        "甘草汤的作用",
        "栀子的功效"
    ]
    
    for query in test_queries:
        print(f"\n🔍 问题: {query}")
        print("-" * 40)
        
        # 搜索
        results = search_documents(query, index, vectorizer, chunks, metadata, top_k=3)
        
        if results:
            print(f"找到 {len(results)} 个相关结果:")
            for i, result in enumerate(results):
                source = Path(result['metadata']['source']).name
                print(f"\n  结果{i+1} (相似度: {result['score']:.3f}):")
                print(f"    来源: {source}")
                print(f"    内容: {result['content'][:150]}...")
            
            # 生成答案
            print(f"\n💡 答案:")
            answer = generate_answer(query, results)
            print(answer)
            
        else:
            print("  未找到相关结果")
        
        print("\n" + "="*50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
