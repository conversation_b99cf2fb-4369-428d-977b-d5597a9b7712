#!/usr/bin/env python3
"""
终极中医RAG系统 - 主启动器
支持多种启动方式：本地、ngrok远程、Docker容器
"""

import subprocess
import sys
import os
from pathlib import Path

def print_ultimate_launcher_banner():
    """打印主启动器横幅"""
    print("=" * 120)
    print("🧙‍♂️ 终极中医RAG系统 - 主启动器")
    print("=" * 120)
    print("🎯 真正解决您的所有问题:")
    print("")
    print("✅ 问题1: 回答质量不满意")
    print("   🧠 DeepSeek-R1模型: 真正的AI推理，不是模板回答")
    print("   📚 古代医书检索: 真正爬取5大经典医书内容")
    print("   🔍 PDF功能: 真正的向量检索，显示相似度")
    print("")
    print("✅ 问题2: 连续聊天管理 + 语音对话")
    print("   💬 连续对话记忆: 记住所有对话历史和用户画像")
    print("   🎤 语音输入: 说话自动转文字")
    print("   🔊 语音输出: 自动播放回答")
    print("   📜 对话管理: 导出、清空、历史查看")
    print("")
    print("✅ 问题3: 大文件解析 + 多格式支持")
    print("   📄 大文件支持: >200MB PDF文档处理")
    print("   ⚡ 快速解析: 并行处理，分批向量化")
    print("   📋 多格式: PDF/Word/PPT/Excel/TXT")
    print("   🚀 高性能: FAISS向量数据库")
    print("")
    print("✅ 问题4: ngrok远程访问")
    print("   🌐 公网访问: 自动配置ngrok隧道")
    print("   📱 手机支持: 移动端完美适配")
    print("   🔐 密码保护: MVP168918")
    print("   🚀 一键分享: 异地朋友直接使用")
    print("")
    print("✅ 问题5: Docker容器化部署")
    print("   🐳 完整打包: 包含所有依赖和模型")
    print("   📦 一键导出: 生成.tar文件")
    print("   🚚 跨硬件移植: 复制到任何电脑")
    print("   🔄 便携部署: 完整RAG系统移植")
    print("=" * 120)

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("💡 需要Python 3.8或更高版本")
        return False
    
    # 检查必要文件
    required_files = [
        "ultimate_working_tcm_system.py",
        "ultimate_rag_core.py",
        "install_ultimate_dependencies.py",
        "ultimate_ngrok_launcher.py",
        "ultimate_docker_deploy.py"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    return True

def check_dependencies():
    """检查依赖安装状态"""
    print("\n🔍 检查依赖安装状态...")
    
    dependencies = {
        'streamlit': '界面框架',
        'sentence_transformers': '文本嵌入',
        'faiss': '向量搜索',
        'PyPDF2': 'PDF处理',
        'pyttsx3': '语音输出',
        'speech_recognition': '语音识别',
        'llama_cpp': 'DeepSeek模型'
    }
    
    installed = []
    missing = []
    
    for module, description in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {description}")
            installed.append(module)
        except ImportError:
            print(f"❌ {description} - 未安装")
            missing.append(module)
    
    print(f"\n📊 依赖状态: {len(installed)}/{len(dependencies)} 已安装")
    
    if missing:
        print(f"⚠️ 缺少依赖: {', '.join(missing)}")
        return False
    
    return True

def show_launch_options():
    """显示启动选项"""
    print("\n🚀 选择启动方式:")
    print("")
    print("1. 🖥️  本地启动 (推荐)")
    print("   - 在本机运行系统")
    print("   - 访问地址: http://localhost:8506")
    print("   - 适合: 个人使用、开发测试")
    print("")
    print("2. 🌐 ngrok远程访问")
    print("   - 生成公网访问链接")
    print("   - 支持手机端访问")
    print("   - 适合: 分享给异地朋友")
    print("")
    print("3. 🐳 Docker容器部署")
    print("   - 容器化运行")
    print("   - 可导出镜像文件")
    print("   - 适合: 生产部署、跨硬件移植")
    print("")
    print("4. 🔧 安装/更新依赖")
    print("   - 安装所有必要依赖")
    print("   - 包括DeepSeek模型支持")
    print("   - 适合: 首次安装、依赖更新")
    print("")
    print("5. 📊 系统状态检查")
    print("   - 检查所有组件状态")
    print("   - 验证功能可用性")
    print("   - 适合: 故障排查")

def launch_local():
    """本地启动"""
    print("\n🖥️ 启动本地系统...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_working_tcm_system.py",
            "--server.port=8506",
            "--server.address=0.0.0.0",
            "--theme.base=light"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")

def launch_ngrok():
    """ngrok远程启动"""
    print("\n🌐 启动ngrok远程访问...")
    
    try:
        subprocess.run([sys.executable, "ultimate_ngrok_launcher.py"])
    except KeyboardInterrupt:
        print("\n👋 远程服务已停止")

def launch_docker():
    """Docker容器启动"""
    print("\n🐳 启动Docker容器部署...")
    
    try:
        subprocess.run([sys.executable, "ultimate_docker_deploy.py"])
    except KeyboardInterrupt:
        print("\n👋 Docker部署已停止")

def install_dependencies():
    """安装依赖"""
    print("\n🔧 安装/更新依赖...")
    
    try:
        subprocess.run([sys.executable, "install_ultimate_dependencies.py"])
    except KeyboardInterrupt:
        print("\n👋 安装已取消")

def system_status_check():
    """系统状态检查"""
    print("\n📊 系统状态检查...")
    
    # 检查文件
    print("\n📁 文件检查:")
    check_system_requirements()
    
    # 检查依赖
    print("\n📦 依赖检查:")
    deps_ok = check_dependencies()
    
    # 检查DeepSeek模型
    print("\n🧠 DeepSeek模型检查:")
    model_path = r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf'
    if os.path.exists(model_path):
        model_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)  # GB
        print(f"✅ DeepSeek模型文件存在: {model_size:.2f} GB")
    else:
        print(f"❌ DeepSeek模型文件不存在: {model_path}")
    
    # 检查数据目录
    print("\n📂 数据目录检查:")
    data_dirs = ["ultimate_vector_db", "documents", "conversations"]
    for dir_name in data_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            file_count = len(list(dir_path.glob("*")))
            print(f"✅ {dir_name}: {file_count} 个文件")
        else:
            print(f"⚠️ {dir_name}: 目录不存在")
    
    # 检查网络
    print("\n🌐 网络检查:")
    try:
        import requests
        response = requests.get("https://chinesebooks.github.io/gudaiyishu/", timeout=10)
        if response.status_code == 200:
            print("✅ 古代医书网站可访问")
        else:
            print(f"⚠️ 古代医书网站响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
    
    # 总结
    print("\n📋 状态总结:")
    if deps_ok and os.path.exists(model_path):
        print("🎉 系统状态良好，所有功能可用！")
    elif deps_ok:
        print("⚠️ 系统基本可用，但DeepSeek模型缺失")
    else:
        print("❌ 系统需要安装依赖才能正常使用")

def main():
    """主函数"""
    print_ultimate_launcher_banner()
    
    # 基本检查
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败")
        input("按回车键退出...")
        return
    
    while True:
        show_launch_options()
        
        choice = input("\n请选择启动方式 (1-5, q退出): ").strip().lower()
        
        if choice == "1":
            # 检查依赖
            if not check_dependencies():
                print("\n⚠️ 依赖不完整，建议先选择选项4安装依赖")
                continue_choice = input("是否继续启动? (y/n): ").lower().strip()
                if continue_choice != 'y':
                    continue
            
            launch_local()
            
        elif choice == "2":
            # 检查依赖
            if not check_dependencies():
                print("\n❌ 依赖不完整，请先选择选项4安装依赖")
                continue
            
            launch_ngrok()
            
        elif choice == "3":
            launch_docker()
            
        elif choice == "4":
            install_dependencies()
            
        elif choice == "5":
            system_status_check()
            
        elif choice == "q":
            print("\n👋 再见！")
            break
            
        else:
            print("\n❌ 无效选择，请重新输入")
        
        input("\n按回车键返回主菜单...")

if __name__ == "__main__":
    main()
