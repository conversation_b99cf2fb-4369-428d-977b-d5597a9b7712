#!/usr/bin/env python3
"""
终极商业化中医RAG系统
产品经理视角：确保每个功能都真正工作并提供商业价值
- 真正的PDF检索功能
- DeepSeek模型集成
- 在线医学资源爬取
- 智能回答生成
- 商业级用户体验
"""

import streamlit as st
import os
import json
import time
import requests
import PyPDF2
import numpy as np
import faiss
import pickle
from pathlib import Path
from typing import List, Dict, Optional
from sentence_transformers import SentenceTransformer
from llama_cpp import Llama
import re
from bs4 import BeautifulSoup
from datetime import datetime
import hashlib
import threading
import queue

# 页面配置
st.set_page_config(
    page_title="🏥 终极中医智能助手",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
CONFIG = {
    'DEEPSEEK_MODEL_PATH': r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf',
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './ultimate_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CHUNK_SIZE': 500,
    'CHUNK_OVERLAP': 50,
    'TOP_K': 5,
    'MAX_TOKENS': 2048,
    'TEMPERATURE': 0.7
}

class UltimateRAGSystem:
    """终极RAG系统 - 确保每个功能都真正工作"""
    
    def __init__(self):
        self.embedding_model = None
        self.llm_model = None
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        self.initialized = False
        
    def initialize(self):
        """初始化系统 - 确保所有组件都正确加载"""
        if self.initialized:
            return True
            
        try:
            with st.spinner("🚀 正在初始化终极RAG系统..."):
                # 1. 加载嵌入模型
                st.write("📥 加载嵌入模型...")
                self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
                st.success("✅ 嵌入模型加载成功")
                
                # 2. 加载DeepSeek模型
                st.write("🧠 加载DeepSeek模型...")
                if os.path.exists(CONFIG['DEEPSEEK_MODEL_PATH']):
                    self.llm_model = Llama(
                        model_path=CONFIG['DEEPSEEK_MODEL_PATH'],
                        n_ctx=4096,
                        n_threads=8,
                        n_gpu_layers=35,  # 使用GPU加速
                        verbose=False
                    )
                    st.success("✅ DeepSeek模型加载成功")
                else:
                    st.error(f"❌ DeepSeek模型文件不存在: {CONFIG['DEEPSEEK_MODEL_PATH']}")
                    return False
                
                # 3. 加载向量数据库
                st.write("📚 加载向量数据库...")
                self.load_vector_database()
                
                self.initialized = True
                st.success("🎉 系统初始化完成！")
                return True
                
        except Exception as e:
            st.error(f"❌ 系统初始化失败: {e}")
            return False
    
    def load_vector_database(self):
        """加载向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])
            
            if vector_db_path.exists():
                # 加载FAISS索引
                index_file = vector_db_path / "index.faiss"
                if index_file.exists():
                    self.vector_index = faiss.read_index(str(index_file))
                
                # 加载文档块
                chunks_file = vector_db_path / "chunks.pkl"
                if chunks_file.exists():
                    with open(chunks_file, 'rb') as f:
                        self.document_chunks = pickle.load(f)
                
                # 加载元数据
                metadata_file = vector_db_path / "metadata.pkl"
                if metadata_file.exists():
                    with open(metadata_file, 'rb') as f:
                        self.chunk_metadata = pickle.load(f)
                
                st.success(f"✅ 已加载 {len(self.document_chunks)} 个文档块")
            else:
                st.warning("⚠️ 向量数据库不存在，请先上传PDF文档")
                
        except Exception as e:
            st.warning(f"⚠️ 加载向量数据库失败: {e}")
    
    def process_pdf_documents(self, uploaded_files):
        """处理PDF文档并建立向量索引"""
        if not uploaded_files:
            return False
            
        try:
            with st.spinner("📄 正在处理PDF文档..."):
                all_chunks = []
                all_metadata = []
                
                for uploaded_file in uploaded_files:
                    st.write(f"处理文件: {uploaded_file.name}")
                    
                    # 保存上传的文件
                    documents_path = Path(CONFIG['DOCUMENTS_PATH'])
                    documents_path.mkdir(exist_ok=True)
                    
                    file_path = documents_path / uploaded_file.name
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    
                    # 提取PDF文本
                    text = self.extract_pdf_text(file_path)
                    if not text:
                        st.warning(f"⚠️ 无法从 {uploaded_file.name} 提取文本")
                        continue
                    
                    # 分割文本
                    chunks = self.split_text_into_chunks(text)
                    
                    # 创建元数据
                    for i, chunk in enumerate(chunks):
                        metadata = {
                            'source': uploaded_file.name,
                            'chunk_id': len(all_chunks) + i,
                            'chunk_index': i,
                            'content': chunk,
                            'upload_time': datetime.now().isoformat()
                        }
                        all_metadata.append(metadata)
                    
                    all_chunks.extend(chunks)
                    st.write(f"✅ 从 {uploaded_file.name} 提取了 {len(chunks)} 个文本块")
                
                if not all_chunks:
                    st.error("❌ 没有提取到任何文本内容")
                    return False
                
                # 创建向量索引
                st.write("🔍 创建向量索引...")
                embeddings = []
                
                progress_bar = st.progress(0)
                for i, chunk in enumerate(all_chunks):
                    embedding = self.embedding_model.encode([chunk])[0]
                    embeddings.append(embedding)
                    progress_bar.progress((i + 1) / len(all_chunks))
                
                embeddings = np.array(embeddings).astype('float32')
                
                # 创建FAISS索引
                dimension = embeddings.shape[1]
                self.vector_index = faiss.IndexFlatIP(dimension)
                faiss.normalize_L2(embeddings)
                self.vector_index.add(embeddings)
                
                # 保存数据
                self.document_chunks = all_chunks
                self.chunk_metadata = all_metadata
                self.save_vector_database()
                
                st.success(f"🎉 成功处理 {len(uploaded_files)} 个PDF文档，创建了 {len(all_chunks)} 个文档块的向量索引！")
                return True
                
        except Exception as e:
            st.error(f"❌ 处理PDF文档失败: {e}")
            return False
    
    def extract_pdf_text(self, pdf_path):
        """提取PDF文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            st.error(f"PDF解析错误: {e}")
            return ""
    
    def split_text_into_chunks(self, text):
        """智能文本分块"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']
        
        start = 0
        while start < len(text):
            end = start + chunk_size
            if end > len(text):
                end = len(text)
            
            chunk = text[start:end]
            
            # 在句号处分割，避免截断句子
            if end < len(text) and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = text[start:end]
            
            if len(chunk.strip()) > 20:  # 过滤太短的块
                chunks.append(chunk.strip())
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def save_vector_database(self):
        """保存向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])
            vector_db_path.mkdir(exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(vector_db_path / "index.faiss"))
            
            # 保存文档块
            with open(vector_db_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # 保存元数据
            with open(vector_db_path / "metadata.pkl", 'wb') as f:
                pickle.dump(self.chunk_metadata, f)
            
            return True
        except Exception as e:
            st.error(f"保存向量数据库失败: {e}")
            return False
    
    def search_documents(self, query, top_k=None):
        """搜索相关文档"""
        if top_k is None:
            top_k = CONFIG['TOP_K']
            
        if self.vector_index is None or not self.document_chunks:
            return []
        
        try:
            # 获取查询向量
            query_embedding = self.embedding_model.encode([query])[0]
            query_vector = np.array([query_embedding]).astype('float32')
            faiss.normalize_L2(query_vector)
            
            # 搜索相似向量
            scores, indices = self.vector_index.search(query_vector, top_k)
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.chunk_metadata):
                    result = self.chunk_metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)
            
            return results
            
        except Exception as e:
            st.error(f"文档搜索失败: {e}")
            return []

# 初始化系统
if 'rag_system' not in st.session_state:
    st.session_state.rag_system = UltimateRAGSystem()

def main():
    """主界面"""
    st.title("🏥 终极中医智能助手")
    st.markdown("### 🚀 商业级RAG系统 - 真正的PDF检索 + DeepSeek智能回答")
    
    # 侧边栏
    with st.sidebar:
        st.header("📋 系统控制")
        
        # 系统初始化
        if st.button("🚀 初始化系统", type="primary"):
            st.session_state.rag_system.initialize()
        
        # 系统状态
        st.subheader("📊 系统状态")
        if st.session_state.rag_system.initialized:
            st.success("✅ 系统已就绪")
            st.metric("文档块数量", len(st.session_state.rag_system.document_chunks))
        else:
            st.warning("⚠️ 系统未初始化")
        
        st.divider()
        
        # PDF上传
        st.subheader("📄 PDF文档管理")
        uploaded_files = st.file_uploader(
            "上传中医PDF文档",
            type=['pdf'],
            accept_multiple_files=True,
            help="支持《黄帝内经》、《伤寒论》等中医经典"
        )
        
        if uploaded_files and st.button("📚 处理文档"):
            st.session_state.rag_system.process_pdf_documents(uploaded_files)
    
    # 主要内容区域
    if not st.session_state.rag_system.initialized:
        st.info("👆 请先点击侧边栏的'初始化系统'按钮")
        return
    
    # 问答界面
    st.subheader("💬 智能问答")
    
    question = st.text_input(
        "请输入您的问题:",
        placeholder="例如：黄帝内经中关于五脏六腑的理论是什么？",
        key="question_input"
    )
    
    if st.button("🔍 提问", type="primary") and question:
        handle_question(question)

def handle_question(question):
    """处理用户问题"""
    with st.spinner("🤔 正在思考中..."):
        # 1. 搜索PDF文档
        pdf_results = st.session_state.rag_system.search_documents(question)
        
        # 2. 在线搜索（简化版）
        online_results = search_online_resources(question)
        
        # 3. 生成智能回答
        answer = generate_intelligent_answer(question, pdf_results, online_results)
        
        # 显示结果
        display_results(question, answer, pdf_results, online_results)

def search_online_resources(query):
    """搜索在线中医资源 - 真正的实现"""
    try:
        from online_medical_crawler import search_online_medical_resources

        with st.spinner("🌐 搜索在线中医资源..."):
            results = search_online_medical_resources(query, max_results=3)

            # 转换格式以匹配系统需求
            formatted_results = []
            for result in results:
                formatted_results.append({
                    'source': result['title'],
                    'content': result['content'],
                    'url': result['url'],
                    'relevance': result['relevance']
                })

            return formatted_results

    except Exception as e:
        st.warning(f"在线搜索失败: {e}")
        # 返回备用结果
        return [{
            'source': '中医基础知识',
            'content': f'关于"{query}"的基础中医理论知识，建议查阅相关典籍获取更详细信息。',
            'url': 'https://chinesebooks.github.io/gudaiyishu/',
            'relevance': 0.5
        }]

def generate_intelligent_answer(question, pdf_results, online_results):
    """使用DeepSeek生成智能回答 - 增强版"""
    if not st.session_state.rag_system.llm_model:
        return generate_fallback_answer(question, pdf_results, online_results)

    # 分析问题类型
    question_type = analyze_question_type(question)

    # 构建智能上下文
    context = build_intelligent_context(question, pdf_results, online_results, question_type)

    # 构建专业提示
    prompt = build_professional_prompt(question, context, question_type)

    try:
        with st.spinner("🧠 DeepSeek正在思考..."):
            response = st.session_state.rag_system.llm_model(
                prompt,
                max_tokens=CONFIG['MAX_TOKENS'],
                temperature=CONFIG['TEMPERATURE'],
                stop=["用户问题：", "问题：", "Human:", "Assistant:"]
            )

            answer = response['choices'][0]['text'].strip()

            # 后处理回答
            answer = post_process_answer(answer, question_type)

            return answer

    except Exception as e:
        st.error(f"DeepSeek生成失败: {e}")
        return generate_fallback_answer(question, pdf_results, online_results)

def analyze_question_type(question):
    """分析问题类型"""
    question_lower = question.lower()

    if any(word in question_lower for word in ['是什么', '什么是', '含义', '概念', '定义']):
        return 'concept'
    elif any(word in question_lower for word in ['怎么', '如何', '方法', '步骤']):
        return 'method'
    elif any(word in question_lower for word in ['为什么', '原因', '机制', '原理']):
        return 'reason'
    elif any(word in question_lower for word in ['历史', '起源', '发展', '来源']):
        return 'history'
    elif any(word in question_lower for word in ['症状', '表现', '特点', '特征']):
        return 'symptom'
    elif any(word in question_lower for word in ['治疗', '调理', '方法', '药方']):
        return 'treatment'
    else:
        return 'general'

def build_intelligent_context(question, pdf_results, online_results, question_type):
    """构建智能上下文"""
    context_parts = []

    # 根据问题类型调整上下文重点
    if question_type in ['treatment', 'method']:
        context_parts.append("⚠️ 注意：以下内容仅供学习参考，实际应用需专业医师指导")
        context_parts.append("")

    # PDF文档上下文
    if pdf_results:
        context_parts.append("【本地文档资料】")
        for i, result in enumerate(pdf_results[:3], 1):
            relevance_indicator = "★★★" if result['similarity_score'] > 0.8 else "★★" if result['similarity_score'] > 0.6 else "★"
            context_parts.append(f"{i}. 《{result['source']}》{relevance_indicator}")
            context_parts.append(f"   {result['content'][:400]}...")
            context_parts.append("")

    # 在线资源上下文
    if online_results:
        context_parts.append("【在线医学资源】")
        for i, result in enumerate(online_results[:2], 1):
            relevance_indicator = "★★★" if result.get('relevance', 0) > 0.8 else "★★" if result.get('relevance', 0) > 0.6 else "★"
            context_parts.append(f"{i}. {result['source']}{relevance_indicator}")
            context_parts.append(f"   {result['content'][:400]}...")
            context_parts.append("")

    return "\n".join(context_parts)

def build_professional_prompt(question, context, question_type):
    """构建专业提示"""

    system_prompt = """你是一位资深的中医学者和临床专家，具有深厚的中医理论基础和丰富的临床经验。你的回答应该：
1. 基于提供的文献资料
2. 体现中医理论的系统性和完整性
3. 语言专业但易于理解
4. 包含适当的免责声明
5. 引用具体的文献来源"""

    question_templates = {
        'concept': "请详细解释这个中医概念的含义、理论基础和临床意义。",
        'method': "请说明具体的方法、步骤和注意事项，并强调需要专业指导。",
        'reason': "请从中医理论角度分析原因和机制。",
        'history': "请介绍历史发展脉络和重要文献记载。",
        'symptom': "请描述相关表现和中医辨证要点。",
        'treatment': "请说明相关理论，但强调需要专业医师诊断和指导。",
        'general': "请全面回答这个问题。"
    }

    template = question_templates.get(question_type, question_templates['general'])

    prompt = f"""{system_prompt}

参考资料：
{context}

用户问题：{question}

回答要求：{template}

请基于上述资料提供专业回答："""

    return prompt

def post_process_answer(answer, question_type):
    """后处理回答"""
    # 添加适当的免责声明
    if question_type == 'treatment':
        if "请咨询专业医师" not in answer:
            answer += "\n\n⚠️ 重要提醒：以上内容仅供学习参考，具体治疗方案请咨询专业中医师。"

    # 格式化回答
    answer = answer.replace("\\n", "\n")
    answer = re.sub(r'\n{3,}', '\n\n', answer)  # 移除多余的换行

    return answer.strip()

def generate_fallback_answer(question, pdf_results, online_results):
    """生成备用回答（当DeepSeek不可用时）"""
    answer_parts = []

    answer_parts.append(f"## 关于「{question}」的资料整理")
    answer_parts.append("")

    if pdf_results:
        answer_parts.append("### 📚 本地文档资料")
        for i, result in enumerate(pdf_results[:2], 1):
            answer_parts.append(f"**{i}. 来源：《{result['source']}》**")
            answer_parts.append(result['content'][:300] + "...")
            answer_parts.append("")

    if online_results:
        answer_parts.append("### 🌐 在线医学资源")
        for i, result in enumerate(online_results[:2], 1):
            answer_parts.append(f"**{i}. 来源：{result['source']}**")
            answer_parts.append(result['content'][:300] + "...")
            answer_parts.append("")

    if not pdf_results and not online_results:
        answer_parts.append("抱歉，暂时没有找到相关资料。建议：")
        answer_parts.append("1. 尝试使用其他关键词搜索")
        answer_parts.append("2. 上传更多相关PDF文档")
        answer_parts.append("3. 咨询专业中医师")

    answer_parts.append("---")
    answer_parts.append("*以上内容整理自相关文献资料，仅供学习参考*")

    return "\n".join(answer_parts)

def display_results(question, answer, pdf_results, online_results):
    """显示问答结果"""
    # 用户问题
    st.markdown(f"""
    <div style="background-color: #E3F2FD; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #2196F3;">
        <strong>🙋 用户问题：</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)
    
    # AI回答
    st.markdown(f"""
    <div style="background-color: #F1F8E9; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #4CAF50;">
        <strong>🤖 智能回答：</strong><br>
        {answer}
    </div>
    """, unsafe_allow_html=True)
    
    # 参考来源
    if pdf_results or online_results:
        st.subheader("📖 参考来源")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**📄 PDF文档检索结果：**")
            for i, result in enumerate(pdf_results[:3], 1):
                with st.expander(f"来源 {i}: {result['source']} (相似度: {result['similarity_score']:.3f})"):
                    st.write(result['content'][:500] + "...")
        
        with col2:
            st.write("**🌐 在线资源检索结果：**")
            for i, result in enumerate(online_results[:2], 1):
                with st.expander(f"在线资源 {i}: {result['source']}"):
                    st.write(result['content'][:500] + "...")

if __name__ == "__main__":
    main()
