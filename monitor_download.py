#!/usr/bin/env python3
"""
监控Ollama模型下载进度
"""

import subprocess
import time
import requests

def check_ollama_models():
    """检查已下载的模型"""
    try:
        result = subprocess.run([
            r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe", 
            "list"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            lines = output.split('\n')
            
            if len(lines) > 1:  # 有标题行和模型行
                models = []
                for line in lines[1:]:  # 跳过标题行
                    if line.strip():
                        parts = line.split()
                        if parts:
                            models.append(parts[0])
                return models
            else:
                return []
        else:
            return []
    except:
        return []

def check_api_status():
    """检查Ollama API状态"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            return True, models
        return False, []
    except:
        return False, []

def monitor_download():
    """监控下载进度"""
    print("🔍 监控DeepSeek模型下载进度...")
    print("=" * 50)
    
    start_time = time.time()
    check_interval = 30  # 每30秒检查一次
    
    while True:
        # 检查本地模型列表
        models = check_ollama_models()
        
        # 检查API状态
        api_ok, api_models = check_api_status()
        
        elapsed = int(time.time() - start_time)
        print(f"\n⏰ 已等待: {elapsed//60}分{elapsed%60}秒")
        print(f"📋 本地模型: {models if models else '无'}")
        print(f"🔗 API状态: {'✅ 正常' if api_ok else '❌ 异常'}")
        print(f"🤖 API模型: {api_models if api_models else '无'}")
        
        # 检查是否有DeepSeek模型
        deepseek_found = False
        for model in models + api_models:
            if 'deepseek' in model.lower():
                print(f"🎉 找到DeepSeek模型: {model}")
                deepseek_found = True
                break
        
        if deepseek_found:
            print("✅ DeepSeek模型下载完成！")
            return True
        
        print("⏳ 继续等待下载完成...")
        time.sleep(check_interval)

def main():
    """主函数"""
    try:
        success = monitor_download()
        if success:
            print("\n🎊 模型下载完成，可以启动RAG系统了！")
        else:
            print("\n❌ 监控中断")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断监控")

if __name__ == "__main__":
    main()
