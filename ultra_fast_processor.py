"""
超快速文档处理器 - AMD GPU优化版
"""
import PyPDF2
import numpy as np
import faiss
import pickle
import json
import time
import gc
import threading
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import config
from gpu_optimizer import setup_gpu_optimization

class UltraFastProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
        # 获取GPU优化配置
        self.gpu_config = setup_gpu_optimization()
        self.batch_size = self.gpu_config['batch_size']
        self.chunk_size = self.gpu_config['chunk_size']
        
        print(f"🚀 超快速处理器初始化")
        print(f"   设备: {self.gpu_config['device_name']}")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   块大小: {self.chunk_size}")
    
    def extract_pdf_parallel(self, pdf_path: str) -> str:
        """并行PDF文本提取"""
        print(f"📄 并行提取PDF: {pdf_path}")
        start_time = time.time()
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                print(f"📖 总页数: {total_pages}")
                
                # 使用多线程并行处理页面
                def extract_page(page_num):
                    try:
                        page = pdf_reader.pages[page_num]
                        return page.extract_text()
                    except Exception as e:
                        print(f"   页面 {page_num+1} 提取失败: {e}")
                        return ""
                
                # 并行提取所有页面
                max_workers = min(8, mp.cpu_count())  # 最多8个线程
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    page_texts = list(executor.map(extract_page, range(total_pages)))
                
                text = "\n".join(page_texts)
                
                extraction_time = time.time() - start_time
                print(f"✅ 并行PDF提取完成，耗时: {extraction_time:.2f}秒")
                print(f"📝 提取文本长度: {len(text)} 字符")
                
                return text
                
        except Exception as e:
            print(f"❌ PDF提取失败: {e}")
            return ""
    
    def smart_text_split(self, text: str) -> List[str]:
        """智能文本分割"""
        print("🔪 智能文本分割...")
        start_time = time.time()
        
        # 预处理文本
        text = ' '.join(text.split())  # 清理空白字符
        
        chunks = []
        chunk_size = self.chunk_size
        overlap = chunk_size // 10  # 10%重叠
        
        # 智能分割点
        split_patterns = ['。', '！', '？', '\n\n', '；']
        
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = start + chunk_size
            if end >= text_length:
                end = text_length
                chunk = text[start:end].strip()
                if len(chunk) > 10:
                    chunks.append(chunk)
                break
            
            # 寻找最佳分割点
            best_split = end
            for pattern in split_patterns:
                split_pos = text.rfind(pattern, start + chunk_size//2, end)
                if split_pos != -1:
                    best_split = split_pos + len(pattern)
                    break
            
            chunk = text[start:best_split].strip()
            if len(chunk) > 10:
                chunks.append(chunk)
            
            start = best_split - overlap
            if start < 0:
                start = best_split
        
        split_time = time.time() - start_time
        print(f"✅ 智能分割完成，耗时: {split_time:.2f}秒")
        print(f"📊 总块数: {len(chunks)}")
        
        return chunks
    
    def parallel_embedding(self, texts: List[str]) -> np.ndarray:
        """并行嵌入生成"""
        print("🔄 并行向量化...")
        start_time = time.time()
        
        from models.model_manager import model_manager
        
        # 分批处理
        batch_size = self.batch_size
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        all_embeddings = []
        
        # 使用线程池并行处理批次
        def process_batch(batch_texts):
            batch_embeddings = []
            for text in batch_texts:
                try:
                    embedding = model_manager.get_embedding(text)
                    batch_embeddings.append(embedding)
                except Exception as e:
                    print(f"   嵌入失败: {e}")
                    # 使用零向量作为备用
                    batch_embeddings.append(np.zeros(768))
            return batch_embeddings
        
        # 并行处理所有批次
        max_workers = min(4, mp.cpu_count())  # 限制线程数避免过载
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_num = i // batch_size + 1
                print(f"   提交批次 {batch_num}/{total_batches} ({len(batch_texts)} 个文本)")
                
                future = executor.submit(process_batch, batch_texts)
                futures.append(future)
            
            # 收集结果
            for i, future in enumerate(futures):
                try:
                    batch_embeddings = future.result(timeout=60)  # 60秒超时
                    all_embeddings.extend(batch_embeddings)
                    print(f"   完成批次 {i+1}/{len(futures)}")
                except Exception as e:
                    print(f"   批次 {i+1} 失败: {e}")
                    # 添加零向量作为备用
                    batch_size_actual = min(batch_size, len(texts) - i * batch_size)
                    all_embeddings.extend([np.zeros(768)] * batch_size_actual)
        
        embeddings_array = np.array(all_embeddings).astype('float32')
        
        embedding_time = time.time() - start_time
        print(f"✅ 并行向量化完成，耗时: {embedding_time:.2f}秒")
        print(f"📊 向量维度: {embeddings_array.shape}")
        
        return embeddings_array
    
    def ultra_fast_index(self, embeddings: np.ndarray) -> bool:
        """超快速索引创建"""
        print("🏗️ 创建超快速索引...")
        start_time = time.time()
        
        try:
            dimension = embeddings.shape[1]
            
            # 使用最快的索引类型
            self.vector_index = faiss.IndexFlatIP(dimension)
            
            # 批量归一化和添加
            batch_size = 1000  # 大批量处理
            for i in range(0, len(embeddings), batch_size):
                batch = embeddings[i:i + batch_size]
                faiss.normalize_L2(batch)
                self.vector_index.add(batch)
                
                if i % 5000 == 0:  # 每5000个向量显示进度
                    print(f"   已添加 {min(i + batch_size, len(embeddings))}/{len(embeddings)} 个向量")
            
            index_time = time.time() - start_time
            print(f"✅ 超快速索引完成，耗时: {index_time:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def process_ultra_fast(self, pdf_path: str, progress_callback=None) -> bool:
        """超快速处理主函数"""
        print(f"🚀 开始超快速处理: {pdf_path}")
        total_start_time = time.time()
        
        try:
            # 1. 并行PDF提取
            if progress_callback:
                progress_callback(10, "📄 并行提取PDF文本...")
            
            text = self.extract_pdf_parallel(pdf_path)
            if not text:
                return False
            
            # 2. 智能文本分割
            if progress_callback:
                progress_callback(25, "🔪 智能分割文本...")
            
            chunks = self.smart_text_split(text)
            if not chunks:
                return False
            
            # 智能限制块数量
            max_chunks = 1000  # 增加到1000个块
            if len(chunks) > max_chunks:
                print(f"⚠️ 块数量过多({len(chunks)})，智能选择前{max_chunks}个")
                # 智能选择：保留开头、中间、结尾的块
                step = len(chunks) // max_chunks
                chunks = chunks[::step][:max_chunks]
            
            # 3. 创建元数据
            if progress_callback:
                progress_callback(35, "📋 创建元数据...")
            
            metadata = []
            for i, chunk in enumerate(chunks):
                metadata.append({
                    "source": pdf_path,
                    "chunk_id": i,
                    "chunk_index": i,
                    "content": chunk
                })
            
            # 4. 并行嵌入生成
            if progress_callback:
                progress_callback(50, "🔄 并行生成向量嵌入...")
            
            embeddings = self.parallel_embedding(chunks)
            
            # 5. 超快速索引
            if progress_callback:
                progress_callback(80, "🏗️ 创建超快速索引...")
            
            if not self.ultra_fast_index(embeddings):
                return False
            
            # 6. 保存数据
            if progress_callback:
                progress_callback(95, "💾 保存处理结果...")
            
            self.document_chunks = chunks
            self.chunk_metadata = metadata
            self.save_ultra_fast()
            
            total_time = time.time() - total_start_time
            
            if progress_callback:
                progress_callback(100, "✅ 超快速处理完成！")
            
            print(f"🎉 超快速处理完成！总耗时: {total_time:.2f}秒")
            print(f"📊 处理了 {len(chunks)} 个文档块")
            print(f"⚡ 平均速度: {len(chunks)/total_time:.1f} 块/秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 超快速处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_ultra_fast(self):
        """超快速保存"""
        try:
            index_path = Path(config.VECTOR_DB_PATH)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 并行保存
            def save_faiss():
                faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            def save_chunks():
                with open(index_path / "chunks.pkl", 'wb') as f:
                    pickle.dump(self.document_chunks, f)
            
            def save_metadata():
                with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                    json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            # 并行执行保存操作
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = [
                    executor.submit(save_faiss),
                    executor.submit(save_chunks),
                    executor.submit(save_metadata)
                ]
                
                for future in futures:
                    future.result()
            
            print("✅ 超快速保存完成")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")

# 全局超快速处理器
ultra_processor = UltraFastProcessor()

def process_pdf_ultra_fast(pdf_path: str, progress_callback=None) -> bool:
    """超快速处理PDF的便捷函数"""
    return ultra_processor.process_ultra_fast(pdf_path, progress_callback)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
        success = process_pdf_ultra_fast(pdf_file)
        if success:
            print("🎉 超快速处理成功！")
        else:
            print("❌ 处理失败")
    else:
        print("用法: python ultra_fast_processor.py <pdf文件路径>")
