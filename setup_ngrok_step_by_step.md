# 🚀 ngrok隧道设置指南

## 📋 前提条件
✅ 您已下载ngrok
✅ 您已注册ngrok账户
✅ 中医RAG系统正在运行 (localhost:8006)

## 🔧 步骤1: 配置ngrok认证

1. **登录ngrok网站**
   - 访问: https://dashboard.ngrok.com/
   - 使用您的账户登录

2. **获取认证令牌**
   - 在Dashboard中找到 "Your Authtoken"
   - 复制您的authtoken (类似: 2abc123def456...)

3. **配置认证令牌**
   ```bash
   # 在命令行中运行 (将YOUR_TOKEN替换为您的实际token)
   ngrok config add-authtoken YOUR_TOKEN
   ```

## 🌐 步骤2: 启动隧道

1. **打开新的命令行窗口**
   - 按 Win+R，输入 cmd，回车
   - 或者在当前目录右键 "在此处打开命令提示符"

2. **启动ngrok隧道**
   ```bash
   # 将本地8006端口映射到公网
   ngrok http 8006
   ```

3. **查看隧道信息**
   - ngrok会显示类似这样的信息:
   ```
   Session Status                online
   Account                       <EMAIL>
   Version                       3.x.x
   Region                        United States (us)
   Latency                       -
   Web Interface                 http://127.0.0.1:4040
   Forwarding                    https://abc123.ngrok.io -> http://localhost:8006
   ```

## 📱 步骤3: 分享给朋友

**复制这个信息发给您的朋友:**

```
🏥 【中医RAG智能诊疗系统】

🌐 访问地址: https://YOUR_NGROK_URL.ngrok.io
(将YOUR_NGROK_URL替换为实际的ngrok地址)

🔐 登录信息:
   用户名: tcm_user
   密码: MVP168918

💡 使用说明:
1. 点击上述网址打开系统
2. 输入用户名和密码登录
3. 即可使用中医智能诊疗功能

🎯 系统功能:
• DeepSeek-R1智能诊疗分析 ✅
• 在线医学资源检索 ✅
• PDF文档上传分析 ✅
• 语音交互支持 ✅
• 会话历史管理 ✅

⚠️ 注意事项:
- 请妥善保管登录凭据
- 仅供家人朋友使用
- 隧道需要保持运行
```

## 🔧 步骤4: 保持隧道运行

1. **保持ngrok窗口打开**
   - 不要关闭运行ngrok的命令行窗口
   - 关闭后隧道会断开

2. **监控隧道状态**
   - 访问: http://127.0.0.1:4040
   - 可以看到隧道的详细信息和访问日志

## ⚠️ 重要提醒

1. **安全性**
   - 系统已设置密码保护 (MVP168918)
   - 仅分享给信任的朋友
   - 定期更换密码

2. **稳定性**
   - 免费版ngrok会定期更换URL
   - 如需固定URL，考虑升级到付费版

3. **网络要求**
   - 确保您的网络连接稳定
   - 防火墙可能需要允许ngrok访问

## 🆘 常见问题

**Q: ngrok提示认证失败?**
A: 检查authtoken是否正确配置

**Q: 朋友无法访问?**
A: 检查ngrok是否正在运行，URL是否正确

**Q: 访问很慢?**
A: 这是免费版的限制，可考虑付费版

**Q: URL经常变化?**
A: 免费版每次重启都会变化，付费版可以固定域名
