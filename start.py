"""
RAG系统快速启动脚本
"""
import subprocess
import sys
import os

def main():
    print("🚀 启动中医经典RAG问答系统")
    print("=" * 40)
    
    # 检查是否安装了streamlit
    try:
        import streamlit
        print("✅ Streamlit已安装")
    except ImportError:
        print("正在安装Streamlit...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
    
    # 启动应用
    print("🌐 正在启动Web界面...")
    print("📝 浏览器将自动打开 http://localhost:8501")
    print("⚠️ 首次启动需要下载模型，请耐心等待...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.address", "localhost",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用！")

if __name__ == "__main__":
    main()
