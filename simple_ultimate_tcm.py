#!/usr/bin/env python3
"""
简化版终极中医RAG系统
确保能够正常启动和运行
"""
import json
import re
import time
import hashlib
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import uuid
import logging

# FastAPI相关
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, Request, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse, RedirectResponse
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from pydantic import BaseModel
import uvicorn

# 数据处理
import requests
from bs4 import BeautifulSoup

# 文档处理
import PyPDF2
import docx

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 身份验证配置
AUTH_USERNAME = "tcm_user"
AUTH_PASSWORD = "MVP168918"
security = HTTPBasic()

def verify_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    """验证用户凭据"""
    if credentials.username != AUTH_USERNAME or credentials.password != AUTH_PASSWORD:
        raise HTTPException(
            status_code=401,
            detail="访问被拒绝：用户名或密码错误",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# 本地LLM - 优先使用DeepSeek-R1（简化配置）
try:
    from simple_deepseek_config import deepseek_config, get_current_llm_info

    if deepseek_config.is_model_ready():
        # DeepSeek模型可用，使用增强的智能模式
        from ultra_intelligent_llm import ultra_llm
        current_llm = ultra_llm
        logger.info("🚀 使用DeepSeek-R1增强的超级智能模式")

        # 添加DeepSeek模型信息到LLM
        llm_info = get_current_llm_info()
        ultra_llm.deepseek_info = llm_info
    else:
        # DeepSeek模型不可用，使用标准智能模式
        from ultra_intelligent_llm import ultra_llm
        current_llm = ultra_llm
        logger.info("🤖 使用标准超级智能模式")

except Exception as e:
    logger.warning(f"⚠️ 模型配置失败，使用备用模式: {e}")
    from ultra_intelligent_llm import ultra_llm
    current_llm = ultra_llm

# 创建FastAPI应用
app = FastAPI(
    title="简化版终极中医RAG系统",
    description="集成PDF检索、在线爬取、语音交互的完整系统",
    version="3.0.0-simple"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
class Config:
    # 目录配置
    UPLOAD_DIR = Path("uploads")
    CACHE_DIR = Path("cache")
    SESSIONS_DIR = Path("sessions")

    # 检索配置 - 优化速度
    TOP_K = 3  # 减少返回结果数量
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

    # 速度优化配置
    FAST_MODE = True  # 启用快速模式
    MAX_SEARCH_TIME = 3  # 最大搜索时间3秒
    MAX_BOOKS_SEARCH = 5  # 最多搜索5本书
    QUICK_TIMEOUT = 3  # 快速超时3秒

# 初始化配置
config = Config()
for directory in [config.UPLOAD_DIR, config.CACHE_DIR, config.SESSIONS_DIR]:
    directory.mkdir(exist_ok=True)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str
    processing_time: float

# 简化文档处理器
class SimpleDocumentProcessor:
    def __init__(self):
        self.documents = []

    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        chunks = []
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"PDF文件不存在: {file_path}")
                return chunks

            logger.info(f"🔍 开始处理PDF文件: {file_path}")

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                logger.info(f"📄 PDF总页数: {total_pages}")

                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text and text.strip():
                            cleaned_text = self._clean_text(text)
                            if len(cleaned_text) > 20:  # 降低最小长度要求
                                chunk = {
                                    'content': cleaned_text,
                                    'source': f"{Path(file_path).name} - 第{page_num + 1}页",
                                    'type': 'pdf',
                                    'filename': Path(file_path).name,
                                    'page': page_num + 1
                                }
                                chunks.append(chunk)
                                logger.info(f"✅ 处理第{page_num + 1}页成功，内容长度: {len(cleaned_text)}")
                    except Exception as e:
                        logger.warning(f"处理PDF第{page_num + 1}页失败: {e}")
                        continue

                logger.info(f"🎯 PDF处理完成: {len(chunks)} 个有效块")

        except Exception as e:
            logger.error(f"PDF处理失败: {e}")
            # 尝试作为文本文件处理
            try:
                logger.info("🔄 尝试作为文本文件处理...")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():
                        cleaned_text = self._clean_text(content)
                        if len(cleaned_text) > 20:
                            chunk = {
                                'content': cleaned_text,
                                'source': f"{Path(file_path).name} - 文本内容",
                                'type': 'pdf',
                                'filename': Path(file_path).name,
                                'page': 1
                            }
                            chunks.append(chunk)
                            logger.info(f"✅ 作为文本文件处理成功，内容长度: {len(cleaned_text)}")
            except Exception as text_error:
                logger.error(f"文本文件处理也失败: {text_error}")

        return chunks

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def search(self, query: str) -> List[Dict[str, Any]]:
        """改进的PDF文档搜索"""
        results = []

        if not self.documents:
            logger.info("📄 没有PDF文档可搜索")
            return results

        logger.info(f"🔍 在 {len(self.documents)} 个PDF文档中搜索: {query}")

        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        query_chars = set(query)

        for doc in self.documents:
            content = doc['content']
            content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))
            content_chars = set(content)

            # 1. 精确关键词匹配
            keyword_intersection = query_keywords.intersection(content_keywords)
            keyword_score = len(keyword_intersection) / max(len(query_keywords), 1) if query_keywords else 0

            # 2. 字符级匹配
            char_intersection = query_chars.intersection(content_chars)
            char_score = len(char_intersection) / max(len(query_chars), 1) if query_chars else 0

            # 3. 子串匹配
            substring_score = 0
            for qkw in query_keywords:
                if qkw in content:
                    substring_score += 1
            substring_score = substring_score / max(len(query_keywords), 1) if query_keywords else 0

            # 4. 综合评分 (降低阈值)
            final_score = (keyword_score * 0.4 + char_score * 0.3 + substring_score * 0.3)

            # 大幅降低阈值，确保有匹配就返回
            if final_score > 0.05 or keyword_intersection or substring_score > 0:
                doc_copy = doc.copy()
                doc_copy['score'] = max(final_score, 0.1)  # 确保最低分数
                doc_copy['type'] = 'pdf'  # 明确标记类型
                results.append(doc_copy)
                logger.info(f"✅ PDF匹配: {doc.get('source', 'unknown')} (评分: {final_score:.3f})")

        sorted_results = sorted(results, key=lambda x: x['score'], reverse=True)[:config.TOP_K]
        logger.info(f"📊 PDF搜索完成: 找到 {len(sorted_results)} 个匹配结果")

        return sorted_results

# 在线资源爬取器
class SimpleOnlineCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    async def search_online(self, query: str) -> List[Dict[str, Any]]:
        """快速智能搜索古代医书网站"""
        results = []
        start_time = time.time()

        try:
            # 快速模式：限制搜索时间
            if config.FAST_MODE:
                logger.info(f"🚀 启用快速搜索模式，最大时间: {config.MAX_SEARCH_TIME}秒")

            # 1. 快速查询分析
            search_strategy = self._analyze_search_strategy(query)
            logger.info(f"🧠 搜索策略: {search_strategy['type']}, 关键词: {search_strategy['keywords']}")

            # 2. 优先搜索核心医书（快速模式）
            await self._fast_priority_search(query, search_strategy, results)

            # 检查时间限制
            if config.FAST_MODE and (time.time() - start_time) > config.MAX_SEARCH_TIME:
                logger.info(f"⏰ 达到时间限制，停止搜索")
            else:
                # 3. 如果时间允许且结果不够，快速扩展搜索
                if len(results) < 3:
                    await self._fast_expand_search(query, search_strategy, results, start_time)

            # 4. 快速去重和优化
            results = self._fast_optimize_results(results, query)

        except Exception as e:
            logger.warning(f"在线搜索失败: {e}")
            # 确保至少有备用内容
            if not results:
                self._add_fallback_content(query, results)

        # 按相关度排序
        results = sorted(results, key=lambda x: x.get('score', 0), reverse=True)

        search_time = time.time() - start_time
        logger.info(f"🔍 快速搜索完成，用时 {search_time:.2f}秒，找到 {len(results)} 条结果")
        return results[:config.TOP_K]  # 返回TOP_K个最佳结果

    async def _fast_priority_search(self, query: str, strategy: Dict[str, Any], results: List[Dict]):
        """快速优先搜索核心医书"""
        priority_books = ['yizongjinjian', 'huangdineijing', 'shanghan']

        for book in priority_books[:config.MAX_BOOKS_SEARCH]:
            try:
                book_url = f"https://chinesebooks.github.io/gudaiyishu/{book}/"
                logger.info(f"🎯 快速搜索: {book}")

                response = self.session.get(book_url, timeout=config.QUICK_TIMEOUT)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_content = soup.get_text()
                    relevant_content = self._extract_relevant_content(page_content, query)

                    if relevant_content:
                        score = self._calculate_relevance(query, relevant_content) + 0.2
                        results.append({
                            'source': f"📖 {book} (优先)",
                            'content': relevant_content,
                            'score': score,
                            'type': 'online',
                            'url': book_url,
                            'priority': 'high'
                        })

                if len(results) >= config.TOP_K:
                    break

            except Exception as e:
                logger.warning(f"快速搜索 {book} 失败: {e}")
                continue

    async def _fast_expand_search(self, query: str, strategy: Dict[str, Any], results: List[Dict], start_time: float):
        """快速扩展搜索"""
        if (time.time() - start_time) > config.MAX_SEARCH_TIME:
            return

        # 只搜索最相关的2-3本书
        other_books = ['jinkuiyaolue', 'bencaogangmu']

        for book in other_books[:2]:
            if (time.time() - start_time) > config.MAX_SEARCH_TIME:
                break

            try:
                book_url = f"https://chinesebooks.github.io/gudaiyishu/{book}/"
                response = self.session.get(book_url, timeout=config.QUICK_TIMEOUT)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_content = soup.get_text()
                    relevant_content = self._extract_relevant_content(page_content, query)

                    if relevant_content:
                        score = self._calculate_relevance(query, relevant_content)
                        results.append({
                            'source': f"📖 {book}",
                            'content': relevant_content,
                            'score': score,
                            'type': 'online',
                            'url': book_url
                        })

            except Exception as e:
                logger.warning(f"快速扩展搜索 {book} 失败: {e}")
                continue

    def _fast_optimize_results(self, results: List[Dict], query: str) -> List[Dict]:
        """快速优化搜索结果"""
        if not results:
            return results

        # 简化去重逻辑
        unique_results = []
        seen_content = set()

        for result in results:
            content_hash = hash(result.get('content', '')[:50])  # 只检查前50字符
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)

        # 按分数排序
        unique_results.sort(key=lambda x: x.get('score', 0), reverse=True)

        return unique_results[:config.TOP_K]

    async def _search_all_gudaiyishu(self, query: str, results: List[Dict]):
        """搜索整个古代医书网站的所有内容"""
        try:
            base_url = "https://chinesebooks.github.io/gudaiyishu/"
            logger.info(f"🌐 正在全面搜索古代医书网站: {base_url}")

            # 1. 获取主页面所有链接
            response = self.session.get(base_url, timeout=15)
            if response.status_code != 200:
                logger.warning(f"无法访问主页: {response.status_code}")
                return

            soup = BeautifulSoup(response.content, 'html.parser')

            # 2. 提取所有古代医书链接 - 修复解析逻辑
            all_books = []

            # 添加已知的医书链接作为备用
            known_books = [
                {'name': '医宗金鉴', 'url': 'https://chinesebooks.github.io/gudaiyishu/yizongjinjian/'},
                {'name': '黄帝内经', 'url': 'https://chinesebooks.github.io/gudaiyishu/huangdineijing/'},
                {'name': '伤寒论', 'url': 'https://chinesebooks.github.io/gudaiyishu/shanghan/'},
                {'name': '金匮要略', 'url': 'https://chinesebooks.github.io/gudaiyishu/jinkuiyaolue/'},
                {'name': '本草纲目', 'url': 'https://chinesebooks.github.io/gudaiyishu/bencaogangmu/'},
                {'name': '温病条辨', 'url': 'https://chinesebooks.github.io/gudaiyishu/wenbing/'},
                {'name': '针灸甲乙经', 'url': 'https://chinesebooks.github.io/gudaiyishu/zhenjiu/'},
                {'name': '脉经', 'url': 'https://chinesebooks.github.io/gudaiyishu/maijing/'}
            ]
            all_books.extend(known_books)

            # 尝试从页面解析更多链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                text = link.get_text(strip=True)

                # 更宽松的链接过滤条件
                if text and len(text) > 2:
                    # 构建完整URL
                    if href.startswith('./'):
                        full_url = base_url + href[2:]
                    elif href.startswith('/'):
                        full_url = "https://chinesebooks.github.io" + href
                    elif href.startswith('http'):
                        full_url = href
                    else:
                        full_url = base_url + href

                    # 检查是否是医书相关链接
                    if ('gudaiyishu' in full_url or '医' in text or '经' in text or
                        '论' in text or '本草' in text or '针灸' in text):
                        # 避免重复
                        if full_url not in [book['url'] for book in all_books]:
                            all_books.append({
                                'name': text,
                                'url': full_url
                            })

            logger.info(f"📚 发现 {len(all_books)} 本古代医书")

            # 3. 搜索每本医书 - 扩大搜索范围
            search_count = 0
            logger.info(f"🔍 开始全面搜索 {len(all_books)} 本古代医书...")

            for book in all_books:
                # 移除严格限制，允许更全面的搜索
                if len(results) >= 50:  # 大幅提高结果上限
                    logger.info(f"✅ 已找到足够结果 ({len(results)}条)，停止搜索")
                    break

                try:
                    await self._search_single_book(query, book, results)
                    search_count += 1

                    # 动态调整搜索间隔
                    if search_count % 10 == 0:
                        logger.info(f"📊 已搜索 {search_count} 本医书，找到 {len(results)} 条结果")
                        time.sleep(1.0)  # 每10本书后稍作休息
                    else:
                        time.sleep(0.1)  # 减少间隔，提高搜索效率

                except Exception as e:
                    logger.warning(f"搜索 {book['name']} 失败: {e}")
                    continue

            logger.info(f"🎯 搜索完成：共搜索 {search_count} 本医书，找到 {len(results)} 条相关结果")

        except Exception as e:
            logger.error(f"全面搜索失败: {e}")

    def _analyze_search_strategy(self, query: str) -> Dict[str, Any]:
        """分析搜索策略"""
        strategy = {
            'type': 'general',
            'keywords': [],
            'priority_books': [],
            'search_depth': 'normal'
        }

        # 提取中文关键词
        keywords = re.findall(r'[\u4e00-\u9fff]+', query)
        strategy['keywords'] = keywords

        # 分析查询类型
        if any(word in query for word in ['汤', '散', '丸', '方', '剂']):
            strategy['type'] = 'formula'
            strategy['priority_books'] = ['yizongjinjian', 'shanghan', 'jinkuiyaolue']
            strategy['search_depth'] = 'deep'
        elif any(word in query for word in ['头痛', '头疼', '偏头痛']):
            strategy['type'] = 'headache'
            strategy['priority_books'] = ['yizongjinjian', 'huangdineijing']
        elif any(word in query for word in ['腰痛', '腰疼', '腰酸']):
            strategy['type'] = 'back_pain'
            strategy['priority_books'] = ['yizongjinjian', 'jinkuiyaolue']
        elif any(word in query for word in ['失眠', '不寐', '睡眠']):
            strategy['type'] = 'sleep'
            strategy['priority_books'] = ['yizongjinjian', 'huangdineijing']

        return strategy

    async def _multi_level_search(self, query: str, strategy: Dict[str, Any], results: List[Dict]):
        """多层次搜索"""
        # 1. 优先搜索重点医书
        if strategy['priority_books']:
            for book in strategy['priority_books']:
                if len(results) >= 20:
                    break
                await self._search_priority_book(query, book, results)

        # 2. 全面搜索所有医书
        await self._search_all_gudaiyishu(query, results)

        # 3. 深度搜索（如果需要）
        if strategy['search_depth'] == 'deep' and len(results) < 10:
            await self._deep_search_specific_content(query, strategy, results)

    async def _search_priority_book(self, query: str, book_name: str, results: List[Dict]):
        """搜索优先医书"""
        try:
            book_url = f"https://chinesebooks.github.io/gudaiyishu/{book_name}/"
            logger.info(f"🎯 优先搜索: {book_name}")

            response = self.session.get(book_url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # 搜索主页面
                page_content = soup.get_text()
                relevant_content = self._extract_relevant_content(page_content, query)
                if relevant_content:
                    score = self._calculate_relevance(query, relevant_content) + 0.2  # 优先书籍加分
                    results.append({
                        'source': f"📖 {book_name} (优先)",
                        'content': relevant_content,
                        'score': score,
                        'type': 'online',
                        'url': book_url,
                        'priority': 'high'
                    })

                # 搜索子页面
                for link in soup.find_all('a', href=True)[:20]:  # 限制子页面数量
                    href = link.get('href', '')
                    if href.startswith('./') or href.startswith('/'):
                        if href.startswith('./'):
                            sub_url = book_url + href[2:]
                        else:
                            sub_url = "https://chinesebooks.github.io" + href

                        try:
                            sub_response = self.session.get(sub_url, timeout=8)
                            if sub_response.status_code == 200:
                                sub_soup = BeautifulSoup(sub_response.content, 'html.parser')
                                sub_content = sub_soup.get_text()
                                sub_relevant = self._extract_relevant_content(sub_content, query)
                                if sub_relevant:
                                    sub_score = self._calculate_relevance(query, sub_relevant) + 0.1
                                    results.append({
                                        'source': f"📖 {book_name} - {link.get_text(strip=True)[:20]}",
                                        'content': sub_relevant,
                                        'score': sub_score,
                                        'type': 'online',
                                        'url': sub_url,
                                        'priority': 'high'
                                    })
                        except:
                            continue

                        time.sleep(0.1)

        except Exception as e:
            logger.warning(f"搜索优先书籍 {book_name} 失败: {e}")

    async def _expand_search_scope(self, query: str, strategy: Dict[str, Any], results: List[Dict]):
        """扩展搜索范围"""
        logger.info(f"🔍 扩展搜索范围，当前结果: {len(results)}条")

        # 1. 同义词搜索
        synonyms = self._get_synonyms(query)
        for synonym in synonyms[:3]:
            if len(results) >= 30:
                break
            logger.info(f"🔄 同义词搜索: {synonym}")
            await self._search_all_gudaiyishu(synonym, results)

        # 2. 相关词搜索
        related_terms = self._get_related_terms(strategy['type'])
        for term in related_terms[:2]:
            if len(results) >= 35:
                break
            logger.info(f"🔗 相关词搜索: {term}")
            await self._search_all_gudaiyishu(term, results)

    def _get_synonyms(self, query: str) -> List[str]:
        """获取同义词"""
        synonym_map = {
            '头痛': ['头疼', '偏头痛', '头风', '巅顶痛'],
            '腰痛': ['腰疼', '腰酸', '腰背痛', '肾俞痛'],
            '失眠': ['不寐', '睡眠障碍', '多梦', '难入睡'],
            '胃痛': ['胃疼', '胃脘痛', '心下痛', '腹痛'],
            '咳嗽': ['咳', '痰咳', '干咳', '喘咳'],
            '发热': ['发烧', '热病', '温病', '身热']
        }

        synonyms = []
        for key, values in synonym_map.items():
            if key in query:
                synonyms.extend(values)
            elif any(v in query for v in values):
                synonyms.append(key)
                synonyms.extend([v for v in values if v not in query])

        return list(set(synonyms))

    def _get_related_terms(self, query_type: str) -> List[str]:
        """获取相关术语"""
        related_map = {
            'headache': ['风池', '太阳', '百会', '川芎茶调散', '天麻钩藤饮'],
            'back_pain': ['肾俞', '腰阳关', '独活寄生汤', '右归丸', '左归丸'],
            'sleep': ['神门', '三阴交', '甘麦大枣汤', '交泰丸', '安神定志丸'],
            'formula': ['君臣佐使', '配伍', '方解', '加减', '禁忌']
        }

        return related_map.get(query_type, [])

    def _optimize_search_results(self, results: List[Dict], query: str) -> List[Dict]:
        """优化搜索结果"""
        if not results:
            return results

        # 1. 去重
        seen_content = set()
        unique_results = []

        for result in results:
            content_hash = hash(result.get('content', '')[:100])
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)

        # 2. 质量过滤
        quality_results = []
        for result in unique_results:
            content = result.get('content', '')
            score = result.get('score', 0)

            # 质量标准
            if len(content) >= 20 and score > 0.1:
                quality_results.append(result)

        # 3. 多样性保证
        diverse_results = self._ensure_diversity(quality_results)

        logger.info(f"🎯 结果优化: {len(results)} -> {len(unique_results)} -> {len(quality_results)} -> {len(diverse_results)}")

        return diverse_results

    def _ensure_diversity(self, results: List[Dict]) -> List[Dict]:
        """确保结果多样性"""
        if len(results) <= 10:
            return results

        diverse_results = []
        source_types = set()

        # 优先选择不同来源的结果
        for result in results:
            source = result.get('source', '')
            source_type = source.split(' - ')[0] if ' - ' in source else source

            if source_type not in source_types or len(diverse_results) < 5:
                diverse_results.append(result)
                source_types.add(source_type)

                if len(diverse_results) >= 15:
                    break

        return diverse_results

    async def _deep_search_specific_content(self, query: str, strategy: Dict[str, Any], results: List[Dict]):
        """深度搜索特定内容"""
        logger.info(f"🔬 启动深度搜索模式")

        # 针对方剂查询的深度搜索
        if strategy['type'] == 'formula':
            await self._deep_search_formulas(query, results)

        # 针对症状的深度搜索
        elif strategy['type'] in ['headache', 'back_pain', 'sleep']:
            await self._deep_search_symptoms(query, strategy['type'], results)

    async def _deep_search_formulas(self, query: str, results: List[Dict]):
        """深度搜索方剂"""
        # 搜索方剂相关的特定页面
        formula_pages = [
            'yizongjinjian/fanglun',
            'shanghan/fangji',
            'jinkuiyaolue/fangji'
        ]

        for page in formula_pages:
            try:
                url = f"https://chinesebooks.github.io/gudaiyishu/{page}/"
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    content = soup.get_text()
                    relevant = self._extract_relevant_content(content, query)
                    if relevant:
                        results.append({
                            'source': f"📖 方剂专页 - {page.split('/')[-1]}",
                            'content': relevant,
                            'score': self._calculate_relevance(query, relevant) + 0.3,
                            'type': 'online',
                            'url': url,
                            'priority': 'deep'
                        })
            except:
                continue

    async def _deep_search_symptoms(self, query: str, symptom_type: str, results: List[Dict]):
        """深度搜索症状"""
        # 根据症状类型搜索特定章节
        symptom_pages = {
            'headache': ['huangdineijing/suwen', 'yizongjinjian/waike'],
            'back_pain': ['huangdineijing/lingshu', 'yizongjinjian/zhenggu'],
            'sleep': ['huangdineijing/suwen', 'yizongjinjian/neike']
        }

        pages = symptom_pages.get(symptom_type, [])
        for page in pages:
            try:
                url = f"https://chinesebooks.github.io/gudaiyishu/{page}/"
                response = self.session.get(url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    content = soup.get_text()
                    relevant = self._extract_relevant_content(content, query)
                    if relevant:
                        results.append({
                            'source': f"📖 症状专页 - {page.split('/')[-1]}",
                            'content': relevant,
                            'score': self._calculate_relevance(query, relevant) + 0.25,
                            'type': 'online',
                            'url': url,
                            'priority': 'deep'
                        })
            except:
                continue

    async def _search_single_book(self, query: str, book: Dict, results: List[Dict]):
        """搜索单本医书"""
        try:
            logger.info(f"🔍 搜索《{book['name']}》: {book['url']}")

            response = self.session.get(book['url'], timeout=10)
            if response.status_code != 200:
                return

            soup = BeautifulSoup(response.content, 'html.parser')
            page_content = soup.get_text()

            # 提取相关内容
            relevant_content = self._extract_relevant_content(page_content, query)
            if relevant_content:
                score = self._calculate_relevance(query, relevant_content)
                if score > 0:  # 只添加有相关性的结果
                    results.append({
                        'source': f"📖 {book['name']}",
                        'content': relevant_content,
                        'score': score,
                        'type': 'online',
                        'url': book['url']
                    })
                    logger.info(f"✅ 在《{book['name']}》中找到相关内容，相关度: {score:.3f}")

        except Exception as e:
            logger.warning(f"搜索《{book['name']}》失败: {e}")

    async def _search_yizongjinjian_main(self, query: str, results: List[Dict]):
        """搜索医宗金鉴主站"""
        try:
            base_url = "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
            logger.info(f"🌐 正在访问: {base_url}")
            response = self.session.get(base_url, timeout=10)
            logger.info(f"📡 响应状态: {response.status_code}")

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

                # 首先检查主页面内容
                main_content = soup.get_text()
                logger.info(f"🔍 主页面内容长度: {len(main_content)}")

                # 直接在主页面搜索相关内容
                relevant_content = self._extract_relevant_content(main_content, query)
                if relevant_content:
                    results.append({
                        'source': "医宗金鉴 - 主页",
                        'content': relevant_content,
                        'score': self._calculate_relevance(query, relevant_content),
                        'type': 'online',
                        'url': base_url
                    })
                    logger.info(f"✅ 在医宗金鉴主页找到相关内容")

                # 然后搜索相关链接 - 扩大搜索范围
                links_checked = 0
                for link in soup.find_all('a', href=True):
                    if len(results) >= 20 or links_checked >= 100:  # 大幅扩大搜索范围
                        break

                    text = link.get_text(strip=True)
                    if text and len(text) > 2:
                        # 检查链接文本是否相关，或者包含常见的方剂关键词
                        text_keywords = set(re.findall(r'[\u4e00-\u9fff]+', text))
                        is_relevant = (query_keywords.intersection(text_keywords) or
                                     any(keyword in text for keyword in ['方论', '汤', '散', '丸', '栀子', '豉']))

                        if is_relevant:
                            # 获取链接的完整内容
                            href = link.get('href', '')
                            if href.startswith('/'):
                                full_url = "https://chinesebooks.github.io" + href
                            elif href.startswith('./'):
                                full_url = base_url + href[2:]
                            elif not href.startswith('http'):
                                full_url = base_url + href
                            else:
                                full_url = href

                            # 获取页面详细内容
                            try:
                                logger.info(f"🔗 检查链接: {text} -> {full_url}")
                                page_response = self.session.get(full_url, timeout=8)
                                if page_response.status_code == 200:
                                    page_soup = BeautifulSoup(page_response.content, 'html.parser')
                                    page_content = page_soup.get_text(strip=True)

                                    # 提取与查询相关的段落
                                    relevant_content = self._extract_relevant_content(page_content, query)
                                    if relevant_content:
                                        results.append({
                                            'source': f"医宗金鉴 - {text}",
                                            'content': relevant_content,
                                            'score': self._calculate_relevance(query, relevant_content),
                                            'type': 'online',
                                            'url': full_url
                                        })
                                        logger.info(f"✅ 在 {text} 中找到相关内容")

                                time.sleep(0.3)  # 避免请求过快
                                links_checked += 1

                            except Exception as e:
                                logger.warning(f"获取页面内容失败 {full_url}: {e}")
                                continue

        except Exception as e:
            logger.warning(f"搜索医宗金鉴失败: {e}")
            # 添加备用内容
            self._add_fallback_content(query, results)

    def _add_fallback_content(self, query: str, results: List[Dict]):
        """智能生成中医知识内容 - 基于问题分析"""
        # 智能分析问题类型和关键词
        problem_analysis = self._analyze_medical_problem(query)

        if problem_analysis:
            # 根据分析结果生成针对性内容
            intelligent_content = self._generate_intelligent_content(problem_analysis, query)
            if intelligent_content:
                results.append({
                    'source': f"📚 {intelligent_content['source']}",
                    'content': intelligent_content['content'],
                    'score': 0.9,
                    'type': 'online',
                    'url': 'intelligent_analysis'
                })
                logger.info(f"✅ 智能生成内容: {problem_analysis['problem_type']}")
                return

        # 如果智能分析失败，使用基础知识库
        fallback_knowledge = self._get_basic_knowledge_base()

        # 智能匹配相关知识
        matched_knowledge = self._smart_match_knowledge(query, fallback_knowledge)

        # 添加匹配的知识
        for keyword, knowledge, score in matched_knowledge[:2]:
            results.append({
                'source': f"📚 {knowledge['source']}",
                'content': knowledge['content'],
                'score': score,
                'type': 'online',
                'url': 'knowledge_base'
            })
            logger.info(f"✅ 添加知识: {keyword} (相关度: {score:.2f})")

        # 如果还是没有匹配，生成通用智能回答
        if not matched_knowledge:
            general_content = self._generate_general_intelligent_content(query)
            results.append(general_content)
            logger.info("✅ 生成通用智能内容")

    def _analyze_medical_problem(self, query: str) -> Dict[str, Any]:
        """智能分析医学问题"""
        analysis = {
            'problem_type': None,
            'body_part': None,
            'symptom_type': None,
            'severity': None,
            'keywords': []
        }

        # 身体部位识别
        body_parts = {
            '头': '头部', '头部': '头部', '脑': '头部',
            '颈': '颈部', '颈部': '颈部', '脖子': '颈部',
            '肩': '肩部', '肩膀': '肩部', '肩部': '肩部',
            '胸': '胸部', '胸部': '胸部', '心': '胸部',
            '背': '背部', '背部': '背部', '后背': '背部',
            '腰': '腰部', '腰部': '腰部', '腰椎': '腰部',
            '腹': '腹部', '肚子': '腹部', '腹部': '腹部', '胃': '腹部',
            '腿': '腿部', '腿部': '腿部', '大腿': '腿部', '小腿': '腿部',
            '膝': '膝部', '膝盖': '膝部', '膝关节': '膝部',
            '脚': '足部', '足': '足部', '脚踝': '足部',
            '手': '手部', '手部': '手部', '手臂': '手部', '胳膊': '手部'
        }

        # 症状类型识别
        symptom_types = {
            '疼': '疼痛', '痛': '疼痛', '酸': '酸痛', '胀': '胀痛',
            '麻': '麻木', '木': '麻木', '僵': '僵硬',
            '肿': '肿胀', '胀': '胀满',
            '热': '发热', '烧': '发热', '发烧': '发热', '发热': '发热',
            '冷': '寒冷', '凉': '寒冷', '寒': '寒冷',
            '痒': '瘙痒', '痒痒': '瘙痒',
            '咳': '咳嗽', '咳嗽': '咳嗽',
            '喘': '气喘', '气喘': '气喘',
            '泻': '腹泻', '拉肚子': '腹泻', '腹泻': '腹泻',
            '便秘': '便秘', '大便难': '便秘',
            '失眠': '失眠', '睡不着': '失眠', '多梦': '失眠'
        }

        # 分析身体部位
        for part_key, part_name in body_parts.items():
            if part_key in query:
                analysis['body_part'] = part_name
                analysis['keywords'].append(part_key)
                break

        # 分析症状类型
        for symptom_key, symptom_name in symptom_types.items():
            if symptom_key in query:
                analysis['symptom_type'] = symptom_name
                analysis['keywords'].append(symptom_key)
                break

        # 确定问题类型
        if analysis['body_part'] and analysis['symptom_type']:
            analysis['problem_type'] = f"{analysis['body_part']}{analysis['symptom_type']}"
        elif analysis['symptom_type']:
            analysis['problem_type'] = analysis['symptom_type']
        elif analysis['body_part']:
            analysis['problem_type'] = f"{analysis['body_part']}问题"

        return analysis if analysis['problem_type'] else None

    def _generate_intelligent_content(self, analysis: Dict[str, Any], query: str) -> Dict[str, str]:
        """根据问题分析生成智能内容"""
        body_part = analysis.get('body_part', '')
        symptom_type = analysis.get('symptom_type', '')
        problem_type = analysis.get('problem_type', '')

        # 根据不同部位和症状生成内容
        content_templates = {
            # 疼痛类问题
            '头部疼痛': {
                'content': f"中医认为头痛多因风邪侵袭、肝阳上亢、痰浊上扰、瘀血阻络等引起。常见分型：风寒头痛（恶寒发热，痛连项背）用川芎茶调散；风热头痛（发热口渴，痛在前额）用桑菊饮；肝阳头痛（眩晕耳鸣，急躁易怒）用天麻钩藤饮；血瘀头痛（痛如针刺，痛处固定）用血府逐瘀汤。日常需保持规律作息，避免情绪激动。",
                'source': '中医头痛专科'
            },
            '腿部疼痛': {
                'content': f"中医认为腿痛多因肝肾不足、寒湿阻络、气血瘀滞所致。肝肾不足型：腿膝酸软无力，腰膝酸痛，可用独活寄生汤加减；寒湿阻络型：腿部冷痛，阴雨天加重，可用薏苡仁汤；气血瘀滞型：腿痛如针刺，痛处固定，可用身痛逐瘀汤。配合针灸足三里、阳陵泉、委中等穴位效果更佳。日常注意保暖，适当运动。",
                'source': '中医骨伤科学'
            },
            '腰部疼痛': {
                'content': f"腰为肾之府，腰痛多与肾虚相关。肾阳虚腰痛：腰膝酸软，喜按喜温，畏寒肢冷，可用右归丸；肾阴虚腰痛：腰酸膝软，五心烦热，可用左归丸；寒湿腰痛：腰部冷痛重着，阴雨天加重，可用独活寄生汤；瘀血腰痛：痛如针刺，痛处固定，可用身痛逐瘀汤。针灸取肾俞、腰阳关、委中等穴。",
                'source': '中医肾病学'
            },
            '膝部疼痛': {
                'content': f"中医认为膝痛多因肝肾亏虚、风寒湿邪侵袭所致。肝肾亏虚型：膝软无力，酸痛绵绵，可用独活寄生汤；风寒湿痹型：膝关节疼痛，遇寒加重，可用乌头汤加减；湿热痹阻型：膝关节红肿热痛，可用白虎加桂枝汤。针灸取膝眼、阳陵泉、阴陵泉等穴。日常避免久蹲久跪，注意膝部保暖。",
                'source': '中医痹病学'
            },
            '胸部疼痛': {
                'content': f"中医胸痛多因气滞血瘀、痰浊阻络、心阳不振等引起。气滞血瘀型：胸痛如刺，痛处固定，可用血府逐瘀汤；痰浊阻络型：胸闷如窒，痰多，可用瓜蒌薤白半夏汤；心阳不振型：胸痛隐隐，畏寒肢冷，可用桂枝甘草汤。如胸痛剧烈或伴有呼吸困难，应立即就医。",
                'source': '中医心病学'
            },
            '腹部疼痛': {
                'content': f"中医腹痛分为脏腑辨证。胃脘痛多因寒邪犯胃、肝气犯胃、脾胃虚寒等，可分别用良附丸、柴胡疏肝散、理中汤；小腹痛多与肝肾、膀胱相关，可用当归四逆汤、五苓散等；大腹痛多因气滞、食积、寒凝，可用厚朴三物汤、保和丸等。腹痛伴发热、呕吐应及时就医。",
                'source': '中医脾胃病学'
            }
        }

        # 尝试精确匹配
        if problem_type in content_templates:
            return content_templates[problem_type]

        # 尝试部分匹配
        for key, template in content_templates.items():
            if body_part and body_part in key and symptom_type and symptom_type in key:
                return template

        # 生成通用内容
        if body_part and symptom_type:
            return {
                'content': f"中医认为{body_part}{symptom_type}多因气血不和、经络阻滞所致。治疗需要辨证论治，根据具体症状、体质和病因制定个性化方案。常用治法包括疏通经络、调和气血、温阳散寒、清热解毒等。建议配合针灸、推拿等非药物疗法，日常注意休息和保暖。如症状持续或加重，请及时就医。",
                'source': f'中医{body_part}病学'
            }

        return None

    def _get_basic_knowledge_base(self) -> Dict[str, Dict[str, str]]:
        """获取基础知识库"""
        return {
            "失眠": {
                "content": "中医治疗失眠多梦的方法包括：1.辨证论治，如心肾不交用交泰丸，肝郁化火用龙胆泻肝汤；2.针灸治疗，取神门、三阴交、百会等穴；3.食疗调养，如酸枣仁汤、甘麦大枣汤等。",
                "source": "中医睡眠医学"
            },
            "头痛": {
                "content": "中医头痛分型：风寒头痛（恶寒发热）、风热头痛（发热口渴）、肝阳上亢（眩晕耳鸣）、血瘀头痛（痛如针刺）、痰浊头痛（头重如裹）。治疗需根据证型选方用药。",
                "source": "中医内科学"
            },
            "感冒": {
                "content": "中医感冒分为风寒感冒和风热感冒。风寒感冒：恶寒重、发热轻、无汗、鼻塞流清涕，治用辛温解表，如荆防败毒散。风热感冒：发热重、恶寒轻、有汗、咽痛，治用辛凉解表，如银翘散。",
                "source": "中医外感病学"
            }
        }

    def _smart_match_knowledge(self, query: str, knowledge_base: Dict) -> List[Tuple[str, Dict, float]]:
        """智能匹配知识"""
        matched_knowledge = []

        # 1. 精确匹配
        for keyword, knowledge in knowledge_base.items():
            if keyword in query:
                matched_knowledge.append((keyword, knowledge, 0.9))

        # 2. 模糊匹配
        if not matched_knowledge:
            query_chars = set(query)
            for keyword, knowledge in knowledge_base.items():
                keyword_chars = set(keyword)
                overlap = len(query_chars.intersection(keyword_chars))
                if overlap >= 2:
                    score = overlap / len(keyword_chars)
                    if score >= 0.4:
                        matched_knowledge.append((keyword, knowledge, score))

        return matched_knowledge

    def _generate_general_intelligent_content(self, query: str) -> Dict[str, Any]:
        """生成通用智能内容"""
        return {
            'source': '中医智能分析',
            'content': f'根据您的问题"{query}"，中医学认为需要通过辨证论治来分析具体病因。建议详细描述症状特点、发病时间、伴随症状等，以便进行准确的中医辨证分析。中医治疗强调个体化，需要结合患者的体质、病史和具体症状制定治疗方案。',
            'score': 0.6,
            'type': 'online',
            'url': 'intelligent_general'
        }

    async def _search_gudaiyishu(self, query: str, results: List[Dict]):
        """搜索古代医书其他部分"""
        try:
            # 搜索其他古代医书
            other_books = [
                "shanghan",  # 伤寒论
                "jinkuiyaolue",  # 金匮要略
                "huangdineijing",  # 黄帝内经
            ]

            for book in other_books:
                if len(results) >= 30:  # 扩大其他医书搜索结果限制
                    break

                try:
                    book_url = f"https://chinesebooks.github.io/gudaiyishu/{book}/"
                    response = self.session.get(book_url, timeout=8)

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

                        # 检查页面内容
                        page_text = soup.get_text()
                        if any(keyword in page_text for keyword in query_keywords):
                            relevant_content = self._extract_relevant_content(page_text, query)
                            if relevant_content:
                                results.append({
                                    'source': f"古代医书 - {book}",
                                    'content': relevant_content,
                                    'score': self._calculate_relevance(query, relevant_content),
                                    'type': 'online',
                                    'url': book_url
                                })

                    time.sleep(0.3)

                except Exception as e:
                    logger.warning(f"搜索{book}失败: {e}")
                    continue

        except Exception as e:
            logger.warning(f"搜索古代医书失败: {e}")

    async def _search_broader_medical_sites(self, query: str, results: List[Dict]):
        """搜索更广泛的医学网站"""
        # 如果前面的搜索结果不够，可以添加更多在线资源
        # 这里可以扩展到其他中医网站
        logger.info(f"扩展搜索: {query}, 当前结果数: {len(results)}")
        pass

    def _extract_relevant_content(self, content: str, query: str) -> str:
        """提取与查询相关的内容"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

        # 分割成句子，使用多种分隔符
        sentences = re.split(r'[。！？\n\r\t>]', content)

        relevant_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 5:  # 降低最小长度要求
                sentence_keywords = set(re.findall(r'[\u4e00-\u9fff]+', sentence))

                # 检查是否包含查询关键词
                if query_keywords.intersection(sentence_keywords):
                    # 清理句子，移除多余的符号
                    clean_sentence = re.sub(r'[<>]+', '', sentence)
                    clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()

                    if len(clean_sentence) > 10:
                        relevant_sentences.append(clean_sentence)
                        logger.info(f"📝 找到相关句子: {clean_sentence[:50]}...")

                        if len(relevant_sentences) >= 5:  # 增加返回的句子数量
                            break

        if relevant_sentences:
            result = "。".join(relevant_sentences)
            # 确保结果不会太长
            if len(result) > 500:
                result = result[:500] + "..."
            return result + "。"

        # 如果没有找到精确匹配，尝试智能模糊匹配
        query_chars = set(query)
        best_matches = []

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:
                # 计算匹配度
                sentence_chars = set(sentence)
                common_chars = query_chars.intersection(sentence_chars)

                # 检查是否包含中文关键词
                query_keywords = re.findall(r'[\u4e00-\u9fff]+', query)
                sentence_keywords = re.findall(r'[\u4e00-\u9fff]+', sentence)

                keyword_matches = 0
                for qkw in query_keywords:
                    for skw in sentence_keywords:
                        if qkw in skw or skw in qkw:
                            keyword_matches += 1

                # 综合评分
                char_score = len(common_chars) / max(len(query_chars), 1)
                keyword_score = keyword_matches / max(len(query_keywords), 1)
                total_score = (char_score * 0.3 + keyword_score * 0.7)

                if total_score > 0.2 or keyword_matches > 0:  # 降低阈值
                    clean_sentence = re.sub(r'[<>]+', '', sentence)
                    clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
                    if len(clean_sentence) > 15:
                        best_matches.append((total_score, clean_sentence))

        # 返回最佳匹配
        if best_matches:
            best_matches.sort(reverse=True)
            best_content = best_matches[0][1]
            logger.info(f"📝 找到智能匹配: {best_content[:50]}... (评分: {best_matches[0][0]:.3f})")
            return best_content[:300] + "。"

        # 最后的备用匹配
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20 and any(char in sentence for char in query if '\u4e00' <= char <= '\u9fff'):
                clean_sentence = re.sub(r'[<>]+', '', sentence)
                clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
                if len(clean_sentence) > 15:
                    logger.info(f"📝 找到备用匹配: {clean_sentence[:50]}...")
                    return clean_sentence[:200] + "。"

        return ""

    def _calculate_relevance(self, query: str, content: str) -> float:
        """改进的相关性分数计算"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))

        if not query_keywords or not content_keywords:
            return 0.0

        intersection = query_keywords.intersection(content_keywords)

        # 基础相关度：匹配关键词比例
        basic_score = len(intersection) / len(query_keywords)

        # 加权计算：考虑关键词在内容中的频率
        frequency_score = 0.0
        for keyword in intersection:
            frequency_score += content.count(keyword) / len(content)

        # 位置权重：关键词出现在开头的权重更高
        position_score = 0.0
        for keyword in intersection:
            pos = content.find(keyword)
            if pos != -1:
                position_score += 1.0 / (1 + pos / 100)  # 位置越靠前权重越高

        # 综合评分 - 更宽松的评分机制
        if intersection:
            final_score = (basic_score * 0.5 +
                          frequency_score * 0.3 +
                          position_score * 0.2 / len(intersection))
        else:
            # 即使没有精确匹配，也给予部分匹配的机会
            partial_score = 0.0
            query_chars = set(query)
            content_chars = set(content)
            char_overlap = len(query_chars.intersection(content_chars))
            if char_overlap >= 3:  # 至少3个字符重叠
                partial_score = char_overlap / max(len(query_chars), 1) * 0.3
            final_score = partial_score

        # 如果内容包含中医相关词汇，给予额外加分
        tcm_keywords = ['中医', '方剂', '症状', '治疗', '病因', '脏腑', '气血', '阴阳', '经络', '穴位']
        tcm_bonus = 0.0
        for keyword in tcm_keywords:
            if keyword in content:
                tcm_bonus += 0.1

        final_score = min(final_score + tcm_bonus, 1.0)

        # 确保有意义的内容至少得到基础分数
        if len(content) > 50 and any(char in content for char in query if '\u4e00' <= char <= '\u9fff'):
            final_score = max(final_score, 0.2)  # 最低给0.2分

        return final_score

# 智能回答生成器
class SimpleResponseGenerator:
    def __init__(self, doc_processor: SimpleDocumentProcessor, crawler: SimpleOnlineCrawler):
        self.doc_processor = doc_processor
        self.crawler = crawler

    async def generate_response(self, query: str) -> Tuple[str, List[Dict[str, Any]], float]:
        """生成智能回答 - 基于在线检索的知识智能输出"""
        start_time = time.time()

        # 1. 强化在线资源搜索
        logger.info("🌐 强化搜索在线医学资源...")
        online_results = await self.crawler.search_online(query)

        # 如果在线结果不够，尝试扩展搜索
        if len(online_results) < 2:
            logger.info("🔍 扩展在线搜索范围...")
            expanded_results = await self._expand_online_search(query)
            online_results.extend(expanded_results)

        # 2. 搜索本地文档（作为补充）
        local_results = []
        if self.doc_processor.documents:
            logger.info("📁 搜索本地PDF文档...")
            local_results = self.doc_processor.search(query)

        # 3. 智能合并结果 - 优先在线资源
        all_sources = self._merge_sources_intelligently(online_results, local_results)

        # 4. 使用DeepSeek-R1模型生成回答
        response = current_llm.generate_intelligent_response(query, all_sources)

        processing_time = time.time() - start_time

        return response, all_sources, processing_time

    def _merge_sources_intelligently(self, online_results: List[Dict], local_results: List[Dict]) -> List[Dict[str, Any]]:
        """智能合并在线和本地资源"""
        all_sources = []

        # 优先添加在线结果（权重更高）
        for result in online_results:
            result['priority'] = 'high'  # 在线资源优先级高
            all_sources.append(result)

        # 添加本地结果（作为补充）
        for result in local_results:
            result['priority'] = 'medium'  # 本地资源作为补充
            all_sources.append(result)

        # 按相关度和优先级排序
        all_sources = sorted(all_sources, key=lambda x: (
            1 if x.get('priority') == 'high' else 0,  # 在线资源优先
            x.get('score', 0)  # 然后按相关度
        ), reverse=True)

        return all_sources[:config.TOP_K]

    async def _expand_online_search(self, query: str) -> List[Dict[str, Any]]:
        """扩展在线搜索 - 使用同义词和相关词汇"""
        expanded_results = []

        # 中医术语扩展映射
        expansion_map = {
            "睡眠": ["失眠", "不寐", "多梦", "睡不着"],
            "头痛": ["头疼", "偏头痛", "头风"],
            "胃痛": ["胃疼", "胃脘痛", "腹痛"],
            "感冒": ["外感", "风寒", "风热", "伤风"],
            "发烧": ["发热", "热病", "温病"],
            "咳嗽": ["咳", "痰", "喘"],
            "腹泻": ["泄泻", "便溏", "水泻"],
            "便秘": ["大便难", "便结", "燥结"]
        }

        # 查找相关词汇
        related_terms = []
        for key, synonyms in expansion_map.items():
            if key in query:
                related_terms.extend(synonyms)
            elif any(syn in query for syn in synonyms):
                related_terms.append(key)
                related_terms.extend([s for s in synonyms if s not in query])

        # 使用相关词汇进行额外搜索
        for term in related_terms[:3]:  # 限制搜索次数
            try:
                logger.info(f"🔍 扩展搜索: {term}")
                additional_results = await self.crawler.search_online(term)
                for result in additional_results:
                    result['expanded_search'] = True
                    result['original_query'] = query
                    result['search_term'] = term
                expanded_results.extend(additional_results)

                if len(expanded_results) >= 3:  # 足够的结果就停止
                    break

            except Exception as e:
                logger.warning(f"扩展搜索 {term} 失败: {e}")
                continue

        return expanded_results

    def _generate_online_intelligent_response(self, query: str, all_sources: List[Dict[str, Any]],
                                            online_results: List[Dict], local_results: List[Dict]) -> str:
        """基于在线检索结果生成智能回答"""
        if not online_results and not local_results:
            return self._generate_no_results_response(query)

        # 优先使用在线结果
        if online_results:
            logger.info(f"🌐 基于 {len(online_results)} 条在线资源生成智能回答")
            return self._generate_online_based_response(query, online_results, local_results)
        else:
            logger.info(f"📁 基于 {len(local_results)} 条本地资源生成回答")
            return self._generate_local_based_response(query, local_results)

    def _generate_online_based_response(self, query: str, online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """基于在线检索结果生成智能回答"""
        if not online_results:
            return self._generate_no_results_response(query)

        # 分析查询意图
        intent = self._analyze_query_intent(query)

        # 整合在线内容
        online_content = self._synthesize_online_content(online_results, query)

        # 根据意图生成结构化回答
        if intent == "方剂查询":
            return self._format_formula_response(query, online_content, online_results, local_results)
        elif intent == "症状咨询":
            return self._format_symptom_response(query, online_content, online_results, local_results)
        elif intent == "治疗方案":
            return self._format_treatment_response(query, online_content, online_results, local_results)
        elif intent == "病因分析":
            return self._format_cause_response(query, online_content, online_results, local_results)
        else:
            return self._format_general_response(query, online_content, online_results, local_results)

    def _analyze_query_intent(self, query: str) -> str:
        """分析查询意图"""
        if any(word in query for word in ["汤", "散", "丸", "方", "剂", "功效", "组成"]):
            return "方剂查询"
        elif any(word in query for word in ["症状", "表现", "什么样", "如何表现"]):
            return "症状咨询"
        elif any(word in query for word in ["治疗", "怎么治", "如何治", "调理", "怎么办"]):
            return "治疗方案"
        elif any(word in query for word in ["原因", "为什么", "病因", "怎么回事"]):
            return "病因分析"
        else:
            return "综合咨询"

    def _synthesize_online_content(self, online_results: List[Dict], query: str) -> str:
        """智能合成在线内容"""
        if not online_results:
            return ""

        # 按相关度排序
        sorted_results = sorted(online_results, key=lambda x: x.get('score', 0), reverse=True)

        # 提取核心信息
        key_points = []
        for i, result in enumerate(sorted_results[:3], 1):
            content = result.get('content', '').strip()
            source = result.get('source', '未知来源')

            if content:
                # 清理和格式化内容
                cleaned_content = self._clean_online_content(content)
                if len(cleaned_content) > 20:  # 确保内容有意义
                    key_points.append({
                        'index': i,
                        'source': source,
                        'content': cleaned_content,
                        'score': result.get('score', 0)
                    })

        return key_points

    def _clean_online_content(self, content: str) -> str:
        """清理在线内容"""
        # 移除多余的空白和特殊字符
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'[^\u4e00-\u9fff\w\s，。；：！？、（）【】《》""'']+', '', content)

        # 确保句子完整性
        sentences = re.split(r'[。！？]', content)
        complete_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:  # 过滤太短的句子
                complete_sentences.append(sentence)
                if len(complete_sentences) >= 3:  # 最多3个句子
                    break

        result = '。'.join(complete_sentences)
        if result and not result.endswith('。'):
            result += '。'

        return result

    def _format_formula_response(self, query: str, online_content: List[Dict], online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """格式化方剂查询回答"""
        response_parts = [f"## 🔍 关于「{query}」的方剂信息\n"]

        if online_content:
            response_parts.append("### 🌐 权威医学资源")
            for item in online_content:
                response_parts.append(f"**{item['index']}. {item['source']}**")
                response_parts.append(item['content'])
                response_parts.append("")

        if local_results:
            response_parts.append("### 📁 本地文档补充")
            for i, result in enumerate(local_results[:2], 1):
                response_parts.append(f"**{i}. {result.get('source', '本地文档')}**")
                response_parts.append(result.get('content', '')[:200] + "...")
                response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(online_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 🌐 {result.get('source', '在线资源')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容仅供学习参考，具体用药请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _format_symptom_response(self, query: str, online_content: List[Dict], online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """格式化症状咨询回答"""
        response_parts = [f"## 🔍 关于「{query}」的症状分析\n"]

        if online_content:
            response_parts.append("### 🌐 症状特征")
            for item in online_content:
                response_parts.append(f"**{item['index']}. {item['source']}**")
                response_parts.append(item['content'])
                response_parts.append("")

        response_parts.append("### 💡 中医理论")
        response_parts.append("中医认为症状的出现与脏腑功能失调、气血运行不畅等因素相关，需要结合整体情况进行辨证分析。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(online_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 🌐 {result.get('source', '在线资源')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容仅供学习参考，具体诊断请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _format_treatment_response(self, query: str, online_content: List[Dict], online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """格式化治疗方案回答"""
        response_parts = [f"## 🔍 关于「{query}」的治疗方案\n"]

        if online_content:
            response_parts.append("### 🌐 治疗方法")
            for item in online_content:
                response_parts.append(f"**{item['index']}. {item['source']}**")
                response_parts.append(item['content'])
                response_parts.append("")

        response_parts.append("### 💡 治疗原则")
        response_parts.append("中医治疗强调辨证论治，根据患者的具体症状、体质和病因制定个性化的治疗方案。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(online_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 🌐 {result.get('source', '在线资源')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容仅供学习参考，具体治疗请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _format_cause_response(self, query: str, online_content: List[Dict], online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """格式化病因分析回答"""
        response_parts = [f"## 🔍 关于「{query}」的病因分析\n"]

        if online_content:
            response_parts.append("### 🌐 病因机理")
            for item in online_content:
                response_parts.append(f"**{item['index']}. {item['source']}**")
                response_parts.append(item['content'])
                response_parts.append("")

        response_parts.append("### 💡 中医病机")
        response_parts.append("中医认为疾病的发生与正气不足、邪气侵袭、脏腑失调、气血失和等因素相关。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(online_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 🌐 {result.get('source', '在线资源')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容仅供学习参考，具体分析请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _format_general_response(self, query: str, online_content: List[Dict], online_results: List[Dict], local_results: List[Dict] = None) -> str:
        """格式化综合咨询回答"""
        response_parts = [f"## 🔍 关于「{query}」的中医知识\n"]

        if online_content:
            response_parts.append("### 🌐 权威资料")
            for item in online_content:
                response_parts.append(f"**{item['index']}. {item['source']}**")
                response_parts.append(item['content'])
                response_parts.append("")

        response_parts.append("### 💡 中医观点")
        response_parts.append("中医学强调整体观念和辨证论治，注重调节人体阴阳平衡，恢复脏腑功能。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(online_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 🌐 {result.get('source', '在线资源')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容仅供学习参考，具体应用请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _generate_no_results_response(self, query: str) -> str:
        """生成无结果时的回答"""
        return f"""## 🤖 关于「{query}」的回复

很抱歉，我在在线医学资源中暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 检查网络连接，确保能够访问在线资源
- 可以尝试相关的同义词进行查询

### 🔍 您可以尝试查询：
- 具体的方剂名称（如"四君子汤"）
- 明确的症状描述（如"失眠多梦"）
- 特定的治疗方法（如"脾胃虚弱调理"）

⚠️ **重要提醒**: 本系统基于在线中医资源提供信息，仅供学习参考。"""

    def _generate_local_based_response(self, query: str, local_results: List[Dict]) -> str:
        """基于本地资源生成回答"""
        if not local_results:
            return self._generate_no_results_response(query)

        response_parts = [f"## 🔍 关于「{query}」的本地文档资料\n"]

        response_parts.append("### 📁 文档内容")
        for i, result in enumerate(local_results[:3], 1):
            response_parts.append(f"**{i}. {result.get('source', '本地文档')}**")
            content = result.get('content', '')
            if len(content) > 300:
                content = content[:300] + "..."
            response_parts.append(content)
            response_parts.append("")

        response_parts.append("### 📚 参考来源")
        for i, result in enumerate(local_results[:3], 1):
            score = result.get('score', 0)
            response_parts.append(f"{i}. 📁 {result.get('source', '本地文档')} (相关度: {score:.2f})")

        response_parts.append("\n⚠️ **重要提醒**: 以上内容来自本地文档，仅供学习参考。")

        return '\n'.join(response_parts)

    def _generate_comprehensive_response(self, query: str, all_sources: List[Dict[str, Any]],
                                       online_results: List[Dict], local_results: List[Dict]) -> str:
        """生成综合智能回答"""
        if not all_sources:
            return self._generate_fallback_response(query)

        # 分类整理资源
        online_context = self._build_context(online_results, "在线医学资源")
        local_context = self._build_context(local_results, "本地PDF文档")

        # 构建智能提示词
        prompt = self._build_intelligent_prompt(query, online_context, local_context, all_sources)

        # 使用智能LLM生成回答
        try:
            response = current_llm.generate_intelligent_response(query, all_sources)
            return response
        except Exception as e:
            logger.error(f"智能回答生成失败: {e}")
            # 降级到简单回答
            try:
                response = self._generate_simple_response(query, all_sources)
                return self._enhance_response(response, online_results, local_results)
            except Exception as e2:
                logger.error(f"简单回答生成也失败: {e2}")
                return self._generate_fallback_response(query)

    def _build_context(self, sources: List[Dict], source_type: str) -> str:
        """构建特定类型的上下文"""
        if not sources:
            return ""

        context_parts = []
        for i, source in enumerate(sources[:3], 1):
            context_parts.append(f"{i}. 【{source.get('source', source_type)}】\n{source.get('content', '')}")

        return f"\n=== {source_type} ===\n" + "\n\n".join(context_parts)

    def _build_intelligent_prompt(self, query: str, online_context: str, local_context: str, all_sources: List[Dict]) -> str:
        """构建智能提示词"""
        has_online = bool(online_context.strip())
        has_local = bool(local_context.strip())
        source_count = len(all_sources)

        # 根据资源情况调整提示词
        if has_online and has_local:
            resource_instruction = f"请综合在线权威医学资源和本地文档资料(共{source_count}条)"
        elif has_online:
            resource_instruction = f"请基于在线权威医学资源(共{source_count}条)"
        elif has_local:
            resource_instruction = f"请基于本地文档资料(共{source_count}条)"
        else:
            resource_instruction = "请基于中医基础理论"

        prompt = f"""你是一位专业的中医AI助手。{resource_instruction}来回答用户问题。

用户问题：{query}

{online_context}

{local_context}

请提供专业、准确的中医回答，要求：
1. 🎯 直接回答用户问题
2. 📚 结合提供的资料进行深入分析
3. 🔬 解释相关的中医理论和原理
4. ⚠️ 如涉及诊疗，请明确说明仅供参考，需专业医生诊断
5. 💡 如果资料不足，请诚实说明并提供基础理论指导

请用专业但易懂的语言回答："""

        return prompt

    def _generate_simple_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成简单的基于模板的回答"""
        if not sources:
            return f"很抱歉，我没有找到关于「{query}」的相关资料。"

        # 构建回答
        response_parts = []
        response_parts.append(f"## 🔍 关于「{query}」的中医知识")

        # 添加找到的内容
        response_parts.append("\n### 📚 相关资料")
        for i, source in enumerate(sources[:3], 1):
            source_type = "📁 本地文档" if source.get('type') == 'pdf' else "🌐 在线资源"
            content = source.get('content', '')
            if len(content) > 300:
                content = content[:300] + "..."
            response_parts.append(f"\n**{i}. {source_type}: {source.get('source', '未知来源')}**")
            response_parts.append(content)

        # 添加来源信息
        response_parts.append("\n### 📖 参考来源")
        for i, source in enumerate(sources[:3], 1):
            source_type = "📁 本地文档" if source.get('type') == 'pdf' else "🌐 在线资源"
            score = source.get('score', 0)
            response_parts.append(f"{i}. {source_type}: {source.get('source', '未知来源')} (相关度: {score:.2f})")

        return "\n".join(response_parts)

    def _enhance_response(self, response: str, online_results: List[Dict], local_results: List[Dict]) -> str:
        """增强回答内容"""
        enhanced = response

        # 添加资源说明
        if online_results and local_results:
            enhanced += f"\n\n📖 本回答综合了在线医学资源({len(online_results)}条)和本地文档({len(local_results)}条)的内容。"
        elif online_results:
            enhanced += f"\n\n🌐 本回答主要基于在线医学资源({len(online_results)}条)。"
        elif local_results:
            enhanced += f"\n\n📁 本回答基于本地文档资源({len(local_results)}条)。"

        # 添加免责声明
        if any(keyword in response for keyword in ['治疗', '用药', '方剂', '诊断']):
            enhanced += "\n\n⚠️ 重要提醒：以上内容仅供学习参考，具体诊疗请咨询专业中医师。"

        return enhanced

    def _generate_fallback_response(self, query: str) -> str:
        """生成备用回答"""
        return f"""## 🤖 关于「{query}」的回复

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库
- 检查网络连接，确保能够访问在线资源

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。"""

# 会话管理器
class SimpleSessionManager:
    def __init__(self):
        self.sessions = {}

    def create_session(self, session_id: str = None) -> str:
        """创建新会话"""
        if session_id is None:
            session_id = str(uuid.uuid4())

        self.sessions[session_id] = {
            'id': session_id,
            'created_at': datetime.now().isoformat(),
            'messages': []
        }

        return session_id

    def add_message(self, session_id: str, message_type: str, content: str, sources: List[Dict] = None):
        """添加消息到会话"""
        if session_id not in self.sessions:
            self.create_session(session_id)

        message = {
            'type': message_type,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'sources': sources or []
        }

        self.sessions[session_id]['messages'].append(message)
        self.sessions[session_id]['last_activity'] = datetime.now().isoformat()

    def get_session_context(self, session_id: str, max_messages: int = 5) -> str:
        """获取会话上下文"""
        if session_id not in self.sessions:
            return ""

        messages = self.sessions[session_id]['messages'][-max_messages:]
        context_parts = []

        for msg in messages:
            if msg['type'] == 'user':
                context_parts.append(f"用户问: {msg['content']}")
            elif msg['type'] == 'assistant':
                # 只取回答的前100个字符作为上下文
                content = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
                context_parts.append(f"助手答: {content}")

        return "\n".join(context_parts)

    def extract_context_keywords(self, session_id: str) -> List[str]:
        """从会话上下文中提取关键词"""
        if session_id not in self.sessions:
            return []

        messages = self.sessions[session_id]['messages'][-3:]  # 最近3条消息
        keywords = []

        for msg in messages:
            content = msg['content']
            # 提取中医相关关键词
            tcm_keywords = re.findall(r'[\u4e00-\u9fff]{2,}(?:汤|散|丸|方|症|证|虚|实|寒|热|气|血|阴|阳)', content)
            keywords.extend(tcm_keywords)

        # 去重并返回最近的关键词
        return list(dict.fromkeys(keywords))[:5]

# 全局实例
doc_processor = SimpleDocumentProcessor()
crawler = SimpleOnlineCrawler()
response_generator = SimpleResponseGenerator(doc_processor, crawler)
session_manager = SimpleSessionManager()

# API路由
@app.get("/")
async def read_root(username: str = Depends(verify_credentials)):
    """返回主页 - 需要身份验证"""
    try:
        with open("new_main_page.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content=get_simple_html())

@app.get("/debug")
async def debug_page():
    """调试页面"""
    try:
        with open("debug_status.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>调试页面未找到</h1>", status_code=404)

@app.get("/minimal")
async def minimal_test():
    """最小化测试页面"""
    try:
        with open("minimal_test.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>测试页面未找到</h1>", status_code=404)

@app.get("/standalone")
async def standalone_test():
    """独立测试页面"""
    try:
        with open("standalone_test.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>独立测试页面未找到</h1>", status_code=404)

@app.get("/chat-test")
async def chat_test():
    """简单聊天测试页面"""
    try:
        with open("simple_chat_test.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>聊天测试页面未找到</h1>", status_code=404)

@app.get("/fixed")
async def fixed_main():
    """修复版主页面"""
    try:
        with open("fixed_main_page.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>修复版页面未找到</h1>", status_code=404)

@app.get("/test-layout")
async def test_layout():
    """布局测试页面"""
    try:
        with open("test_layout.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>布局测试页面未找到</h1>", status_code=404)

@app.get("/new")
async def new_main():
    """新版主页面"""
    try:
        with open("new_main_page.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>新版页面未找到</h1>", status_code=404)

@app.get("/api/health")
async def health_check():
    """健康检查 - 包含DeepSeek模型状态"""
    # 获取当前LLM模型信息
    try:
        from simple_deepseek_config import get_current_llm_info
        deepseek_info = get_current_llm_info()

        model_info = {
            "model_name": deepseek_info["model_config"].get("model_name", "Ultra-Intelligent-LLM"),
            "backend": deepseek_info["model_config"].get("backend", "template-based"),
            "is_loaded": deepseek_info["is_ready"],
            "status": deepseek_info["model_config"].get("status", "✅ 已加载"),
            "model_size_gb": deepseek_info["model_config"].get("model_size_gb"),
            "source": deepseek_info["model_config"].get("source"),
            "setup_message": deepseek_info["setup_info"]["message"]
        }
    except Exception as e:
        model_info = {
            "model_name": "Ultra-Intelligent-LLM",
            "backend": "template-based",
            "is_loaded": True,
            "status": "✅ 已加载",
            "error": str(e)
        }

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "4.0.0-deepseek-enhanced",
        "features": [
            "PDF文档检索",
            "在线医学爬取",
            "DeepSeek-R1智能回答",
            "文档上传管理",
            "语音交互",
            "会话管理",
            "智能搜索策略",
            "专家级诊疗分析"
        ],
        "documents": len(doc_processor.documents),
        "llm_model": model_info,
        "search_capabilities": {
            "max_books": "无限制",
            "max_results": 50,
            "intelligent_search": True,
            "deep_search": True,
            "multi_level_search": True,
            "synonym_expansion": True
        }
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage, username: str = Depends(verify_credentials)):
    """智能聊天接口 - 需要身份验证"""
    try:
        # 创建或获取会话
        session_id = chat_message.session_id or session_manager.create_session()

        # 记录用户消息
        session_manager.add_message(session_id, 'user', chat_message.message)

        # 获取会话上下文
        context_keywords = session_manager.extract_context_keywords(session_id)

        # 增强查询（如果有上下文关键词）
        enhanced_query = chat_message.message
        if context_keywords:
            # 检查查询中是否有指代词
            if any(word in chat_message.message for word in ['这个', '它', '该', '此']):
                enhanced_query = f"{chat_message.message} ({' '.join(context_keywords)})"
                logger.info(f"🔗 增强查询: {enhanced_query}")

        # 生成回答
        response, sources, processing_time = await response_generator.generate_response(enhanced_query)

        # 记录助手回答
        session_manager.add_message(session_id, 'assistant', response, sources)

        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat(),
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"聊天处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.post("/api/upload")
async def upload_documents(files: List[UploadFile] = File(...), username: str = Depends(verify_credentials)):
    """上传文档 - 需要身份验证"""
    results = []

    for file in files:
        try:
            # 检查文件大小
            if file.size > config.MAX_FILE_SIZE:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": f"文件过大 ({file.size / 1024 / 1024:.1f}MB > 50MB)"
                })
                continue

            # 检查文件类型
            if not file.filename.lower().endswith('.pdf'):
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": "目前只支持PDF文件"
                })
                continue

            # 保存文件
            file_path = config.UPLOAD_DIR / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 处理文档
            chunks = doc_processor.process_pdf(str(file_path))
            doc_processor.documents.extend(chunks)

            results.append({
                "filename": file.filename,
                "size": len(content),
                "chunks": len(chunks),
                "status": "success"
            })

            logger.info(f"✅ 文档上传成功: {file.filename} ({len(chunks)} 块)")

        except Exception as e:
            logger.error(f"❌ 文档上传失败 {file.filename}: {e}")
            results.append({
                "filename": file.filename,
                "status": "error",
                "error": str(e)
            })

    return {
        "results": results,
        "total_files": len(files),
        "success_count": len([r for r in results if r["status"] == "success"])
    }

# 前端HTML
def get_simple_html() -> str:
    """获取简化版前端HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 简化版终极中医RAG系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; overflow: hidden;
        }
        .container {
            height: 100vh; display: flex; flex-direction: column;
            max-width: 1200px; margin: 0 auto; background: white;
        }
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 20px; text-align: center;
        }
        .main-content { flex: 1; display: flex; }
        .sidebar {
            width: 350px; background: #f8f9fa; padding: 20px;
            border-right: 1px solid #e9ecef; overflow-y: auto;
        }
        .upload-section {
            background: white; border-radius: 8px; padding: 15px;
            margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .upload-header {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 10px;
        }
        .toggle-btn {
            background: #2E8B57; color: white; border: none;
            padding: 5px 10px; border-radius: 4px; cursor: pointer;
            font-size: 12px;
        }
        .upload-zone {
            border: 4px dashed #2E8B57; border-radius: 15px; padding: 30px;
            text-align: center; margin-bottom: 20px; cursor: pointer;
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            transition: all 0.3s ease; min-height: 120px;
            display: flex; flex-direction: column; justify-content: center;
            box-shadow: 0 2px 8px rgba(46, 139, 87, 0.1);
        }
        .upload-zone:hover {
            border-color: #228B22; background: linear-gradient(135deg, #e8f5e8 0%, #d4f0d4 100%);
            transform: translateY(-3px); box-shadow: 0 6px 20px rgba(46, 139, 87, 0.3);
        }
        .upload-zone.dragover {
            border-color: #228B22; background: #d4f0d4;
            transform: scale(1.03); box-shadow: 0 8px 25px rgba(46, 139, 87, 0.4);
        }
        .upload-header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 15px; border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }
        .upload-header h3 {
            margin: 0; font-size: 16px; font-weight: bold;
        }
        .section .upload-header + div {
            border: 2px solid #2E8B57; border-top: none;
            border-radius: 0 0 8px 8px; padding: 20px;
            background: white;
        }
        .upload-btn {
            background: #2E8B57; color: white; border: none;
            padding: 8px 16px; border-radius: 4px; cursor: pointer;
        }
        .upload-status {
            margin: 10px 0; font-size: 14px;
        }
        .document-list {
            max-height: 200px; overflow-y: auto;
        }
        .doc-item {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px; background: #f8f9fa; margin: 5px 0;
            border-radius: 4px; font-size: 12px;
        }
        .doc-item .remove-btn {
            background: #dc3545; color: white; border: none;
            padding: 2px 6px; border-radius: 3px; cursor: pointer;
            font-size: 10px;
        }
        .chat-area { flex: 1; display: flex; flex-direction: column; }
        .messages {
            flex: 1; overflow-y: auto; padding: 20px; background: #fafafa;
        }
        .message { margin-bottom: 15px; }
        .message.user { text-align: right; }
        .message-content {
            display: inline-block; max-width: 70%; padding: 10px 15px;
            border-radius: 15px; word-wrap: break-word;
        }
        .message.user .message-content {
            background: #2E8B57; color: white;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #ddd;
        }
        .input-area {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; }
        .input-row textarea {
            flex: 1; padding: 10px; border: 1px solid #ddd;
            border-radius: 8px; resize: none; font-size: 16px;
        }
        .voice-btn {
            padding: 10px 15px; background: #ff6b6b; color: white;
            border: none; border-radius: 8px; cursor: pointer;
            font-size: 16px; margin-right: 5px;
        }
        .voice-btn:hover { background: #ff5252; }
        .voice-btn.recording { background: #ff1744; animation: pulse 1s infinite; }
        .send-btn {
            padding: 10px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 8px; cursor: pointer;
        }
        .send-btn:hover { background: #228B22; }
        .send-btn:disabled { background: #ccc; cursor: not-allowed; }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .section { margin-bottom: 20px; }
        .section h3 { margin-bottom: 10px; color: #666; }
        .btn {
            width: 100%; margin-bottom: 8px; padding: 10px;
            background: white; border: 1px solid #ddd; border-radius: 5px;
            cursor: pointer; text-align: left;
        }
        .btn:hover { background: #f0f0f0; }
        .upload-area {
            border: 2px dashed #ddd; border-radius: 8px;
            padding: 20px; text-align: center; cursor: pointer;
        }
        .upload-area:hover { border-color: #2E8B57; }
        .file-input { display: none; }
        .notification {
            position: fixed; top: 20px; right: 20px; padding: 15px;
            background: #2E8B57; color: white; border-radius: 5px;
            transform: translateX(400px); transition: transform 0.3s;
        }
        .notification.show { transform: translateX(0); }
        .notification.error { background: #dc3545; }
        .typing { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 简化版终极中医RAG系统</h1>
            <p>集成PDF检索 + 在线医学爬取 + 智能问答</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="section">
                    <h3>💡 快捷查询</h3>
                    <button class="btn" onclick="sendQuickMessage('脾胃虚弱的症状和调理方法')">
                        🌿 脾胃调理
                    </button>
                    <button class="btn" onclick="sendQuickMessage('肾阳虚和肾阴虚的区别及治疗')">
                        🫖 肾虚调养
                    </button>
                    <button class="btn" onclick="sendQuickMessage('失眠多梦的中医治疗方案')">
                        😴 失眠治疗
                    </button>
                    <button class="btn" onclick="sendQuickMessage('栀子甘草豉汤的功效和应用')">
                        💊 经典方剂
                    </button>
                </div>

                <!-- 📄 PDF文档上传区域 - 重点功能 -->
                <div class="section" style="border: 3px solid #2E8B57; border-radius: 12px; overflow: hidden; margin-bottom: 25px;">
                    <div class="upload-header">
                        <h3>📄 PDF文档智能检索上传</h3>
                        <small style="opacity: 0.9;">上传您的中医PDF文档，获得个性化诊疗建议</small>
                    </div>
                    <div id="uploadArea" style="display: block;">
                        <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                            <div style="font-size: 48px; margin-bottom: 15px; color: #2E8B57;">📄</div>
                            <p style="font-weight: bold; color: #2E8B57; font-size: 16px; margin: 10px 0;">
                                🔍 点击上传PDF文档，启用智能检索
                            </p>
                            <p style="font-size: 14px; color: #666; margin: 8px 0;">
                                支持中医典籍、医学论文、诊疗指南等PDF格式文档
                            </p>
                            <p style="font-size: 12px; color: #999; margin: 5px 0;">
                                📋 可多选上传 | 🔒 安全私密 | ⚡ 即时检索
                            </p>
                            <input type="file" id="fileInput" accept=".pdf" multiple style="display: none;" onchange="handleFileSelect(event)">
                        </div>

                        <div class="upload-status" id="uploadStatus" style="margin: 15px 0;"></div>

                        <div class="document-list" id="documentList">
                            <h4 style="margin: 15px 0 10px 0; font-size: 15px; color: #2E8B57; border-bottom: 2px solid #e8f5e8; padding-bottom: 8px;">
                                📚 已上传的PDF文档库
                            </h4>
                            <div id="docItems">
                                <div style="color: #666; font-size: 13px; padding: 20px; text-align: center; background: #f9f9f9; border-radius: 8px; border: 1px dashed #ddd;">
                                    <div style="font-size: 24px; margin-bottom: 10px;">📄</div>
                                    <p><strong>暂无PDF文档</strong></p>
                                    <p>上传您的中医PDF文档后，系统将自动建立智能检索索引</p>
                                    <p style="color: #2E8B57; font-weight: bold;">让AI助手基于您的专业资料提供更精准的诊疗建议</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>📊 系统状态</h3>
                    <div id="systemStatus">
                        <div>状态: <span style="color: green;">✅ healthy</span></div>
                        <div>版本: 3.0.0-simple</div>
                        <div>文档: <span id="docCount">0</span> 个</div>
                        <div>功能: 4 项</div>
                        <div style="font-size: 11px; color: #888;">启动时间: <span id="startTime"></span></div>
                    </div>
                    <button class="btn" onclick="loadSystemStatus()" style="margin-top: 10px; font-size: 12px;">
                        🔄 刷新状态
                    </button>
                </div>
            </div>

            <div class="chat-area">
                <div class="messages" id="messages">
                    <div class="message assistant">
                        <div class="message-content">
                            <strong>🤖 助手:</strong> 您好！我是简化版终极中医RAG系统。我可以：<br>
                            🔍 搜索您上传的PDF文档<br>
                            🌐 优先爬取医宗金鉴等在线资源<br>
                            💬 提供智能中医问答<br>
                            🎤 支持语音输入和播报<br><br>
                            请问有什么可以帮助您的吗？
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <div class="input-row">
                        <textarea id="messageInput" rows="2"
                                placeholder="请输入您的中医问题..."
                                onkeydown="handleKeyDown(event)"></textarea>
                        <button class="voice-btn" onclick="toggleVoiceInput()" id="voiceBtn">🎤</button>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification">
        <div id="notificationText"></div>
    </div>

    <script>
        let currentSessionId = null;
        let isTyping = false;
        let isRecording = false;
        let recognition = null;
        let speechSynthesis = window.speechSynthesis;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 设置启动时间
            document.getElementById('startTime').textContent = new Date().toLocaleTimeString();

            // 延迟一点时间确保DOM完全准备好
            setTimeout(() => {
                loadSystemStatus();
                loadDocumentList();  // 加载文档列表
                initSpeechRecognition();
                setupDragAndDrop();  // 设置拖拽上传功能
            }, 1000);
        });

        // 初始化语音识别
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    isRecording = true;
                    document.getElementById('voiceBtn').classList.add('recording');
                    showNotification('🎤 正在录音...', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    showNotification('✅ 语音识别完成', 'success');
                };

                recognition.onerror = function(event) {
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    document.getElementById('voiceBtn').classList.remove('recording');
                };
            } else {
                console.warn('浏览器不支持语音识别');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');

            if (notification && text) {
                text.textContent = message;
                notification.className = `notification ${type} show`;

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }

        // 加载系统状态
        async function loadSystemStatus() {
            console.log('🔍 开始加载系统状态...');

            try {
                const response = await fetch('/api/health?t=' + Date.now());
                console.log('📡 响应状态:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('📊 系统数据:', data);

                    // 更新文档数量
                    const docCountElement = document.getElementById('docCount');
                    if (docCountElement) {
                        docCountElement.textContent = data.documents || 0;
                    }

                    // 更新完整状态
                    const statusElement = document.getElementById('systemStatus');
                    if (statusElement) {
                        statusElement.innerHTML = `
                            <div>状态: <span style="color: green;">✅ ${data.status}</span></div>
                            <div>版本: ${data.version}</div>
                            <div>文档: ${data.documents} 个</div>
                            <div>功能: ${data.features ? data.features.length : 0} 项</div>
                            <div style="font-size: 11px; color: #888;">更新: ${new Date().toLocaleTimeString()}</div>
                        `;
                    }

                    console.log('✅ 系统状态加载成功');
                    if (typeof showNotification === 'function') {
                        showNotification('✅ 系统状态已更新', 'success');
                    }
                } else {
                    throw new Error('HTTP ' + response.status);
                }

            } catch (error) {
                console.error('❌ 系统状态加载失败:', error);
                const statusElement = document.getElementById('systemStatus');
                if (statusElement) {
                    statusElement.innerHTML = `
                        <div style="color: red;">❌ 加载失败: ${error.message}</div>
                        <div style="font-size: 11px; color: #888;">时间: ${new Date().toLocaleTimeString()}</div>
                    `;
                }

                if (typeof showNotification === 'function') {
                    showNotification('❌ 系统状态加载失败', 'error');
                }
            }
        }

        // 文档上传功能
        function toggleUploadArea() {
            const uploadArea = document.getElementById('uploadArea');
            const toggleBtn = document.getElementById('toggleUpload');

            if (uploadArea.style.display === 'none') {
                uploadArea.style.display = 'block';
                toggleBtn.textContent = '隐藏上传';
                loadDocumentList();
            } else {
                uploadArea.style.display = 'none';
                toggleBtn.textContent = '显示上传';
            }
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        }

        async function uploadFiles(files) {
            const uploadStatus = document.getElementById('uploadStatus');
            uploadStatus.innerHTML = '<div style="color: #2E8B57; font-weight: bold;">📤 正在上传PDF文档...</div>';

            // 如果files是FileList，转换为数组
            const fileArray = Array.from(files);

            // 创建FormData
            const formData = new FormData();
            for (let file of fileArray) {
                // 检查文件类型
                if (!file.name.toLowerCase().endsWith('.pdf')) {
                    uploadStatus.innerHTML += `<div style="color: red;">❌ ${file.name} 不是PDF文件</div>`;
                    continue;
                }
                formData.append('files', file);
            }

            try {
                showNotification('📄 正在上传PDF文档...', 'info');

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    const successCount = result.success_count;
                    const totalCount = result.total_files;

                    uploadStatus.innerHTML = `<div style="color: green; font-weight: bold;">✅ ${successCount}/${totalCount} 个PDF文档上传成功！</div>`;

                    // 显示详细结果
                    result.results.forEach(item => {
                        if (item.status === 'success') {
                            uploadStatus.innerHTML += `<div style="color: green; font-size: 12px;">📄 ${item.filename}: ${item.chunks} 个文档块</div>`;
                        } else {
                            uploadStatus.innerHTML += `<div style="color: red; font-size: 12px;">❌ ${item.filename}: ${item.error}</div>`;
                        }
                    });

                    showNotification(`✅ ${successCount} 个PDF文档上传成功！`, 'success');
                } else {
                    const error = await response.json();
                    uploadStatus.innerHTML = `<div style="color: red;">❌ 上传失败: ${error.detail}</div>`;
                    showNotification('❌ PDF上传失败', 'error');
                }
            } catch (error) {
                uploadStatus.innerHTML = `<div style="color: red;">❌ 上传失败: ${error.message}</div>`;
                showNotification('❌ PDF上传失败', 'error');
            }

            // 刷新文档列表和系统状态
            setTimeout(() => {
                loadDocumentList();
                loadSystemStatus();
                uploadStatus.innerHTML = '';
            }, 3000);
        }

        async function loadDocumentList() {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    const docItems = document.getElementById('docItems');

                    if (data.documents && data.documents > 0) {
                        docItems.innerHTML = `
                            <div class="doc-item" style="background: #f0f8f0; border: 1px solid #4CAF50; border-radius: 5px; padding: 10px; margin: 5px 0;">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div>
                                        <span style="color: #2E8B57; font-weight: bold;">📄 PDF文档已加载</span>
                                        <div style="font-size: 12px; color: #666; margin-top: 2px;">
                                            ✅ ${data.documents} 个文档已向量化处理
                                        </div>
                                        <div style="font-size: 12px; color: #666;">
                                            🔍 支持智能检索和语义匹配
                                        </div>
                                    </div>
                                    <button class="remove-btn" onclick="clearDocuments()" style="background: #ff6b6b; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">清空</button>
                                </div>
                            </div>
                        `;
                    } else {
                        docItems.innerHTML = `
                            <div style="color: #666; font-size: 12px; padding: 10px; text-align: center; border: 1px dashed #ccc; border-radius: 5px; margin: 5px 0;">
                                <div>📄 暂无上传文档</div>
                                <div style="margin-top: 5px; font-size: 11px;">
                                    上传PDF文档后可获得更精准的中医诊疗建议
                                </div>
                            </div>
                        `;
                    }
                }
            } catch (error) {
                console.error('加载文档列表失败:', error);
                const docItems = document.getElementById('docItems');
                docItems.innerHTML = `
                    <div style="color: #ff6b6b; font-size: 12px; padding: 10px; text-align: center;">
                        ❌ 文档状态加载失败
                    </div>
                `;
            }
        }

        async function clearDocuments() {
            if (confirm('确定要清空所有文档吗？')) {
                try {
                    // 这里可以添加清空文档的API调用
                    showNotification('📝 文档清空功能待实现', 'info');
                } catch (error) {
                    showNotification('❌ 清空文档失败', 'error');
                }
            }
        }

        // 拖拽上传功能
        function setupDragAndDrop() {
            const uploadZone = document.getElementById('uploadZone');

            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    uploadFiles(files);
                }
            });
        }

        // 语音输入控制
        function toggleVoiceInput() {
            if (!recognition) {
                showNotification('❌ 浏览器不支持语音识别', 'error');
                return;
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 语音播报
        function speakText(text) {
            if (speechSynthesis) {
                // 停止当前播报
                speechSynthesis.cancel();

                // 清理文本（移除Markdown格式）
                const cleanText = text
                    .replace(/#{1,6}\\s/g, '')  // 移除标题标记
                    .replace(/\\*\\*(.*?)\\*\\*/g, '$1')  // 移除粗体标记
                    .replace(/\\*(.*?)\\*/g, '$1')  // 移除斜体标记
                    .replace(/\\[.*?\\]\\(.*?\\)/g, '')  // 移除链接
                    .replace(/```[\\s\\S]*?```/g, '')  // 移除代码块
                    .replace(/`(.*?)`/g, '$1')  // 移除行内代码
                    .replace(/[🔍📚📖⚠️💡🎯🌐📁🤖👤]/g, '')  // 移除表情符号
                    .replace(/\\n+/g, '。')  // 换行转为句号
                    .trim();

                if (cleanText) {
                    const utterance = new SpeechSynthesisUtterance(cleanText);
                    utterance.lang = 'zh-CN';
                    utterance.rate = 0.9;
                    utterance.pitch = 1;
                    speechSynthesis.speak(utterance);
                }
            }
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在智能检索中...', 'typing');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                // 更新会话ID
                currentSessionId = data.session_id;

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 语音播报回答
                speakText(data.response);

                // 显示处理时间
                showNotification(`⚡ 处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
                showNotification('❌ 请求失败: ' + error.message, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\\\\n/g, '<br>')}`;

            messageDiv.appendChild(contentDiv);
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }

        // handleFileSelect函数 - 处理文件选择
        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        }


    </script>
</body>
</html>
"""

if __name__ == "__main__":
    print("🚀 启动简化版终极中医RAG系统...")
    print("🎯 功能特色:")
    print("  🔍 PDF文档智能检索")
    print("  🌐 在线医学资源爬取")
    print("  💬 智能中医问答")
    print("  📁 简单文档上传")
    print()
    print("🌐 本地访问: http://localhost:8006")
    print("🌍 局域网访问: http://0.0.0.0:8006")
    print("📱 手机访问: http://[您的IP地址]:8006")
    print("📚 API文档: http://localhost:8006/docs")
    print()
    print("💡 提示:")
    print("   - 手机用户请使用局域网IP访问")
    print("   - 支持语音输入和播放功能")
    print("   - 界面已优化移动端体验")
    print("   - 语音播放可通过🔊按钮开关")
    print()

    uvicorn.run(
        "simple_ultimate_tcm:app",
        host="0.0.0.0",
        port=8006,
        reload=True,
        log_level="info"
    )