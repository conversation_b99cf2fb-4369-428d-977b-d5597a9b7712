#!/usr/bin/env python3
"""
智能设备路由器 - 根据设备类型自动选择界面
电脑用户：完整功能界面
手机用户：极简聊天界面
"""

import streamlit as st
import subprocess
import sys
import os

# 页面配置
st.set_page_config(
    page_title="🏥 家庭医生助手",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 设备检测CSS和JavaScript
st.markdown("""
<style>
.device-selector {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    margin: 2rem 0;
}

.device-button {
    display: inline-block;
    padding: 1rem 2rem;
    margin: 1rem;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 10px;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.device-button:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.auto-detect {
    background: rgba(255,255,255,0.1);
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
}
</style>

<script>
function detectDevice() {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?=.*Tablet)|Tablet/i.test(userAgent);
    
    if (isMobile && !isTablet) {
        return 'mobile';
    } else {
        return 'desktop';
    }
}

// 自动检测并跳转
window.onload = function() {
    const device = detectDevice();
    const autoRedirect = localStorage.getItem('tcm_auto_redirect');
    
    if (autoRedirect === 'true') {
        if (device === 'mobile') {
            window.location.href = '?interface=mobile';
        } else {
            window.location.href = '?interface=desktop';
        }
    }
}
</script>
""", unsafe_allow_html=True)

def get_query_params():
    """获取URL查询参数"""
    try:
        query_params = st.experimental_get_query_params()
        return query_params
    except:
        return {}

def show_device_selector():
    """显示设备选择界面"""
    st.markdown("""
    <div class="device-selector">
        <h1>🏥 家庭私人医生小帮手</h1>
        <p>请选择您的设备类型以获得最佳体验</p>
        
        <div class="auto-detect">
            <h3>🤖 自动检测</h3>
            <p>系统将自动检测您的设备类型</p>
            <button onclick="
                localStorage.setItem('tcm_auto_redirect', 'true');
                const device = detectDevice();
                if (device === 'mobile') {
                    window.location.href = '?interface=mobile';
                } else {
                    window.location.href = '?interface=desktop';
                }
            " class="device-button">
                🚀 自动选择界面
            </button>
        </div>
        
        <div style="margin: 2rem 0;">
            <h3>📱 手动选择</h3>
            <a href="?interface=mobile" class="device-button">
                📱 手机版<br>
                <small>极简聊天界面</small>
            </a>
            <a href="?interface=desktop" class="device-button">
                💻 电脑版<br>
                <small>完整功能界面</small>
            </a>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # 功能说明
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 📱 手机版特点
        - 🎤 语音对话功能
        - 💬 简洁文字聊天
        - 📱 移动端优化界面
        - 🚀 快速响应
        - 🎯 专注核心功能
        """)
    
    with col2:
        st.markdown("""
        ### 💻 电脑版特点
        - 📄 PDF文档上传
        - 🔍 高级搜索功能
        - 📊 详细分析报告
        - 🛠️ 系统管理功能
        - 🌐 远程访问设置
        """)

def launch_mobile_interface():
    """启动手机版界面"""
    try:
        # 导入手机版系统
        import importlib.util
        
        mobile_file = "手机版TCM系统.py"
        if not os.path.exists(mobile_file):
            st.error(f"❌ 手机版文件不存在: {mobile_file}")
            return
        
        # 动态导入并运行手机版
        spec = importlib.util.spec_from_file_location("mobile_tcm", mobile_file)
        mobile_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(mobile_module)
        
        # 运行手机版主函数
        mobile_module.main()
        
    except Exception as e:
        st.error(f"❌ 启动手机版失败: {e}")
        st.markdown("### 🔄 备用方案")
        if st.button("🚀 直接启动手机版"):
            subprocess.run([sys.executable, "手机版TCM系统.py"])

def launch_desktop_interface():
    """启动电脑版界面"""
    try:
        # 导入完整版系统
        import importlib.util
        
        desktop_file = "ultimate_final_tcm_system.py"
        if not os.path.exists(desktop_file):
            st.error(f"❌ 电脑版文件不存在: {desktop_file}")
            return
        
        # 显示启动信息
        st.success("🚀 正在启动完整版系统...")
        st.info("💡 完整版功能包括文档上传、高级搜索、系统管理等")
        
        # 提供手动启动选项
        if st.button("🖥️ 启动完整版系统"):
            subprocess.run([sys.executable, desktop_file])
        
        # 或者直接重定向
        st.markdown("""
        <script>
        setTimeout(function() {
            window.location.href = '/';
        }, 3000);
        </script>
        """, unsafe_allow_html=True)
        
    except Exception as e:
        st.error(f"❌ 启动电脑版失败: {e}")

def main():
    """主路由函数"""
    # 获取查询参数
    query_params = get_query_params()
    interface_type = query_params.get('interface', [None])[0]
    
    # 根据参数选择界面
    if interface_type == 'mobile':
        # 手机版界面
        st.markdown("### 📱 手机版加载中...")
        launch_mobile_interface()
        
    elif interface_type == 'desktop':
        # 电脑版界面
        st.markdown("### 💻 电脑版加载中...")
        launch_desktop_interface()
        
    else:
        # 显示设备选择界面
        show_device_selector()
        
        # 底部信息
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; color: #666; margin-top: 2rem;">
            <h4>🎯 使用建议</h4>
            <p><strong>手机用户</strong>：选择手机版，享受语音对话和简洁界面</p>
            <p><strong>电脑用户</strong>：选择电脑版，使用完整功能和文档管理</p>
            <p><strong>平板用户</strong>：建议选择电脑版，获得更好的显示效果</p>
            
            <div style="margin-top: 2rem; padding: 1rem; background: #f0f0f0; border-radius: 10px;">
                <h5>🔗 远程访问说明</h5>
                <p>通过Ngrok分享的链接，朋友可以直接访问此页面</p>
                <p>系统会自动检测设备类型，提供最适合的界面</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
