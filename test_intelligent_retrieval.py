#!/usr/bin/env python3
"""
测试智能检索器的准确性
"""

import sys
import json
from pathlib import Path
from intelligent_rag_retriever import IntelligentRAGRetriever

def test_retrieval_accuracy():
    """测试检索准确性"""
    print("🧪 测试智能RAG检索器...")
    
    # 初始化检索器
    retriever = IntelligentRAGRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 测试查询
    test_cases = [
        {
            'query': '肾虚脾虚怎么治疗',
            'expected_keywords': ['肾虚', '脾虚', '治疗', '补肾', '健脾'],
            'description': '肾脾双虚治疗查询'
        },
        {
            'query': '栀子甘草豉汤的功效',
            'expected_keywords': ['栀子', '甘草', '豉汤', '功效'],
            'description': '方剂功效查询'
        },
        {
            'query': '湿气重的症状有哪些',
            'expected_keywords': ['湿气', '症状', '湿邪'],
            'description': '症状查询'
        },
        {
            'query': '气血不足如何调理',
            'expected_keywords': ['气血', '不足', '调理', '补气', '养血'],
            'description': '调理方法查询'
        },
        {
            'query': '黄帝内经关于阴阳的理论',
            'expected_keywords': ['黄帝内经', '阴阳', '理论'],
            'description': '经典理论查询'
        }
    ]
    
    print(f"\n📊 开始测试 {len(test_cases)} 个查询...")
    
    total_score = 0
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['description']} ---")
        print(f"查询: {test_case['query']}")
        
        # 执行检索
        search_results = retriever.search(test_case['query'], top_k=5)
        
        if not search_results:
            print("❌ 未找到任何结果")
            results.append({
                'query': test_case['query'],
                'score': 0,
                'results_count': 0,
                'relevance': 'no_results'
            })
            continue
        
        print(f"✅ 找到 {len(search_results)} 个结果")
        
        # 分析结果质量
        relevance_score = 0
        keyword_matches = 0
        
        for j, result in enumerate(search_results):
            print(f"\n  结果 {j+1}:")
            print(f"    方法: {result.get('methods', result.get('method', 'unknown'))}")
            print(f"    分数: {result.get('combined_score', result.get('score', 0)):.3f}")
            print(f"    来源: {result.get('metadata', {}).get('source', 'unknown')}")
            print(f"    内容预览: {result['content'][:100]}...")
            
            # 检查关键词匹配
            content_lower = result['content'].lower()
            matched_keywords = [kw for kw in test_case['expected_keywords'] 
                              if kw.lower() in content_lower]
            
            if matched_keywords:
                keyword_matches += len(matched_keywords)
                print(f"    匹配关键词: {matched_keywords}")
            
            # 计算相关性分数
            if result.get('combined_score', result.get('score', 0)) > 0.5:
                relevance_score += 2
            elif result.get('combined_score', result.get('score', 0)) > 0.3:
                relevance_score += 1
        
        # 计算总分
        test_score = min(100, (relevance_score * 10) + (keyword_matches * 5))
        total_score += test_score
        
        print(f"\n  测试评分: {test_score}/100")
        print(f"  关键词匹配: {keyword_matches}/{len(test_case['expected_keywords'])}")
        
        results.append({
            'query': test_case['query'],
            'score': test_score,
            'results_count': len(search_results),
            'keyword_matches': keyword_matches,
            'expected_keywords': len(test_case['expected_keywords']),
            'relevance': 'good' if test_score >= 70 else 'fair' if test_score >= 40 else 'poor'
        })
    
    # 计算平均分
    average_score = total_score / len(test_cases)
    
    print(f"\n🎯 测试总结:")
    print(f"平均分数: {average_score:.1f}/100")
    print(f"总体评价: {'优秀' if average_score >= 80 else '良好' if average_score >= 60 else '需要改进'}")
    
    # 保存详细结果
    detailed_results = {
        'average_score': average_score,
        'total_tests': len(test_cases),
        'test_results': results,
        'retriever_stats': retriever.get_retrieval_stats()
    }
    
    with open('retrieval_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, ensure_ascii=False, indent=2)
    
    print(f"详细结果已保存到: retrieval_test_results.json")
    
    return average_score

def compare_with_old_system():
    """与旧系统对比"""
    print("\n🔄 与旧系统对比...")
    
    # 这里可以添加与旧系统的对比逻辑
    print("旧系统问题:")
    print("- 相似度阈值过高(0.65)")
    print("- 文档块过小(200字符)")
    print("- 单一检索方法")
    print("- MCP结果固定化")
    
    print("\n新系统改进:")
    print("- 降低相似度阈值(0.35)")
    print("- 增大文档块(500字符)")
    print("- 多种检索方法融合")
    print("- 智能MCP服务")
    print("- 语义重排序")

def main():
    """主函数"""
    print("🚀 智能检索系统测试")
    
    try:
        # 测试检索准确性
        score = test_retrieval_accuracy()
        
        # 与旧系统对比
        compare_with_old_system()
        
        print(f"\n✨ 测试完成！系统评分: {score:.1f}/100")
        
        if score >= 80:
            print("🎉 检索系统表现优秀！")
        elif score >= 60:
            print("👍 检索系统表现良好，还有优化空间")
        else:
            print("⚠️ 检索系统需要进一步优化")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
