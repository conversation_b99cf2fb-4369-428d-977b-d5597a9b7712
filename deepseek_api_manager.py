#!/usr/bin/env python3
"""
DeepSeek模型API管理器
通过LM Studio API调用本地DeepSeek模型
支持Docker导出和跨硬件移植
"""

import requests
import json
import subprocess
import time
import os
import psutil
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class DeepSeekAPIManager:
    """DeepSeek API管理器"""
    
    def __init__(self):
        self.model_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
        self.api_base_url = "http://localhost:1234/v1"
        self.lmstudio_process = None
        self.model_loaded = False
        
    def check_model_file(self) -> bool:
        """检查模型文件是否存在"""
        if os.path.exists(self.model_path):
            model_size = os.path.getsize(self.model_path) / (1024 * 1024 * 1024)
            print(f"✅ 发现DeepSeek模型: {self.model_path}")
            print(f"📊 模型大小: {model_size:.2f} GB")
            return True
        else:
            print(f"❌ 模型文件不存在: {self.model_path}")
            return False
    
    def check_lmstudio_running(self) -> bool:
        """检查LM Studio是否运行"""
        try:
            # 检查进程
            for proc in psutil.process_iter(['pid', 'name']):
                if 'lmstudio' in proc.info['name'].lower():
                    print(f"✅ 发现LM Studio进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    return True
            
            # 检查API端口
            response = requests.get(f"{self.api_base_url}/models", timeout=5)
            if response.status_code == 200:
                print("✅ LM Studio API服务正在运行")
                return True
                
        except Exception as e:
            print(f"⚠️ LM Studio检查失败: {e}")
        
        return False
    
    def start_lmstudio_server(self) -> bool:
        """启动LM Studio服务器"""
        print("🚀 启动LM Studio服务器...")
        
        # 查找LM Studio可执行文件
        lmstudio_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\LM Studio\LM Studio.exe",
            r"C:\Program Files\LM Studio\LM Studio.exe",
            r"C:\Program Files (x86)\LM Studio\LM Studio.exe"
        ]
        
        lmstudio_exe = None
        for path in lmstudio_paths:
            if os.path.exists(path):
                lmstudio_exe = path
                break
        
        if not lmstudio_exe:
            print("❌ 未找到LM Studio可执行文件")
            print("💡 请手动启动LM Studio并加载DeepSeek模型")
            return False
        
        try:
            # 启动LM Studio
            self.lmstudio_process = subprocess.Popen([
                lmstudio_exe, "--server", "--port", "1234"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print("⏳ 等待LM Studio启动...")
            time.sleep(10)
            
            # 检查是否启动成功
            if self.check_lmstudio_running():
                return True
            else:
                print("❌ LM Studio启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动LM Studio失败: {e}")
            return False
    
    def load_model_via_api(self) -> bool:
        """通过API加载模型"""
        print("📥 通过API加载DeepSeek模型...")
        
        try:
            # 获取可用模型列表
            response = requests.get(f"{self.api_base_url}/models", timeout=10)
            if response.status_code != 200:
                print("❌ 无法获取模型列表")
                return False
            
            models = response.json()
            print(f"📋 可用模型: {len(models.get('data', []))} 个")
            
            # 查找DeepSeek模型
            deepseek_model = None
            for model in models.get('data', []):
                if 'deepseek' in model.get('id', '').lower():
                    deepseek_model = model['id']
                    break
            
            if deepseek_model:
                print(f"✅ 发现DeepSeek模型: {deepseek_model}")
                self.model_loaded = True
                return True
            else:
                print("⚠️ 未在LM Studio中找到DeepSeek模型")
                print("💡 请在LM Studio中手动加载DeepSeek模型")
                return False
                
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
            return False
    
    def generate_response(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7) -> str:
        """生成回答"""
        if not self.model_loaded:
            return "DeepSeek模型未加载"
        
        try:
            payload = {
                "model": "deepseek-r1",  # 或者使用实际的模型ID
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }
            
            response = requests.post(
                f"{self.api_base_url}/chat/completions",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"API调用失败: {response.status_code}"
                
        except Exception as e:
            return f"生成回答时出错: {e}"
    
    def initialize(self) -> bool:
        """初始化DeepSeek API"""
        print("🧠 初始化DeepSeek API管理器...")
        
        # 1. 检查模型文件
        if not self.check_model_file():
            return False
        
        # 2. 检查LM Studio是否运行
        if not self.check_lmstudio_running():
            # 尝试启动LM Studio
            if not self.start_lmstudio_server():
                print("💡 请手动启动LM Studio并加载DeepSeek模型")
                print("   1. 打开LM Studio")
                print("   2. 在Local Server选项卡中启动服务器")
                print("   3. 加载DeepSeek-R1-0528-Qwen3-8B-Q4_K_M模型")
                return False
        
        # 3. 加载模型
        if not self.load_model_via_api():
            return False
        
        print("✅ DeepSeek API初始化成功")
        return True
    
    def test_generation(self) -> bool:
        """测试生成功能"""
        print("🧪 测试DeepSeek生成功能...")
        
        test_prompt = "你好，请简单介绍一下中医的基本理论。"
        response = self.generate_response(test_prompt, max_tokens=100)
        
        if "出错" in response or "失败" in response:
            print(f"❌ 生成测试失败: {response}")
            return False
        else:
            print(f"✅ 生成测试成功")
            print(f"📝 测试回答: {response[:100]}...")
            return True

class DockerDeploymentManager:
    """Docker部署管理器 - 支持DeepSeek API"""
    
    def __init__(self):
        self.model_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    
    def create_deployment_dockerfile(self):
        """创建部署用的Dockerfile"""
        dockerfile_content = '''# 终极中医RAG系统 - 支持DeepSeek API
FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc g++ cmake build-essential \\
    portaudio19-dev python3-pyaudio \\
    espeak espeak-data libespeak1 libespeak-dev \\
    curl wget git \\
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p /app/working_vector_db \\
    /app/documents \\
    /app/conversations \\
    /app/logs \\
    /app/models

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEEPSEEK_API_BASE=http://host.docker.internal:1234/v1

# 暴露端口
EXPOSE 8507

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost:8507/_stcore/health || exit 1

# 启动命令
CMD ["python", "-m", "streamlit", "run", "working_tcm_system_api.py", \\
     "--server.port=8507", \\
     "--server.address=0.0.0.0", \\
     "--theme.base=light", \\
     "--server.headless=true"]
'''
        
        with open("Dockerfile.api", "w", encoding="utf-8") as f:
            f.write(dockerfile_content)
        
        print("✅ 创建了支持API的Dockerfile")
    
    def create_docker_compose_with_api(self):
        """创建支持API的docker-compose文件"""
        compose_content = '''version: '3.8'

services:
  tcm-rag-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: tcm-rag-api
    ports:
      - "8507:8507"
    volumes:
      - ./working_vector_db:/app/working_vector_db
      - ./documents:/app/documents
      - ./conversations:/app/conversations
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
      - DEEPSEEK_API_BASE=http://host.docker.internal:1234/v1
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    depends_on:
      - deepseek-api
    
  # 可选：如果需要在容器中运行LM Studio
  deepseek-api:
    image: nginx:alpine
    container_name: deepseek-api-proxy
    ports:
      - "1234:1234"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    restart: unless-stopped
'''
        
        with open("docker-compose.api.yml", "w", encoding="utf-8") as f:
            f.write(compose_content)
        
        print("✅ 创建了支持API的docker-compose文件")
    
    def create_deployment_guide(self):
        """创建部署指南"""
        guide_content = '''# DeepSeek API版本部署指南

## 本机部署

### 1. 启动LM Studio
```bash
# 手动启动LM Studio
# 1. 打开LM Studio
# 2. 在Local Server选项卡中启动服务器 (端口1234)
# 3. 加载DeepSeek-R1-0528-Qwen3-8B-Q4_K_M模型
```

### 2. 启动RAG系统
```bash
python working_tcm_system_api.py
```

## Docker部署

### 1. 构建镜像
```bash
docker build -f Dockerfile.api -t tcm-rag-api .
```

### 2. 运行容器
```bash
# 确保主机上LM Studio在端口1234运行
docker run -d \\
  --name tcm-rag-api \\
  -p 8507:8507 \\
  --add-host host.docker.internal:host-gateway \\
  -e DEEPSEEK_API_BASE=http://host.docker.internal:1234/v1 \\
  tcm-rag-api
```

## 跨硬件移植

### 方案A：轻量化移植（推荐）
1. 导出Docker镜像（不包含模型文件）
2. 在目标机器上：
   - 导入Docker镜像
   - 复制模型文件到指定路径
   - 启动LM Studio并加载模型
   - 运行容器

### 方案B：完整移植
1. 将模型文件打包到Docker镜像中
2. 导出完整镜像（约5GB）
3. 在目标机器上导入并运行

## 优势
- ✅ 不需要编译llama-cpp-python
- ✅ 支持GPU加速（通过LM Studio）
- ✅ 轻量化Docker镜像
- ✅ 易于跨硬件移植
- ✅ 可以利用LM Studio的优化
'''
        
        with open("deployment_guide_api.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        
        print("✅ 创建了API版本部署指南")

def main():
    """主函数"""
    print("=" * 80)
    print("🧠 DeepSeek API管理器测试")
    print("=" * 80)
    
    # 初始化API管理器
    api_manager = DeepSeekAPIManager()
    
    if api_manager.initialize():
        # 测试生成功能
        if api_manager.test_generation():
            print("\n🎉 DeepSeek API完全可用！")
            
            # 创建Docker部署文件
            docker_manager = DockerDeploymentManager()
            docker_manager.create_deployment_dockerfile()
            docker_manager.create_docker_compose_with_api()
            docker_manager.create_deployment_guide()
            
            print("\n📦 Docker部署文件已创建")
            print("💡 现在可以使用API版本进行部署")
        else:
            print("\n⚠️ DeepSeek生成功能测试失败")
    else:
        print("\n❌ DeepSeek API初始化失败")
        print("💡 请确保：")
        print("   1. LM Studio已安装并运行")
        print("   2. DeepSeek模型已加载")
        print("   3. API服务器在端口1234运行")

if __name__ == "__main__":
    main()
