"""
文档处理器 - 负责PDF解析、文本分块和向量化
"""
import PyPDF2
import numpy as np
import faiss
import pickle
import json
from pathlib import Path
from typing import List, Dict, Tuple
import config
from models.model_manager import model_manager

class DocumentProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF提取文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"PDF解析错误: {e}")
            return ""
    
    def split_text_into_chunks(self, text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """将文本分割成块"""
        if chunk_size is None:
            chunk_size = config.CHUNK_SIZE
        if overlap is None:
            overlap = config.CHUNK_OVERLAP
            
        chunks = []
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = start + chunk_size
            if end > text_length:
                end = text_length
            
            chunk = text[start:end]
            
            # 尝试在句号处分割，避免截断句子
            if end < text_length and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:  # 确保块不会太小
                    end = start + last_period + 1
                    chunk = text[start:end]
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= text_length:
                break
                
        return [chunk for chunk in chunks if len(chunk.strip()) > 10]  # 过滤太短的块
    
    def create_vector_index(self, texts: List[str]) -> bool:
        """创建向量索引"""
        try:
            print("正在创建向量索引...")
            embeddings = []
            
            for i, text in enumerate(texts):
                if i % 10 == 0:
                    print(f"处理进度: {i+1}/{len(texts)}")
                
                embedding = model_manager.get_embedding(text)
                embeddings.append(embedding)
            
            embeddings = np.array(embeddings).astype('float32')
            
            # 创建FAISS索引
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
            
            # 归一化向量以使用余弦相似度
            faiss.normalize_L2(embeddings)
            self.vector_index.add(embeddings)
            
            print(f"向量索引创建完成，包含 {len(texts)} 个文档块")
            return True
            
        except Exception as e:
            print(f"创建向量索引失败: {e}")
            return False
    
    def save_index(self, index_path: str = None):
        """保存向量索引和元数据"""
        if index_path is None:
            index_path = config.VECTOR_DB_PATH
        
        try:
            index_path = Path(index_path)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            # 保存文档块和元数据
            with open(index_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            print("索引保存完成")
            return True
            
        except Exception as e:
            print(f"保存索引失败: {e}")
            return False
    
    def load_index(self, index_path: str = None):
        """加载向量索引和元数据"""
        if index_path is None:
            index_path = config.VECTOR_DB_PATH
        
        try:
            index_path = Path(index_path)
            
            # 加载FAISS索引
            self.vector_index = faiss.read_index(str(index_path / "vector_index.faiss"))
            
            # 加载文档块
            with open(index_path / "chunks.pkl", 'rb') as f:
                self.document_chunks = pickle.load(f)
            
            # 加载元数据
            with open(index_path / "metadata.json", 'r', encoding='utf-8') as f:
                self.chunk_metadata = json.load(f)
            
            print(f"索引加载完成，包含 {len(self.document_chunks)} 个文档块")
            return True
            
        except Exception as e:
            print(f"加载索引失败: {e}")
            return False
    
    def process_documents(self, pdf_files: List[str]) -> bool:
        """处理多个PDF文档"""
        all_chunks = []
        all_metadata = []
        
        for pdf_file in pdf_files:
            print(f"处理文档: {pdf_file}")
            
            # 提取文本
            text = self.extract_text_from_pdf(pdf_file)
            if not text:
                print(f"无法从 {pdf_file} 提取文本")
                continue
            
            # 分割文本
            chunks = self.split_text_into_chunks(text)
            
            # 创建元数据
            for i, chunk in enumerate(chunks):
                metadata = {
                    "source": pdf_file,
                    "chunk_id": len(all_chunks) + i,
                    "chunk_index": i,
                    "content": chunk
                }
                all_metadata.append(metadata)
            
            all_chunks.extend(chunks)
            print(f"从 {pdf_file} 提取了 {len(chunks)} 个文本块")
        
        if not all_chunks:
            print("没有提取到任何文本块")
            return False
        
        # 保存到实例变量
        self.document_chunks = all_chunks
        self.chunk_metadata = all_metadata
        
        # 创建向量索引
        if self.create_vector_index(all_chunks):
            return self.save_index()
        
        return False
    
    def search_similar_chunks(self, query: str, top_k: int = None) -> List[Dict]:
        """搜索相似的文档块"""
        if top_k is None:
            top_k = config.TOP_K_RETRIEVAL
        
        if self.vector_index is None:
            raise ValueError("向量索引未加载")
        
        try:
            # 获取查询向量
            query_embedding = model_manager.get_embedding(query)
            query_vector = np.array([query_embedding]).astype('float32')
            faiss.normalize_L2(query_vector)
            
            # 搜索相似向量
            scores, indices = self.vector_index.search(query_vector, top_k)
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.chunk_metadata):
                    result = self.chunk_metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []

# 全局文档处理器实例
doc_processor = DocumentProcessor()
