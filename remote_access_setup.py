#!/usr/bin/env python3
"""
异地访问设置工具
支持家人朋友从任何地方访问您的中医智能助手
"""
import subprocess
import sys
import socket
import json
import time
from pathlib import Path
import requests

def check_ngrok_installed():
    """检查ngrok是否已安装"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_ngrok():
    """安装ngrok"""
    print("🔧 正在安装ngrok...")
    
    # Windows安装指令
    install_commands = [
        "winget install ngrok",
        "choco install ngrok",
        "scoop install ngrok"
    ]
    
    for cmd in install_commands:
        try:
            print(f"尝试: {cmd}")
            result = subprocess.run(cmd.split(), capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ ngrok安装成功！")
                return True
        except:
            continue
    
    print("❌ 自动安装失败")
    print("📋 请手动安装ngrok：")
    print("   1. 访问 https://ngrok.com/download")
    print("   2. 下载Windows版本")
    print("   3. 解压到系统PATH目录")
    print("   4. 重新运行此脚本")
    return False

def setup_ngrok_auth():
    """设置ngrok认证"""
    print("🔑 设置ngrok认证...")
    print("📋 请按以下步骤操作：")
    print("   1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken")
    print("   2. 注册/登录ngrok账户（免费）")
    print("   3. 复制您的authtoken")
    
    authtoken = input("请粘贴您的authtoken: ").strip()
    
    if authtoken:
        try:
            result = subprocess.run(['ngrok', 'config', 'add-authtoken', authtoken], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ authtoken设置成功！")
                return True
            else:
                print(f"❌ 设置失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 设置失败: {e}")
            return False
    else:
        print("❌ 未输入authtoken")
        return False

def start_ngrok_tunnel(port=8517):
    """启动ngrok隧道"""
    try:
        print(f"🚀 正在创建公网隧道 (端口 {port})...")
        
        # 启动ngrok
        process = subprocess.Popen(
            ['ngrok', 'http', str(port), '--log=stdout'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(3)
        
        # 获取公网地址
        try:
            response = requests.get('http://localhost:4040/api/tunnels')
            tunnels = response.json()
            
            if tunnels['tunnels']:
                public_url = tunnels['tunnels'][0]['public_url']
                print(f"✅ 隧道创建成功！")
                print(f"🌐 公网访问地址: {public_url}")
                return public_url, process
            else:
                print("❌ 未找到活跃隧道")
                return None, process
                
        except Exception as e:
            print(f"❌ 获取隧道信息失败: {e}")
            return None, process
            
    except Exception as e:
        print(f"❌ 启动隧道失败: {e}")
        return None, None

def create_share_info(public_url, local_ip):
    """创建分享信息"""
    share_info = f"""
🏥 家庭中医智能助手 - 访问指南

👋 您好！我为您分享了一个中医智能助手，可以查询中医方剂、症状等信息。

📱 访问方式：

🌐 【异地访问】（推荐）
   {public_url}
   
🏠 【局域网访问】（同一WiFi下）
   http://{local_ip}:8517

💡 使用说明：
   - 在浏览器中打开上述任一地址
   - 支持手机、电脑、平板访问
   - 可以询问中医相关问题
   - 支持查询方剂、症状、功效等

🔍 示例问题：
   - "栀子甘草豉汤方的组成是什么？"
   - "防己黄芪汤的功效和作用"
   - "甘草的功效有哪些？"

⚠️ 重要提醒：
   - 本系统仅供中医学习参考
   - 不能替代专业医生诊断
   - 如有疾病请及时就医

🔒 隐私安全：
   - 所有数据在本地处理
   - 不会收集个人信息
   - 仅用于中医知识查询

祝您使用愉快！ 🌿
"""
    
    return share_info

def save_access_info(public_url, local_ip):
    """保存访问信息"""
    access_info = {
        "public_url": public_url,
        "local_url": f"http://{local_ip}:8517",
        "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
        "status": "active"
    }
    
    with open("remote_access_info.json", "w", encoding="utf-8") as f:
        json.dump(access_info, f, indent=2, ensure_ascii=False)
    
    # 保存分享文本
    share_text = create_share_info(public_url, local_ip)
    with open("分享给家人朋友.txt", "w", encoding="utf-8") as f:
        f.write(share_text)
    
    print("💾 访问信息已保存到:")
    print("   - remote_access_info.json")
    print("   - 分享给家人朋友.txt")

def main():
    """主函数"""
    print("🌐 家庭中医智能助手 - 异地访问设置")
    print("=" * 50)
    
    # 获取本地IP
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
    except:
        local_ip = "localhost"
    
    print(f"🖥️ 本机IP: {local_ip}")
    print()
    
    # 检查ngrok
    if not check_ngrok_installed():
        print("❌ 未检测到ngrok")
        if input("是否自动安装ngrok？(y/n): ").lower() == 'y':
            if not install_ngrok():
                return
        else:
            print("💡 请手动安装ngrok后重新运行")
            return
    
    print("✅ ngrok已安装")
    
    # 设置认证
    if not Path.home().joinpath('.ngrok2/ngrok.yml').exists():
        print("🔑 需要设置ngrok认证")
        if not setup_ngrok_auth():
            return
    
    print("✅ ngrok认证已配置")
    print()
    
    # 启动隧道
    public_url, process = start_ngrok_tunnel()
    
    if public_url:
        print()
        print("🎉 异地访问设置成功！")
        print("=" * 50)
        print(f"🌐 公网地址: {public_url}")
        print(f"🏠 局域网地址: http://{local_ip}:8517")
        print()
        
        # 保存信息
        save_access_info(public_url, local_ip)
        
        # 显示分享信息
        share_text = create_share_info(public_url, local_ip)
        print("📋 分享给家人朋友的信息：")
        print("-" * 30)
        print(share_text)
        print("-" * 30)
        print()
        
        print("💡 使用说明：")
        print("   - 隧道将保持运行直到您关闭此程序")
        print("   - 家人朋友可以从任何地方访问公网地址")
        print("   - 按 Ctrl+C 停止隧道服务")
        print("   - 分享信息已保存到 '分享给家人朋友.txt'")
        print()
        
        try:
            print("🔄 隧道运行中... (按 Ctrl+C 停止)")
            process.wait()
        except KeyboardInterrupt:
            print("\n\n👋 正在停止隧道...")
            process.terminate()
            process.wait()
            print("✅ 隧道已停止")
    
    else:
        print("❌ 隧道创建失败")
        print("💡 请检查网络连接或ngrok配置")

if __name__ == "__main__":
    main()
