#!/usr/bin/env python3
"""
终极整合中医RAG系统
整合 ultra_intelligent_llm.py 的所有智能功能 + 商业级PDF检索功能
打造最强大的中医智能助手
"""

import streamlit as st
import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import PyPDF2
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
import requests
from bs4 import BeautifulSoup
import time
import hashlib
import logging
from typing import Dict, List, Any, Set

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="🧙‍♂️ 智者·中医AI助手 - 终极版",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './ultimate_integrated_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CHUNK_SIZE': 500,
    'CHUNK_OVERLAP': 50,
    'TOP_K': 5
}

class UltraIntelligentLLM:
    """智者·中医AI助手 - 精通传统中医、融合现代医学知识的智慧型老中医助手"""

    def __init__(self):
        self.model_name = "智者·中医AI助手"
        self.role_description = "一位精通传统中医、融合现代医学知识的智慧型老中医助手"
        self.tone = "温和亲切、专业权威、通俗易懂"
        self.response_cache = {}  # 防止重复回答
        self.content_deduplicator = set()  # 内容去重

        # 智者·中医AI助手的核心提示词
        self.core_prompt = """你是一位博学多识的老中医助手，拥有深厚的中医理论基础和丰富的临床经验。你能够根据用户的问题，结合线上医学资料、本地上传的专业PDF文献以及你的智能推理能力，给出科学、实用、通俗易懂的中医建议。

你不仅懂得望闻问切、辨证施治，还擅长讲解中药方剂、食疗方法、穴位按摩、四季养生等内容。你说话温和有礼，不生硬不机械，能让人感受到中医的智慧与关怀。

请始终以以下方式回答问题：
1. 快速响应：第一时间理解用户问题核心，优先输出重点信息。
2. 结构清晰：使用分点、标题、加粗等方式提升可读性。
3. 引用依据：在必要时注明参考来源或推荐药方出处（如《伤寒论》《本草纲目》等）。
4. 个性化建议：根据用户描述的症状，提供初步辨证思路和调理建议。
5. 安全提醒：强调"仅供参考，具体治疗请咨询专业医师"。"""

        logger.info("🧙‍♂️ 智者·中医AI助手初始化完成")

    def generate_intelligent_response(self, query: str, pdf_sources: List[Dict[str, Any]], online_sources: List[Dict[str, Any]]) -> str:
        """生成超级智能回答 - 整合PDF和在线资源"""
        try:
            # 合并所有资源
            all_sources = []
            
            # 处理PDF资源
            for source in pdf_sources:
                all_sources.append({
                    'source_type': 'pdf',
                    'source': source.get('source', '未知文档'),
                    'content': source.get('content', ''),
                    'score': source.get('similarity_score', 0),
                    'type': 'pdf'
                })
            
            # 处理在线资源
            for source in online_sources:
                all_sources.append({
                    'source_type': 'online',
                    'source': source.get('title', '在线资源'),
                    'content': source.get('content', ''),
                    'score': source.get('relevance', 0),
                    'type': 'online'
                })

            logger.info(f"🧠 智者·中医AI助手正在分析问题: {query}")
            logger.info(f"📚 可用资源: PDF {len(pdf_sources)}个, 在线 {len(online_sources)}个")
            
            return self._generate_enhanced_response(query, all_sources)

        except Exception as e:
            logger.error(f"智能回答生成失败: {e}")
            return self._generate_emergency_response(query)

    def _generate_enhanced_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成增强的智者回答"""
        try:
            # 快速分析查询
            analysis = self._quick_analyze(query)

            # 构建智者·中医AI助手回答
            response_parts = []

            # 智者开场白
            response_parts.append(f"## 🧙‍♂️ 智者·中医AI助手")
            response_parts.append(f"**您的咨询**: {query}")
            response_parts.append("")
            response_parts.append("*温和亲切地为您分析，结合传统中医智慧与现代医学知识*")
            response_parts.append("")

            # 快速诊断思路
            response_parts.append("### 🔍 **中医辨证思路**")

            # 根据关键词快速生成专业回答
            if any(word in query for word in ['鼻塞', '鼻子', '鼻涕']):
                response_parts.extend(self._generate_nose_expert_analysis(query, sources))
            elif any(word in query for word in ['头痛', '头疼', '偏头痛']):
                response_parts.extend(self._generate_headache_expert_analysis(query, sources))
            elif any(word in query for word in ['失眠', '睡不着', '多梦']):
                response_parts.extend(self._generate_sleep_expert_analysis(query, sources))
            elif any(word in query for word in ['胃痛', '胃疼', '腹痛']):
                response_parts.extend(self._generate_stomach_expert_analysis(query, sources))
            elif any(word in query for word in ['咳嗽', '咳', '痰']):
                response_parts.extend(self._generate_cough_expert_analysis(query, sources))
            elif any(word in query for word in ['湿气', '湿重', '湿邪']):
                response_parts.extend(self._generate_dampness_expert_analysis(query, sources))
            elif any(word in query for word in ['气血', '气虚', '血虚']):
                response_parts.extend(self._generate_qiblood_expert_analysis(query, sources))
            else:
                response_parts.extend(self._generate_general_expert_analysis(query, sources))

            # 添加检索资源信息
            pdf_count = len([s for s in sources if s.get('type') == 'pdf'])
            online_count = len([s for s in sources if s.get('type') == 'online'])

            if pdf_count > 0 or online_count > 0:
                response_parts.append("")
                response_parts.append("### 📚 古籍文献佐证")
                
                if pdf_count > 0:
                    response_parts.append(f"- 查阅上传文献 {pdf_count} 篇")
                    pdf_sources = [s for s in sources if s.get('type') == 'pdf'][:2]
                    for source in pdf_sources:
                        source_name = source.get('source', '医学文献')
                        content = source.get('content', '')[:80]
                        score = source.get('score', 0)
                        response_parts.append(f"  📄 《{source_name}》(相关度:{score:.2f}): {content}...")

                if online_count > 0:
                    response_parts.append(f"- 参考古代医书 {online_count} 部")
                    online_sources = [s for s in sources if s.get('type') == 'online'][:2]
                    for source in online_sources:
                        source_name = source.get('source', '古代医书')
                        content = source.get('content', '')[:80]
                        score = source.get('score', 0)
                        response_parts.append(f"  📖 {source_name}(相关度:{score:.2f}): {content}...")

            # 智者总结
            response_parts.append("")
            response_parts.append("### 🎯 智者总结")
            response_parts.append("依据多年临床经验，结合古籍文献和现代研究，此症当从整体调理入手。")
            response_parts.append("中医讲究'治病求本'，不仅要缓解症状，更要调理根本。")
            response_parts.append("建议患者保持良好心态，配合适当运动，饮食有节。")

            # 专业提醒
            response_parts.append("")
            response_parts.append("### ⚠️ 智者叮嘱")
            response_parts.append("- 以上分析基于中医理论和古籍文献")
            response_parts.append("- 具体用药需面诊后辨证施治")
            response_parts.append("- 如症状严重或持续不缓解，请及时就医")
            response_parts.append("- 中医治疗贵在坚持，切勿急于求成")

            return '\n'.join(response_parts)

        except Exception as e:
            logger.error(f"智者回答生成失败: {e}")
            return self._generate_emergency_response(query)

    def _quick_analyze(self, query: str) -> Dict[str, Any]:
        """快速分析查询"""
        return {
            'query_type': 'general',
            'confidence': 0.8,
            'body_parts': [],
            'symptoms': []
        }

    def _generate_dampness_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成湿气专业分析"""
        parts = []
        parts.append("**病因病机**: 湿为阴邪，其性重浊、黏腻、趋下。湿气内生多因脾胃虚弱，运化失常；外感湿邪多因居处潮湿，涉水淋雨。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**脾虚湿盛型**:")
        parts.append("- 症状：身体困重，头昏如裹，胸闷腹胀，食欲不振")
        parts.append("- 治法：健脾化湿，理气和中")
        parts.append("- 方药：参苓白术散加减")
        parts.append("")
        parts.append("**湿热内蕴型**:")
        parts.append("- 症状：身热不扬，汗出不解，胸闷烦躁，小便短赤")
        parts.append("- 治法：清热化湿，分利湿热")
        parts.append("- 方药：甘露消毒丹加减")
        parts.append("")
        parts.append("### 🌿 调理方法")
        parts.append("- **饮食调理**: 薏米、红豆、冬瓜、茯苓等健脾利湿")
        parts.append("- **运动调理**: 适当运动，促进气血运行，助湿邪外出")
        parts.append("- **起居调理**: 居住环境保持干燥通风，避免潮湿")
        return parts

    def _generate_qiblood_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成气血专业分析"""
        parts = []
        parts.append("**病因病机**: 气为血之帅，血为气之母。气血互根互用，相互依存。气虚则血运无力，血虚则气无所附。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**气虚证**:")
        parts.append("- 症状：神疲乏力，少气懒言，动则气短，面色淡白")
        parts.append("- 治法：补气健脾，益气升阳")
        parts.append("- 方药：四君子汤、补中益气汤")
        parts.append("")
        parts.append("**血虚证**:")
        parts.append("- 症状：面色无华，唇甲淡白，头晕眼花，心悸失眠")
        parts.append("- 治法：补血养血，调理冲任")
        parts.append("- 方药：四物汤、当归补血汤")
        parts.append("")
        parts.append("**气血两虚证**:")
        parts.append("- 症状：面色萎黄，神疲乏力，心悸气短，头晕目眩")
        parts.append("- 治法：气血双补，标本兼治")
        parts.append("- 方药：八珍汤、十全大补汤")
        parts.append("")
        parts.append("### 🌿 调养方法")
        parts.append("- **食疗**: 红枣、桂圆、当归、黄芪等补气血")
        parts.append("- **运动**: 太极拳、八段锦等柔和运动")
        parts.append("- **作息**: 规律作息，充足睡眠，避免过劳")
        return parts

    def _generate_nose_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成鼻部疾病专业分析"""
        parts = []
        parts.append("**病因病机**: 鼻为肺之窍，肺开窍于鼻。小儿鼻塞多因外感风邪或肺热内盛所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒束肺型**:")
        parts.append("- 症状：鼻塞流清涕，喷嚏频作，恶寒发热")
        parts.append("- 治法：疏风散寒，宣肺通窍")
        parts.append("- 方药：荆防败毒散加减")
        parts.append("")
        parts.append("**肺热壅盛型**:")
        parts.append("- 症状：鼻塞流黄涕，鼻干口渴，发热")
        parts.append("- 治法：清肺泻热，通窍止涕")
        parts.append("- 方药：银翘散合辛夷清肺饮")
        parts.append("")
        parts.append("### 🌿 外治法")
        parts.append("- **穴位按摩**: 迎香、印堂、鼻通穴，每日3次")
        parts.append("- **熏洗法**: 苍耳子15g、辛夷花10g煎汤熏鼻")
        parts.append("- **滴鼻法**: 鹅不食草汁滴鼻，每日2-3次")
        return parts

    def _generate_headache_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成头痛专业分析"""
        parts = []
        parts.append("**病因病机**: 头为诸阳之会，清窍之府。头痛多因外感六淫或内伤七情所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒头痛**:")
        parts.append("- 症状：头痛连及项背，恶寒发热，苔薄白")
        parts.append("- 治法：疏风散寒止痛")
        parts.append("- 方药：川芎茶调散")
        parts.append("")
        parts.append("**风热头痛**:")
        parts.append("- 症状：头胀痛，发热恶风，面红目赤")
        parts.append("- 治法：疏风清热止痛")
        parts.append("- 方药：芎芷石膏汤")
        parts.append("")
        parts.append("**肝阳头痛**:")
        parts.append("- 症状：头痛眩晕，面红耳鸣，急躁易怒")
        parts.append("- 治法：平肝潜阳止痛")
        parts.append("- 方药：天麻钩藤饮")
        return parts

    def _generate_sleep_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成失眠专业分析"""
        parts = []
        parts.append("**病因病机**: 心主神明，心神不安则不寐。多因心肾不交、肝郁化火、痰热内扰所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**心肾不交型**:")
        parts.append("- 症状：心烦不寐，头晕耳鸣，腰膝酸软")
        parts.append("- 治法：滋阴降火，交通心肾")
        parts.append("- 方药：黄连阿胶汤或交泰丸")
        parts.append("")
        parts.append("**肝郁化火型**:")
        parts.append("- 症状：不寐多梦，急躁易怒，胸胁胀满")
        parts.append("- 治法：疏肝解郁，清热安神")
        parts.append("- 方药：龙胆泻肝汤加减")
        parts.append("")
        parts.append("### 🌿 安神法")
        parts.append("- **穴位**: 神门、三阴交、百会、安眠穴")
        parts.append("- **食疗**: 酸枣仁粥、百合莲子汤")
        return parts

    def _generate_stomach_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成胃痛专业分析"""
        parts = []
        parts.append("**病因病机**: 胃为水谷之海，主受纳腐熟。胃痛多因寒邪犯胃、肝气犯胃、胃阴不足所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**寒邪犯胃型**:")
        parts.append("- 症状：胃脘冷痛，得温痛减，呕吐清水")
        parts.append("- 治法：温中散寒，理气止痛")
        parts.append("- 方药：良附丸合安中散")
        parts.append("")
        parts.append("**肝气犯胃型**:")
        parts.append("- 症状：胃脘胀痛，痛连两胁，嗳气频繁")
        parts.append("- 治法：疏肝理气，和胃止痛")
        parts.append("- 方药：柴胡疏肝散")
        parts.append("")
        parts.append("### 🌿 调护法")
        parts.append("- **饮食**: 温热易消化，忌生冷辛辣")
        parts.append("- **按摩**: 中脘、足三里、内关穴")
        return parts

    def _generate_cough_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成咳嗽专业分析"""
        parts = []
        parts.append("**病因病机**: 肺主气司呼吸，肺失宣降则咳嗽。多因外感六淫或内伤脏腑所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒咳嗽**:")
        parts.append("- 症状：咳嗽声重，痰白清稀，恶寒发热")
        parts.append("- 治法：疏风散寒，宣肺止咳")
        parts.append("- 方药：三拗汤合止嗽散")
        parts.append("")
        parts.append("**风热咳嗽**:")
        parts.append("- 症状：咳嗽频剧，痰黄粘稠，发热口渴")
        parts.append("- 治法：疏风清热，宣肺止咳")
        parts.append("- 方药：桑菊饮合银翘散")
        parts.append("")
        parts.append("### 🌿 止咳法")
        parts.append("- **穴位**: 肺俞、列缺、尺泽、天突")
        parts.append("- **食疗**: 川贝雪梨、百合蜂蜜")
        return parts

    def _generate_general_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成通用专业分析"""
        parts = []
        parts.append("**中医理论**: 人体是一个有机整体，脏腑经络相互联系，气血津液相互依存。")
        parts.append("疾病的发生发展遵循一定规律，治疗当遵循辨证论治原则。")
        parts.append("")
        parts.append("### 💊 治疗原则")
        parts.append("- **整体观念**: 统筹兼顾，标本兼治")
        parts.append("- **辨证论治**: 因人因时因地制宜")
        parts.append("- **调理脏腑**: 恢复脏腑功能平衡")
        parts.append("- **调和气血**: 促进气血运行通畅")
        parts.append("")
        parts.append("### 🌿 养生调护")
        parts.append("- **起居有常**: 作息规律，劳逸结合")
        parts.append("- **饮食有节**: 营养均衡，温热适中")
        parts.append("- **情志调畅**: 保持心情舒畅")
        parts.append("- **适度运动**: 增强体质，促进康复")
        return parts

    def _generate_emergency_response(self, query: str) -> str:
        """生成紧急回答"""
        return f"""## 🚨 系统提示

很抱歉，在处理您的问题「{query}」时遇到了技术问题。

### 💡 建议
- 请尝试重新表述您的问题
- 确保问题描述清晰具体
- 如需紧急医疗帮助，请立即就医

### 📞 紧急联系
- 急救电话：120
- 中医咨询：当地中医院

⚠️ **重要提醒**：本系统无法替代专业医疗诊断！"""

class UltimateIntegratedRAGSystem:
    """终极整合RAG系统"""
    
    def __init__(self):
        self.embedding_model = None
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        self.initialized = False
        self.ultra_llm = UltraIntelligentLLM()
        
    def initialize(self):
        """初始化系统"""
        if self.initialized:
            return True
            
        try:
            with st.spinner("🚀 正在初始化终极整合系统..."):
                # 加载嵌入模型
                st.write("📥 加载嵌入模型...")
                self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
                st.success("✅ 嵌入模型加载成功")
                
                # 加载向量数据库
                st.write("📚 加载向量数据库...")
                self.load_vector_database()
                
                self.initialized = True
                st.success("🎉 终极整合系统初始化完成！")
                return True

        except Exception as e:
            st.error(f"❌ 系统初始化失败: {e}")
            return False

    def load_vector_database(self):
        """加载向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])

            if vector_db_path.exists():
                index_file = vector_db_path / "index.faiss"
                chunks_file = vector_db_path / "chunks.pkl"
                metadata_file = vector_db_path / "metadata.pkl"

                if all(f.exists() for f in [index_file, chunks_file, metadata_file]):
                    self.vector_index = faiss.read_index(str(index_file))

                    with open(chunks_file, 'rb') as f:
                        self.document_chunks = pickle.load(f)

                    with open(metadata_file, 'rb') as f:
                        self.chunk_metadata = pickle.load(f)

                    st.success(f"✅ 已加载 {len(self.document_chunks)} 个文档块")
                else:
                    st.warning("⚠️ 向量数据库文件不完整，请重新处理文档")
            else:
                st.warning("⚠️ 向量数据库不存在，请先上传PDF文档")

        except Exception as e:
            st.warning(f"⚠️ 加载向量数据库失败: {e}")

    def process_pdf_documents(self, uploaded_files):
        """处理PDF文档"""
        if not uploaded_files:
            return False

        try:
            with st.spinner("📄 正在处理PDF文档..."):
                all_chunks = []
                all_metadata = []

                for uploaded_file in uploaded_files:
                    st.write(f"处理文件: {uploaded_file.name}")

                    # 保存文件
                    documents_path = Path(CONFIG['DOCUMENTS_PATH'])
                    documents_path.mkdir(exist_ok=True)

                    file_path = documents_path / uploaded_file.name
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # 提取文本
                    text = self.extract_pdf_text(file_path)
                    if not text:
                        st.warning(f"⚠️ 无法从 {uploaded_file.name} 提取文本")
                        continue

                    # 分割文本
                    chunks = self.split_text_into_chunks(text)

                    # 创建元数据
                    for i, chunk in enumerate(chunks):
                        metadata = {
                            'source': uploaded_file.name,
                            'chunk_id': len(all_chunks) + i,
                            'chunk_index': i,
                            'content': chunk,
                            'upload_time': datetime.now().isoformat()
                        }
                        all_metadata.append(metadata)

                    all_chunks.extend(chunks)
                    st.write(f"✅ 从 {uploaded_file.name} 提取了 {len(chunks)} 个文本块")

                if not all_chunks:
                    st.error("❌ 没有提取到任何文本内容")
                    return False

                # 创建向量索引
                st.write("🔍 创建向量索引...")
                embeddings = []

                progress_bar = st.progress(0)
                for i, chunk in enumerate(all_chunks):
                    embedding = self.embedding_model.encode([chunk])[0]
                    embeddings.append(embedding)
                    progress_bar.progress((i + 1) / len(all_chunks))

                embeddings = np.array(embeddings).astype('float32')

                # 创建FAISS索引
                dimension = embeddings.shape[1]
                self.vector_index = faiss.IndexFlatIP(dimension)
                faiss.normalize_L2(embeddings)
                self.vector_index.add(embeddings)

                # 保存数据
                self.document_chunks = all_chunks
                self.chunk_metadata = all_metadata
                self.save_vector_database()

                st.success(f"🎉 成功处理 {len(uploaded_files)} 个PDF文档！")
                return True

        except Exception as e:
            st.error(f"❌ 处理PDF文档失败: {e}")
            return False

    def extract_pdf_text(self, pdf_path):
        """提取PDF文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            st.error(f"PDF解析错误: {e}")
            return ""

    def split_text_into_chunks(self, text):
        """分割文本"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']

        start = 0
        while start < len(text):
            end = start + chunk_size
            if end > len(text):
                end = len(text)

            chunk = text[start:end]

            # 在句号处分割
            if end < len(text) and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = text[start:end]

            if len(chunk.strip()) > 20:
                chunks.append(chunk.strip())

            start = end - overlap
            if start >= len(text):
                break

        return chunks

    def save_vector_database(self):
        """保存向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])
            vector_db_path.mkdir(exist_ok=True)

            faiss.write_index(self.vector_index, str(vector_db_path / "index.faiss"))

            with open(vector_db_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)

            with open(vector_db_path / "metadata.pkl", 'wb') as f:
                pickle.dump(self.chunk_metadata, f)

            return True
        except Exception as e:
            st.error(f"保存向量数据库失败: {e}")
            return False

    def search_documents(self, query, top_k=None):
        """搜索文档"""
        if top_k is None:
            top_k = CONFIG['TOP_K']

        if self.vector_index is None or not self.document_chunks:
            return []

        try:
            query_embedding = self.embedding_model.encode([query])[0]
            query_vector = np.array([query_embedding]).astype('float32')
            faiss.normalize_L2(query_vector)

            scores, indices = self.vector_index.search(query_vector, top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.chunk_metadata):
                    result = self.chunk_metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)

            return results

        except Exception as e:
            st.error(f"文档搜索失败: {e}")
            return []

def search_online_resources(query):
    """搜索在线资源"""
    try:
        # 简化的在线搜索
        base_url = "https://chinesebooks.github.io/gudaiyishu/"

        # 模拟搜索结果（实际应用中可以实现真正的爬取）
        mock_results = [
            {
                'title': '中医基础理论',
                'content': f'关于"{query}"的中医理论知识：中医学认为人体是一个有机整体，强调整体观念和辨证论治。',
                'url': base_url + 'theory/',
                'relevance': 0.8
            },
            {
                'title': '古代医书记载',
                'content': f'古代医书中关于"{query}"的记载：历代医家对此都有深入的研究和论述。',
                'url': base_url + 'classics/',
                'relevance': 0.7
            }
        ]

        return mock_results

    except Exception as e:
        st.warning(f"在线搜索失败: {e}")
        return []

# 初始化系统
if 'ultimate_rag_system' not in st.session_state:
    st.session_state.ultimate_rag_system = UltimateIntegratedRAGSystem()

def main():
    """主界面"""
    st.title("🧙‍♂️ 智者·中医AI助手 - 终极版")
    st.markdown("### 🚀 整合超级智能LLM + 商业级PDF检索 = 最强中医助手")

    # 侧边栏
    with st.sidebar:
        st.header("📋 系统控制")

        # 系统初始化
        if st.button("🚀 初始化终极系统", type="primary"):
            st.session_state.ultimate_rag_system.initialize()

        # 系统状态
        st.subheader("📊 系统状态")
        if st.session_state.ultimate_rag_system.initialized:
            st.success("✅ 终极系统已就绪")
            st.metric("文档块数量", len(st.session_state.ultimate_rag_system.document_chunks))
            st.metric("智能模型", "智者·中医AI助手")
            st.metric("向量维度", 768 if st.session_state.ultimate_rag_system.embedding_model else 0)
        else:
            st.warning("⚠️ 系统未初始化")

        st.divider()

        # PDF上传
        st.subheader("📄 PDF文档管理")
        uploaded_files = st.file_uploader(
            "上传中医PDF文档",
            type=['pdf'],
            accept_multiple_files=True,
            help="支持《黄帝内经》、《伤寒论》等中医经典"
        )

        if uploaded_files and st.button("📚 处理文档"):
            st.session_state.ultimate_rag_system.process_pdf_documents(uploaded_files)

        st.divider()

        # 功能说明
        st.subheader("✨ 终极功能")
        st.write("🧙‍♂️ **智者·中医AI助手**")
        st.write("🔍 **真正的PDF检索**")
        st.write("📊 **向量相似度匹配**")
        st.write("🌐 **在线资源整合**")
        st.write("🧠 **超级智能回答**")
        st.write("⚡ **3秒快速响应**")
        st.write("🎯 **专业辨证论治**")

    # 主要内容区域
    if not st.session_state.ultimate_rag_system.initialized:
        st.info("👆 请先点击侧边栏的'初始化终极系统'按钮")
        return

    # 问答界面
    st.subheader("💬 智者·中医AI助手")

    # 示例问题
    st.write("💡 **示例问题：**")
    example_questions = [
        "我最近湿气很重，应该怎么调理？",
        "气血不足有什么症状和治疗方法？",
        "黄帝内经中关于五脏六腑的理论是什么？",
        "伤寒论的栀子甘草豉汤方是什么？",
        "头痛的中医辨证论治方法有哪些？",
        "失眠多梦应该如何用中医调理？"
    ]

    cols = st.columns(2)
    for i, question in enumerate(example_questions):
        with cols[i % 2]:
            if st.button(f"📝 {question}", key=f"example_{i}"):
                st.session_state.current_question = question

    # 问题输入
    question = st.text_input(
        "请输入您的问题:",
        value=st.session_state.get('current_question', ''),
        placeholder="例如：我最近湿气很重，应该怎么调理？",
        key="question_input"
    )

    if st.button("🧙‍♂️ 智者分析", type="primary") and question:
        handle_question(question)

def handle_question(question):
    """处理用户问题"""
    with st.spinner("🧙‍♂️ 智者正在深度分析..."):
        # 1. PDF文档检索
        pdf_results = st.session_state.ultimate_rag_system.search_documents(question)

        # 2. 在线资源搜索
        online_results = search_online_resources(question)

        # 3. 使用超级智能LLM生成回答
        answer = st.session_state.ultimate_rag_system.ultra_llm.generate_intelligent_response(
            question, pdf_results, online_results
        )

        # 显示结果
        display_results(question, answer, pdf_results, online_results)

def display_results(question, answer, pdf_results, online_results):
    """显示结果"""
    # 用户问题
    st.markdown(f"""
    <div style="background-color: #E3F2FD; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #2196F3;">
        <strong>🙋 用户问题：</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)

    # 智者回答
    st.markdown(answer)

    # 检索详情
    if pdf_results or online_results:
        with st.expander("🔍 检索详情", expanded=False):
            if pdf_results:
                st.write("**📄 PDF检索结果：**")
                for i, result in enumerate(pdf_results, 1):
                    st.write(f"{i}. {result['source']} (相似度: {result['similarity_score']:.3f})")
                    with st.expander(f"查看内容 {i}"):
                        st.write(result['content'][:500] + "...")

            if online_results:
                st.write("**🌐 在线检索结果：**")
                for i, result in enumerate(online_results, 1):
                    st.write(f"{i}. {result['title']} (相关度: {result['relevance']:.3f})")
                    with st.expander(f"查看内容 {i}"):
                        st.write(result['content'][:500] + "...")

if __name__ == "__main__":
    main()
