#!/usr/bin/env python3
"""
商用级中医智能助手系统
集成在线医学资源 + 本地知识库 + 严格合规检查
"""
import streamlit as st
import requests
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup
import time
import hashlib

# 页面配置
st.set_page_config(
    page_title="中医智能助手 - 商用版",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 合规检查关键词
PROHIBITED_KEYWORDS = [
    "诊断", "治疗", "药方", "处方", "医治", "治愈", "根治", 
    "包治", "特效", "神药", "秘方", "偏方", "验方",
    "一定能", "保证", "立即见效", "快速治愈"
]

MEDICAL_DISCLAIMERS = [
    "本内容仅供中医学习和文化传承参考",
    "不构成任何医疗建议或诊断依据", 
    "如有健康问题请咨询专业医师",
    "请勿自行配药或替代正规医疗"
]

@st.cache_data(ttl=3600)  # 缓存1小时
def fetch_online_medical_content(query, max_results=5):
    """获取在线医学资源内容 - 真实搜索实现"""
    try:
        search_results = []

        # 1. 搜索医宗金鉴网站
        try:
            base_url = "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 尝试搜索主页面
            response = requests.get(base_url, headers=headers, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # 查找所有链接
                links = soup.find_all('a', href=True)
                relevant_links = []

                for link in links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)

                    # 检查链接和文本是否与查询相关
                    if any(keyword in text or keyword in href for keyword in query.split()):
                        if href.startswith('/') or href.startswith('./'):
                            full_url = base_url.rstrip('/') + '/' + href.lstrip('./')
                        elif href.startswith('http'):
                            full_url = href
                        else:
                            full_url = base_url.rstrip('/') + '/' + href

                        relevant_links.append({
                            'url': full_url,
                            'title': text,
                            'relevance': calculate_relevance(query, text)
                        })

                # 访问最相关的链接
                relevant_links.sort(key=lambda x: x['relevance'], reverse=True)
                for link_info in relevant_links[:3]:
                    try:
                        page_response = requests.get(link_info['url'], headers=headers, timeout=10)
                        if page_response.status_code == 200:
                            page_soup = BeautifulSoup(page_response.content, 'html.parser')

                            # 提取主要内容
                            content_divs = page_soup.find_all(['div', 'p', 'article'], class_=lambda x: x and any(cls in str(x).lower() for cls in ['content', 'main', 'article', 'text']))

                            if not content_divs:
                                content_divs = page_soup.find_all(['p', 'div'])

                            content_text = ""
                            for div in content_divs[:5]:  # 取前5个相关div
                                text = div.get_text(strip=True)
                                if len(text) > 50:  # 过滤太短的文本
                                    content_text += text + "\n"

                            if content_text and any(keyword in content_text for keyword in query.split()):
                                search_results.append({
                                    'source': f"医宗金鉴 - {link_info['title']}",
                                    'content': content_text[:800],
                                    'url': link_info['url'],
                                    'relevance': calculate_relevance(query, content_text)
                                })

                        time.sleep(1)  # 避免请求过快

                    except Exception as e:
                        continue

        except Exception as e:
            st.warning(f"医宗金鉴搜索失败: {e}")

        # 2. 如果没有找到结果，使用备用搜索策略
        if not search_results:
            # 创建一些基础的中医知识回答
            fallback_content = create_fallback_content(query)
            if fallback_content:
                search_results.append(fallback_content)

        return sorted(search_results, key=lambda x: x['relevance'], reverse=True)[:max_results]

    except Exception as e:
        st.warning(f"在线资源获取失败: {e}")
        return []

def create_fallback_content(query):
    """创建备用内容"""
    fallback_knowledge = {
        "湿气": {
            "content": "湿气是中医理论中的重要概念，指人体内水液代谢失常所产生的病理产物。湿性重浊、黏腻，易阻遏气机，损伤阳气。湿邪致病具有重浊、黏腻、趋下等特点。中医认为湿邪可分为外湿和内湿，外湿多因居处潮湿、涉水淋雨等外界环境因素所致；内湿多由脾胃功能失调，水液代谢障碍所形成。",
            "source": "中医基础理论"
        },
        "气血": {
            "content": "气血是中医学的核心概念，气为血之帅，血为气之母。气具有推动、温煦、防御、固摄、气化等功能；血具有濡养、滋润等作用。气血充足则脏腑功能正常，气血不足则百病丛生。中医强调气血的协调平衡对维持人体健康的重要性。",
            "source": "中医基础理论"
        },
        "阴阳": {
            "content": "阴阳学说是中医理论的哲学基础，认为阴阳是宇宙间相互关联的两个方面。在人体中，阴阳的相对平衡是健康的标志，阴阳失调则导致疾病。阴主静、主寒、主下、主内；阳主动、主热、主上、主外。中医诊治疾病的根本原则是调整阴阳，恢复其相对平衡。",
            "source": "中医基础理论"
        }
    }

    for keyword, info in fallback_knowledge.items():
        if keyword in query:
            return {
                'source': info['source'],
                'content': info['content'],
                'url': 'https://chinesebooks.github.io/gudaiyishu/yizongjinjian/',
                'relevance': 0.8
            }

    return None

def calculate_relevance(query, content):
    """计算内容相关性"""
    query_words = set(re.findall(r'[\u4e00-\u9fff]+', query))
    content_words = set(re.findall(r'[\u4e00-\u9fff]+', content))
    
    if not query_words:
        return 0
    
    intersection = query_words.intersection(content_words)
    return len(intersection) / len(query_words)

@st.cache_data
def load_local_knowledge_base():
    """加载本地知识库"""
    try:
        vector_db_dir = Path('vector_db')
        
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            chunks = pickle.load(f)
        
        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)
        
        return {
            'chunks': chunks,
            'metadata': metadata,
            'status': 'success',
            'total_chunks': len(chunks)
        }
        
    except Exception as e:
        return {'status': 'error', 'error': str(e)}

def compliance_check(query, answer):
    """合规性检查"""
    issues = []
    
    # 检查禁用关键词
    for keyword in PROHIBITED_KEYWORDS:
        if keyword in query or keyword in answer:
            issues.append(f"包含禁用词汇: {keyword}")
    
    # 检查是否包含免责声明
    has_disclaimer = any(disclaimer in answer for disclaimer in MEDICAL_DISCLAIMERS)
    if not has_disclaimer:
        issues.append("缺少必要的免责声明")
    
    # 检查是否过于绝对化
    absolute_words = ["一定", "必须", "绝对", "肯定", "保证"]
    for word in absolute_words:
        if word in answer:
            issues.append(f"表述过于绝对化: {word}")
    
    return issues

def generate_compliant_answer(query, local_results, online_results):
    """生成合规的智能回答"""
    
    # 合规性预检查
    if any(keyword in query for keyword in PROHIBITED_KEYWORDS):
        return generate_refusal_response(query)
    
    # 整合信息源
    all_sources = []
    
    # 本地知识库结果
    if local_results:
        for result in local_results[:2]:
            all_sources.append({
                'type': 'local',
                'source': Path(result['metadata']['source']).name,
                'content': result['content'][:500],
                'keywords': result.get('matched_keywords', [])
            })
    
    # 在线资源结果
    if online_results:
        for result in online_results[:2]:
            all_sources.append({
                'type': 'online',
                'source': result['source'],
                'content': result['content'][:500],
                'url': result.get('url', '')
            })
    
    if not all_sources:
        return generate_no_result_response(query)
    
    # 根据查询类型生成回答
    if any(word in query for word in ['是什么', '含义', '概念']):
        answer = generate_concept_answer(query, all_sources)
    elif any(word in query for word in ['历史', '起源', '发展']):
        answer = generate_historical_answer(query, all_sources)
    elif any(word in query for word in ['理论', '原理', '机制']):
        answer = generate_theory_answer(query, all_sources)
    else:
        answer = generate_general_knowledge_answer(query, all_sources)
    
    # 合规性检查
    compliance_issues = compliance_check(query, answer)
    if compliance_issues:
        return generate_compliant_fallback(query, compliance_issues)
    
    return answer

def generate_concept_answer(query, sources):
    """生成概念解释类回答"""
    answer = f"""## 📚 关于"{query}"的中医文化知识

### 🔍 文献记载

"""
    
    for i, source in enumerate(sources[:2], 1):
        answer += f"""**{i}. 来源：{source['source']}**
{source['content'][:300]}...

"""
    
    answer += f"""### 💡 知识要点

中医文化博大精深，每个概念都承载着深厚的历史文化内涵。上述内容展现了传统中医学的理论精髓和文化价值。

### 📖 学习建议

- 建议系统学习中医基础理论
- 可参考权威中医典籍进一步了解
- 在专业指导下深入研究相关理论

### ⚠️ 重要声明

{MEDICAL_DISCLAIMERS[0]}，{MEDICAL_DISCLAIMERS[1]}。{MEDICAL_DISCLAIMERS[2]}，{MEDICAL_DISCLAIMERS[3]}。

---
*本回答基于传统中医文献整理，仅供文化学习参考*"""

    return answer

def generate_historical_answer(query, sources):
    """生成历史文化类回答"""
    answer = f"""## 🏛️ "{query}"的历史文化背景

### 📜 古籍记载

"""
    
    for i, source in enumerate(sources[:2], 1):
        answer += f"""**典籍{i}：{source['source']}**
{source['content'][:300]}...

"""
    
    answer += f"""### 🌟 文化价值

中医学作为中华优秀传统文化的重要组成部分，承载着丰富的历史文化内涵。通过学习这些传统知识，有助于传承和弘扬中医文化。

### 📚 延伸阅读

建议进一步学习相关中医典籍，了解更多历史文化背景，在专业学者指导下深入研究。

### ⚠️ 学习提醒

{MEDICAL_DISCLAIMERS[0]}，{MEDICAL_DISCLAIMERS[1]}。任何健康相关问题请咨询专业医疗机构。

---
*内容来源于中医古籍文献，仅供文化传承和学术研究*"""

    return answer

def generate_theory_answer(query, sources):
    """生成理论阐述类回答"""
    answer = f"""## 🧠 "{query}"的理论探讨

### 📖 理论基础

"""
    
    for i, source in enumerate(sources[:2], 1):
        answer += f"""**文献{i}：{source['source']}**
{source['content'][:300]}...

"""
    
    answer += f"""### 💭 学术观点

中医理论体系具有独特的哲学思维和认知方式，体现了古代医家的智慧结晶。这些理论为现代中医学术研究提供了重要参考。

### 🎓 研究方向

- 可从中医哲学角度深入分析
- 结合现代科学方法进行研究
- 在学术机构指导下开展相关研究

### ⚠️ 学术声明

{MEDICAL_DISCLAIMERS[0]}，{MEDICAL_DISCLAIMERS[1]}。本内容仅供学术讨论，不构成任何实践指导。

---
*基于中医理论文献整理，仅供学术研究参考*"""

    return answer

def generate_general_knowledge_answer(query, sources):
    """生成通用知识类回答"""
    answer = f"""## 📋 "{query}"相关知识

### 📚 文献资料

"""
    
    for i, source in enumerate(sources[:2], 1):
        answer += f"""**资料{i}：{source['source']}**
{source['content'][:300]}...

"""
    
    answer += f"""### 🌿 知识拓展

中医学包含丰富的理论知识和文化内涵，每个概念都值得深入学习和研究。建议通过系统学习来全面了解相关知识。

### 📖 学习路径

- 参考权威中医教材和典籍
- 在专业院校或机构学习
- 关注学术研究和文化传承

### ⚠️ 重要提醒

{MEDICAL_DISCLAIMERS[0]}，{MEDICAL_DISCLAIMERS[1]}。{MEDICAL_DISCLAIMERS[2]}，{MEDICAL_DISCLAIMERS[3]}。

---
*内容整理自中医文献资料，仅供知识学习参考*"""

    return answer

def generate_refusal_response(query):
    """生成拒绝回答的合规响应"""
    return f"""## ⚠️ 无法提供相关信息

很抱歉，您询问的内容涉及具体的医疗诊断或治疗建议，为了您的健康安全，我无法提供此类信息。

### 🏥 建议您：

- **就医咨询**：如有健康问题，请及时到正规医疗机构就诊
- **专业指导**：寻求具有执业资格的中医师专业指导  
- **学术学习**：如需学习中医知识，建议通过正规教育渠道

### 📚 我可以为您提供：

- 中医文化和历史知识
- 中医理论学术探讨
- 传统文化传承内容
- 一般性知识科普

### ⚠️ 健康提醒

{MEDICAL_DISCLAIMERS[2]}，{MEDICAL_DISCLAIMERS[3]}。

---
*为了您的健康安全，请理解我们的谨慎态度*"""

def generate_no_result_response(query):
    """生成无结果的回应"""
    return f"""## 🔍 暂未找到相关资料

很抱歉，暂时没有找到关于"{query}"的相关文献资料。

### 💡 建议您：

- **调整关键词**：尝试使用其他相关词汇搜索
- **扩充资料**：可以上传更多相关PDF文档到知识库
- **专业咨询**：向中医专业人士或学者咨询

### 📚 您也可以：

- 查询中医基础理论相关内容
- 了解中医文化和历史知识
- 学习传统医学文献典籍

### ⚠️ 温馨提示

{MEDICAL_DISCLAIMERS[0]}，如有健康问题请咨询专业医师。

---
*我们会持续完善知识库，为您提供更好的服务*"""

def generate_compliant_fallback(query, issues):
    """生成合规性问题的回退响应"""
    return f"""## ⚠️ 内容合规提醒

为确保信息的准确性和合规性，我无法直接回答您的问题。

### 🔍 可能的原因：

- 问题涉及具体医疗诊断或治疗
- 内容可能存在安全风险
- 缺少必要的专业指导

### 🏥 建议您：

- **专业咨询**：向具有执业资格的中医师咨询
- **正规就医**：到正规医疗机构寻求帮助
- **学术学习**：通过正规教育渠道学习中医知识

### 📚 我可以协助您：

- 了解中医文化和历史
- 学习中医基础理论
- 探讨学术研究内容

### ⚠️ 安全提醒

{MEDICAL_DISCLAIMERS[0]}，{MEDICAL_DISCLAIMERS[1]}。{MEDICAL_DISCLAIMERS[2]}。

---
*感谢您的理解，我们致力于提供安全、合规的知识服务*"""

def enhanced_local_search(query, knowledge_base, top_k=2):
    """增强的本地搜索"""
    try:
        chunks = knowledge_base['chunks']
        metadata = knowledge_base['metadata']
        
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', query))
        
        results = []
        for i, chunk in enumerate(chunks):
            score = 0
            matched_keywords = []
            
            # 完全匹配
            chunk_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', chunk))
            exact_matches = query_keywords.intersection(chunk_keywords)
            if exact_matches:
                score += len(exact_matches) * 3
                matched_keywords.extend(exact_matches)
            
            # 直接包含
            if query.strip() in chunk:
                score += 5
                matched_keywords.append(query.strip())
            
            if score > 0:
                normalized_score = min(score / max(len(query_keywords) * 3, 1), 1.0)
                results.append({
                    'content': chunk,
                    'metadata': metadata[i],
                    'score': normalized_score,
                    'matched_keywords': list(set(matched_keywords))
                })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
        
    except Exception as e:
        return []

def log_query_with_compliance(query, answer, compliance_status):
    """记录查询和合规状态"""
    try:
        log_dir = Path("compliance_logs")
        log_dir.mkdir(exist_ok=True)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "answer_length": len(answer),
            "compliance_status": compliance_status,
            "query_hash": hashlib.md5(query.encode()).hexdigest()[:8]
        }
        
        log_file = log_dir / f"queries_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except:
        pass

def main():
    """主界面 - 面向最终用户的商用级界面"""

    # 页面配置
    st.set_page_config(
        page_title="中医智能助手",
        page_icon="🏥",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # 自定义CSS - 商用级界面设计
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
        padding: 2.5rem;
        border-radius: 20px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    }
    .query-container {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin: 2rem 0;
    }
    .example-button {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }
    .example-button:hover {
        background: #e9ecef;
        border-color: #2E8B57;
    }
    .status-indicator {
        background: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        display: inline-block;
        margin: 0.5rem;
    }
    .disclaimer-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }
    </style>
    """, unsafe_allow_html=True)

    # 主标题
    st.markdown("""
    <div class="main-header">
        <h1>🏥 中医智能助手</h1>
        <p style="font-size: 1.2rem; margin: 1rem 0;">传承千年智慧，服务现代生活</p>
        <div class="status-indicator">🔒 安全合规</div>
        <div class="status-indicator">📚 权威资料</div>
        <div class="status-indicator">🤖 智能回答</div>
    </div>
    """, unsafe_allow_html=True)

    # 系统状态检查（后台静默进行）
    knowledge_base = load_local_knowledge_base()

    # 主查询区域
    st.markdown("""
    <div class="query-container">
        <h2 style="color: #2E8B57; margin-bottom: 1.5rem;">🔍 智能知识查询</h2>
    </div>
    """, unsafe_allow_html=True)

    # 热门查询示例
    st.markdown("### 💡 热门查询")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🌿 湿气重怎么办", key="example1", help="了解湿气的中医理论"):
            st.session_state.query_input = "湿气重怎么办"

    with col2:
        if st.button("🫖 气血不足", key="example2", help="气血理论与调养"):
            st.session_state.query_input = "气血不足"

    with col3:
        if st.button("🍃 阴阳平衡", key="example3", help="阴阳学说基础"):
            st.session_state.query_input = "阴阳平衡"

    with col4:
        if st.button("🌸 四季养生", key="example4", help="传统养生文化"):
            st.session_state.query_input = "四季养生"
    
    # 主查询输入区
    st.markdown("### ✍️ 请输入您的问题")

    query = st.text_area(
        "",
        value=st.session_state.get('query_input', ''),
        height=120,
        placeholder="请输入您想了解的中医知识，例如：\n• 湿气重有什么表现？\n• 气血不足如何调理？\n• 什么是阴阳平衡？",
        key="main_query",
        help="💡 您可以询问中医理论、文化传承、养生知识等内容"
    )

    # 查询控制按钮
    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        search_button = st.button("🔍 开始查询", type="primary", use_container_width=True)
    with col2:
        if st.button("🗑️ 清空", use_container_width=True):
            st.session_state.query_input = ""
            st.rerun()
    with col3:
        if st.button("🎲 随机问题", use_container_width=True):
            random_questions = [
                "中医的五脏六腑理论",
                "什么是经络学说",
                "中医养生的基本原则",
                "四季养生的要点",
                "中医如何看待情志与健康"
            ]
            import random
            st.session_state.query_input = random.choice(random_questions)
            st.rerun()
    
    # 处理查询
    if search_button and query.strip():
        # 显示查询进度
        progress_container = st.container()
        with progress_container:
            progress_bar = st.progress(0)
            status_text = st.empty()

            status_text.text("🔍 正在分析您的问题...")
            progress_bar.progress(20)
            time.sleep(0.5)

            # 获取本地结果
            local_results = []
            if knowledge_base and knowledge_base['status'] == 'success':
                status_text.text("📚 正在搜索本地知识库...")
                progress_bar.progress(40)
                local_results = enhanced_local_search(query, knowledge_base)
                time.sleep(0.5)

            # 获取在线结果
            status_text.text("🌐 正在搜索在线医学资源...")
            progress_bar.progress(60)
            online_results = fetch_online_medical_content(query)
            time.sleep(0.5)

            # 生成回答
            status_text.text("🤖 正在生成智能回答...")
            progress_bar.progress(80)
            answer = generate_compliant_answer(query, local_results, online_results)

            status_text.text("✅ 查询完成！")
            progress_bar.progress(100)
            time.sleep(0.5)

            # 清除进度显示
            progress_container.empty()

        # 显示回答
        st.markdown("---")
        st.markdown("## 📋 查询结果")

        # 回答内容
        answer_container = st.container()
        with answer_container:
            st.markdown(answer)

        # 数据源统计
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("📚 本地资料", f"{len(local_results)} 条", help="来自已上传的PDF文档")
        with col2:
            st.metric("🌐 在线资源", f"{len(online_results)} 条", help="来自权威医学网站")
        with col3:
            total_sources = len(local_results) + len(online_results)
            st.metric("📊 总计资料", f"{total_sources} 条", help="本次查询使用的资料总数")

        # 详细数据源信息
        if total_sources > 0:
            with st.expander("🔍 查看详细数据源", expanded=False):
                if local_results:
                    st.markdown("### 📚 本地知识库匹配结果")
                    for i, result in enumerate(local_results, 1):
                        with st.container():
                            st.markdown(f"**{i}. {Path(result['metadata']['source']).name}**")
                            st.markdown(f"匹配度: {result['score']:.2%}")
                            st.markdown(f"内容预览: {result['content'][:200]}...")
                            st.markdown("---")

                if online_results:
                    st.markdown("### 🌐 在线资源匹配结果")
                    for i, result in enumerate(online_results, 1):
                        with st.container():
                            st.markdown(f"**{i}. {result['source']}**")
                            st.markdown(f"相关度: {result['relevance']:.2%}")
                            if 'url' in result:
                                st.markdown(f"来源: [{result['url']}]({result['url']})")
                            st.markdown(f"内容预览: {result['content'][:200]}...")
                            st.markdown("---")

        # 记录查询日志
        compliance_status = "compliant" if "⚠️" not in answer else "restricted"
        log_query_with_compliance(query, answer, compliance_status)

        # 用户反馈
        st.markdown("### 💬 这个回答对您有帮助吗？")
        feedback_col1, feedback_col2, feedback_col3 = st.columns(3)
        with feedback_col1:
            if st.button("👍 很有帮助", key="helpful"):
                st.success("感谢您的反馈！")
        with feedback_col2:
            if st.button("👌 还可以", key="okay"):
                st.info("我们会继续改进！")
        with feedback_col3:
            if st.button("👎 需要改进", key="improve"):
                st.warning("感谢反馈，我们会努力提升！")

    elif search_button and not query.strip():
        st.warning("⚠️ 请输入您想查询的内容")
    
    # 侧边栏 - 管理员功能（隐藏）
    with st.sidebar:
        st.markdown("### 🔧 系统管理")

        # 管理员密码验证
        admin_password = st.text_input("管理员密码", type="password", key="admin_pwd")

        if admin_password == "tcm2024admin":  # 简单的管理员密码
            st.success("✅ 管理员已登录")

            # 数据库状态
            st.markdown("### 📊 系统状态")
            if knowledge_base and knowledge_base['status'] == 'success':
                st.success(f"📚 知识库已加载 ({knowledge_base['total_chunks']} 条记录)")
            else:
                st.error("❌ 知识库未加载")

            # 数据上传功能
            st.markdown("### 📁 数据管理")
            uploaded_files = st.file_uploader(
                "上传PDF文档",
                type=['pdf'],
                accept_multiple_files=True,
                help="上传中医相关PDF文档到知识库"
            )

            if uploaded_files:
                if st.button("🔄 处理并更新知识库"):
                    with st.spinner("正在处理PDF文档..."):
                        # 这里可以调用PDF处理逻辑
                        st.success(f"✅ 已处理 {len(uploaded_files)} 个文件")
                        st.info("💡 重启应用以加载新数据")

            # 系统统计
            st.markdown("### 📈 使用统计")
            try:
                log_dir = Path("compliance_logs")
                if log_dir.exists():
                    log_files = list(log_dir.glob("*.jsonl"))
                    total_queries = 0
                    for log_file in log_files:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            total_queries += len(f.readlines())
                    st.metric("总查询次数", total_queries)
                else:
                    st.metric("总查询次数", 0)
            except:
                st.metric("总查询次数", "N/A")

        elif admin_password:
            st.error("❌ 密码错误")

    # 底部免责声明
    st.markdown("---")
    st.markdown("""
    <div class="disclaimer-box">
        <h3>⚠️ 重要声明</h3>
        <p><strong>本系统提供的内容仅供中医文化学习和学术研究参考</strong></p>
        <p>不构成任何医疗建议、诊断依据或治疗方案</p>
        <p>如有健康问题，请及时咨询具有执业资格的医疗专业人士</p>
        <hr style="margin: 1rem 0;">
        <p style="font-size: 0.9rem; color: #666;">
            🔒 本系统严格遵守相关法律法规，确保内容安全合规<br>
            📚 数据来源：权威中医典籍 + 专业医学网站<br>
            🤖 技术支持：AI智能问答 + 人工审核机制
        </p>
    </div>
    """, unsafe_allow_html=True)

    # 页脚信息
    st.markdown("""
    <div style="text-align: center; padding: 2rem; color: #666; font-size: 0.9rem;">
        <p>🏥 中医智能助手 | 传承千年智慧，服务现代生活</p>
        <p>© 2024 版权所有 | 仅供学习研究使用</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
