#!/usr/bin/env python3
"""
RAG系统依赖安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("🚀 开始安装RAG系统依赖...")
    
    # 基础依赖
    basic_packages = [
        "numpy",
        "pandas",
        "scikit-learn",
        "sentence-transformers",
        "faiss-cpu",
        "jieba",
        "fastapi",
        "uvicorn",
        "pydantic",
        "requests",
        "beautifulsoup4",
        "PyPDF2",
        "python-multipart"
    ]
    
    # 安装基础包
    print("\n📦 安装基础依赖包...")
    failed_packages = []
    
    for package in basic_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 报告结果
    if failed_packages:
        print(f"\n❌ 以下包安装失败: {failed_packages}")
        print("请手动安装这些包或检查网络连接")
    else:
        print("\n🎉 所有依赖包安装成功！")
    
    print("\n📋 下一步:")
    print("1. 运行 python rag_system_fixer.py 诊断和修复系统")
    print("2. 运行 python enhanced_mcp_service.py 启动MCP服务")
    print("3. 测试智能检索功能")

if __name__ == "__main__":
    main()
