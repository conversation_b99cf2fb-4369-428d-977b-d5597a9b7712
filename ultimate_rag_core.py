#!/usr/bin/env python3
"""
终极RAG核心系统
支持大文件处理、多格式文档、高性能向量检索
"""

import streamlit as st
import os
import pickle
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
from pathlib import Path
from datetime import datetime
import PyPDF2
import logging
from typing import Dict, List, Any
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import gc
import time

# 多格式文档处理
try:
    import docx
    import pandas as pd
    from pptx import Presentation
    import openpyxl
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False

logger = logging.getLogger(__name__)

# 配置
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './ultimate_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CHUNK_SIZE': 800,
    'CHUNK_OVERLAP': 100,
    'TOP_K': 8,
    'MAX_WORKERS': mp.cpu_count(),
    'BATCH_SIZE': 64,
    'MAX_FILE_SIZE': 500 * 1024 * 1024,  # 500MB
    'PROCESS_TIMEOUT': 3600
}

class LargeFileProcessor:
    """大文件处理器 - 支持>200MB文件"""

    def __init__(self):
        self.supported_formats = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls', '.txt', '.md']

    def process_large_file(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大文件"""
        try:
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            st.info(f"📄 处理大文件: {file_name} ({file_size:.1f}MB)")

            extension = file_path.suffix.lower()

            if extension == '.pdf':
                return self._process_large_pdf(file_path, file_name, progress_callback)
            elif extension in ['.docx', '.doc'] and MULTI_FORMAT_AVAILABLE:
                return self._process_large_word(file_path, file_name, progress_callback)
            elif extension in ['.pptx', '.ppt'] and MULTI_FORMAT_AVAILABLE:
                return self._process_large_ppt(file_path, file_name, progress_callback)
            elif extension in ['.xlsx', '.xls'] and MULTI_FORMAT_AVAILABLE:
                return self._process_large_excel(file_path, file_name, progress_callback)
            elif extension in ['.txt', '.md']:
                return self._process_large_text(file_path, file_name, progress_callback)
            else:
                st.warning(f"⚠️ 不支持的文件格式: {extension}")
                return [], []

        except Exception as e:
            st.error(f"❌ 处理大文件失败 {file_name}: {e}")
            return [], []

    def _process_large_pdf(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大PDF文件"""
        try:
            chunks = []
            metadata = []

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)

                st.info(f"📑 PDF总页数: {total_pages}")

                # 分批处理页面
                batch_size = 20  # 每批处理20页
                text_parts = []

                for batch_start in range(0, total_pages, batch_size):
                    batch_end = min(batch_start + batch_size, total_pages)

                    if progress_callback:
                        progress = batch_end / total_pages
                        progress_callback(progress, f"处理页面 {batch_start+1}-{batch_end}/{total_pages}")

                    # 处理当前批次
                    batch_text = []
                    for page_num in range(batch_start, batch_end):
                        try:
                            page_text = pdf_reader.pages[page_num].extract_text()
                            if page_text.strip():
                                batch_text.append(page_text.strip())
                        except Exception as e:
                            logger.warning(f"页面 {page_num+1} 处理失败: {e}")
                            continue

                    if batch_text:
                        text_parts.extend(batch_text)

                    # 强制垃圾回收
                    gc.collect()

                # 合并所有文本
                full_text = '\n'.join(text_parts)

                # 智能分块
                chunks = self._intelligent_chunking(full_text)

                # 创建元数据
                for i, chunk in enumerate(chunks):
                    meta = {
                        'source': file_name,
                        'chunk_id': f"{file_name}_{i}",
                        'chunk_index': i,
                        'content': chunk,
                        'upload_time': datetime.now().isoformat(),
                        'file_type': '.pdf',
                        'total_pages': total_pages,
                        'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                    }
                    metadata.append(meta)

                return chunks, metadata

        except Exception as e:
            st.error(f"大PDF处理失败: {e}")
            return [], []

    def _process_large_word(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大Word文档"""
        try:
            doc = docx.Document(file_path)
            text_parts = []

            total_paragraphs = len(doc.paragraphs)

            for i, paragraph in enumerate(doc.paragraphs):
                if progress_callback and i % 100 == 0:
                    progress = i / total_paragraphs
                    progress_callback(progress, f"处理段落 {i+1}/{total_paragraphs}")

                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())

            full_text = '\n'.join(text_parts)
            chunks = self._intelligent_chunking(full_text)

            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.docx',
                    'total_paragraphs': total_paragraphs,
                    'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            st.error(f"大Word文档处理失败: {e}")
            return [], []

    def _process_large_ppt(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大PPT文件"""
        try:
            prs = Presentation(file_path)
            text_parts = []

            total_slides = len(prs.slides)

            for i, slide in enumerate(prs.slides):
                if progress_callback:
                    progress = (i + 1) / total_slides
                    progress_callback(progress, f"处理幻灯片 {i+1}/{total_slides}")

                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                if slide_text:
                    text_parts.append(f"幻灯片{i+1}: " + " ".join(slide_text))

            full_text = '\n'.join(text_parts)
            chunks = self._intelligent_chunking(full_text)

            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.pptx',
                    'total_slides': total_slides,
                    'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            st.error(f"大PPT处理失败: {e}")
            return [], []

    def _process_large_excel(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大Excel文件"""
        try:
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            text_parts = []

            total_sheets = len(excel_file.sheet_names)

            for i, sheet_name in enumerate(excel_file.sheet_names):
                if progress_callback:
                    progress = (i + 1) / total_sheets
                    progress_callback(progress, f"处理工作表 {i+1}/{total_sheets}: {sheet_name}")

                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    sheet_text = f"工作表: {sheet_name}\n"
                    sheet_text += df.to_string(index=False)
                    text_parts.append(sheet_text)
                except Exception as e:
                    logger.warning(f"工作表 {sheet_name} 处理失败: {e}")
                    continue

            full_text = '\n'.join(text_parts)
            chunks = self._intelligent_chunking(full_text)

            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.xlsx',
                    'total_sheets': total_sheets,
                    'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            st.error(f"大Excel处理失败: {e}")
            return [], []

    def _process_large_text(self, file_path: Path, file_name: str, progress_callback=None) -> tuple:
        """处理大文本文件"""
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
            text = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    break
                except UnicodeDecodeError:
                    continue

            if text is None:
                st.error(f"❌ 无法读取文本文件 {file_name}")
                return [], []

            if progress_callback:
                progress_callback(0.5, "正在分块处理...")

            chunks = self._intelligent_chunking(text)

            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': file_path.suffix,
                    'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            st.error(f"大文本文件处理失败: {e}")
            return [], []

    def _intelligent_chunking(self, text: str) -> List[str]:
        """智能分块"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']

        # 按段落分割
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果当前块加上新段落不超过限制
            if len(current_chunk) + len(paragraph) <= chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                # 保存当前块
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())

                # 开始新块
                if len(paragraph) <= chunk_size:
                    current_chunk = paragraph + "\n\n"
                else:
                    # 段落太长，需要进一步分割
                    sub_chunks = self._split_long_paragraph(paragraph, chunk_size, overlap)
                    chunks.extend(sub_chunks)
                    current_chunk = ""

        # 添加最后一块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        # 过滤太短的块
        return [chunk for chunk in chunks if len(chunk.strip()) > 50]

    def _split_long_paragraph(self, paragraph: str, chunk_size: int, overlap: int) -> List[str]:
        """分割长段落"""
        chunks = []
        start = 0

        while start < len(paragraph):
            end = start + chunk_size
            if end > len(paragraph):
                end = len(paragraph)

            chunk = paragraph[start:end]

            # 在句号处分割
            if end < len(paragraph) and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = paragraph[start:end]

            chunks.append(chunk.strip())
            start = end - overlap

            if start >= len(paragraph):
                break

        return chunks

class HighPerformanceVectorDB:
    """高性能向量数据库"""

    def __init__(self):
        self.index = None
        self.embedding_model = None
        self.chunks = []
        self.metadata = []
        self.dimension = 768

    def initialize(self):
        """初始化向量数据库"""
        try:
            st.write("📥 加载嵌入模型...")
            self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
            self.dimension = self.embedding_model.get_sentence_embedding_dimension()

            # 创建高性能FAISS索引
            self.index = faiss.IndexHNSWFlat(self.dimension, 32)
            self.index.hnsw.efConstruction = 200
            self.index.hnsw.efSearch = 100

            st.success("✅ 高性能向量数据库初始化成功")
            return True

        except Exception as e:
            st.error(f"❌ 向量数据库初始化失败: {e}")
            return False

    def add_documents_batch(self, chunks: List[str], metadata: List[Dict], progress_callback=None):
        """批量添加文档"""
        try:
            if not chunks:
                return False

            # 批量编码
            batch_size = CONFIG['BATCH_SIZE']
            embeddings = []

            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]

                if progress_callback:
                    progress = (i + batch_size) / len(chunks)
                    progress_callback(progress, f"向量化进度: {i+1}-{min(i+batch_size, len(chunks))}/{len(chunks)}")

                try:
                    batch_embeddings = self.embedding_model.encode(
                        batch,
                        show_progress_bar=False,
                        batch_size=32,
                        normalize_embeddings=True
                    )
                    embeddings.extend(batch_embeddings)

                    # 强制垃圾回收
                    gc.collect()

                except Exception as e:
                    st.error(f"批次 {i//batch_size + 1} 向量化失败: {e}")
                    continue

            if not embeddings:
                st.error("❌ 没有生成任何向量")
                return False

            # 添加到索引
            embeddings_array = np.array(embeddings).astype('float32')
            self.index.add(embeddings_array)

            # 更新数据
            self.chunks.extend(chunks)
            self.metadata.extend(metadata)

            st.success(f"✅ 成功添加 {len(chunks)} 个文档块到向量数据库")
            return True

        except Exception as e:
            st.error(f"❌ 批量添加文档失败: {e}")
            return False

    def search(self, query: str, top_k: int = None) -> List[Dict]:
        """搜索相似文档"""
        if top_k is None:
            top_k = CONFIG['TOP_K']

        if self.index is None or not self.chunks:
            return []

        try:
            # 编码查询
            query_embedding = self.embedding_model.encode([query], normalize_embeddings=True)[0]
            query_vector = np.array([query_embedding]).astype('float32')

            # 搜索
            scores, indices = self.index.search(query_vector, top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if 0 <= idx < len(self.metadata):
                    result = self.metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    result['content'] = self.chunks[idx]
                    results.append(result)

            return results

        except Exception as e:
            st.error(f"❌ 文档搜索失败: {e}")
            return []

    def save_database(self):
        """保存数据库"""
        try:
            db_path = Path(CONFIG['VECTOR_DB_PATH'])
            db_path.mkdir(exist_ok=True)

            # 保存FAISS索引
            faiss.write_index(self.index, str(db_path / "index.faiss"))

            # 保存文档块
            with open(db_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.chunks, f)

            # 保存元数据
            with open(db_path / "metadata.pkl", 'wb') as f:
                pickle.dump(self.metadata, f)

            st.success("✅ 向量数据库保存成功")
            return True

        except Exception as e:
            st.error(f"❌ 保存向量数据库失败: {e}")
            return False

    def load_database(self):
        """加载数据库"""
        try:
            db_path = Path(CONFIG['VECTOR_DB_PATH'])

            if not db_path.exists():
                st.warning("⚠️ 向量数据库不存在")
                return False

            index_file = db_path / "index.faiss"
            chunks_file = db_path / "chunks.pkl"
            metadata_file = db_path / "metadata.pkl"

            if not all(f.exists() for f in [index_file, chunks_file, metadata_file]):
                st.warning("⚠️ 向量数据库文件不完整")
                return False

            # 加载FAISS索引
            self.index = faiss.read_index(str(index_file))

            # 加载文档块
            with open(chunks_file, 'rb') as f:
                self.chunks = pickle.load(f)

            # 加载元数据
            with open(metadata_file, 'rb') as f:
                self.metadata = pickle.load(f)

            st.success(f"✅ 已加载 {len(self.chunks)} 个文档块")
            return True

        except Exception as e:
            st.error(f"❌ 加载向量数据库失败: {e}")
            return False

    def get_stats(self) -> Dict:
        """获取数据库统计信息"""
        return {
            'total_chunks': len(self.chunks),
            'total_documents': len(set(meta['source'] for meta in self.metadata)),
            'index_size': self.index.ntotal if self.index else 0,
            'dimension': self.dimension
        }

class UltimateRAGCore:
    """终极RAG核心系统"""

    def __init__(self):
        self.vector_db = HighPerformanceVectorDB()
        self.file_processor = LargeFileProcessor()
        self.initialized = False

    def initialize(self):
        """初始化系统"""
        if self.initialized:
            return True

        try:
            with st.spinner("🚀 正在初始化终极RAG系统..."):
                # 初始化向量数据库
                if not self.vector_db.initialize():
                    return False

                # 尝试加载现有数据库
                self.vector_db.load_database()

                self.initialized = True
                st.success("🎉 终极RAG系统初始化完成！")
                return True

        except Exception as e:
            st.error(f"❌ 系统初始化失败: {e}")
            return False

    def process_documents(self, uploaded_files):
        """处理上传的文档"""
        if not uploaded_files:
            return False

        try:
            with st.spinner("⚡ 正在处理文档..."):
                all_chunks = []
                all_metadata = []

                # 创建进度条
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i, uploaded_file in enumerate(uploaded_files):
                    file_progress = i / len(uploaded_files)
                    status_text.text(f"处理文件 {i+1}/{len(uploaded_files)}: {uploaded_file.name}")

                    # 保存文件
                    documents_path = Path(CONFIG['DOCUMENTS_PATH'])
                    documents_path.mkdir(exist_ok=True)

                    file_path = documents_path / uploaded_file.name
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # 处理文件
                    def update_progress(progress, message):
                        overall_progress = file_progress + (progress / len(uploaded_files))
                        progress_bar.progress(overall_progress)
                        status_text.text(f"{uploaded_file.name}: {message}")

                    chunks, metadata = self.file_processor.process_large_file(
                        file_path, uploaded_file.name, update_progress
                    )

                    if chunks:
                        all_chunks.extend(chunks)
                        all_metadata.extend(metadata)
                        st.success(f"✅ {uploaded_file.name}: 提取了 {len(chunks)} 个文本块")
                    else:
                        st.warning(f"⚠️ {uploaded_file.name}: 处理失败")

                    # 强制垃圾回收
                    gc.collect()

                if not all_chunks:
                    st.error("❌ 没有提取到任何文本内容")
                    return False

                # 添加到向量数据库
                status_text.text("正在创建向量索引...")

                def update_vector_progress(progress, message):
                    progress_bar.progress(progress)
                    status_text.text(message)

                success = self.vector_db.add_documents_batch(
                    all_chunks, all_metadata, update_vector_progress
                )

                if success:
                    # 保存数据库
                    self.vector_db.save_database()

                    progress_bar.progress(1.0)
                    status_text.text("处理完成！")

                    st.success(f"🎉 成功处理 {len(uploaded_files)} 个文档，共 {len(all_chunks)} 个文本块！")
                    return True
                else:
                    st.error("❌ 向量索引创建失败")
                    return False

        except Exception as e:
            st.error(f"❌ 处理文档失败: {e}")
            return False

    def search_documents(self, query: str, top_k: int = None) -> List[Dict]:
        """搜索文档"""
        return self.vector_db.search(query, top_k)

    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        stats = self.vector_db.get_stats()
        stats['initialized'] = self.initialized
        return stats