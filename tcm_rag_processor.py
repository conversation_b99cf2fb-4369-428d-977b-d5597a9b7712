"""
中医RAG系统专用处理器 - 修复线程问题
"""
import PyPDF2
import numpy as np
import faiss
import pickle
import json
import time
import gc
import threading
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import config
from gpu_optimizer import setup_gpu_optimization

class TCMRAGProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
        # 获取GPU优化配置
        self.gpu_config = setup_gpu_optimization()
        self.batch_size = self.gpu_config['batch_size']
        self.chunk_size = self.gpu_config['chunk_size']
        
        print(f"🏥 中医RAG处理器初始化")
        print(f"   设备: {self.gpu_config['device_name']}")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   块大小: {self.chunk_size}")
    
    def extract_tcm_pdf(self, pdf_path: str) -> str:
        """提取中医PDF文本"""
        print(f"📄 提取中医文献: {pdf_path}")
        start_time = time.time()
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                print(f"📖 总页数: {total_pages}")
                
                # 并行提取页面
                def extract_page(page_num):
                    try:
                        page = pdf_reader.pages[page_num]
                        text = page.extract_text()
                        # 中医文本特殊处理
                        text = self.clean_tcm_text(text)
                        return text
                    except Exception as e:
                        print(f"   页面 {page_num+1} 提取失败: {e}")
                        return ""
                
                # 使用线程池并行处理
                max_workers = min(8, mp.cpu_count())
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    page_texts = list(executor.map(extract_page, range(total_pages)))
                
                text = "\n".join(page_texts)
                
                extraction_time = time.time() - start_time
                print(f"✅ 中医文献提取完成，耗时: {extraction_time:.2f}秒")
                print(f"📝 提取文本长度: {len(text)} 字符")
                
                return text
                
        except Exception as e:
            print(f"❌ 中医文献提取失败: {e}")
            return ""
    
    def clean_tcm_text(self, text: str) -> str:
        """清理中医文本"""
        if not text:
            return ""
        
        # 移除多余空白
        text = ' '.join(text.split())
        
        # 中医文本特殊字符处理
        replacements = {
            '　': ' ',  # 全角空格
            '，': '，',  # 统一逗号
            '。': '。',  # 统一句号
            '；': '；',  # 统一分号
            '：': '：',  # 统一冒号
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def split_tcm_text(self, text: str) -> List[str]:
        """中医文本智能分割"""
        print("🔪 中医文本智能分割...")
        start_time = time.time()
        
        chunks = []
        chunk_size = self.chunk_size
        overlap = chunk_size // 10
        
        # 中医文本分割点（按重要性排序）
        tcm_split_patterns = [
            '。',      # 句号 - 最重要
            '；',      # 分号
            '，',      # 逗号
            '\n\n',    # 段落
            '：',      # 冒号
            '、',      # 顿号
        ]
        
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = start + chunk_size
            if end >= text_length:
                end = text_length
                chunk = text[start:end].strip()
                if len(chunk) > 10:
                    chunks.append(chunk)
                break
            
            # 寻找最佳中医文本分割点
            best_split = end
            for pattern in tcm_split_patterns:
                split_pos = text.rfind(pattern, start + chunk_size//2, end)
                if split_pos != -1:
                    best_split = split_pos + len(pattern)
                    break
            
            chunk = text[start:best_split].strip()
            if len(chunk) > 10:
                chunks.append(chunk)
            
            start = best_split - overlap
            if start < 0:
                start = best_split
        
        split_time = time.time() - start_time
        print(f"✅ 中医文本分割完成，耗时: {split_time:.2f}秒")
        print(f"📊 总块数: {len(chunks)}")
        
        return chunks
    
    def create_tcm_embeddings(self, texts: List[str]) -> np.ndarray:
        """创建中医文本嵌入"""
        print("🔄 生成中医文本向量...")
        start_time = time.time()
        
        from models.model_manager import model_manager
        
        embeddings = []
        batch_size = self.batch_size
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            print(f"   处理批次 {batch_num}/{total_batches} ({len(batch_texts)} 个中医文本)")
            
            try:
                batch_embeddings = []
                for text in batch_texts:
                    embedding = model_manager.get_embedding(text)
                    batch_embeddings.append(embedding)
                
                embeddings.extend(batch_embeddings)
                
                # 每10个批次清理内存
                if batch_num % 10 == 0:
                    gc.collect()
                    
            except Exception as e:
                print(f"   批次 {batch_num} 处理失败: {e}")
                # 使用零向量作为备用
                for _ in batch_texts:
                    embeddings.append(np.zeros(768))
        
        embeddings_array = np.array(embeddings).astype('float32')
        
        embedding_time = time.time() - start_time
        print(f"✅ 中医向量化完成，耗时: {embedding_time:.2f}秒")
        print(f"📊 向量维度: {embeddings_array.shape}")
        
        return embeddings_array
    
    def create_tcm_index(self, embeddings: np.ndarray) -> bool:
        """创建中医知识索引"""
        print("🏗️ 创建中医知识索引...")
        start_time = time.time()
        
        try:
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)
            
            # 归一化向量
            faiss.normalize_L2(embeddings)
            
            # 批量添加向量
            batch_size = 1000
            for i in range(0, len(embeddings), batch_size):
                batch = embeddings[i:i + batch_size]
                self.vector_index.add(batch)
                
                if i % 5000 == 0:
                    print(f"   已添加 {min(i + batch_size, len(embeddings))}/{len(embeddings)} 个向量")
            
            index_time = time.time() - start_time
            print(f"✅ 中医知识索引完成，耗时: {index_time:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 中医索引创建失败: {e}")
            return False
    
    def process_tcm_document(self, pdf_path: str) -> bool:
        """处理中医文档 - 无UI回调版本"""
        print(f"🏥 开始处理中医文档: {pdf_path}")
        total_start_time = time.time()
        
        try:
            # 1. 提取中医文本
            print("📄 第1步: 提取中医文献文本...")
            text = self.extract_tcm_pdf(pdf_path)
            if not text:
                print("❌ 中医文本提取失败")
                return False
            
            # 2. 分割中医文本
            print("🔪 第2步: 智能分割中医文本...")
            chunks = self.split_tcm_text(text)
            if not chunks:
                print("❌ 中医文本分割失败")
                return False
            
            # 智能限制块数量
            max_chunks = 1000
            if len(chunks) > max_chunks:
                print(f"⚠️ 块数量过多({len(chunks)})，智能选择前{max_chunks}个")
                step = len(chunks) // max_chunks
                chunks = chunks[::step][:max_chunks]
            
            # 3. 创建元数据
            print("📋 第3步: 创建中医文档元数据...")
            metadata = []
            for i, chunk in enumerate(chunks):
                metadata.append({
                    "source": pdf_path,
                    "chunk_id": i,
                    "chunk_index": i,
                    "content": chunk,
                    "document_type": "中医经典",
                    "processed_time": time.time()
                })
            
            # 4. 生成中医嵌入
            print("🔄 第4步: 生成中医文本向量嵌入...")
            embeddings = self.create_tcm_embeddings(chunks)
            
            # 5. 创建中医索引
            print("🏗️ 第5步: 创建中医知识索引...")
            if not self.create_tcm_index(embeddings):
                return False
            
            # 6. 保存中医数据
            print("💾 第6步: 保存中医知识库...")
            self.document_chunks = chunks
            self.chunk_metadata = metadata
            self.save_tcm_data()
            
            total_time = time.time() - total_start_time
            
            print(f"🎉 中医文档处理完成！总耗时: {total_time:.2f}秒")
            print(f"📊 处理了 {len(chunks)} 个中医文本块")
            print(f"⚡ 平均速度: {len(chunks)/total_time:.1f} 块/秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 中医文档处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_tcm_data(self):
        """保存中医数据"""
        try:
            index_path = Path(config.VECTOR_DB_PATH)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            # 保存文档块
            with open(index_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # 保存元数据
            with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            print("✅ 中医知识库保存完成")
            
        except Exception as e:
            print(f"❌ 中医数据保存失败: {e}")

# 全局中医RAG处理器
tcm_processor = TCMRAGProcessor()

def process_tcm_pdf(pdf_path: str) -> bool:
    """处理中医PDF的便捷函数"""
    return tcm_processor.process_tcm_document(pdf_path)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
        success = process_tcm_pdf(pdf_file)
        if success:
            print("🎉 中医文档处理成功！")
        else:
            print("❌ 中医文档处理失败")
    else:
        print("用法: python tcm_rag_processor.py <中医PDF文件路径>")
