#!/usr/bin/env python3
"""
测试终极商业化中医RAG系统
快速验证核心功能是否正常工作
"""

import streamlit as st
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试所有必要的导入"""
    st.write("🔍 测试系统导入...")
    
    try:
        import PyPDF2
        st.success("✅ PyPDF2 导入成功")
    except ImportError as e:
        st.error(f"❌ PyPDF2 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        st.success("✅ NumPy 导入成功")
    except ImportError as e:
        st.error(f"❌ NumPy 导入失败: {e}")
        return False
    
    try:
        import faiss
        st.success("✅ FAISS 导入成功")
    except ImportError as e:
        st.error(f"❌ FAISS 导入失败: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        st.success("✅ SentenceTransformers 导入成功")
    except ImportError as e:
        st.error(f"❌ SentenceTransformers 导入失败: {e}")
        return False
    
    try:
        from llama_cpp import Llama
        st.success("✅ llama-cpp-python 导入成功")
    except ImportError as e:
        st.warning(f"⚠️ llama-cpp-python 导入失败: {e}")
        st.info("💡 可以继续测试其他功能，但DeepSeek模型将不可用")
    
    return True

def test_deepseek_model():
    """测试DeepSeek模型"""
    st.write("🧠 测试DeepSeek模型...")
    
    model_path = r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf'
    
    if os.path.exists(model_path):
        st.success(f"✅ DeepSeek模型文件存在")
        st.info(f"📁 路径: {model_path}")
        
        try:
            from llama_cpp import Llama
            with st.spinner("加载DeepSeek模型..."):
                model = Llama(
                    model_path=model_path,
                    n_ctx=1024,  # 减小上下文长度用于测试
                    n_threads=4,
                    n_gpu_layers=10,  # 减少GPU层数用于测试
                    verbose=False
                )
            st.success("✅ DeepSeek模型加载成功！")
            
            # 测试生成
            test_prompt = "中医是什么？"
            with st.spinner("测试模型生成..."):
                response = model(test_prompt, max_tokens=50, temperature=0.7)
                st.success("✅ 模型生成测试成功！")
                st.write("🤖 测试回答:", response['choices'][0]['text'])
            
            return True
            
        except Exception as e:
            st.error(f"❌ DeepSeek模型加载失败: {e}")
            return False
    else:
        st.error(f"❌ DeepSeek模型文件不存在: {model_path}")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    st.write("📊 测试嵌入模型...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        with st.spinner("加载嵌入模型..."):
            model = SentenceTransformer('moka-ai/m3e-base')
        
        st.success("✅ 嵌入模型加载成功！")
        
        # 测试编码
        test_text = "中医理论"
        with st.spinner("测试文本编码..."):
            embedding = model.encode([test_text])[0]
        
        st.success(f"✅ 文本编码成功！向量维度: {len(embedding)}")
        return True
        
    except Exception as e:
        st.error(f"❌ 嵌入模型测试失败: {e}")
        return False

def test_pdf_processing():
    """测试PDF处理"""
    st.write("📄 测试PDF处理...")
    
    # 检查是否有PDF文件
    documents_dir = Path("./documents")
    if documents_dir.exists():
        pdf_files = list(documents_dir.glob("*.pdf"))
        if pdf_files:
            st.success(f"✅ 找到 {len(pdf_files)} 个PDF文件")
            
            # 测试读取第一个PDF
            try:
                import PyPDF2
                pdf_file = pdf_files[0]
                
                with open(pdf_file, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    page_count = len(pdf_reader.pages)
                    
                    if page_count > 0:
                        first_page_text = pdf_reader.pages[0].extract_text()
                        st.success(f"✅ PDF读取成功！页数: {page_count}")
                        st.write(f"📖 第一页内容预览: {first_page_text[:200]}...")
                        return True
                    else:
                        st.warning("⚠️ PDF文件为空")
                        return False
                        
            except Exception as e:
                st.error(f"❌ PDF处理失败: {e}")
                return False
        else:
            st.warning("⚠️ documents目录中没有PDF文件")
            return False
    else:
        st.warning("⚠️ documents目录不存在")
        return False

def test_online_crawler():
    """测试在线爬虫"""
    st.write("🌐 测试在线爬虫...")
    
    try:
        from online_medical_crawler import search_online_medical_resources
        
        with st.spinner("测试在线搜索..."):
            results = search_online_medical_resources("湿气", max_results=1)
        
        if results:
            st.success(f"✅ 在线搜索成功！找到 {len(results)} 个结果")
            result = results[0]
            st.write(f"📖 标题: {result['title']}")
            st.write(f"🔗 URL: {result['url']}")
            st.write(f"📄 内容预览: {result['content'][:200]}...")
            return True
        else:
            st.warning("⚠️ 在线搜索没有返回结果")
            return False
            
    except Exception as e:
        st.error(f"❌ 在线爬虫测试失败: {e}")
        return False

def test_vector_database():
    """测试向量数据库"""
    st.write("🗄️ 测试向量数据库...")
    
    vector_db_path = Path("./ultimate_vector_db")
    
    if vector_db_path.exists():
        index_file = vector_db_path / "index.faiss"
        chunks_file = vector_db_path / "chunks.pkl"
        metadata_file = vector_db_path / "metadata.pkl"
        
        if all(f.exists() for f in [index_file, chunks_file, metadata_file]):
            st.success("✅ 向量数据库文件完整")
            
            try:
                import faiss
                import pickle
                
                # 加载索引
                index = faiss.read_index(str(index_file))
                st.success(f"✅ FAISS索引加载成功！向量数量: {index.ntotal}")
                
                # 加载文档块
                with open(chunks_file, 'rb') as f:
                    chunks = pickle.load(f)
                st.success(f"✅ 文档块加载成功！数量: {len(chunks)}")
                
                return True
                
            except Exception as e:
                st.error(f"❌ 向量数据库加载失败: {e}")
                return False
        else:
            st.warning("⚠️ 向量数据库文件不完整")
            return False
    else:
        st.warning("⚠️ 向量数据库目录不存在")
        return False

def main():
    """主测试函数"""
    st.title("🧪 终极商业化中医RAG系统 - 功能测试")
    st.markdown("### 🔍 系统功能全面检测")
    
    st.info("💡 这个测试页面将验证系统的各个核心功能是否正常工作")
    
    # 创建测试按钮
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔍 测试导入", type="primary"):
            test_imports()
    
    with col2:
        if st.button("🧠 测试DeepSeek"):
            test_deepseek_model()
    
    with col3:
        if st.button("📊 测试嵌入模型"):
            test_embedding_model()
    
    col4, col5, col6 = st.columns(3)
    
    with col4:
        if st.button("📄 测试PDF处理"):
            test_pdf_processing()
    
    with col5:
        if st.button("🌐 测试在线爬虫"):
            test_online_crawler()
    
    with col6:
        if st.button("🗄️ 测试向量数据库"):
            test_vector_database()
    
    st.divider()
    
    # 全面测试按钮
    if st.button("🚀 运行全面测试", type="primary"):
        st.markdown("## 🧪 全面系统测试")
        
        results = {}
        
        with st.expander("📋 测试结果详情", expanded=True):
            results['imports'] = test_imports()
            results['embedding'] = test_embedding_model()
            results['pdf'] = test_pdf_processing()
            results['online'] = test_online_crawler()
            results['vector_db'] = test_vector_database()
            results['deepseek'] = test_deepseek_model()
        
        # 总结
        st.markdown("## 📊 测试总结")
        
        passed = sum(results.values())
        total = len(results)
        
        if passed == total:
            st.success(f"🎉 所有测试通过！({passed}/{total})")
            st.balloons()
        elif passed >= total * 0.7:
            st.warning(f"⚠️ 大部分功能正常 ({passed}/{total})")
        else:
            st.error(f"❌ 多个功能存在问题 ({passed}/{total})")
        
        # 详细结果
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            st.write(f"{status} {test_name}: {'通过' if result else '失败'}")
        
        if passed >= total * 0.7:
            st.info("💡 系统基本可用，可以启动主应用进行测试")
            if st.button("🚀 启动主应用"):
                st.markdown("请运行以下命令启动主应用：")
                st.code("streamlit run ultimate_commercial_tcm.py")

if __name__ == "__main__":
    main()
