#!/usr/bin/env python3
"""
Elasticsearch MCP服务器
基于真正的MCP协议实现的Elasticsearch检索服务
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path
import sys

# MCP相关导入 - 使用正确的导入路径
try:
    from mcp.server import Server
    from mcp.types import (
        Tool,
        TextContent,
        LoggingLevel
    )
    from mcp import stdio_server
    MCP_AVAILABLE = True
    print("✅ MCP SDK已正确加载")
except ImportError as e:
    MCP_AVAILABLE = False
    print(f"⚠️ MCP SDK导入失败: {e}")

# Elasticsearch导入
try:
    from elasticsearch import Elasticsearch, AsyncElasticsearch
    from elasticsearch.helpers import bulk
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    print("⚠️ Elasticsearch未安装，请运行: pip install elasticsearch")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ElasticsearchMCPServer:
    """Elasticsearch MCP服务器"""
    
    def __init__(self):
        self.es_client = None
        self.async_es_client = None
        self.initialized = False
        
        # Elasticsearch配置
        self.es_config = {
            'host': 'localhost',
            'port': 9200,
            'index_name': 'tcm_knowledge',
            'timeout': 30
        }
        
        # MCP服务器配置
        self.server_name = "elasticsearch-mcp-server"
        self.server_version = "1.0.0"
        
        # 工具定义
        self.tools = [
            Tool(
                name="search_elasticsearch",
                description="在Elasticsearch中搜索中医知识",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询"
                        },
                        "size": {
                            "type": "integer",
                            "description": "返回结果数量",
                            "default": 10
                        },
                        "index": {
                            "type": "string",
                            "description": "索引名称",
                            "default": "tcm_knowledge"
                        }
                    },
                    "required": ["query"]
                }
            ),
            Tool(
                name="index_document",
                description="向Elasticsearch索引文档",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "document": {
                            "type": "object",
                            "description": "要索引的文档"
                        },
                        "index": {
                            "type": "string",
                            "description": "索引名称",
                            "default": "tcm_knowledge"
                        },
                        "doc_id": {
                            "type": "string",
                            "description": "文档ID（可选）"
                        }
                    },
                    "required": ["document"]
                }
            ),
            Tool(
                name="create_index",
                description="创建Elasticsearch索引",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "index_name": {
                            "type": "string",
                            "description": "索引名称"
                        },
                        "mapping": {
                            "type": "object",
                            "description": "索引映射配置"
                        }
                    },
                    "required": ["index_name"]
                }
            ),
            Tool(
                name="get_index_stats",
                description="获取索引统计信息",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "index": {
                            "type": "string",
                            "description": "索引名称",
                            "default": "tcm_knowledge"
                        }
                    }
                }
            )
        ]
    
    async def initialize_elasticsearch(self) -> bool:
        """初始化Elasticsearch连接"""
        try:
            if not ELASTICSEARCH_AVAILABLE:
                logger.error("Elasticsearch未安装")
                return False
            
            # 创建同步客户端
            self.es_client = Elasticsearch(
                [f"http://{self.es_config['host']}:{self.es_config['port']}"],
                timeout=self.es_config['timeout']
            )
            
            # 创建异步客户端
            self.async_es_client = AsyncElasticsearch(
                [f"http://{self.es_config['host']}:{self.es_config['port']}"],
                timeout=self.es_config['timeout']
            )
            
            # 测试连接
            if await self.async_es_client.ping():
                logger.info("✅ Elasticsearch连接成功")
                
                # 确保索引存在
                await self._ensure_index_exists()
                
                self.initialized = True
                return True
            else:
                logger.error("❌ Elasticsearch连接失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ Elasticsearch初始化失败: {e}")
            return False
    
    async def _ensure_index_exists(self):
        """确保索引存在"""
        try:
            index_name = self.es_config['index_name']
            
            if not await self.async_es_client.indices.exists(index=index_name):
                # 创建索引映射
                mapping = {
                    "mappings": {
                        "properties": {
                            "title": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "content": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            },
                            "source": {
                                "type": "keyword"
                            },
                            "category": {
                                "type": "keyword"
                            },
                            "tags": {
                                "type": "keyword"
                            },
                            "timestamp": {
                                "type": "date"
                            },
                            "embedding": {
                                "type": "dense_vector",
                                "dims": 768
                            }
                        }
                    },
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0,
                        "analysis": {
                            "analyzer": {
                                "ik_max_word": {
                                    "type": "ik_max_word"
                                },
                                "ik_smart": {
                                    "type": "ik_smart"
                                }
                            }
                        }
                    }
                }
                
                await self.async_es_client.indices.create(
                    index=index_name,
                    body=mapping
                )
                logger.info(f"✅ 创建索引: {index_name}")
            else:
                logger.info(f"✅ 索引已存在: {index_name}")
                
        except Exception as e:
            logger.warning(f"⚠️ 索引创建失败: {e}")
    
    async def search_elasticsearch(self, query: str, size: int = 10, index: str = None) -> List[Dict]:
        """在Elasticsearch中搜索"""
        try:
            if not self.initialized:
                raise Exception("Elasticsearch未初始化")
            
            index_name = index or self.es_config['index_name']
            
            # 构建搜索查询
            search_body = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^2", "content"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            },
                            {
                                "match_phrase": {
                                    "content": {
                                        "query": query,
                                        "boost": 2
                                    }
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                },
                "highlight": {
                    "fields": {
                        "content": {},
                        "title": {}
                    }
                },
                "size": size
            }
            
            # 执行搜索
            response = await self.async_es_client.search(
                index=index_name,
                body=search_body
            )
            
            # 处理结果
            results = []
            for hit in response['hits']['hits']:
                result = {
                    'id': hit['_id'],
                    'score': hit['_score'],
                    'source': hit['_source'],
                    'highlight': hit.get('highlight', {})
                }
                results.append(result)
            
            logger.info(f"🔍 Elasticsearch搜索完成: {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ Elasticsearch搜索失败: {e}")
            raise
    
    async def index_document(self, document: Dict, index: str = None, doc_id: str = None) -> Dict:
        """索引文档到Elasticsearch"""
        try:
            if not self.initialized:
                raise Exception("Elasticsearch未初始化")
            
            index_name = index or self.es_config['index_name']
            
            # 添加时间戳
            if 'timestamp' not in document:
                from datetime import datetime
                document['timestamp'] = datetime.now().isoformat()
            
            # 索引文档
            response = await self.async_es_client.index(
                index=index_name,
                id=doc_id,
                body=document
            )
            
            logger.info(f"✅ 文档索引成功: {response['_id']}")
            return response
            
        except Exception as e:
            logger.error(f"❌ 文档索引失败: {e}")
            raise
    
    async def create_index(self, index_name: str, mapping: Dict = None) -> Dict:
        """创建索引"""
        try:
            if not self.initialized:
                raise Exception("Elasticsearch未初始化")
            
            # 使用默认映射如果未提供
            if not mapping:
                mapping = {
                    "mappings": {
                        "properties": {
                            "title": {"type": "text"},
                            "content": {"type": "text"},
                            "source": {"type": "keyword"},
                            "timestamp": {"type": "date"}
                        }
                    }
                }
            
            response = await self.async_es_client.indices.create(
                index=index_name,
                body=mapping
            )
            
            logger.info(f"✅ 索引创建成功: {index_name}")
            return response
            
        except Exception as e:
            logger.error(f"❌ 索引创建失败: {e}")
            raise
    
    async def get_index_stats(self, index: str = None) -> Dict:
        """获取索引统计信息"""
        try:
            if not self.initialized:
                raise Exception("Elasticsearch未初始化")
            
            index_name = index or self.es_config['index_name']
            
            # 获取索引统计
            stats = await self.async_es_client.indices.stats(index=index_name)
            count = await self.async_es_client.count(index=index_name)
            
            result = {
                'index_name': index_name,
                'document_count': count['count'],
                'index_size': stats['indices'][index_name]['total']['store']['size_in_bytes'],
                'status': 'healthy' if self.initialized else 'unhealthy'
            }
            
            logger.info(f"📊 索引统计: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取索引统计失败: {e}")
            raise

# 全局服务实例
es_mcp_server = ElasticsearchMCPServer()

async def handle_tool_call(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """处理工具调用"""
    try:
        if name == "search_elasticsearch":
            query = arguments.get("query", "")
            size = arguments.get("size", 10)
            index = arguments.get("index")
            
            results = await es_mcp_server.search_elasticsearch(query, size, index)
            
            return [TextContent(
                type="text",
                text=json.dumps(results, ensure_ascii=False, indent=2)
            )]
        
        elif name == "index_document":
            document = arguments.get("document", {})
            index = arguments.get("index")
            doc_id = arguments.get("doc_id")
            
            result = await es_mcp_server.index_document(document, index, doc_id)
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
        
        elif name == "create_index":
            index_name = arguments.get("index_name", "")
            mapping = arguments.get("mapping")
            
            result = await es_mcp_server.create_index(index_name, mapping)
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
        
        elif name == "get_index_stats":
            index = arguments.get("index")
            
            result = await es_mcp_server.get_index_stats(index)
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
        
        else:
            raise ValueError(f"未知工具: {name}")
            
    except Exception as e:
        logger.error(f"工具调用失败 {name}: {e}")
        return [TextContent(
            type="text",
            text=f"错误: {str(e)}"
        )]

async def main():
    """主函数"""
    if not MCP_AVAILABLE:
        print("❌ MCP SDK未安装，无法启动MCP服务器")
        print("请运行: pip install mcp")
        return
    
    # 初始化Elasticsearch
    if not await es_mcp_server.initialize_elasticsearch():
        print("❌ Elasticsearch初始化失败")
        return
    
    # 创建MCP服务器
    server = Server(es_mcp_server.server_name)
    
    @server.list_tools()
    async def handle_list_tools() -> List[Tool]:
        """列出可用工具"""
        return es_mcp_server.tools
    
    @server.call_tool()
    async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理工具调用"""
        return await handle_tool_call(name, arguments)
    
    # 启动服务器
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name=es_mcp_server.server_name,
                server_version=es_mcp_server.server_version,
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities={}
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())
