"""
简化的RAG系统 - 专为现代化聊天界面设计
"""
import os
import pickle
import json
import re
from pathlib import Path
from typing import List, Dict, Tuple, Any
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

class RAGSystem:
    def __init__(self, model_name: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"):
        """初始化RAG系统"""
        self.model_name = model_name
        self.model = None
        self.index = None
        self.chunks = []
        self.metadata = []
        self.vector_db_path = Path("vector_db")
        self.vector_db_path.mkdir(exist_ok=True)
        
        # 初始化模型
        self._load_model()
        
        # 中医知识库
        self.tcm_knowledge = {
            "湿气": {
                "definition": "湿气是中医理论中的重要概念，指人体内水液代谢失常所产生的病理产物。",
                "characteristics": "湿性重浊、黏腻，易阻遏气机，损伤阳气。",
                "types": "湿邪可分为外湿和内湿，外湿多因居处潮湿、涉水淋雨等外界环境因素所致；内湿多由脾胃功能失调，水液代谢障碍所形成。",
                "symptoms": "湿气重的表现包括：身体沉重、头昏脑胀、四肢困倦、胸闷腹胀、食欲不振、大便黏腻等。",
                "treatment": "治疗原则以健脾化湿、理气化湿为主，常用方剂如平胃散、二陈汤等。"
            },
            "气血": {
                "definition": "气血是中医学的核心概念，气为血之帅，血为气之母。",
                "qi_functions": "气具有推动、温煦、防御、固摄、气化等功能。",
                "blood_functions": "血具有濡养、滋润等作用。",
                "relationship": "气血充足则脏腑功能正常，气血不足则百病丛生。",
                "deficiency_symptoms": "气虚表现为乏力、气短、声低懒言；血虚表现为面色苍白、头晕心悸、失眠多梦。"
            },
            "阴阳": {
                "definition": "阴阳学说是中医理论的哲学基础，认为阴阳是宇宙间相互关联的两个方面。",
                "balance": "在人体中，阴阳的相对平衡是健康的标志，阴阳失调则导致疾病。",
                "characteristics": "阴主静、主寒、主下、主内；阳主动、主热、主上、主外。",
                "treatment_principle": "中医诊治疾病的根本原则是调整阴阳，恢复其相对平衡。"
            },
            "五脏六腑": {
                "five_organs": "五脏包括心、肝、脾、肺、肾，主要功能是化生和储藏精气。",
                "six_bowels": "六腑包括胆、胃、小肠、大肠、膀胱、三焦，主要功能是受纳和传化水谷。",
                "relationship": "脏腑之间通过经络相互联系，形成统一的功能整体。"
            },
            "经络": {
                "definition": "经络是人体内气血运行的通道，包括经脉和络脉。",
                "twelve_meridians": "十二正经是经络系统的主体，包括手三阴、手三阳、足三阴、足三阳。",
                "functions": "经络具有运行气血、联络脏腑、沟通内外、调节机能的作用。"
            }
        }
    
    def _load_model(self):
        """加载嵌入模型"""
        try:
            print("🔄 正在加载嵌入模型...")
            self.model = SentenceTransformer(self.model_name)
            print("✅ 嵌入模型加载成功")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            # 使用备用模型
            try:
                self.model = SentenceTransformer("all-MiniLM-L6-v2")
                print("✅ 备用模型加载成功")
            except Exception as e2:
                print(f"❌ 备用模型也加载失败: {e2}")
                self.model = None
    
    def build_vector_database(self):
        """构建向量数据库"""
        try:
            # 收集所有文档块
            all_chunks = []
            all_metadata = []
            
            # 从uploads目录处理文档
            uploads_dir = Path("uploads")
            if uploads_dir.exists():
                from document_processor import DocumentProcessor
                processor = DocumentProcessor()
                
                for file_path in uploads_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in ['.pdf', '.txt', '.doc', '.docx']:
                        try:
                            chunks = processor.process_document(str(file_path))
                            for chunk in chunks:
                                all_chunks.append(chunk)
                                all_metadata.append({
                                    'source': str(file_path),
                                    'type': 'document'
                                })
                        except Exception as e:
                            print(f"⚠️ 处理文档 {file_path} 失败: {e}")
            
            # 添加内置中医知识
            for topic, content in self.tcm_knowledge.items():
                for key, value in content.items():
                    chunk_text = f"{topic} - {key}: {value}"
                    all_chunks.append(chunk_text)
                    all_metadata.append({
                        'source': f'内置知识库 - {topic}',
                        'type': 'builtin',
                        'topic': topic,
                        'aspect': key
                    })
            
            if not all_chunks:
                print("⚠️ 没有找到可处理的文档")
                return False
            
            # 生成嵌入向量
            if not self.model:
                print("❌ 嵌入模型未加载")
                return False
            
            print(f"🔄 正在为 {len(all_chunks)} 个文档块生成嵌入向量...")
            embeddings = self.model.encode(all_chunks, show_progress_bar=True)
            
            # 构建FAISS索引
            dimension = embeddings.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
            
            # 标准化向量
            faiss.normalize_L2(embeddings)
            self.index.add(embeddings.astype('float32'))
            
            # 保存数据
            self.chunks = all_chunks
            self.metadata = all_metadata
            
            # 持久化
            self._save_vector_database()
            
            print(f"✅ 向量数据库构建完成，包含 {len(all_chunks)} 个文档块")
            return True
            
        except Exception as e:
            print(f"❌ 向量数据库构建失败: {e}")
            return False
    
    def _save_vector_database(self):
        """保存向量数据库"""
        try:
            # 保存FAISS索引
            faiss.write_index(self.index, str(self.vector_db_path / "index.faiss"))
            
            # 保存文档块和元数据
            with open(self.vector_db_path / "chunks.pkl", "wb") as f:
                pickle.dump(self.chunks, f)
            
            with open(self.vector_db_path / "metadata.pkl", "wb") as f:
                pickle.dump(self.metadata, f)
            
            print("✅ 向量数据库保存成功")
            
        except Exception as e:
            print(f"❌ 向量数据库保存失败: {e}")
    
    def load_vector_database(self):
        """加载向量数据库"""
        try:
            # 检查文件是否存在
            index_path = self.vector_db_path / "index.faiss"
            chunks_path = self.vector_db_path / "chunks.pkl"
            metadata_path = self.vector_db_path / "metadata.pkl"
            
            if not all(p.exists() for p in [index_path, chunks_path, metadata_path]):
                print("⚠️ 向量数据库文件不完整，将重新构建")
                return self.build_vector_database()
            
            # 加载FAISS索引
            self.index = faiss.read_index(str(index_path))
            
            # 加载文档块和元数据
            with open(chunks_path, "rb") as f:
                self.chunks = pickle.load(f)
            
            with open(metadata_path, "rb") as f:
                self.metadata = pickle.load(f)
            
            print(f"✅ 向量数据库加载成功，包含 {len(self.chunks)} 个文档块")
            return True
            
        except Exception as e:
            print(f"❌ 向量数据库加载失败: {e}")
            return False
    
    def query(self, question: str, top_k: int = 3) -> Tuple[str, List[Dict[str, Any]]]:
        """查询RAG系统"""
        try:
            # 检查系统状态
            if not self.model:
                return self._fallback_response(question)
            
            if not self.index or not self.chunks:
                # 尝试加载或构建数据库
                if not self.load_vector_database():
                    return self._fallback_response(question)
            
            # 生成查询向量
            query_embedding = self.model.encode([question])
            faiss.normalize_L2(query_embedding)
            
            # 搜索相似文档
            scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
            
            # 收集相关文档
            relevant_docs = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(self.chunks) and score > 0.3:  # 相似度阈值
                    relevant_docs.append({
                        'content': self.chunks[idx],
                        'metadata': self.metadata[idx],
                        'score': float(score),
                        'rank': i + 1
                    })
            
            # 生成回答
            response = self._generate_response(question, relevant_docs)
            
            # 格式化来源信息
            sources = []
            for doc in relevant_docs:
                sources.append({
                    'source': doc['metadata']['source'],
                    'content': doc['content'][:200] + "..." if len(doc['content']) > 200 else doc['content'],
                    'score': doc['score'],
                    'type': doc['metadata'].get('type', 'unknown')
                })
            
            return response, sources
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return self._fallback_response(question)
    
    def _generate_response(self, question: str, relevant_docs: List[Dict]) -> str:
        """生成回答"""
        if not relevant_docs:
            return self._fallback_response(question)[0]
        
        # 分析问题类型
        question_lower = question.lower()
        
        # 构建回答
        response_parts = []
        
        # 添加问题确认
        response_parts.append(f"## 关于「{question}」的解答\n")
        
        # 根据相关文档生成回答
        if any("湿气" in doc['content'] for doc in relevant_docs):
            response_parts.append("### 🌿 湿气相关知识\n")
        elif any("气血" in doc['content'] for doc in relevant_docs):
            response_parts.append("### 🫖 气血理论\n")
        elif any("阴阳" in doc['content'] for doc in relevant_docs):
            response_parts.append("### ☯️ 阴阳学说\n")
        else:
            response_parts.append("### 📚 中医知识\n")
        
        # 整合相关信息
        for i, doc in enumerate(relevant_docs[:2], 1):
            content = doc['content']
            source = doc['metadata']['source']
            
            response_parts.append(f"**{i}. 来源：{source}**\n")
            response_parts.append(f"{content}\n\n")
        
        # 添加总结和建议
        response_parts.append("### 💡 要点总结\n")
        response_parts.append("根据上述中医理论，建议您：\n")
        response_parts.append("- 深入学习相关中医基础理论\n")
        response_parts.append("- 结合个人体质特点进行调养\n")
        response_parts.append("- 在专业中医师指导下实践\n\n")
        
        # 添加免责声明
        response_parts.append("### ⚠️ 重要提醒\n")
        response_parts.append("本回答仅供中医文化学习参考，不构成医疗建议。如有健康问题，请咨询专业医疗机构。\n")
        
        return "".join(response_parts)
    
    def _fallback_response(self, question: str) -> Tuple[str, List[Dict]]:
        """备用回答"""
        # 简单的关键词匹配
        question_lower = question.lower()
        
        if "湿气" in question_lower:
            content = self.tcm_knowledge["湿气"]
            response = f"""## 关于湿气的中医知识

### 🌿 湿气的基本概念
{content['definition']}

### 📋 湿气的特点
{content['characteristics']}

### 🔍 湿气的分类
{content['types']}

### 💡 常见表现
{content['symptoms']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
            
            sources = [{'source': '内置中医知识库', 'content': content['definition'], 'score': 1.0, 'type': 'builtin'}]
            return response, sources
        
        elif "气血" in question_lower:
            content = self.tcm_knowledge["气血"]
            response = f"""## 关于气血的中医理论

### 🫖 气血的基本概念
{content['definition']}

### ⚡ 气的功能
{content['qi_functions']}

### 🩸 血的作用
{content['blood_functions']}

### 🔄 气血关系
{content['relationship']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
            
            sources = [{'source': '内置中医知识库', 'content': content['definition'], 'score': 1.0, 'type': 'builtin'}]
            return response, sources
        
        else:
            response = """## 🤖 智能助手回复

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库
- 查询中医基础理论相关内容

### 📚 我可以帮您了解：
- 湿气调理相关知识
- 气血养生理论
- 阴阳平衡概念
- 五脏六腑功能
- 经络穴位基础

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。"""
            
            return response, []
