{
  "average_score": 96.0,
  "total_tests": 5,
  "test_results": [
    {
      "query": "肾虚脾虚怎么治疗",
      "score": 100,
      "results_count": 5,
      "keyword_matches": 5,
      "expected_keywords": 5,
      "relevance": "good"
    },
    {
      "query": "栀子甘草豉汤的功效",
      "score": 100,
      "results_count": 5,
      "keyword_matches": 9,
      "expected_keywords": 4,
      "relevance": "good"
    },
    {
      "query": "湿气重的症状有哪些",
      "score": 90,
      "results_count": 5,
      "keyword_matches": 6,
      "expected_keywords": 3,
      "relevance": "good"
    },
    {
      "query": "气血不足如何调理",
      "score": 90,
      "results_count": 5,
      "keyword_matches": 6,
      "expected_keywords": 5,
      "relevance": "good"
    },
    {
      "query": "黄帝内经关于阴阳的理论",
      "score": 100,
      "results_count": 5,
      "keyword_matches": 12,
      "expected_keywords": 3,
      "relevance": "good"
    }
  ],
  "retriever_stats": {
    "total_chunks": 290,
    "total_metadata": 290,
    "vector_index_size": 52,
    "tfidf_features": 5000,
    "config": {
      "CHUNK_SIZE": 500,
      "CHUNK_OVERLAP": 100,
      "TOP_K_RETRIEVAL": 5,
      "SIMILARITY_THRESHOLD": 0.35,
      "MIN_RELEVANCE_SCORE": 0.35,
      "RERANK_THRESHOLD": 0.5,
      "EMBEDDING_MODEL": "moka-ai/m3e-base",
      "VECTOR_DB_PATH": 