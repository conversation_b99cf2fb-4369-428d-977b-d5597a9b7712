"""
快速启动家庭中医智能助手
"""
import subprocess
import sys
from pathlib import Path

def main():
    print("🏥 家庭中医智能助手")
    print("="*50)
    
    # 创建必要目录
    directories = [
        "family_tcm_knowledge",
        "family_tcm_knowledge/documents",
        "user_logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    
    print("\n🚀 启动Web界面...")
    print("📝 访问地址: http://localhost:8511")
    print("🌐 局域网访问: http://您的IP地址:8511")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "family_web_interface.py",
            "--server.address", "0.0.0.0",
            "--server.port", "8511",
            "--server.headless", "true",
            "--server.maxUploadSize", "200",
            "--theme.primaryColor", "#4CAF50"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用家庭中医智能助手！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
