"""
现代化中医RAG系统 - FastAPI后端
支持聊天界面、语音交互、文档上传
"""
import os
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
import uuid

from fastapi import FastAPI, File, UploadFile, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, StreamingResponse
from pydantic import BaseModel
import uvicorn
from sse_starlette.sse import EventSourceResponse

# 导入我们的RAG组件
from rag_system import RAGSystem
from document_processor import DocumentProcessor

# 初始化FastAPI应用
app = FastAPI(
    title="中医智能助手 API",
    description="现代化中医RAG系统，支持聊天界面和语音交互",
    version="2.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 全局变量
rag_system = None
doc_processor = None
active_connections: List[WebSocket] = []

# Pydantic模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_voice: Optional[bool] = False

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str

class DocumentInfo(BaseModel):
    filename: str
    size: int
    upload_time: str
    status: str

# 初始化系统
async def initialize_system():
    """初始化RAG系统和文档处理器"""
    global rag_system, doc_processor
    
    try:
        # 创建必要的目录
        Path("uploads").mkdir(exist_ok=True)
        Path("vector_db").mkdir(exist_ok=True)
        Path("sessions").mkdir(exist_ok=True)
        
        # 初始化文档处理器
        doc_processor = DocumentProcessor()
        
        # 初始化RAG系统
        rag_system = RAGSystem()
        
        # 尝试加载现有的向量数据库
        if Path("vector_db/chunks.pkl").exists():
            rag_system.load_vector_database()
            print("✅ 向量数据库加载成功")
        else:
            print("⚠️ 未找到现有向量数据库，请上传文档")
            
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    await initialize_system()

# API路由
@app.get("/")
async def read_root():
    """返回前端页面"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content)

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "rag_system": rag_system is not None,
        "doc_processor": doc_processor is not None
    }

@app.post("/api/upload", response_model=Dict[str, Any])
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    if not doc_processor:
        raise HTTPException(status_code=500, detail="文档处理器未初始化")
    
    results = []
    
    for file in files:
        try:
            # 保存文件
            file_path = Path("uploads") / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # 处理文档
            chunks = doc_processor.process_document(str(file_path))
            
            results.append({
                "filename": file.filename,
                "size": len(content),
                "chunks": len(chunks),
                "status": "success"
            })
            
        except Exception as e:
            results.append({
                "filename": file.filename,
                "status": "error",
                "error": str(e)
            })
    
    # 重建向量数据库
    try:
        if rag_system:
            rag_system.build_vector_database()
            print("✅ 向量数据库重建成功")
    except Exception as e:
        print(f"⚠️ 向量数据库重建失败: {e}")
    
    return {"results": results, "total_files": len(files)}

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """聊天接口"""
    if not rag_system:
        raise HTTPException(status_code=500, detail="RAG系统未初始化")
    
    try:
        # 生成会话ID
        session_id = chat_message.session_id or str(uuid.uuid4())
        
        # 查询RAG系统
        response, sources = rag_system.query(chat_message.message)
        
        # 保存会话记录
        session_data = {
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "user_message": chat_message.message,
            "assistant_response": response,
            "sources": sources,
            "use_voice": chat_message.use_voice
        }
        
        session_file = Path("sessions") / f"{session_id}.jsonl"
        with open(session_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(session_data, ensure_ascii=False) + "\n")
        
        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.get("/api/chat/stream")
async def chat_stream(message: str, session_id: Optional[str] = None):
    """流式聊天接口 (SSE)"""
    if not rag_system:
        raise HTTPException(status_code=500, detail="RAG系统未初始化")
    
    async def generate():
        try:
            # 模拟流式响应
            session_id_actual = session_id or str(uuid.uuid4())
            
            yield f"data: {json.dumps({'type': 'session_id', 'data': session_id_actual}, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'status', 'data': '正在搜索相关文档...'}, ensure_ascii=False)}\n\n"
            
            # 查询RAG系统
            response, sources = rag_system.query(message)
            
            yield f"data: {json.dumps({'type': 'status', 'data': '正在生成回答...'}, ensure_ascii=False)}\n\n"
            
            # 分段发送响应
            words = response.split()
            current_text = ""
            
            for i, word in enumerate(words):
                current_text += word + " "
                if i % 3 == 0:  # 每3个词发送一次
                    yield f"data: {json.dumps({'type': 'text', 'data': current_text}, ensure_ascii=False)}\n\n"
                    await asyncio.sleep(0.1)  # 模拟打字效果
            
            # 发送完整响应和来源
            yield f"data: {json.dumps({'type': 'complete', 'data': response}, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'sources', 'data': sources}, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'done'}, ensure_ascii=False)}\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'data': str(e)}, ensure_ascii=False)}\n\n"
    
    return EventSourceResponse(generate())

@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    """WebSocket聊天接口"""
    await websocket.accept()
    active_connections.append(websocket)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data.get("type") == "chat":
                message = message_data.get("message", "")
                session_id = message_data.get("session_id", str(uuid.uuid4()))
                
                # 发送状态更新
                await websocket.send_text(json.dumps({
                    "type": "status",
                    "data": "正在处理您的问题..."
                }, ensure_ascii=False))
                
                # 查询RAG系统
                if rag_system:
                    response, sources = rag_system.query(message)
                    
                    # 发送响应
                    await websocket.send_text(json.dumps({
                        "type": "response",
                        "data": {
                            "response": response,
                            "sources": sources,
                            "session_id": session_id,
                            "timestamp": datetime.now().isoformat()
                        }
                    }, ensure_ascii=False))
                else:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "data": "RAG系统未初始化"
                    }, ensure_ascii=False))
                    
    except WebSocketDisconnect:
        active_connections.remove(websocket)

@app.get("/api/sessions/{session_id}")
async def get_session(session_id: str):
    """获取会话历史"""
    session_file = Path("sessions") / f"{session_id}.jsonl"
    
    if not session_file.exists():
        raise HTTPException(status_code=404, detail="会话不存在")
    
    messages = []
    with open(session_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.strip():
                messages.append(json.loads(line))
    
    return {"session_id": session_id, "messages": messages}

@app.get("/api/documents")
async def list_documents():
    """列出已上传的文档"""
    uploads_dir = Path("uploads")
    documents = []
    
    if uploads_dir.exists():
        for file_path in uploads_dir.iterdir():
            if file_path.is_file():
                stat = file_path.stat()
                documents.append({
                    "filename": file_path.name,
                    "size": stat.st_size,
                    "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    "status": "processed"
                })
    
    return {"documents": documents, "total": len(documents)}

@app.delete("/api/documents/{filename}")
async def delete_document(filename: str):
    """删除文档"""
    file_path = Path("uploads") / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文档不存在")
    
    try:
        file_path.unlink()
        
        # 重建向量数据库
        if rag_system:
            rag_system.build_vector_database()
        
        return {"message": f"文档 {filename} 删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
