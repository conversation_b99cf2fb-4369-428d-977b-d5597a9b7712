#!/usr/bin/env python3
"""
DeepSeek依赖安装脚本
解决llama-cpp-python安装问题
"""

import subprocess
import sys
import os
import platform
import requests
from pathlib import Path

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤{step}: {message}")
    print('='*60)

def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本过低，需要3.8+")
        return False

def check_system_info():
    """检查系统信息"""
    print_step(2, "检查系统信息")
    
    system = platform.system()
    machine = platform.machine()
    print(f"操作系统: {system}")
    print(f"架构: {machine}")
    
    return system, machine

def install_build_tools():
    """安装构建工具"""
    print_step(3, "检查构建工具")
    
    # 检查cmake
    try:
        result = subprocess.run(['cmake', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CMake已安装")
        else:
            print("❌ CMake未安装")
    except FileNotFoundError:
        print("❌ CMake未安装")
        print("💡 请从 https://cmake.org/download/ 下载安装CMake")
    
    # Windows特殊处理
    if platform.system() == "Windows":
        print("\n🔧 Windows系统需要Visual Studio Build Tools")
        print("💡 请安装以下之一:")
        print("   1. Visual Studio Community (免费)")
        print("   2. Visual Studio Build Tools")
        print("   3. 或使用预编译的wheel文件")

def try_precompiled_wheels():
    """尝试安装预编译的wheel文件"""
    print_step(4, "尝试安装预编译版本")
    
    # Python 3.13的预编译wheel可能不可用，尝试通用方法
    commands = [
        # 方法1: 尝试CPU版本
        [sys.executable, "-m", "pip", "install", "llama-cpp-python", "--no-cache-dir"],
        
        # 方法2: 尝试指定CPU架构
        [sys.executable, "-m", "pip", "install", "llama-cpp-python", "--no-cache-dir", "--force-reinstall"],
        
        # 方法3: 尝试从conda-forge安装
        ["conda", "install", "-c", "conda-forge", "llama-cpp-python"],
    ]
    
    for i, cmd in enumerate(commands, 1):
        try:
            print(f"\n尝试方法{i}: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ 安装成功!")
                return True
            else:
                print(f"❌ 方法{i}失败:")
                print(result.stderr[:500])
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 方法{i}超时")
        except FileNotFoundError:
            print(f"⚠️ 方法{i}命令不存在")
        except Exception as e:
            print(f"❌ 方法{i}出错: {e}")
    
    return False

def install_alternative_llm():
    """安装替代的LLM库"""
    print_step(5, "安装替代LLM库")
    
    alternatives = [
        "transformers",
        "torch",
        "openai",
        "anthropic"
    ]
    
    for lib in alternatives:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", lib], 
                         capture_output=True, text=True, check=True)
            print(f"✅ {lib} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {lib} 安装失败")

def test_installation():
    """测试安装"""
    print_step(6, "测试安装")
    
    try:
        import llama_cpp
        print("✅ llama-cpp-python 导入成功")
        print(f"版本: {llama_cpp.__version__}")
        return True
    except ImportError as e:
        print(f"❌ llama-cpp-python 导入失败: {e}")
        return False

def create_fallback_config():
    """创建备用配置"""
    print_step(7, "创建备用配置")
    
    config_content = """
# DeepSeek备用配置
# 如果llama-cpp-python安装失败，使用以下配置

DEEPSEEK_FALLBACK_CONFIG = {
    'use_api_mode': True,
    'api_base_url': 'http://localhost:1234/v1',
    'model_name': 'deepseek-r1',
    'fallback_message': '''
    DeepSeek直接调用暂时不可用。
    
    解决方案：
    1. 安装Visual Studio Build Tools
    2. 重新运行: pip install llama-cpp-python
    3. 或使用LM Studio API模式
    
    当前系统将使用备用回答机制。
    '''
}
"""
    
    with open("deepseek_fallback_config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 创建了备用配置文件: deepseek_fallback_config.py")

def main():
    """主函数"""
    print("🔧 DeepSeek依赖安装工具")
    print("解决llama-cpp-python安装问题")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查系统信息
    system, machine = check_system_info()
    
    # 检查构建工具
    install_build_tools()
    
    # 尝试安装预编译版本
    if try_precompiled_wheels():
        if test_installation():
            print("\n🎉 llama-cpp-python 安装成功!")
            return True
    
    # 安装替代库
    install_alternative_llm()
    
    # 创建备用配置
    create_fallback_config()
    
    print("\n" + "="*60)
    print("📋 安装总结")
    print("="*60)
    
    if test_installation():
        print("🎉 DeepSeek直接调用可用")
    else:
        print("⚠️ DeepSeek直接调用不可用，但系统仍可正常运行")
        print("\n💡 手动安装建议:")
        print("1. 安装Visual Studio Build Tools:")
        print("   https://visualstudio.microsoft.com/visual-cpp-build-tools/")
        print("2. 重新运行: pip install llama-cpp-python")
        print("3. 或使用LM Studio作为API服务器")
    
    print("\n🚀 系统可以正常启动，使用以下命令:")
    print("   streamlit run ultimate_final_tcm_system.py --server.port=8507")
    
    return True

if __name__ == "__main__":
    main()
