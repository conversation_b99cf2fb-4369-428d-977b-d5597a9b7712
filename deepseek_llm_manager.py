#!/usr/bin/env python3
"""
DeepSeek-R1 LLM管理器
集成DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf模型
"""
import logging
import os
import sys
from typing import Dict, List, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class DeepSeekLLMManager:
    """DeepSeek-R1 LLM管理器"""
    
    def __init__(self):
        self.model = None
        self.model_path = None
        self.is_loaded = False
        self.model_name = "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M"
        
        # 尝试初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化DeepSeek模型"""
        try:
            # 1. 尝试使用llama-cpp-python
            success = self._try_llama_cpp()
            if success:
                return
            
            # 2. 尝试使用ollama
            success = self._try_ollama()
            if success:
                return
            
            # 3. 降级到模拟模式
            logger.warning("⚠️ 无法加载DeepSeek模型，使用模拟模式")
            self._setup_simulation_mode()
            
        except Exception as e:
            logger.error(f"❌ DeepSeek模型初始化失败: {e}")
            self._setup_simulation_mode()
    
    def _try_llama_cpp(self) -> bool:
        """尝试使用llama-cpp-python加载模型"""
        try:
            from llama_cpp import Llama
            
            # 查找模型文件 - 包含您的LM Studio路径
            model_paths = [
                # 本地models目录
                "models/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "../models/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "~/models/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                # LM Studio路径
                r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                # 其他可能路径
                "./DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
            ]
            
            for path in model_paths:
                expanded_path = Path(path).expanduser()
                if expanded_path.exists():
                    logger.info(f"🔍 找到模型文件: {expanded_path}")
                    
                    # 加载模型
                    logger.info("🚀 正在加载DeepSeek-R1模型...")
                    self.model = Llama(
                        model_path=str(expanded_path),
                        n_ctx=4096,  # 上下文长度
                        n_threads=4,  # 线程数
                        n_gpu_layers=32,  # GPU层数（如果有GPU）
                        verbose=False
                    )
                    
                    self.model_path = str(expanded_path)
                    self.is_loaded = True
                    self.backend = "llama-cpp"
                    
                    logger.info("✅ DeepSeek-R1模型加载成功 (llama-cpp)")
                    return True
            
            logger.warning("⚠️ 未找到DeepSeek模型文件")
            return False
            
        except ImportError:
            logger.warning("⚠️ llama-cpp-python未安装")
            return False
        except Exception as e:
            logger.warning(f"⚠️ llama-cpp加载失败: {e}")
            return False
    
    def _try_ollama(self) -> bool:
        """尝试使用ollama加载模型"""
        try:
            import requests
            
            # 检查ollama是否运行
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                
                # 查找DeepSeek模型
                deepseek_models = [m for m in models if "deepseek" in m.get("name", "").lower()]
                
                if deepseek_models:
                    self.model = deepseek_models[0]["name"]
                    self.is_loaded = True
                    self.backend = "ollama"
                    
                    logger.info(f"✅ 找到Ollama DeepSeek模型: {self.model}")
                    return True
                else:
                    logger.warning("⚠️ Ollama中未找到DeepSeek模型")
                    return False
            else:
                logger.warning("⚠️ Ollama服务未运行")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ Ollama连接失败: {e}")
            return False
    
    def _setup_simulation_mode(self):
        """设置模拟模式"""
        self.backend = "simulation"
        self.is_loaded = True
        logger.info("🤖 DeepSeek模拟模式已启用")
    
    def generate_intelligent_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成智能回答"""
        if not self.is_loaded:
            return "❌ 模型未加载，无法生成回答"
        
        try:
            # 构建提示词
            prompt = self._build_prompt(query, sources)
            
            # 根据后端类型生成回答
            if self.backend == "llama-cpp":
                return self._generate_with_llama_cpp(prompt)
            elif self.backend == "ollama":
                return self._generate_with_ollama(prompt)
            else:
                return self._generate_simulation_response(query, sources)
                
        except Exception as e:
            logger.error(f"❌ 生成回答失败: {e}")
            return f"❌ 生成回答时发生错误: {str(e)}"
    
    def _build_prompt(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """构建DeepSeek专用提示词"""
        # 整理源信息
        source_content = ""
        if sources:
            source_content = "\n\n参考资料：\n"
            for i, source in enumerate(sources[:5], 1):
                content = source.get('content', '')[:300]
                source_name = source.get('source', f'资料{i}')
                source_content += f"{i}. 【{source_name}】\n{content}\n\n"
        
        # 构建专业提示词
        prompt = f"""你是一位专业的中医AI助手，具有深厚的中医理论基础和丰富的临床经验。请基于提供的参考资料，为用户提供专业、准确的中医建议。

用户问题：{query}

{source_content}

请按以下要求回答：
1. 🎯 直接回答用户的具体问题
2. 📚 结合参考资料进行深入分析
3. 🔬 解释相关的中医理论和病机
4. 💊 如涉及治疗，提供具体的方剂或方法
5. ⚠️ 明确说明仅供参考，需专业医生诊断

请用专业但易懂的语言回答："""

        return prompt
    
    def _generate_with_llama_cpp(self, prompt: str) -> str:
        """使用llama-cpp生成回答"""
        try:
            response = self.model(
                prompt,
                max_tokens=1024,
                temperature=0.7,
                top_p=0.9,
                stop=["用户问题:", "参考资料:"],
                echo=False
            )
            
            return response["choices"][0]["text"].strip()
            
        except Exception as e:
            logger.error(f"❌ llama-cpp生成失败: {e}")
            return f"❌ 模型生成失败: {str(e)}"
    
    def _generate_with_ollama(self, prompt: str) -> str:
        """使用ollama生成回答"""
        try:
            import requests
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "num_predict": 1024
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json().get("response", "").strip()
            else:
                return f"❌ Ollama请求失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"❌ Ollama生成失败: {e}")
            return f"❌ Ollama生成失败: {str(e)}"
    
    def _generate_simulation_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成模拟回答（当真实模型不可用时）"""
        # 导入超级智能LLM作为后备
        try:
            from ultra_intelligent_llm import ultra_llm
            return ultra_llm.generate_intelligent_response(query, sources)
        except Exception as e:
            logger.error(f"❌ 后备模型也失败: {e}")
            return f"""## 🤖 DeepSeek-R1 模拟回答

关于「{query}」的问题，由于模型加载问题，当前使用模拟模式回答。

### 💡 建议
1. 检查DeepSeek-R1模型文件是否存在
2. 确认llama-cpp-python或ollama是否正确安装
3. 验证系统资源是否充足

### 📚 参考资料
{"已找到 " + str(len(sources)) + " 条相关资料" if sources else "暂无相关资料"}

⚠️ **重要提醒**: 当前为模拟模式，建议配置真实的DeepSeek-R1模型以获得更好的回答质量。"""
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "backend": getattr(self, 'backend', 'unknown'),
            "is_loaded": self.is_loaded,
            "model_path": self.model_path,
            "status": "✅ 已加载" if self.is_loaded else "❌ 未加载"
        }

# 创建全局DeepSeek实例
deepseek_llm = DeepSeekLLMManager()
