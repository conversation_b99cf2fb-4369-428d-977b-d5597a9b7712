#!/usr/bin/env python3
"""
🏥 完美统一中医智能助手 - 终极版本
✅ 语音对话功能 + 正确RAG检索 + Elasticsearch检索 + 聊天管理 + 文档上传 + 多端访问
🎯 删除所有冗余版本，只保留这一个完美版本
"""

import streamlit as st

import json
import re
from pathlib import Path
from datetime import datetime
import requests
import time
import logging
from typing import Dict, List, Any

# 文档处理
try:
    import PyPDF2
    import docx
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False
    st.warning("⚠️ 多格式文档处理不可用，请运行: pip install PyPDF2 python-docx")

# 向量搜索
try:
    import faiss
    from sentence_transformers import SentenceTransformer
    VECTOR_SEARCH_AVAILABLE = True
except ImportError:
    VECTOR_SEARCH_AVAILABLE = False
    st.error("❌ 向量搜索不可用，请运行: pip install faiss-cpu sentence-transformers")

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    st.warning("⚠️ 语音功能不可用，请运行: pip install pyttsx3 SpeechRecognition")

# 智能检索器
try:
    from intelligent_rag_retriever import IntelligentRAGRetriever
    INTELLIGENT_RETRIEVAL_AVAILABLE = True
except ImportError:
    INTELLIGENT_RETRIEVAL_AVAILABLE = False
    st.warning("⚠️ 智能检索器不可用，请确保 intelligent_rag_retriever.py 存在")

# DeepSeek模型
try:
    from deepseek_ollama_api import DeepSeekOllamaAPI
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False
    st.warning("⚠️ DeepSeek模型不可用，请确保 deepseek_ollama_api.py 存在")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局配置
CONFIG = {
    'EMBEDDING_MODEL': './models/m3e-base',  # 本地m3e-base中文嵌入模型
    'VECTOR_DB_PATH': './perfect_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CONVERSATION_PATH': './conversations',
    'UPLOAD_PATH': './uploads',
    'CHUNK_SIZE': 1000,
    'CHUNK_OVERLAP': 100,
    'TOP_K': 10,
    'MIN_RELEVANCE_SCORE': 0.3,
    'BATCH_SIZE': 16,
    'MAX_FILE_SIZE': 500 * 1024 * 1024,  # 500MB
    'MAX_WORKERS': 8,
    'MCP_SERVICE_URL': 'http://localhost:8006',
    'SIMILARITY_THRESHOLD': 0.35,
    'RERANK_THRESHOLD': 0.5,
    'ANCIENT_BOOKS_URLS': [
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/yizongjinjian/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/huangdineijing/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/shanghan/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/jinkuiyaolue/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/bencaogangmu/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/zhongyiyaoxue/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/zhenjiu/',
        'https://github.com/BillHCM7777779/gudaiyishu/blob/main/wenbing/'
    ]
}

# 🎤 语音管理器
class VoiceManager:
    """语音管理器 - 支持语音输入和输出"""
    
    def __init__(self):
        self.voice_available = VOICE_AVAILABLE
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.is_speaking = False
        
        if self.voice_available:
            try:
                # 初始化TTS引擎
                self.tts_engine = pyttsx3.init()
                self.tts_engine.setProperty('rate', 150)
                self.tts_engine.setProperty('volume', 0.8)
                
                # 设置中文语音
                voices = self.tts_engine.getProperty('voices')
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                
                # 初始化语音识别
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                
                # 调整环境噪音
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                logger.info("✅ 语音功能初始化成功")
                
            except Exception as e:
                logger.error(f"❌ 语音功能初始化失败: {e}")
                self.voice_available = False
    
    def listen_for_speech(self, timeout: int = 10, phrase_time_limit: int = 15) -> str:
        """监听语音输入"""
        if not self.voice_available:
            return None
        
        try:
            with self.microphone as source:
                st.info("🎤 正在监听，请说话...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
            
            st.info("🔄 正在识别语音...")
            
            # 尝试多种识别方式
            try:
                text = self.recognizer.recognize_google(audio, language='zh-CN')
                return text
            except:
                try:
                    text = self.recognizer.recognize_sphinx(audio, language='zh-CN')
                    return text
                except:
                    return None
                    
        except sr.WaitTimeoutError:
            st.warning("⏰ 语音输入超时")
            return None
        except sr.UnknownValueError:
            st.warning("🤷 无法识别语音内容，请重试")
            return None
        except Exception as e:
            st.error(f"❌ 语音识别失败: {e}")
            return None
    
    def speak_text(self, text: str):
        """朗读文本"""
        if not self.voice_available or not self.tts_engine or self.is_speaking:
            return False
        
        try:
            self.is_speaking = True
            
            # 清理文本
            clean_text = re.sub(r'[#*`\[\]()]', '', text)
            clean_text = re.sub(r'https?://\S+', '', clean_text)
            clean_text = re.sub(r'[🏥🔍📋💊⚠️📚🎤🔊]', '', clean_text)
            clean_text = clean_text.replace('\n', ' ').strip()
            
            # 限制长度
            if len(clean_text) > 300:
                clean_text = clean_text[:300] + "..."
            
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            
            return True
            
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
            return False
        finally:
            self.is_speaking = False
    
    def stop_speaking(self):
        """停止语音播放"""
        if self.tts_engine:
            try:
                self.tts_engine.stop()
                self.is_speaking = False
            except:
                pass

# 🔍 智能MCP检索系统
class IntelligentMCPSystem:
    """智能MCP检索系统 - 集成Elasticsearch和智能检索"""
    
    def __init__(self):
        self.mcp_service_url = CONFIG['MCP_SERVICE_URL']
        self.available = False
        self.check_availability()
    
    def check_availability(self):
        """检查MCP服务可用性"""
        try:
            response = requests.get(f"{self.mcp_service_url}/health", timeout=5)
            self.available = response.status_code == 200
            if self.available:
                logger.info("✅ 智能MCP服务连接成功")
            else:
                logger.warning("⚠️ 智能MCP服务不可用")
        except Exception as e:
            logger.warning(f"⚠️ MCP服务检查失败: {e}")
            self.available = False
    
    def intelligent_search(self, query: str, max_results: int = 5) -> List[Dict]:
        """智能搜索"""
        if not self.available:
            return self._fallback_search(query, max_results)
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "max_results": max_results,
                    "domain": "medical"
                },
                "id": "search_request"
            }
            
            response = requests.post(
                f"{self.mcp_service_url}/mcp",
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and result['result'].get('results'):
                    return result['result']['results']
            
            return self._fallback_search(query, max_results)
            
        except Exception as e:
            logger.warning(f"⚠️ MCP搜索失败: {e}")
            return self._fallback_search(query, max_results)
    
    def _fallback_search(self, query: str, max_results: int) -> List[Dict]:
        """回退搜索方法"""
        # 基本中医知识库
        basic_knowledge = [
            {
                "title": "阴阳学说基础理论",
                "content": "阴阳学说认为宇宙间一切事物都存在着相互对立统一的阴阳两个方面。阴阳既对立又统一，既相互依存又相互转化。在人体，阴阳的相对平衡是维持正常生理功能的基础。",
                "source": "中医基础理论",
                "domain": "medical",
                "score": 0.9,
                "highlights": ["阴阳", "理论"]
            },
            {
                "title": "五行学说与脏腑关系",
                "content": "五行学说以木、火、土、金、水五种物质的特性来说明脏腑的生理功能和相互关系。五行之间存在相生相克的关系，指导中医诊断和治疗。",
                "source": "中医基础理论", 
                "domain": "medical",
                "score": 0.8,
                "highlights": ["五行", "脏腑"]
            },
            {
                "title": "气血津液理论",
                "content": "气血津液是构成人体和维持人体生命活动的基本物质。气为血之帅，血为气之母，气血相互依存，津液润养全身。",
                "source": "中医基础理论",
                "domain": "medical", 
                "score": 0.8,
                "highlights": ["气血", "津液"]
            }
        ]
        
        # 简单关键词匹配
        query_lower = query.lower()
        results = []
        
        for knowledge in basic_knowledge:
            if (query_lower in knowledge["title"].lower() or 
                query_lower in knowledge["content"].lower() or
                any(term in knowledge["content"].lower() for term in ["阴阳", "五行", "气血"] if term in query_lower)):
                results.append(knowledge)
        
        return results[:max_results]

# 📚 聊天管理器
class ChatManager:
    """聊天管理器 - 管理对话历史和会话"""

    def __init__(self):
        self.conversations_path = Path(CONFIG['CONVERSATION_PATH'])
        self.conversations_path.mkdir(exist_ok=True)

        # 初始化会话状态
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = []
        if 'current_session_id' not in st.session_state:
            st.session_state.current_session_id = self._generate_session_id()

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    def add_message(self, role: str, content: str, metadata: Dict = None):
        """添加消息到聊天历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "session_id": st.session_state.current_session_id,
            "metadata": metadata or {}
        }
        st.session_state.chat_history.append(message)
        self.save_conversation()

    def save_conversation(self):
        """保存对话到文件"""
        try:
            session_file = self.conversations_path / f"{st.session_state.current_session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(st.session_state.chat_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存对话失败: {e}")

    def load_conversation(self, session_id: str) -> bool:
        """加载指定会话"""
        try:
            session_file = self.conversations_path / f"{session_id}.json"
            if session_file.exists():
                with open(session_file, 'r', encoding='utf-8') as f:
                    st.session_state.chat_history = json.load(f)
                st.session_state.current_session_id = session_id
                return True
        except Exception as e:
            logger.error(f"加载对话失败: {e}")
        return False

    def get_conversation_list(self) -> List[str]:
        """获取对话列表"""
        try:
            sessions = []
            for file in self.conversations_path.glob("session_*.json"):
                sessions.append(file.stem)
            return sorted(sessions, reverse=True)
        except Exception as e:
            logger.error(f"获取对话列表失败: {e}")
            return []

    def new_conversation(self):
        """开始新对话"""
        st.session_state.chat_history = []
        st.session_state.current_session_id = self._generate_session_id()

    def delete_conversation(self, session_id: str):
        """删除指定对话"""
        try:
            session_file = self.conversations_path / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
                return True
        except Exception as e:
            logger.error(f"删除对话失败: {e}")
        return False

# 📁 文档上传管理器
class DocumentUploadManager:
    """文档上传管理器 - 支持多格式文档上传和处理"""

    def __init__(self):
        self.upload_path = Path(CONFIG['UPLOAD_PATH'])
        self.upload_path.mkdir(exist_ok=True)

        # 支持的文件格式
        self.supported_formats = ['.pdf', '.txt', '.docx', '.doc', '.xlsx', '.pptx']

        # 初始化上传历史
        if 'upload_history' not in st.session_state:
            st.session_state.upload_history = []

    def upload_files(self, files: List) -> Dict[str, Any]:
        """上传并处理文件"""
        results = {
            'total_files': len(files),
            'successful_uploads': 0,
            'failed_uploads': 0,
            'processed_chunks': 0,
            'errors': [],
            'file_details': []
        }

        for file in files:
            try:
                # 检查文件格式
                if not self._is_supported_format(file.name):
                    results['failed_uploads'] += 1
                    results['errors'].append(f"{file.name}: 不支持的文件格式")
                    continue

                # 检查文件大小
                if file.size > CONFIG['MAX_FILE_SIZE']:
                    results['failed_uploads'] += 1
                    results['errors'].append(f"{file.name}: 文件过大")
                    continue

                # 保存文件
                file_path = self.upload_path / file.name
                with open(file_path, 'wb') as f:
                    f.write(file.getvalue())

                # 处理文件
                chunks = self._process_file(file)

                if chunks:
                    results['successful_uploads'] += 1
                    results['processed_chunks'] += len(chunks)

                    # 记录文件详情
                    file_detail = {
                        'name': file.name,
                        'size': file.size,
                        'chunks': len(chunks),
                        'upload_time': datetime.now().isoformat(),
                        'file_type': Path(file.name).suffix.lower()
                    }
                    results['file_details'].append(file_detail)
                    st.session_state.upload_history.append(file_detail)

                else:
                    results['failed_uploads'] += 1
                    results['errors'].append(f"{file.name}: 处理失败")

            except Exception as e:
                results['failed_uploads'] += 1
                results['errors'].append(f"{file.name}: {str(e)}")

        return results

    def _is_supported_format(self, filename: str) -> bool:
        """检查是否为支持的文件格式"""
        return Path(filename).suffix.lower() in self.supported_formats

    def _process_file(self, file) -> List[str]:
        """处理单个文件"""
        extension = Path(file.name).suffix.lower()

        try:
            if extension == '.pdf':
                return self._process_pdf(file)
            elif extension == '.txt':
                return self._process_txt(file)
            elif extension in ['.docx', '.doc'] and MULTI_FORMAT_AVAILABLE:
                return self._process_word(file)
            else:
                return []
        except Exception as e:
            logger.error(f"处理文件失败 {file.name}: {e}")
            return []

    def _process_pdf(self, file) -> List[str]:
        """处理PDF文件"""
        try:
            pdf_reader = PyPDF2.PdfReader(file)
            text_parts = []

            for page in pdf_reader.pages:
                text_parts.append(page.extract_text())

            full_text = '\n'.join(filter(None, text_parts))
            return self._split_text(full_text)
        except Exception as e:
            logger.error(f"PDF处理失败: {e}")
            return []

    def _process_txt(self, file) -> List[str]:
        """处理文本文件"""
        try:
            content = file.getvalue()

            # 尝试多种编码
            text = None
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    text = content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if text is None:
                return []

            return self._split_text(text)
        except Exception as e:
            logger.error(f"文本处理失败: {e}")
            return []

    def _process_word(self, file) -> List[str]:
        """处理Word文档"""
        try:
            doc = docx.Document(file)
            text_parts = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())

            full_text = '\n'.join(text_parts)
            return self._split_text(full_text)
        except Exception as e:
            logger.error(f"Word处理失败: {e}")
            return []

    def _split_text(self, text: str) -> List[str]:
        """分割文本为块"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']

        for i in range(0, len(text), chunk_size - overlap):
            chunk = text[i:i + chunk_size]
            if chunk.strip():
                chunks.append(chunk.strip())

        return chunks

    def get_upload_history(self) -> List[Dict]:
        """获取上传历史"""
        return st.session_state.upload_history

    def clear_upload_history(self):
        """清空上传历史"""
        st.session_state.upload_history = []

    def delete_uploaded_file(self, filename: str) -> bool:
        """删除已上传的文件"""
        try:
            file_path = self.upload_path / filename
            if file_path.exists():
                file_path.unlink()

                # 从历史记录中移除
                st.session_state.upload_history = [
                    item for item in st.session_state.upload_history
                    if item['name'] != filename
                ]
                return True
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
        return False

# 🤖 智能回答生成器
class IntelligentResponseGenerator:
    """智能回答生成器 - 集成RAG检索和智能回答"""

    def __init__(self):
        self.initialized = False
        self.mcp_system = IntelligentMCPSystem()
        self.rag_retriever = None
        self.deepseek_api = None

        # 尝试初始化智能检索器
        if INTELLIGENT_RETRIEVAL_AVAILABLE:
            try:
                self.rag_retriever = IntelligentRAGRetriever()
                if self.rag_retriever.initialize():
                    logger.info("✅ 智能RAG检索器初始化成功")
                else:
                    logger.warning("⚠️ 智能RAG检索器初始化失败")
                    self.rag_retriever = None
            except Exception as e:
                logger.error(f"❌ 智能RAG检索器初始化异常: {e}")
                self.rag_retriever = None

        # 尝试初始化DeepSeek模型
        if DEEPSEEK_AVAILABLE:
            try:
                self.deepseek_api = DeepSeekOllamaAPI()
                if self.deepseek_api.available:
                    logger.info("✅ DeepSeek模型初始化成功")
                else:
                    logger.warning("⚠️ DeepSeek模型不可用")
                    self.deepseek_api = None
            except Exception as e:
                logger.error(f"❌ DeepSeek模型初始化异常: {e}")
                self.deepseek_api = None

    def initialize(self) -> bool:
        """初始化生成器"""
        st.info("🔄 初始化智能回答生成器...")
        self.initialized = True
        st.success("✅ 智能回答生成器初始化成功")
        return True

    def generate_response(self, query: str) -> str:
        """生成智能回答"""
        if not self.initialized:
            return "系统未初始化，请先初始化系统"

        st.info("🧠 正在生成智能回答...")

        # 1. 使用MCP系统进行智能检索
        mcp_results = self.mcp_system.intelligent_search(query, max_results=3)

        # 2. 使用RAG检索器进行向量检索
        rag_results = []
        if self.rag_retriever:
            try:
                rag_results = self.rag_retriever.search(query, top_k=3)
            except Exception as e:
                logger.warning(f"⚠️ RAG检索失败: {e}")

        # 3. 结合检索结果生成回答
        return self._generate_enhanced_response(query, mcp_results, rag_results)

    def _generate_enhanced_response(self, query: str, mcp_results: List[Dict], rag_results: List[Dict]) -> str:
        """生成增强回答"""
        # 构建上下文
        context_parts = []

        # 添加MCP检索结果
        if mcp_results:
            context_parts.append("【智能检索结果】")
            for i, result in enumerate(mcp_results, 1):
                title = result.get('title', '未知')
                content = result.get('content', '')[:200]
                score = result.get('score', 0)
                context_parts.append(f"{i}. {title} (相关度: {score:.2f})\n   {content}...")

        # 添加RAG检索结果
        if rag_results:
            context_parts.append("\n【文档检索结果】")
            for i, result in enumerate(rag_results, 1):
                content = result.get('content', '')[:200]
                score = result.get('combined_score', result.get('score', 0))
                context_parts.append(f"{i}. 文档片段 (相关度: {score:.2f})\n   {content}...")

        context = "\n\n".join(context_parts)

        # 必须使用DeepSeek模型生成回答 - 禁止降级
        if not self.deepseek_api or not self.deepseek_api.available:
            error_msg = "❌ DeepSeek模型不可用，请检查Ollama服务状态"
            st.error(error_msg)
            logger.error(error_msg)
            return error_msg

        try:
            st.info("🤖 正在使用DeepSeek模型生成专业回答...")
            deepseek_response = self.deepseek_api.generate_response(query, context)

            if deepseek_response and "生成回答时发生错误" not in deepseek_response and "不可用" not in deepseek_response:
                return deepseek_response
            else:
                error_msg = "❌ DeepSeek模型响应异常，请重试或检查服务状态"
                st.error(error_msg)
                logger.error(f"DeepSeek响应异常: {deepseek_response}")
                return error_msg

        except Exception as e:
            error_msg = f"❌ DeepSeek模型调用失败: {str(e)}"
            st.error(error_msg)
            logger.error(error_msg)
            return error_msg

    def _generate_context_based_response(self, query: str, context: str) -> str:
        """基于上下文生成回答"""
        response = f"## 🏥 中医智能分析\n\n"
        response += f"**您的问题**: {query}\n\n"
        response += f"**基于检索资料的专业分析**:\n\n"

        # 分析查询类型并生成相应回答
        if any(keyword in query for keyword in ["治疗", "怎么办", "如何"]):
            response += self._generate_treatment_advice(query, context)
        elif any(keyword in query for keyword in ["是什么", "什么是", "介绍"]):
            response += self._generate_explanation(query, context)
        elif any(keyword in query for keyword in ["症状", "表现", "特点"]):
            response += self._generate_symptom_analysis(query, context)
        else:
            response += self._generate_general_analysis(query, context)

        response += f"\n\n**检索依据**:\n{context[:500]}...\n\n"
        response += "**⚠️ 重要提醒**: 以上建议仅供参考，具体诊疗请咨询专业中医师。"

        return response

    def _generate_treatment_advice(self, query: str, context: str) -> str:
        """生成治疗建议"""
        # 基于查询和上下文生成治疗建议
        return f"""根据中医理论和检索到的资料分析（针对：{query[:50]}...）：

### 🎯 治疗原则
- 辨证论治，因人而异
- 调理脏腑，平衡阴阳
- 标本兼治，注重整体

### 💊 可能的治疗方法
1. **中药调理**: 根据具体证型选择合适方剂
2. **针灸治疗**: 疏通经络，调节气血
3. **生活调理**: 饮食起居，情志调节

### 📋 建议步骤
1. 寻求专业中医师诊断
2. 确定具体证型和病因
3. 制定个性化治疗方案
4. 定期复诊，调整治疗"""

    def _generate_explanation(self, query: str, context: str) -> str:
        """生成解释说明"""
        # 基于查询和上下文生成解释
        return f"""基于中医理论的专业解释（关于：{query[:30]}...）：

### 🔍 基本概念
根据检索到的资料，这是中医学中的重要概念，具有深厚的理论基础。

### 📚 理论依据
- 源于古代医学经典
- 经过历代医家发展完善
- 在现代临床中仍有重要应用

### 🎯 实际意义
- 指导临床诊断
- 影响治疗方案选择
- 体现中医整体观念

### 💡 现代理解
结合现代医学研究，这一概念在当代仍具有重要的指导价值。"""

    def _generate_symptom_analysis(self, query: str, context: str) -> str:
        """生成症状分析"""
        # 基于查询和上下文生成症状分析
        return f"""中医症状分析（症状：{query[:30]}...）：

### 🔍 症状特点
根据中医理论，症状的出现往往反映了脏腑功能的失调。

### 📊 可能病机
- 气血运行不畅
- 脏腑功能失调
- 阴阳平衡失衡
- 外邪侵袭人体

### 🎯 辨证要点
- 观察症状的性质和特点
- 结合舌脉等客观指标
- 考虑发病的时间和诱因
- 评估整体的体质状态

### 💊 调理方向
建议通过专业的中医辨证，确定具体的治疗方案。"""

    def _generate_general_analysis(self, query: str, context: str) -> str:
        """生成通用分析"""
        # 基于查询和上下文生成通用分析
        return f"""中医专业分析（问题：{query[:30]}...）：

### 🧠 综合评估
基于检索到的相关资料和中医理论，为您提供专业的分析和建议。

### 📋 要点总结
- 中医强调整体观念和辨证论治
- 每个人的体质和病情都有其特殊性
- 治疗需要个性化的方案设计

### 🎯 建议方向
1. 详细了解相关的中医理论
2. 寻求专业中医师的指导
3. 结合个人实际情况制定方案
4. 注重预防和日常调理

### 📚 学习建议
可以进一步学习相关的中医经典著作，加深对中医理论的理解。"""

    def _generate_template_response(self, query: str) -> str:
        """生成模板回答"""
        return f"""## 🏥 中医咨询回答

**您的问题**: {query}

### 💡 基本建议
感谢您的咨询。基于中医理论，我为您提供以下建议：

### 🔍 分析要点
中医诊疗强调"望闻问切"四诊合参，需要全面了解您的具体情况才能给出准确的建议。

### 🎯 建议步骤
1. **寻求专业诊疗**: 建议咨询执业中医师
2. **详细描述症状**: 包括发病时间、症状特点等
3. **生活调理**: 注意饮食、作息、情志调节
4. **体质调理**: 根据个人体质进行调养

### ⚠️ 重要提醒
本回答仅供参考，不能替代专业医疗建议。具体诊疗请咨询专业中医师。

如需更详细的解答，请提供具体的症状描述或问题。"""

# 🏥 主应用类
class PerfectUnifiedTCMSystem:
    """完美统一中医智能助手"""

    def __init__(self):
        # 快速初始化基础组件
        self.chat_manager = ChatManager()
        self.upload_manager = DocumentUploadManager()

        # 延迟初始化重型组件
        self.voice_manager = None
        self.mcp_system = None
        self.response_generator = None

        # 初始化状态
        self._components_loaded = False

        # 初始化系统状态
        if 'system_initialized' not in st.session_state:
            st.session_state.system_initialized = False

        if 'voice_enabled' not in st.session_state:
            st.session_state.voice_enabled = False



    def _load_components_progressively(self):
        """渐进式加载组件"""
        if self._components_loaded:
            return True

        try:
            # 步骤1：加载语音管理器
            if self.voice_manager is None:
                with st.spinner("🎤 正在加载语音功能..."):
                    self.voice_manager = VoiceManager()
                st.success("✅ 语音功能加载完成")

            # 步骤2：加载MCP系统
            if self.mcp_system is None:
                with st.spinner("📡 正在连接MCP服务..."):
                    self.mcp_system = IntelligentMCPSystem()
                if self.mcp_system.available:
                    st.success("✅ MCP服务连接成功")
                else:
                    st.warning("⚠️ MCP服务连接失败，将使用备用方案")

            # 步骤3：加载回答生成器
            if self.response_generator is None:
                with st.spinner("🤖 正在加载AI模型..."):
                    self.response_generator = IntelligentResponseGenerator()
                st.success("✅ AI模型加载完成")

            self._components_loaded = True
            return True

        except Exception as e:
            st.error(f"❌ 组件加载失败: {e}")
            logger.error(f"组件加载失败: {e}")
            return False

    def initialize_system(self):
        """初始化系统"""
        if not st.session_state.system_initialized:
            # 渐进式加载组件
            if not self._load_components_progressively():
                return False

            with st.spinner("🔄 正在初始化智能回答系统..."):
                try:
                    # 初始化回答生成器
                    self.response_generator.initialize()

                    st.session_state.system_initialized = True
                    st.success("✅ 系统初始化完成！")
                    return True

                except Exception as e:
                    st.error(f"❌ 系统初始化失败: {e}")
                    logger.error(f"系统初始化失败: {e}")
                    return False
        return True

    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.title("🏥 中医智能助手")
            st.markdown("---")

            # 系统状态
            st.subheader("📊 系统状态")
            if st.session_state.get('system_initialized', False):
                st.success("✅ 系统已就绪")
            else:
                st.warning("⚠️ 系统未初始化")

            # 组件加载状态
            if not self._components_loaded:
                st.info("🔄 组件未加载，请先初始化系统")
                if st.button("🚀 加载组件", type="primary"):
                    self._load_components_progressively()
                    st.rerun()
                return

            # MCP服务状态
            if self.mcp_system and self.mcp_system.available:
                st.success("✅ MCP服务连接正常")
            else:
                st.warning("⚠️ MCP服务不可用")

            # DeepSeek模型状态
            if (self.response_generator and
                self.response_generator.deepseek_api and
                self.response_generator.deepseek_api.available):
                st.success("✅ DeepSeek模型可用")
            else:
                st.warning("⚠️ DeepSeek模型不可用")

            # 语音功能控制
            st.subheader("🎤 语音功能")
            if self.voice_manager and self.voice_manager.voice_available:
                st.session_state.voice_enabled = st.checkbox(
                    "启用语音功能",
                    value=st.session_state.voice_enabled
                )

                if st.session_state.voice_enabled:
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("🎤 语音输入"):
                            self.handle_voice_input()
                    with col2:
                        if st.button("🔊 朗读回答"):
                            self.handle_voice_output()
            else:
                st.warning("⚠️ 语音功能不可用")

            # 对话管理
            st.subheader("💬 对话管理")
            if st.button("🆕 新对话"):
                self.chat_manager.new_conversation()
                st.rerun()

            # 历史对话
            conversations = self.chat_manager.get_conversation_list()
            if conversations:
                selected_conv = st.selectbox(
                    "📚 历史对话",
                    ["当前对话"] + conversations,
                    key="conversation_selector"
                )

                if selected_conv != "当前对话" and selected_conv != st.session_state.current_session_id:
                    if st.button("📂 加载对话"):
                        self.chat_manager.load_conversation(selected_conv)
                        st.rerun()

            # 文档上传
            st.subheader("📁 文档上传")
            uploaded_files = st.file_uploader(
                "上传文档",
                accept_multiple_files=True,
                type=['pdf', 'txt', 'docx', 'doc', 'xlsx', 'pptx'],
                key="file_uploader"
            )

            if uploaded_files:
                if st.button("📤 处理文档"):
                    self.handle_file_upload(uploaded_files)

            # 上传历史
            upload_history = self.upload_manager.get_upload_history()
            if upload_history:
                st.subheader("📋 上传历史")
                for item in upload_history[-5:]:  # 显示最近5个
                    st.text(f"📄 {item['name']} ({item['chunks']} 块)")

    def render_main_interface(self):
        """渲染主界面"""
        st.title("🏥 完美统一中医智能助手")
        st.markdown("---")

        # 系统介绍
        col1, col2, col3 = st.columns(3)
        with col1:
            st.info("🎤 **语音对话**\n支持语音输入和语音播放")
        with col2:
            st.info("🔍 **智能检索**\n集成RAG和Elasticsearch检索")
        with col3:
            st.info("💬 **聊天管理**\n自动保存对话历史")

        # 初始化系统
        if not st.session_state.system_initialized:
            if st.button("🚀 初始化系统", type="primary"):
                self.initialize_system()
                st.rerun()
            return

        # 显示对话历史
        self.render_chat_history()

        # 用户输入区域
        self.render_input_area()

    def render_chat_history(self):
        """渲染对话历史"""
        st.subheader("💬 对话历史")

        if not st.session_state.chat_history:
            st.info("👋 欢迎使用完美统一中医智能助手！请输入您的问题。")
            return

        # 显示对话
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                with st.chat_message("user"):
                    st.write(message["content"])
                    st.caption(f"🕒 {message['timestamp']}")
            else:
                with st.chat_message("assistant"):
                    st.markdown(message["content"])
                    st.caption(f"🕒 {message['timestamp']}")

    def render_input_area(self):
        """渲染输入区域"""
        st.subheader("💭 请输入您的问题")

        # 文本输入
        user_input = st.text_area(
            "您的问题:",
            placeholder="例如：失眠多梦怎么办？肚子疼湿气重如何治疗？",
            height=100,
            key="user_input"
        )

        # 提交按钮
        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("🚀 提交问题", type="primary"):
                if user_input.strip():
                    self.handle_user_input(user_input.strip())
                    st.rerun()
                else:
                    st.warning("请输入问题内容")

        with col2:
            if st.button("🔄 清空输入"):
                st.session_state.user_input = ""
                st.rerun()

        # 快速问题按钮
        st.subheader("🔥 热门问题")
        quick_questions = [
            "失眠多梦怎么办？",
            "肚子疼湿气重如何治疗？",
            "什么是阴阳学说？",
            "中医四诊是什么？",
            "如何调理脾胃虚弱？"
        ]

        cols = st.columns(len(quick_questions))
        for i, question in enumerate(quick_questions):
            with cols[i]:
                if st.button(question, key=f"quick_{i}"):
                    self.handle_user_input(question)
                    st.rerun()

    def handle_user_input(self, user_input: str):
        """处理用户输入"""
        # 检查组件是否已加载
        if not self._components_loaded:
            st.error("❌ 系统组件未加载，请先初始化系统")
            return

        # 添加用户消息到历史
        self.chat_manager.add_message("user", user_input)

        # 生成回答
        with st.spinner("🧠 正在思考中..."):
            if self.response_generator:
                response = self.response_generator.generate_response(user_input)
            else:
                response = "❌ AI模型未加载，请重新初始化系统"

        # 添加助手回答到历史
        self.chat_manager.add_message("assistant", response)

        # 如果启用语音，自动播放回答
        if (st.session_state.voice_enabled and
            self.voice_manager and
            self.voice_manager.voice_available):
            self.voice_manager.speak_text(response)

    def handle_voice_input(self):
        """处理语音输入"""
        if not self.voice_manager or not self.voice_manager.voice_available:
            st.error("❌ 语音功能不可用")
            return

        with st.spinner("🎤 正在监听语音..."):
            voice_text = self.voice_manager.listen_for_speech()

        if voice_text:
            st.success(f"🎤 识别到: {voice_text}")
            self.handle_user_input(voice_text)
            st.rerun()
        else:
            st.warning("⚠️ 未能识别语音内容")

    def handle_voice_output(self):
        """处理语音输出"""
        if not st.session_state.chat_history:
            st.warning("⚠️ 没有可朗读的内容")
            return

        # 获取最后一条助手回答
        last_assistant_message = None
        for message in reversed(st.session_state.chat_history):
            if message["role"] == "assistant":
                last_assistant_message = message
                break

        if last_assistant_message:
            if self.voice_manager and self.voice_manager.voice_available:
                with st.spinner("🔊 正在朗读..."):
                    success = self.voice_manager.speak_text(last_assistant_message["content"])

                if success:
                    st.success("✅ 朗读完成")
                else:
                    st.error("❌ 朗读失败")
            else:
                st.error("❌ 语音功能不可用")
        else:
            st.warning("⚠️ 没有找到助手回答")

    def handle_file_upload(self, files):
        """处理文件上传"""
        with st.spinner("📤 正在处理文档..."):
            results = self.upload_manager.upload_files(files)

        # 显示处理结果
        if results['successful_uploads'] > 0:
            st.success(f"✅ 成功处理 {results['successful_uploads']} 个文件，共 {results['processed_chunks']} 个文档块")

        if results['failed_uploads'] > 0:
            st.error(f"❌ {results['failed_uploads']} 个文件处理失败")
            for error in results['errors']:
                st.error(f"   {error}")

        # 显示文件详情
        if results['file_details']:
            st.subheader("📋 处理详情")
            for detail in results['file_details']:
                st.info(f"📄 {detail['name']}: {detail['chunks']} 个文档块")

def main():
    """主函数"""
    # 配置页面
    st.set_page_config(
        page_title="完美统一中医智能助手",
        page_icon="🏥",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    try:
        # 创建应用实例
        app = PerfectUnifiedTCMSystem()

        # 渲染界面
        app.render_sidebar()
        app.render_main_interface()

    except Exception as e:
        st.error(f"❌ 系统错误: {e}")
        logger.error(f"系统错误: {e}")

if __name__ == "__main__":
    main()
