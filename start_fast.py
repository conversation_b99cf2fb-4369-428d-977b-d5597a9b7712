"""
快速PDF处理版启动脚本
"""
import subprocess
import sys
import psutil
import gc
import os

def optimize_for_speed():
    """为速度优化环境"""
    # 设置环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:32'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
    os.environ['PYTHONHASHSEED'] = '0'
    os.environ['OMP_NUM_THREADS'] = '4'  # 限制OpenMP线程数
    
    # 强制垃圾回收
    gc.collect()

def main():
    print("⚡ RAG系统快速处理版启动")
    print("=" * 40)
    
    # 显示内存状态
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"   使用率: {memory.percent:.1f}%")
    
    if memory.percent > 85:
        print("⚠️ 内存使用较高，建议先清理")
        response = input("是否继续启动？(y/n): ").lower().strip()
        if response != 'y':
            return
    
    # 优化环境
    optimize_for_speed()
    print("✅ 环境优化完成")
    
    print("\n🚀 启动快速处理版...")
    print("📝 访问地址: http://localhost:8506")
    print("⚡ 优化特点:")
    print("   - 批量处理加速")
    print("   - 小块分割策略") 
    print("   - 智能内存管理")
    print("   - 进度实时显示")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app_fast.py", 
            "--server.address", "localhost",
            "--server.port", "8506",
            "--server.headless", "true",
            "--server.maxUploadSize", "50"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用快速处理版！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
