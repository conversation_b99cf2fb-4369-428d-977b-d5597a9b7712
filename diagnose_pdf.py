"""
PDF文档处理诊断工具
"""
import os
import sys
import traceback
from pathlib import Path
import time

def test_pdf_extraction():
    """测试PDF文本提取"""
    print("🔍 测试PDF文本提取...")
    
    pdf_path = "documents/金匮要略+(中医临床必读丛书).pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ 文件不存在: {pdf_path}")
        return False
    
    file_size = os.path.getsize(pdf_path) / 1024  # KB
    print(f"📄 文件大小: {file_size:.1f} KB")
    
    try:
        import PyPDF2
        print("✅ PyPDF2导入成功")
        
        start_time = time.time()
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            page_count = len(pdf_reader.pages)
            print(f"📖 PDF页数: {page_count}")
            
            # 提取前几页文本测试
            text_sample = ""
            for i, page in enumerate(pdf_reader.pages[:3]):  # 只测试前3页
                try:
                    page_text = page.extract_text()
                    text_sample += page_text
                    print(f"   页面 {i+1}: 提取了 {len(page_text)} 个字符")
                except Exception as e:
                    print(f"   页面 {i+1}: 提取失败 - {e}")
            
            extraction_time = time.time() - start_time
            print(f"⏱️ 前3页提取耗时: {extraction_time:.2f}秒")
            print(f"📝 提取的文本样本 (前200字符):")
            print(text_sample[:200] + "..." if len(text_sample) > 200 else text_sample)
            
            return True, len(text_sample), page_count
            
    except Exception as e:
        print(f"❌ PDF提取失败: {e}")
        traceback.print_exc()
        return False, 0, 0

def test_text_chunking(text_length):
    """测试文本分块"""
    print(f"\n🔍 测试文本分块 (假设文本长度: {text_length})...")
    
    try:
        import config
        chunk_size = config.CHUNK_SIZE
        chunk_overlap = config.CHUNK_OVERLAP
        
        # 估算块数量
        estimated_chunks = max(1, text_length // (chunk_size - chunk_overlap))
        print(f"📊 配置: 块大小={chunk_size}, 重叠={chunk_overlap}")
        print(f"📊 估算块数量: {estimated_chunks}")
        
        if estimated_chunks > 1000:
            print("⚠️ 警告: 块数量过多，可能导致处理缓慢")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文本分块测试失败: {e}")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print(f"\n🔍 测试嵌入模型...")
    
    try:
        from models.model_manager import model_manager
        
        if model_manager.embedding_model is None:
            print("❌ 嵌入模型未加载")
            return False
        
        # 测试小文本嵌入
        test_text = "这是一个测试文本"
        start_time = time.time()
        
        embedding = model_manager.get_embedding(test_text)
        embedding_time = time.time() - start_time
        
        print(f"✅ 嵌入模型工作正常")
        print(f"📊 向量维度: {len(embedding)}")
        print(f"⏱️ 单次嵌入耗时: {embedding_time:.3f}秒")
        
        # 估算大文档处理时间
        estimated_time_per_chunk = embedding_time * 2  # 考虑开销
        print(f"📊 估算每块处理时间: {estimated_time_per_chunk:.3f}秒")
        
        return True, estimated_time_per_chunk
        
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        traceback.print_exc()
        return False, 0

def test_faiss_index():
    """测试FAISS索引创建"""
    print(f"\n🔍 测试FAISS索引创建...")
    
    try:
        import faiss
        import numpy as np
        
        # 创建测试向量
        dimension = 768  # m3e-base的向量维度
        test_vectors = np.random.random((10, dimension)).astype('float32')
        
        start_time = time.time()
        
        # 创建索引
        index = faiss.IndexFlatIP(dimension)
        faiss.normalize_L2(test_vectors)
        index.add(test_vectors)
        
        index_time = time.time() - start_time
        
        print(f"✅ FAISS索引创建成功")
        print(f"📊 测试向量数: 10")
        print(f"⏱️ 索引创建耗时: {index_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ FAISS索引测试失败: {e}")
        traceback.print_exc()
        return False

def estimate_processing_time(page_count, text_length, embedding_time_per_chunk):
    """估算总处理时间"""
    print(f"\n📊 处理时间估算...")
    
    # 估算块数量
    chunk_size = 300  # 优化后的块大小
    estimated_chunks = max(1, text_length // chunk_size)
    
    # 各阶段时间估算
    pdf_extraction_time = page_count * 0.5  # 每页0.5秒
    chunking_time = estimated_chunks * 0.01  # 每块0.01秒
    embedding_time = estimated_chunks * embedding_time_per_chunk
    index_time = estimated_chunks * 0.01  # 每块0.01秒索引时间
    
    total_time = pdf_extraction_time + chunking_time + embedding_time + index_time
    
    print(f"   PDF提取: {pdf_extraction_time:.1f}秒")
    print(f"   文本分块: {chunking_time:.1f}秒") 
    print(f"   向量嵌入: {embedding_time:.1f}秒")
    print(f"   索引创建: {index_time:.1f}秒")
    print(f"   总计: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    
    if total_time > 300:  # 5分钟
        print("⚠️ 警告: 预计处理时间超过5分钟")
        return False
    
    return True

def main():
    """主诊断函数"""
    print("🔧 PDF文档处理诊断工具")
    print("=" * 50)
    
    # 1. 测试PDF提取
    pdf_success, text_length, page_count = test_pdf_extraction()
    if not pdf_success:
        print("\n❌ PDF提取失败，无法继续处理")
        return
    
    # 2. 测试文本分块
    chunk_success = test_text_chunking(text_length)
    if not chunk_success:
        print("\n⚠️ 文本分块可能有问题")
    
    # 3. 测试嵌入模型
    embedding_success, embedding_time = test_embedding_model()
    if not embedding_success:
        print("\n❌ 嵌入模型测试失败")
        return
    
    # 4. 测试FAISS索引
    faiss_success = test_faiss_index()
    if not faiss_success:
        print("\n❌ FAISS索引测试失败")
        return
    
    # 5. 估算处理时间
    time_ok = estimate_processing_time(page_count, text_length, embedding_time)
    
    print("\n" + "=" * 50)
    print("📋 诊断结果:")
    
    if pdf_success and chunk_success and embedding_success and faiss_success:
        if time_ok:
            print("✅ 所有测试通过，文档应该能正常处理")
        else:
            print("⚠️ 文档可以处理，但可能需要较长时间")
    else:
        print("❌ 发现问题，需要修复后才能正常处理")
    
    print(f"\n💡 建议:")
    if text_length > 100000:  # 10万字符
        print("   - 文档较大，建议分批处理")
    if page_count > 100:
        print("   - 页数较多，考虑提取关键章节")
    
    print("   - 确保有足够内存 (建议<80%使用率)")
    print("   - 处理过程中避免其他重型操作")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 诊断已取消")
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {e}")
        traceback.print_exc()
