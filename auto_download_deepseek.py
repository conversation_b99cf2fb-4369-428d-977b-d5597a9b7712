#!/usr/bin/env python3
"""
自动下载DeepSeek-R1兼容版本
"""

import os
import sys
import requests
from pathlib import Path
import time

def auto_download():
    """自动下载推荐版本"""
    
    print("🤖 自动下载DeepSeek-R1-0528-Qwen3-8B (Q4_0兼容版本)")
    print("=" * 60)
    
    # 推荐的Q4_0版本
    model_info = {
        "url": "https://huggingface.co/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf",
        "size": "4.79GB",
        "name": "Q4_0"
    }
    
    # 设置保存路径到RAG系统目录
    base_dir = Path("./models")  # 保存到当前RAG系统的models目录
    base_dir.mkdir(parents=True, exist_ok=True)
    
    filename = "deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf"
    model_path = base_dir / filename
    
    print(f"📍 下载URL: {model_info['url']}")
    print(f"💾 保存路径: {model_path}")
    print(f"📊 预期大小: {model_info['size']}")
    print()
    
    # 检查是否已存在
    if model_path.exists():
        size = model_path.stat().st_size / (1024**3)
        print(f"✅ 文件已存在: {size:.2f}GB")
        
        # 测试现有文件
        if test_model_loading(str(model_path)):
            print("🎉 现有模型可用!")
            create_system_config(str(model_path))
            return True
        else:
            print("⚠️ 现有文件损坏，重新下载...")
            model_path.unlink()
    
    try:
        print("🚀 开始下载...")
        start_time = time.time()
        
        response = requests.get(model_info['url'], stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # 显示进度
                    if total_size > 0:
                        progress = downloaded / total_size * 100
                        speed = downloaded / (time.time() - start_time) / (1024 * 1024)  # MB/s
                        print(f"\r📥 下载进度: {progress:.1f}% ({downloaded/(1024**3):.2f}GB/{total_size/(1024**3):.2f}GB) 速度: {speed:.1f}MB/s", end="")
        
        print(f"\n✅ 下载完成!")
        
        # 验证文件
        if model_path.exists():
            actual_size = model_path.stat().st_size / (1024**3)
            print(f"📊 文件大小: {actual_size:.2f}GB")
            
            # 测试模型加载
            if test_model_loading(str(model_path)):
                print("🎉 模型下载并测试成功!")
                create_system_config(str(model_path))
                return True
            else:
                print("⚠️ 模型下载成功但测试失败")
                return False
        else:
            print("❌ 文件下载失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        if model_path.exists():
            model_path.unlink()
        return False

def test_model_loading(model_path: str):
    """测试模型加载"""
    print(f"\n🧪 测试模型加载...")
    
    try:
        from llama_cpp import Llama
        
        print("   🔄 使用最小参数测试...")
        model = Llama(
            model_path=model_path,
            n_ctx=256,
            n_threads=1,
            n_gpu_layers=0,
            verbose=False,
            use_mmap=False,
            use_mlock=False
        )
        
        # 简单推理测试
        response = model("Hello", max_tokens=5, echo=False)
        print(f"   ✅ 测试成功: {response}")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def create_system_config(model_path: str):
    """创建系统配置"""
    print(f"\n🔧 更新系统配置...")
    
    try:
        # 读取当前系统文件
        system_file = "ultimate_final_tcm_system.py"
        if not os.path.exists(system_file):
            print(f"⚠️ 系统文件不存在: {system_file}")
            return
        
        with open(system_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 更新模型路径到RAG系统本地路径
        old_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
        # 使用相对路径，便于系统移植
        relative_path = f"./models/{Path(model_path).name}"
        new_content = content.replace(old_path, relative_path)
        
        # 写回文件
        with open(system_file, "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("✅ 系统配置已更新")
        
        # 创建独立配置文件
        config_content = f'''# DeepSeek-R1 兼容配置
DEEPSEEK_R1_MODEL_PATH = r"{model_path}"
DEEPSEEK_R1_CONFIG = {{
    "n_ctx": 2048,
    "n_threads": 4,
    "n_gpu_layers": 0,
    "use_mmap": False,
    "use_mlock": False,
    "verbose": True
}}
'''
        
        with open("deepseek_r1_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("✅ 配置文件已创建: deepseek_r1_config.py")
        
    except Exception as e:
        print(f"⚠️ 更新配置失败: {e}")

def main():
    """主函数"""
    success = auto_download()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 DeepSeek-R1兼容版本安装完成!")
        print("=" * 60)
        print("💡 下一步:")
        print("1. 重启您的系统:")
        print("   streamlit run ultimate_final_tcm_system.py --server.port=8507")
        print("2. 点击'🚀 初始化系统'")
        print("3. 享受智能的DeepSeek-R1模型!")
        print()
        print("🎯 您现在拥有:")
        print("   ✅ 兼容的DeepSeek-R1-0528-Qwen3-8B-Q4_0模型")
        print("   ✅ 优化的加载配置")
        print("   ✅ 通过测试的系统集成")
    else:
        print("\n❌ 安装失败")
        print("💡 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确保有足够磁盘空间 (>5GB)")
        print("3. 重新运行此脚本")

if __name__ == "__main__":
    main()
