#!/usr/bin/env python3
"""
安装终极中医RAG系统的所有依赖
包括DeepSeek模型、语音功能、多格式文档处理等
"""

import subprocess
import sys
import os
import platform

def print_ultimate_banner():
    """打印终极安装横幅"""
    print("=" * 100)
    print("🚀 终极中医RAG系统 - 依赖安装器")
    print("=" * 100)
    print("📦 将安装以下功能模块:")
    print("")
    print("🧠 DeepSeek模型支持:")
    print("   - llama-cpp-python (GPU加速)")
    print("   - 模型推理引擎")
    print("")
    print("🔊 语音功能:")
    print("   - pyttsx3 (文本转语音)")
    print("   - SpeechRecognition (语音识别)")
    print("   - PyAudio (音频处理)")
    print("")
    print("📄 多格式文档处理:")
    print("   - PyPDF2 (PDF处理)")
    print("   - python-docx (Word文档)")
    print("   - python-pptx (PowerPoint)")
    print("   - openpyxl (Excel)")
    print("   - pandas (数据处理)")
    print("")
    print("🔍 向量检索:")
    print("   - faiss-cpu/faiss-gpu (向量数据库)")
    print("   - sentence-transformers (文本嵌入)")
    print("")
    print("🌐 网络功能:")
    print("   - aiohttp (异步HTTP)")
    print("   - requests (HTTP请求)")
    print("   - beautifulsoup4 (HTML解析)")
    print("")
    print("🖥️ 界面框架:")
    print("   - streamlit (Web界面)")
    print("=" * 100)

def install_package(package_name, description="", use_pip_extra=None):
    """安装单个包"""
    try:
        print(f"📥 安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        cmd = [sys.executable, "-m", "pip", "install"]
        
        if use_pip_extra:
            cmd.extend(use_pip_extra)
        
        cmd.append(package_name)
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def install_core_dependencies():
    """安装核心依赖"""
    print("\n🔧 安装核心依赖...")
    
    core_packages = [
        ("streamlit", "Web界面框架"),
        ("numpy", "数值计算"),
        ("pandas", "数据处理"),
        ("requests", "HTTP请求"),
        ("beautifulsoup4", "HTML解析"),
        ("aiohttp", "异步HTTP客户端"),
        ("pathlib", "路径处理"),
    ]
    
    success_count = 0
    for package, description in core_packages:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 核心依赖安装结果: {success_count}/{len(core_packages)} 成功")
    return success_count == len(core_packages)

def install_document_processing():
    """安装文档处理依赖"""
    print("\n📄 安装文档处理依赖...")
    
    doc_packages = [
        ("PyPDF2", "PDF文档处理"),
        ("python-docx", "Word文档处理"),
        ("python-pptx", "PowerPoint处理"),
        ("openpyxl", "Excel文档处理"),
        ("markdown", "Markdown处理"),
    ]
    
    success_count = 0
    for package, description in doc_packages:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 文档处理依赖安装结果: {success_count}/{len(doc_packages)} 成功")
    return success_count >= len(doc_packages) - 1  # 允许1个失败

def install_vector_search():
    """安装向量搜索依赖"""
    print("\n🔍 安装向量搜索依赖...")
    
    # 检测GPU
    has_gpu = check_gpu_availability()
    
    if has_gpu:
        print("🎮 检测到GPU，安装GPU版本...")
        faiss_package = "faiss-gpu"
    else:
        print("💻 使用CPU版本...")
        faiss_package = "faiss-cpu"
    
    vector_packages = [
        ("sentence-transformers", "文本嵌入模型"),
        (faiss_package, "向量数据库"),
        ("scikit-learn", "机器学习工具"),
    ]
    
    success_count = 0
    for package, description in vector_packages:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 向量搜索依赖安装结果: {success_count}/{len(vector_packages)} 成功")
    return success_count >= 2  # 至少需要sentence-transformers和faiss

def install_deepseek_model():
    """安装DeepSeek模型支持"""
    print("\n🧠 安装DeepSeek模型支持...")
    
    # 检测系统和GPU
    system = platform.system().lower()
    has_gpu = check_gpu_availability()
    
    if has_gpu:
        print("🎮 检测到GPU，安装GPU加速版本...")
        if system == "windows":
            # Windows GPU版本
            llama_package = "llama-cpp-python[cuda]"
            extra_args = ["--extra-index-url", "https://download.pytorch.org/whl/cu118"]
        else:
            # Linux/Mac GPU版本
            llama_package = "llama-cpp-python[cuda]"
            extra_args = None
    else:
        print("💻 安装CPU版本...")
        llama_package = "llama-cpp-python"
        extra_args = None
    
    success = install_package(llama_package, "DeepSeek模型推理引擎", extra_args)
    
    if success:
        print("✅ DeepSeek模型支持安装成功")
        
        # 检查模型文件
        model_path = r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf'
        if os.path.exists(model_path):
            print(f"✅ 发现DeepSeek模型文件: {model_path}")
        else:
            print(f"⚠️ DeepSeek模型文件不存在: {model_path}")
            print("💡 请确保模型文件路径正确")
    else:
        print("❌ DeepSeek模型支持安装失败")
    
    return success

def install_voice_features():
    """安装语音功能"""
    print("\n🔊 安装语音功能...")
    
    # 语音输出
    tts_success = install_package("pyttsx3", "文本转语音")
    
    # 语音识别
    sr_success = install_package("SpeechRecognition", "语音识别")
    
    # 音频处理
    audio_success = install_pyaudio_optimized()
    
    success_count = sum([tts_success, sr_success, audio_success])
    print(f"\n📊 语音功能安装结果: {success_count}/3 成功")
    
    return success_count >= 2  # 至少需要TTS和语音识别

def install_pyaudio_optimized():
    """优化安装PyAudio"""
    print("🎵 安装音频处理库...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # Windows系统优化安装
        print("🪟 Windows系统，尝试多种安装方式...")
        
        # 方法1: 直接安装
        if install_package("PyAudio", "音频输入输出"):
            return True
        
        # 方法2: 使用conda
        try:
            result = subprocess.run(["conda", "install", "-c", "anaconda", "pyaudio", "-y"], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print("✅ PyAudio通过conda安装成功")
                return True
        except:
            pass
        
        # 方法3: 预编译版本
        print("💡 尝试安装预编译版本...")
        precompiled_urls = [
            "https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp39-cp39-win_amd64.whl",
            "https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp38-cp38-win_amd64.whl",
        ]
        
        for url in precompiled_urls:
            try:
                result = subprocess.run([sys.executable, "-m", "pip", "install", url], 
                                      capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    print("✅ PyAudio预编译版本安装成功")
                    return True
            except:
                continue
        
        print("⚠️ PyAudio安装失败，语音输入功能可能受限")
        return False
        
    else:
        # Linux/Mac系统
        return install_package("PyAudio", "音频输入输出")

def check_gpu_availability():
    """检查GPU可用性"""
    try:
        import subprocess
        
        # 检查NVIDIA GPU
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        if result.returncode == 0:
            print("🎮 检测到NVIDIA GPU")
            return True
        
        # 检查AMD GPU (Linux)
        result = subprocess.run(["rocm-smi"], capture_output=True, text=True)
        if result.returncode == 0:
            print("🎮 检测到AMD GPU")
            return True
        
        return False
        
    except:
        return False

def test_installations():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    test_results = {}
    
    # 测试核心功能
    try:
        import streamlit
        test_results['streamlit'] = True
        print("✅ Streamlit 可用")
    except ImportError:
        test_results['streamlit'] = False
        print("❌ Streamlit 不可用")
    
    # 测试文档处理
    try:
        import PyPDF2
        test_results['pdf'] = True
        print("✅ PDF处理 可用")
    except ImportError:
        test_results['pdf'] = False
        print("❌ PDF处理 不可用")
    
    # 测试向量搜索
    try:
        import faiss
        import sentence_transformers
        test_results['vector_search'] = True
        print("✅ 向量搜索 可用")
    except ImportError:
        test_results['vector_search'] = False
        print("❌ 向量搜索 不可用")
    
    # 测试DeepSeek模型
    try:
        from llama_cpp import Llama
        test_results['deepseek'] = True
        print("✅ DeepSeek模型支持 可用")
    except ImportError:
        test_results['deepseek'] = False
        print("❌ DeepSeek模型支持 不可用")
    
    # 测试语音功能
    try:
        import pyttsx3
        import speech_recognition
        test_results['voice'] = True
        print("✅ 语音功能 可用")
    except ImportError:
        test_results['voice'] = False
        print("❌ 语音功能 不可用")
    
    return test_results

def main():
    """主函数"""
    print_ultimate_banner()
    
    print("🚀 开始安装终极中医RAG系统依赖...")
    
    # 1. 安装核心依赖
    core_success = install_core_dependencies()
    
    # 2. 安装文档处理
    doc_success = install_document_processing()
    
    # 3. 安装向量搜索
    vector_success = install_vector_search()
    
    # 4. 安装DeepSeek模型支持
    deepseek_success = install_deepseek_model()
    
    # 5. 安装语音功能
    voice_success = install_voice_features()
    
    # 6. 测试安装结果
    test_results = test_installations()
    
    # 7. 总结
    print("\n" + "=" * 100)
    print("📊 安装结果总结:")
    print(f"   🔧 核心依赖: {'✅ 成功' if core_success else '❌ 失败'}")
    print(f"   📄 文档处理: {'✅ 成功' if doc_success else '❌ 失败'}")
    print(f"   🔍 向量搜索: {'✅ 成功' if vector_success else '❌ 失败'}")
    print(f"   🧠 DeepSeek模型: {'✅ 成功' if deepseek_success else '❌ 失败'}")
    print(f"   🔊 语音功能: {'✅ 成功' if voice_success else '❌ 失败'}")
    
    success_count = sum([core_success, doc_success, vector_success, deepseek_success, voice_success])
    
    if success_count >= 4:
        print("\n🎉 终极中医RAG系统依赖安装完成！")
        print("💡 现在可以启动系统:")
        print("   python ultimate_working_tcm_system.py")
    elif success_count >= 3:
        print("\n⚠️ 大部分依赖安装成功，系统可以运行但功能可能受限")
        print("💡 建议检查失败的依赖并重新安装")
    else:
        print("\n❌ 安装失败，请检查错误信息并重试")
    
    print("=" * 100)

if __name__ == "__main__":
    main()
