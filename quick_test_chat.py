#!/usr/bin/env python3
"""
快速测试聊天功能
"""
import requests
import json

def test_chat():
    """测试聊天功能"""
    url = "http://localhost:8006/api/chat"
    
    test_messages = [
        "栀子甘草豉汤的功效是什么？",
        "脾胃虚弱的症状有哪些？",
        "失眠多梦怎么治疗？"
    ]
    
    for message in test_messages:
        print(f"\n🔍 测试问题: {message}")
        
        try:
            response = requests.post(
                url,
                json={"message": message},
                headers={"Content-Type": "application/json"},
                timeout=20
            )
            
            print(f"📡 状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功获取回答")
                print(f"📝 回答长度: {len(data.get('response', ''))} 字符")
                print(f"📚 资源数量: {len(data.get('sources', []))}")
                
                # 显示回答预览
                answer = data.get('response', '')
                if answer:
                    preview = answer[:200] + "..." if len(answer) > 200 else answer
                    print(f"💬 回答预览:\n{preview}")
                
            else:
                print(f"❌ 请求失败")
                print(f"错误内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_chat()
