#!/usr/bin/env python3
"""
修复向量数据库文件格式
"""
import pickle
import json
import shutil
from pathlib import Path

print('🔧 修复向量数据库文件格式...')

vector_db_dir = Path('vector_db')

# 1. 复制索引文件到正确的名称
if (vector_db_dir / 'index.faiss').exists():
    shutil.copy2(vector_db_dir / 'index.faiss', vector_db_dir / 'vector_index.faiss')
    print('✅ 复制 index.faiss -> vector_index.faiss')

# 2. 转换metadata.pkl到metadata.json
if (vector_db_dir / 'metadata.pkl').exists():
    with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
        metadata = pickle.load(f)
    
    with open(vector_db_dir / 'metadata.json', 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print('✅ 转换 metadata.pkl -> metadata.json')

print('🎉 文件格式修复完成！')

# 验证文件
print('\n📋 验证修复结果:')
files_to_check = ['vector_index.faiss', 'metadata.json', 'chunks.pkl']
for file_name in files_to_check:
    file_path = vector_db_dir / file_name
    if file_path.exists():
        print(f'✅ {file_name}: 存在 ({file_path.stat().st_size} bytes)')
    else:
        print(f'❌ {file_name}: 不存在')
