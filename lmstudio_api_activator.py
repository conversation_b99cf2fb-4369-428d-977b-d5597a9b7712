#!/usr/bin/env python3
"""
LM Studio API激活器
专门用于激活LM Studio的API服务
"""

import requests
import time
import sys
import json
from pathlib import Path

class LMStudioAPIActivator:
    """LM Studio API激活器"""
    
    def __init__(self):
        self.api_base = "http://localhost:1234/v1"
        self.server_base = "http://localhost:1234"
        
    def check_api(self):
        """检查API状态"""
        try:
            response = requests.get(f"{self.api_base}/models", timeout=3)
            if response.status_code == 200:
                data = response.json()
                models = [model['id'] for model in data.get('data', [])]
                return True, models
            return False, []
        except:
            return False, []
    
    def try_activate_api(self):
        """尝试激活API"""
        activation_endpoints = [
            f"{self.server_base}/v1/models",
            f"{self.server_base}/api/models",
            f"{self.server_base}/models",
            f"{self.server_base}/status",
            f"{self.server_base}/health"
        ]
        
        for endpoint in activation_endpoints:
            try:
                print(f"尝试激活: {endpoint}")
                response = requests.get(endpoint, timeout=5)
                print(f"响应: {response.status_code}")
                
                if response.status_code == 200:
                    # 检查是否激活了API
                    time.sleep(2)
                    api_ok, models = self.check_api()
                    if api_ok:
                        return True, f"API已激活，模型: {models}"
                        
            except Exception as e:
                print(f"端点 {endpoint} 失败: {e}")
                continue
        
        return False, "无法激活API"
    
    def try_post_requests(self):
        """尝试POST请求来激活服务"""
        post_endpoints = [
            (f"{self.api_base}/chat/completions", {
                "model": "any",
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 1
            }),
            (f"{self.server_base}/api/start", {}),
            (f"{self.server_base}/start", {}),
        ]
        
        for endpoint, data in post_endpoints:
            try:
                print(f"尝试POST: {endpoint}")
                response = requests.post(endpoint, json=data, timeout=5)
                print(f"POST响应: {response.status_code}")
                
                # 不管POST是否成功，都检查API是否被激活
                time.sleep(2)
                api_ok, models = self.check_api()
                if api_ok:
                    return True, f"API已激活，模型: {models}"
                    
            except Exception as e:
                print(f"POST {endpoint} 失败: {e}")
                continue
        
        return False, "POST激活失败"
    
    def wait_and_retry(self, max_wait=60):
        """等待并重试"""
        print(f"等待API激活 (最多{max_wait}秒)...")
        
        for i in range(max_wait):
            # 每5秒检查一次
            if i % 5 == 0:
                api_ok, models = self.check_api()
                if api_ok and models:
                    return True, f"API就绪，模型: {', '.join(models)}"
                
                # 每15秒尝试激活一次
                if i % 15 == 0 and i > 0:
                    print(f"尝试激活... ({i}/{max_wait}秒)")
                    self.try_activate_api()
            
            time.sleep(1)
        
        return False, "等待超时"
    
    def full_activation_sequence(self):
        """完整的激活序列"""
        print("🚀 开始API激活序列...")
        
        # 1. 首先检查当前状态
        api_ok, models = self.check_api()
        if api_ok and models:
            return True, f"API已就绪，模型: {', '.join(models)}"
        
        print("API未就绪，开始激活...")
        
        # 2. 尝试GET请求激活
        success, message = self.try_activate_api()
        if success:
            return True, message
        
        # 3. 尝试POST请求激活
        success, message = self.try_post_requests()
        if success:
            return True, message
        
        # 4. 等待并重试
        print("直接激活失败，等待手动操作...")
        success, message = self.wait_and_retry(30)
        if success:
            return True, message
        
        return False, "API激活失败，请手动操作"

def main():
    """主函数"""
    activator = LMStudioAPIActivator()
    
    print("🎯 LM Studio API激活器")
    print("=" * 30)
    
    try:
        success, message = activator.full_activation_sequence()
        
        if success:
            print(f"✅ 成功: {message}")
            
            # 最终测试
            print("🧪 进行最终测试...")
            api_ok, models = activator.check_api()
            if api_ok and models:
                print(f"🎉 API完全就绪！可用模型: {', '.join(models)}")
                return True
            else:
                print("⚠️ 最终测试失败")
                return False
        else:
            print(f"❌ 失败: {message}")
            print("💡 请手动操作:")
            print("1. 在LM Studio中确保模型已加载")
            print("2. 检查是否有'Start Server'按钮并点击")
            print("3. 或者尝试重新加载模型")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
