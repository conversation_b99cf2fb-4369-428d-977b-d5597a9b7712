#!/usr/bin/env python3
"""
测试健康检查API
"""
import requests
import json

def test_health_api():
    """测试健康检查API"""
    url = "http://localhost:8006/api/health"
    
    try:
        print("🔍 测试健康检查API...")
        response = requests.get(url, timeout=10)
        
        print(f"📡 状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ JSON解析成功")
                print(f"📊 完整响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 检查必要字段
                required_fields = ['status', 'version', 'features', 'documents']
                for field in required_fields:
                    if field in data:
                        print(f"✅ {field}: {data[field]}")
                    else:
                        print(f"❌ 缺少字段: {field}")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_health_api()
