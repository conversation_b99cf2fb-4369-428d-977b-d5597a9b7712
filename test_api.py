#!/usr/bin/env python3
"""
测试API功能
"""
import requests
import json

def test_chat_api():
    """测试聊天API"""
    url = "http://localhost:8006/api/chat"
    
    data = {
        "message": "测试消息",
        "session_id": None
    }
    
    try:
        print("🔍 测试聊天API...")
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📡 状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ JSON解析成功")
                print(f"📊 响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_health_api():
    """测试健康检查API"""
    url = "http://localhost:8006/api/health"
    
    try:
        print("🔍 测试健康检查API...")
        response = requests.get(url, timeout=10)
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查成功")
            print(f"📊 系统状态: {data}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查请求失败: {e}")

if __name__ == "__main__":
    test_health_api()
    print("\n" + "="*50 + "\n")
    test_chat_api()
