#!/usr/bin/env python3
"""
DeepSeek模型管理器
下载、配置和测试DeepSeek模型
"""

import os
import sys
import requests
from pathlib import Path
import hashlib
from urllib.parse import urlparse

class DeepSeekModelManager:
    """DeepSeek模型管理器"""
    
    def __init__(self):
        self.base_dir = Path.home() / ".lmstudio" / "models"
        self.models_info = {
            "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "size": "4.68GB",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "description": "DeepSeek R1 8B模型，Q4量化版本"
            },
            "DeepSeek-Coder-1.3B-Q4_K_M": {
                "url": "https://huggingface.co/TheBloke/DeepSeek-Coder-1.3B-Instruct-GGUF/resolve/main/deepseek-coder-1.3b-instruct.Q4_K_M.gguf",
                "size": "0.8GB", 
                "path": "TheBloke/DeepSeek-Coder-1.3B-Instruct-GGUF/deepseek-coder-1.3b-instruct.Q4_K_M.gguf",
                "description": "DeepSeek Coder 1.3B模型，更小更快"
            },
            "Qwen2-1.5B-Q4_K_M": {
                "url": "https://huggingface.co/Qwen/Qwen2-1.5B-Instruct-GGUF/resolve/main/qwen2-1_5b-instruct-q4_k_m.gguf",
                "size": "1.0GB",
                "path": "Qwen/Qwen2-1.5B-Instruct-GGUF/qwen2-1_5b-instruct-q4_k_m.gguf", 
                "description": "Qwen2 1.5B模型，兼容性好"
            }
        }
    
    def check_existing_models(self):
        """检查现有模型"""
        print("🔍 检查现有模型...")
        
        found_models = []
        for model_name, info in self.models_info.items():
            model_path = self.base_dir / info["path"]
            if model_path.exists():
                size = model_path.stat().st_size / (1024**3)
                found_models.append({
                    "name": model_name,
                    "path": str(model_path),
                    "size": f"{size:.2f}GB",
                    "status": "✅ 存在"
                })
                print(f"✅ {model_name}: {size:.2f}GB")
            else:
                print(f"❌ {model_name}: 不存在")
        
        return found_models
    
    def download_model(self, model_name: str, force: bool = False):
        """下载模型"""
        if model_name not in self.models_info:
            print(f"❌ 未知模型: {model_name}")
            return False
        
        info = self.models_info[model_name]
        model_path = self.base_dir / info["path"]
        
        # 检查是否已存在
        if model_path.exists() and not force:
            print(f"✅ 模型已存在: {model_path}")
            return True
        
        # 创建目录
        model_path.parent.mkdir(parents=True, exist_ok=True)
        
        print(f"📥 开始下载 {model_name} ({info['size']})...")
        print(f"📍 下载地址: {info['url']}")
        print(f"💾 保存路径: {model_path}")
        
        try:
            # 下载文件
            response = requests.get(info['url'], stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # 显示进度
                        if total_size > 0:
                            progress = downloaded / total_size * 100
                            print(f"\r📥 下载进度: {progress:.1f}% ({downloaded/(1024**3):.2f}GB/{total_size/(1024**3):.2f}GB)", end="")
            
            print(f"\n✅ {model_name} 下载完成!")
            return True
            
        except Exception as e:
            print(f"\n❌ 下载失败: {e}")
            # 清理不完整的文件
            if model_path.exists():
                model_path.unlink()
            return False
    
    def test_model(self, model_path: str):
        """测试模型"""
        print(f"\n🧪 测试模型: {model_path}")
        
        try:
            from llama_cpp import Llama
            
            # 最小配置测试
            model = Llama(
                model_path=model_path,
                n_ctx=256,
                n_threads=1,
                n_gpu_layers=0,
                verbose=False,
                use_mmap=False
            )
            
            # 简单测试
            response = model("Hello", max_tokens=5, echo=False)
            print(f"✅ 模型测试通过: {response}")
            return True
            
        except Exception as e:
            print(f"❌ 模型测试失败: {e}")
            return False
    
    def recommend_model(self):
        """推荐适合的模型"""
        print("\n💡 模型推荐:")
        
        # 检查系统内存
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            print(f"📊 系统内存: {memory_gb:.1f}GB")
            
            if memory_gb >= 16:
                recommended = "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M"
                print(f"🎯 推荐: {recommended} (大模型，性能最佳)")
            elif memory_gb >= 8:
                recommended = "Qwen2-1.5B-Q4_K_M"
                print(f"🎯 推荐: {recommended} (中等模型，平衡性能)")
            else:
                recommended = "DeepSeek-Coder-1.3B-Q4_K_M"
                print(f"🎯 推荐: {recommended} (小模型，兼容性好)")
                
        except ImportError:
            recommended = "Qwen2-1.5B-Q4_K_M"
            print(f"🎯 推荐: {recommended} (通用选择)")
        
        return recommended
    
    def interactive_download(self):
        """交互式下载"""
        print("\n🤖 DeepSeek模型下载向导")
        print("=" * 40)
        
        # 显示可用模型
        print("📋 可用模型:")
        for i, (name, info) in enumerate(self.models_info.items(), 1):
            print(f"{i}. {name}")
            print(f"   大小: {info['size']}")
            print(f"   描述: {info['description']}")
            print()
        
        # 推荐模型
        recommended = self.recommend_model()
        
        # 用户选择
        try:
            choice = input(f"\n请选择要下载的模型 (1-{len(self.models_info)}) 或按回车使用推荐模型: ").strip()
            
            if not choice:
                model_name = recommended
            else:
                model_names = list(self.models_info.keys())
                model_name = model_names[int(choice) - 1]
            
            print(f"📥 准备下载: {model_name}")
            
            # 确认下载
            confirm = input("确认下载? (y/N): ").strip().lower()
            if confirm == 'y':
                success = self.download_model(model_name)
                if success:
                    model_path = str(self.base_dir / self.models_info[model_name]["path"])
                    self.test_model(model_path)
                    
                    # 更新配置
                    self.update_system_config(model_path)
                    
            else:
                print("❌ 下载已取消")
                
        except (ValueError, IndexError):
            print("❌ 无效选择")
        except KeyboardInterrupt:
            print("\n❌ 用户取消")
    
    def update_system_config(self, model_path: str):
        """更新系统配置"""
        print(f"\n🔧 更新系统配置...")
        
        config_content = f'''# DeepSeek模型配置
# 自动生成于 {datetime.now()}

DEEPSEEK_MODEL_PATH = r"{model_path}"

# 推荐的加载参数
DEEPSEEK_CONFIG = {{
    "n_ctx": 2048,
    "n_threads": 4,
    "n_gpu_layers": 0,
    "use_mmap": False,
    "use_mlock": False,
    "verbose": True
}}
'''
        
        with open("deepseek_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print("✅ 配置文件已更新: deepseek_config.py")

def main():
    """主函数"""
    print("🤖 DeepSeek模型管理器")
    print("=" * 50)
    
    manager = DeepSeekModelManager()
    
    # 检查现有模型
    existing = manager.check_existing_models()
    
    if existing:
        print(f"\n✅ 找到 {len(existing)} 个现有模型")
        for model in existing:
            print(f"   {model['name']}: {model['size']}")
        
        # 测试现有模型
        test_choice = input("\n是否测试现有模型? (y/N): ").strip().lower()
        if test_choice == 'y':
            for model in existing:
                manager.test_model(model['path'])
    else:
        print("\n❌ 未找到现有模型")
    
    # 询问是否下载新模型
    download_choice = input("\n是否下载新模型? (y/N): ").strip().lower()
    if download_choice == 'y':
        manager.interactive_download()
    
    print("\n🎉 模型管理完成!")
    print("💡 现在可以启动系统: streamlit run ultimate_final_tcm_system.py --server.port=8507")

if __name__ == "__main__":
    from datetime import datetime
    main()
