#!/usr/bin/env python3
"""
快速启动家庭中医智能助手
跳过复杂检查，直接启动
"""
import sys
import subprocess
from pathlib import Path

def quick_dependency_check():
    """快速依赖检查"""
    print("🔍 快速检查关键依赖...")
    
    critical_imports = {
        "streamlit": "streamlit",
        "faiss": "faiss", 
        "numpy": "numpy",
        "pandas": "pandas"
    }
    
    for name, module in critical_imports.items():
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            return False
    
    return True

def setup_directories():
    """快速设置目录"""
    print("📁 设置必要目录...")
    
    directories = [
        "family_tcm_knowledge",
        "family_tcm_knowledge/documents", 
        "user_logs",
        "vector_db"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录设置完成")

def check_vector_db():
    """检查向量数据库"""
    vector_db_path = Path("vector_db")
    required_files = ["index.faiss", "chunks.pkl", "metadata.pkl"]
    
    print("🔍 检查向量数据库...")
    
    for file_name in required_files:
        file_path = vector_db_path / file_name
        if not file_path.exists():
            print(f"❌ 缺少文件: {file_name}")
            print("💡 请先运行 emergency_fix.py 生成向量数据库")
            return False
        else:
            print(f"✅ {file_name}")
    
    return True

def find_available_port(start_port=8511):
    """找到可用端口"""
    import socket
    for port in range(start_port, start_port + 10):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return None

def main():
    """主函数"""
    print("🚀 家庭中医智能助手 - 快速启动")
    print("=" * 50)
    
    # 1. 快速依赖检查
    if not quick_dependency_check():
        print("\n❌ 依赖检查失败")
        input("按回车键退出...")
        return
    
    # 2. 设置目录
    setup_directories()
    
    # 3. 检查向量数据库
    if not check_vector_db():
        print("\n❌ 向量数据库检查失败")
        print("💡 正在尝试运行修复脚本...")
        
        try:
            subprocess.run([sys.executable, "emergency_fix.py"], check=True)
            print("✅ 向量数据库修复完成")
        except subprocess.CalledProcessError:
            print("❌ 向量数据库修复失败")
            input("按回车键退出...")
            return
        except FileNotFoundError:
            print("❌ 找不到 emergency_fix.py")
            input("按回车键退出...")
            return
    
    # 4. 选择启动模式
    print("\n🎯 选择启动模式:")
    print("1. 修复版RAG系统 (推荐，更稳定)")
    print("2. 完整家庭中医系统 (功能更全面)")
    
    choice = input("请选择 (1/2，默认1): ").strip()
    if not choice:
        choice = "1"
    
    # 5. 找到可用端口
    port = find_available_port()
    if not port:
        print("❌ 无法找到可用端口")
        return
    
    # 6. 启动对应系统
    if choice == "1":
        script_name = "quick_start_fixed.py"
        system_name = "修复版RAG系统"
    else:
        script_name = "family_web_interface.py" 
        system_name = "完整家庭中医系统"
    
    print(f"\n🏥 启动{system_name}...")
    print(f"📝 访问地址: http://localhost:{port}")
    print(f"🌐 局域网访问: http://您的IP地址:{port}")
    print("\n💡 使用提示:")
    print("   - 支持查询栀子甘草豉汤等中医方剂")
    print("   - 可以上传PDF文档扩充知识库")
    print("   - 按 Ctrl+C 停止服务")
    print("\n⚠️ 重要提醒:")
    print("   - 本系统仅供中医知识学习参考")
    print("   - 不能替代专业医生诊断治疗")
    
    try:
        # 启动Streamlit应用
        cmd = [
            sys.executable, "-m", "streamlit", "run", script_name,
            "--server.address", "0.0.0.0",
            "--server.port", str(port),
            "--server.headless", "true",
            "--server.maxUploadSize", "200",
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false"
        ]
        
        print(f"\n🔧 启动命令: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用家庭中医智能助手！")
        print("💾 所有数据已保存，下次启动时会自动加载")
        print("🏥 祝您和家人身体健康！")
    except FileNotFoundError:
        print(f"\n❌ 找不到启动脚本: {script_name}")
        print("💡 请确保文件存在于当前目录")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查依赖安装和系统配置")

if __name__ == "__main__":
    main()
