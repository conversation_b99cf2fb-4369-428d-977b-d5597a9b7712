
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import logging
import gc
from typing import Optional

class DeepSeekR1Manager:
    """DeepSeek-R1-0528-Qwen3-8B 管理器 - Transformers版本"""
    
    def __init__(self):
        self.model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.initialized = False
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def initialize(self, load_in_8bit: bool = True) -> bool:
        """初始化模型"""
        if self.initialized:
            return True
        
        try:
            print(f"🚀 正在加载DeepSeek-R1模型到 {self.device}...")
            
            # 加载分词器
            print("📝 加载分词器...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            print("🧠 加载模型...")
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "low_cpu_mem_usage": True,
            }
            
            # 如果是CPU或者内存有限，使用8bit量化
            if load_in_8bit and self.device == "cuda":
                model_kwargs["load_in_8bit"] = True
                model_kwargs["device_map"] = "auto"
            elif self.device == "cpu":
                model_kwargs["torch_dtype"] = torch.float32
            else:
                model_kwargs["device_map"] = "auto"
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                **model_kwargs
            )
            
            # 如果不是自动设备映射，手动移动到设备
            if "device_map" not in model_kwargs:
                self.model = self.model.to(self.device)
            
            self.model.eval()  # 设置为评估模式
            
            self.initialized = True
            print("✅ DeepSeek-R1模型加载成功!")
            
            # 测试生成
            test_response = self.generate("你好", max_length=50)
            if test_response:
                print(f"🧪 测试成功: {test_response[:50]}...")
                return True
            else:
                print("⚠️ 测试生成失败")
                return False
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.logger.error(f"模型初始化失败: {e}")
            return False
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7, 
                do_sample: bool = True, top_p: float = 0.9) -> Optional[str]:
        """生成回答"""
        if not self.initialized:
            return "模型未初始化"
        
        try:
            # 编码输入
            inputs = self.tokenizer(
                prompt, 
                return_tensors="pt", 
                padding=True, 
                truncation=True,
                max_length=2048
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成参数
            generation_kwargs = {
                "max_length": len(inputs["input_ids"][0]) + max_length,
                "temperature": temperature,
                "do_sample": do_sample,
                "top_p": top_p,
                "pad_token_id": self.tokenizer.pad_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "repetition_penalty": 1.1,
            }
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs["input_ids"],
                    attention_mask=inputs.get("attention_mask"),
                    **generation_kwargs
                )
            
            # 解码
            generated_text = self.tokenizer.decode(
                outputs[0], 
                skip_special_tokens=True
            )
            
            # 提取新生成的部分
            response = generated_text[len(prompt):].strip()
            
            # 清理GPU内存
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            return response
            
        except Exception as e:
            self.logger.error(f"生成失败: {e}")
            return f"生成失败: {str(e)}"
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.initialized = False
        print("🧹 资源清理完成")

# 测试函数
def test_deepseek():
    """测试DeepSeek-R1"""
    print("🧪 测试DeepSeek-R1...")
    
    manager = DeepSeekR1Manager()
    
    if manager.initialize():
        # 测试中医问题
        questions = [
            "中医是什么？",
            "什么是四君子汤？",
            "针灸的原理是什么？"
        ]
        
        for question in questions:
            print(f"\n❓ 问题: {question}")
            response = manager.generate(question, max_length=200)
            print(f"🤖 回答: {response}")
            print("-" * 50)
        
        manager.cleanup()
        return True
    else:
        print("❌ 模型初始化失败")
        return False

if __name__ == "__main__":
    test_deepseek()
