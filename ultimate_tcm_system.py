#!/usr/bin/env python3
"""
终极版中医RAG系统 - 整合所有功能
包含：真实PDF检索、在线医学爬取、多模型切换、语音交互、会话导出
"""
import os
import json
import asyncio
import pickle
import re
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import uuid
import logging

# FastAPI相关
from fastapi import FastAPI, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
import uvicorn

# 数据处理
import numpy as np
import pandas as pd
import requests
from bs4 import BeautifulSoup

# 文档处理
import PyPDF2
import docx
from openpyxl import load_workbook

# ML相关
try:
    from sentence_transformers import SentenceTransformer
    import faiss
    ADVANCED_ML_AVAILABLE = True
except ImportError:
    ADVANCED_ML_AVAILABLE = False
    print("⚠️ 高级ML库未安装，将使用基础功能")

# 中文分词
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="终极版中医RAG系统",
    description="集成PDF检索、在线爬取、多模型、语音交互的完整系统",
    version="3.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
class Config:
    # 目录配置
    UPLOAD_DIR = Path("uploads")
    VECTOR_DB_DIR = Path("vector_db")
    SESSIONS_DIR = Path("sessions")
    CACHE_DIR = Path("cache")
    
    # 模型配置
    MODELS = {
        "default": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "chinese": "shibing624/text2vec-base-chinese",
        "medical": "sentence-transformers/all-MiniLM-L6-v2",
        "fast": "sentence-transformers/all-MiniLM-L6-v2"
    }
    
    # 在线资源配置
    ONLINE_SOURCES = [
        "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/",
        "https://www.zhzyw.com/",  # 中华中医网
        "https://www.cntcm.com.cn/"  # 中国中医药网
    ]
    
    # 文档处理配置
    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 50
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    # 检索配置
    TOP_K = 5
    SIMILARITY_THRESHOLD = 0.3

# 初始化配置
config = Config()
for directory in [config.UPLOAD_DIR, config.VECTOR_DB_DIR, config.SESSIONS_DIR, config.CACHE_DIR]:
    directory.mkdir(exist_ok=True)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_voice: Optional[bool] = False
    model_name: Optional[str] = "default"

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str
    model_used: str
    processing_time: float

class DocumentInfo(BaseModel):
    filename: str
    size: int
    type: str
    upload_time: str
    status: str
    chunks_count: Optional[int] = 0

# 高级文档处理器
class AdvancedDocumentProcessor:
    def __init__(self):
        self.supported_formats = ['.pdf', '.txt', '.doc', '.docx', '.xlsx', '.xls']
        
    def process_document(self, file_path: str) -> List[Dict[str, Any]]:
        """处理文档并返回结构化数据"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        suffix = file_path.suffix.lower()
        
        if suffix == '.pdf':
            return self._process_pdf(file_path)
        elif suffix == '.txt':
            return self._process_txt(file_path)
        elif suffix in ['.doc', '.docx']:
            return self._process_docx(file_path)
        elif suffix in ['.xlsx', '.xls']:
            return self._process_excel(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {suffix}")
    
    def _process_pdf(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        chunks = []
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text.strip():
                            # 清理文本
                            cleaned_text = self._clean_text(text)
                            
                            # 分块处理
                            page_chunks = self._split_text(cleaned_text)
                            
                            for chunk_idx, chunk in enumerate(page_chunks):
                                chunks.append({
                                    'content': chunk,
                                    'metadata': {
                                        'source': str(file_path),
                                        'page': page_num + 1,
                                        'chunk_id': f"{page_num}_{chunk_idx}",
                                        'type': 'pdf',
                                        'file_size': file_path.stat().st_size
                                    }
                                })
                    except Exception as e:
                        logger.warning(f"处理PDF第{page_num + 1}页失败: {e}")
                        continue
                        
        except Exception as e:
            raise ValueError(f"PDF处理失败: {e}")
        
        return chunks
    
    def _process_txt(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理文本文件"""
        chunks = []
        
        # 尝试不同编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                    
                cleaned_text = self._clean_text(content)
                text_chunks = self._split_text(cleaned_text)
                
                for chunk_idx, chunk in enumerate(text_chunks):
                    chunks.append({
                        'content': chunk,
                        'metadata': {
                            'source': str(file_path),
                            'chunk_id': f"txt_{chunk_idx}",
                            'type': 'txt',
                            'encoding': encoding,
                            'file_size': file_path.stat().st_size
                        }
                    })
                break
                
            except UnicodeDecodeError:
                continue
        
        if not chunks:
            raise ValueError("无法识别文件编码")
        
        return chunks
    
    def _process_docx(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Word文档"""
        chunks = []
        
        try:
            doc = docx.Document(file_path)
            
            # 处理段落
            for para_idx, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    chunks.append({
                        'content': self._clean_text(paragraph.text),
                        'metadata': {
                            'source': str(file_path),
                            'paragraph': para_idx + 1,
                            'type': 'docx_paragraph',
                            'file_size': file_path.stat().st_size
                        }
                    })
            
            # 处理表格
            for table_idx, table in enumerate(doc.tables):
                table_text = ""
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_text += " | ".join(row_text) + "\n"
                
                if table_text.strip():
                    chunks.append({
                        'content': self._clean_text(table_text),
                        'metadata': {
                            'source': str(file_path),
                            'table': table_idx + 1,
                            'type': 'docx_table',
                            'file_size': file_path.stat().st_size
                        }
                    })
                    
        except Exception as e:
            raise ValueError(f"DOCX处理失败: {e}")
        
        return chunks
    
    def _process_excel(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Excel文件"""
        chunks = []
        
        try:
            workbook = load_workbook(file_path, read_only=True)
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                
                sheet_data = []
                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else "" for cell in row]
                    if any(cell.strip() for cell in row_data):
                        sheet_data.append(" | ".join(row_data))
                
                if sheet_data:
                    content = "\n".join(sheet_data)
                    chunks.append({
                        'content': self._clean_text(content),
                        'metadata': {
                            'source': str(file_path),
                            'sheet': sheet_name,
                            'type': 'excel',
                            'file_size': file_path.stat().st_size
                        }
                    })
                    
        except Exception as e:
            raise ValueError(f"Excel处理失败: {e}")
        
        return chunks
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符但保留中文
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,;:!?()[\]{}""''—–-]', '', text)
        
        return text.strip()
    
    def _split_text(self, text: str) -> List[str]:
        """分割文本为块"""
        if len(text) <= config.CHUNK_SIZE:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + config.CHUNK_SIZE
            
            if end >= len(text):
                chunks.append(text[start:])
                break
            
            # 寻找合适的分割点
            split_point = end
            for i in range(end, start + config.CHUNK_SIZE - config.CHUNK_OVERLAP, -1):
                if text[i] in '。！？\n':
                    split_point = i + 1
                    break
            
            chunks.append(text[start:split_point])
            start = split_point - config.CHUNK_OVERLAP
        
        return [chunk.strip() for chunk in chunks if chunk.strip()]

# 在线资源爬取器
class OnlineResourceCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.cache_dir = config.CACHE_DIR
        
    async def crawl_medical_resources(self, query: str) -> List[Dict[str, Any]]:
        """爬取在线医学资源"""
        results = []
        
        for source_url in config.ONLINE_SOURCES:
            try:
                source_results = await self._crawl_single_source(source_url, query)
                results.extend(source_results)
            except Exception as e:
                logger.warning(f"爬取 {source_url} 失败: {e}")
                continue
        
        return results
    
    async def _crawl_single_source(self, base_url: str, query: str) -> List[Dict[str, Any]]:
        """爬取单个资源"""
        results = []
        
        # 检查缓存
        cache_key = hashlib.md5(f"{base_url}_{query}".encode()).hexdigest()
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists() and (time.time() - cache_file.stat().st_mtime) < 3600:  # 1小时缓存
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        try:
            # 访问主页
            response = self.session.get(base_url, timeout=15)
            if response.status_code != 200:
                return results
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找相关链接
            links = self._find_relevant_links(soup, query, base_url)
            
            # 访问相关页面
            for link_info in links[:5]:  # 限制访问数量
                try:
                    page_content = self._extract_page_content(link_info['url'])
                    if page_content and self._is_relevant_content(page_content, query):
                        results.append({
                            'source': link_info['title'],
                            'content': page_content[:1000],  # 限制长度
                            'url': link_info['url'],
                            'relevance': self._calculate_relevance(query, page_content),
                            'type': 'online'
                        })
                    
                    time.sleep(1)  # 避免请求过快
                    
                except Exception as e:
                    logger.warning(f"处理页面 {link_info['url']} 失败: {e}")
                    continue
            
            # 缓存结果
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"爬取 {base_url} 失败: {e}")
        
        return results
    
    def _find_relevant_links(self, soup: BeautifulSoup, query: str, base_url: str) -> List[Dict[str, str]]:
        """查找相关链接"""
        links = []
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        
        for link in soup.find_all('a', href=True):
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            if not text or len(text) < 2:
                continue
            
            # 构建完整URL
            if href.startswith('/'):
                full_url = base_url.rstrip('/') + href
            elif href.startswith('./'):
                full_url = base_url.rstrip('/') + '/' + href[2:]
            elif href.startswith('http'):
                full_url = href
            else:
                full_url = base_url.rstrip('/') + '/' + href
            
            # 检查相关性
            text_keywords = set(re.findall(r'[\u4e00-\u9fff]+', text))
            if query_keywords.intersection(text_keywords):
                links.append({
                    'title': text,
                    'url': full_url,
                    'relevance': len(query_keywords.intersection(text_keywords)) / len(query_keywords)
                })
        
        return sorted(links, key=lambda x: x['relevance'], reverse=True)
    
    def _extract_page_content(self, url: str) -> str:
        """提取页面内容"""
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code != 200:
                return ""
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取主要内容
            content_selectors = [
                'article', '.content', '.main', '#content', 
                '.post', '.entry', 'main', '.article-content'
            ]
            
            content = ""
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    content = elements[0].get_text(strip=True)
                    break
            
            if not content:
                content = soup.get_text(strip=True)
            
            return self._clean_extracted_content(content)
            
        except Exception as e:
            logger.warning(f"提取页面内容失败 {url}: {e}")
            return ""
    
    def _clean_extracted_content(self, content: str) -> str:
        """清理提取的内容"""
        # 移除多余空白
        content = re.sub(r'\s+', ' ', content)
        
        # 移除导航和无关内容
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) > 10 and not any(skip in line.lower() for skip in 
                ['导航', '菜单', 'menu', 'nav', '版权', 'copyright', '联系我们']):
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _is_relevant_content(self, content: str, query: str) -> bool:
        """判断内容是否相关"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))
        
        intersection = query_keywords.intersection(content_keywords)
        return len(intersection) >= max(1, len(query_keywords) * 0.3)
    
    def _calculate_relevance(self, query: str, content: str) -> float:
        """计算相关性分数"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))
        
        if not query_keywords:
            return 0.0
        
        intersection = query_keywords.intersection(content_keywords)
        return len(intersection) / len(query_keywords)

# 多模型向量检索系统
class MultiModelVectorSystem:
    def __init__(self):
        self.models = {}
        self.vector_stores = {}
        self.document_chunks = []
        self.current_model = "default"

        # 初始化模型
        if ADVANCED_ML_AVAILABLE:
            self._initialize_models()
        else:
            logger.warning("高级ML功能不可用，使用基础检索")

    def _initialize_models(self):
        """初始化所有模型"""
        for model_name, model_path in config.MODELS.items():
            try:
                logger.info(f"加载模型: {model_name}")
                model = SentenceTransformer(model_path)
                self.models[model_name] = model
                logger.info(f"✅ 模型 {model_name} 加载成功")
            except Exception as e:
                logger.error(f"❌ 模型 {model_name} 加载失败: {e}")

    def switch_model(self, model_name: str) -> bool:
        """切换模型"""
        if model_name in self.models:
            self.current_model = model_name
            logger.info(f"切换到模型: {model_name}")
            return True
        return False

    def add_documents(self, chunks: List[Dict[str, Any]]) -> None:
        """添加文档到向量库"""
        if not ADVANCED_ML_AVAILABLE:
            self.document_chunks.extend(chunks)
            return

        # 提取文本内容
        texts = [chunk['content'] for chunk in chunks]

        # 为每个模型生成向量
        for model_name, model in self.models.items():
            try:
                logger.info(f"使用模型 {model_name} 生成向量...")
                vectors = model.encode(texts, show_progress_bar=True)

                # 创建或更新FAISS索引
                if model_name not in self.vector_stores:
                    dimension = vectors.shape[1]
                    index = faiss.IndexFlatIP(dimension)  # 内积相似度
                    self.vector_stores[model_name] = {
                        'index': index,
                        'chunks': []
                    }

                # 添加向量到索引
                self.vector_stores[model_name]['index'].add(vectors.astype('float32'))
                self.vector_stores[model_name]['chunks'].extend(chunks)

                logger.info(f"✅ 模型 {model_name} 向量添加完成")

            except Exception as e:
                logger.error(f"❌ 模型 {model_name} 向量生成失败: {e}")

        # 保存到全局chunks
        self.document_chunks.extend(chunks)

    def search(self, query: str, model_name: str = None, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关文档"""
        if model_name is None:
            model_name = self.current_model

        if top_k is None:
            top_k = config.TOP_K

        if not ADVANCED_ML_AVAILABLE:
            return self._basic_search(query, top_k)

        if model_name not in self.models or model_name not in self.vector_stores:
            logger.warning(f"模型 {model_name} 不可用，使用基础搜索")
            return self._basic_search(query, top_k)

        try:
            # 生成查询向量
            model = self.models[model_name]
            query_vector = model.encode([query])

            # 搜索相似向量
            vector_store = self.vector_stores[model_name]
            scores, indices = vector_store['index'].search(
                query_vector.astype('float32'), top_k * 2
            )

            # 过滤结果
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(vector_store['chunks']) and score > config.SIMILARITY_THRESHOLD:
                    chunk = vector_store['chunks'][idx].copy()
                    chunk['score'] = float(score)
                    chunk['model_used'] = model_name
                    results.append(chunk)

            return sorted(results, key=lambda x: x['score'], reverse=True)[:top_k]

        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return self._basic_search(query, top_k)

    def _basic_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """基础关键词搜索"""
        if JIEBA_AVAILABLE:
            query_words = set(jieba.cut(query))
        else:
            query_words = set(re.findall(r'[\u4e00-\u9fff]+', query))

        results = []
        for chunk in self.document_chunks:
            content = chunk['content']
            if JIEBA_AVAILABLE:
                content_words = set(jieba.cut(content))
            else:
                content_words = set(re.findall(r'[\u4e00-\u9fff]+', content))

            # 计算相似度
            intersection = query_words.intersection(content_words)
            if intersection:
                score = len(intersection) / len(query_words.union(content_words))
                if score > config.SIMILARITY_THRESHOLD:
                    chunk_copy = chunk.copy()
                    chunk_copy['score'] = score
                    chunk_copy['model_used'] = 'basic'
                    results.append(chunk_copy)

        return sorted(results, key=lambda x: x['score'], reverse=True)[:top_k]

    def save_vector_database(self) -> None:
        """保存向量数据库"""
        try:
            # 保存文档chunks
            chunks_file = config.VECTOR_DB_DIR / "document_chunks.pkl"
            with open(chunks_file, 'wb') as f:
                pickle.dump(self.document_chunks, f)

            # 保存FAISS索引
            if ADVANCED_ML_AVAILABLE:
                for model_name, vector_store in self.vector_stores.items():
                    index_file = config.VECTOR_DB_DIR / f"faiss_index_{model_name}.bin"
                    faiss.write_index(vector_store['index'], str(index_file))

                    chunks_file = config.VECTOR_DB_DIR / f"chunks_{model_name}.pkl"
                    with open(chunks_file, 'wb') as f:
                        pickle.dump(vector_store['chunks'], f)

            logger.info("✅ 向量数据库保存成功")

        except Exception as e:
            logger.error(f"❌ 向量数据库保存失败: {e}")

    def load_vector_database(self) -> bool:
        """加载向量数据库"""
        try:
            # 加载文档chunks
            chunks_file = config.VECTOR_DB_DIR / "document_chunks.pkl"
            if chunks_file.exists():
                with open(chunks_file, 'rb') as f:
                    self.document_chunks = pickle.load(f)
                logger.info(f"✅ 加载了 {len(self.document_chunks)} 个文档块")

            # 加载FAISS索引
            if ADVANCED_ML_AVAILABLE:
                for model_name in self.models.keys():
                    index_file = config.VECTOR_DB_DIR / f"faiss_index_{model_name}.bin"
                    chunks_file = config.VECTOR_DB_DIR / f"chunks_{model_name}.pkl"

                    if index_file.exists() and chunks_file.exists():
                        try:
                            index = faiss.read_index(str(index_file))
                            with open(chunks_file, 'rb') as f:
                                chunks = pickle.load(f)

                            self.vector_stores[model_name] = {
                                'index': index,
                                'chunks': chunks
                            }
                            logger.info(f"✅ 模型 {model_name} 向量库加载成功")
                        except Exception as e:
                            logger.warning(f"⚠️ 模型 {model_name} 向量库加载失败: {e}")

            return True

        except Exception as e:
            logger.error(f"❌ 向量数据库加载失败: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'total_chunks': len(self.document_chunks),
            'current_model': self.current_model,
            'available_models': list(self.models.keys()),
            'vector_stores': {}
        }

        for model_name, vector_store in self.vector_stores.items():
            stats['vector_stores'][model_name] = {
                'vectors_count': vector_store['index'].ntotal if hasattr(vector_store['index'], 'ntotal') else 0,
                'chunks_count': len(vector_store['chunks'])
            }

        return stats

# 智能回答生成器
class IntelligentResponseGenerator:
    def __init__(self, vector_system: MultiModelVectorSystem, crawler: OnlineResourceCrawler):
        self.vector_system = vector_system
        self.crawler = crawler

    async def generate_response(self, query: str, model_name: str = "default") -> Tuple[str, List[Dict[str, Any]], float]:
        """生成智能回答"""
        start_time = time.time()

        # 1. 向量检索本地文档
        local_results = self.vector_system.search(query, model_name, top_k=3)

        # 2. 在线资源检索
        online_results = await self.crawler.crawl_medical_resources(query)

        # 3. 合并和排序结果
        all_sources = self._merge_sources(local_results, online_results)

        # 4. 生成回答
        response = self._generate_comprehensive_response(query, all_sources)

        processing_time = time.time() - start_time

        return response, all_sources, processing_time

    def _merge_sources(self, local_results: List[Dict], online_results: List[Dict]) -> List[Dict[str, Any]]:
        """合并本地和在线结果"""
        all_sources = []

        # 添加本地结果
        for result in local_results:
            all_sources.append({
                'source': result['metadata']['source'],
                'content': result['content'][:500],
                'score': result.get('score', 0),
                'type': 'local',
                'model_used': result.get('model_used', 'unknown'),
                'metadata': result['metadata']
            })

        # 添加在线结果
        for result in online_results:
            all_sources.append({
                'source': result['source'],
                'content': result['content'][:500],
                'score': result.get('relevance', 0),
                'type': 'online',
                'url': result.get('url', ''),
                'metadata': {'type': 'online'}
            })

        # 按分数排序
        return sorted(all_sources, key=lambda x: x['score'], reverse=True)[:config.TOP_K]

    def _generate_comprehensive_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成综合回答"""
        if not sources:
            return self._generate_fallback_response(query)

        # 分析查询类型
        query_type = self._analyze_query_type(query)

        # 构建回答
        response_parts = []

        # 添加标题
        response_parts.append(f"## 🔍 关于「{query}」的中医知识")

        # 添加主要内容
        if sources:
            response_parts.append("\n### 📚 专业解答")

            # 合并相关内容
            combined_content = self._combine_source_content(sources, query)
            response_parts.append(combined_content)

            # 添加来源信息
            response_parts.append("\n### 📖 参考来源")
            for i, source in enumerate(sources[:3], 1):
                source_type = "📁 本地文档" if source['type'] == 'local' else "🌐 在线资源"
                response_parts.append(f"{i}. {source_type}: {source['source']} (相关度: {source['score']:.2f})")

        # 添加建议和提醒
        response_parts.append(self._add_suggestions_and_warnings(query_type))

        return "\n".join(response_parts)

    def _analyze_query_type(self, query: str) -> str:
        """分析查询类型"""
        query_lower = query.lower()

        if any(keyword in query_lower for keyword in ["症状", "表现", "怎么办", "如何", "治疗"]):
            return "treatment"
        elif any(keyword in query_lower for keyword in ["是什么", "定义", "概念", "理论"]):
            return "definition"
        elif any(keyword in query_lower for keyword in ["预防", "养生", "保健", "调理"]):
            return "prevention"
        elif any(keyword in query_lower for keyword in ["药方", "方剂", "处方", "配方"]):
            return "prescription"
        else:
            return "general"

    def _combine_source_content(self, sources: List[Dict[str, Any]], query: str) -> str:
        """合并来源内容"""
        if not sources:
            return "暂未找到相关资料。"

        # 提取关键信息
        key_points = []
        for source in sources:
            content = source['content']

            # 提取与查询相关的句子
            sentences = re.split(r'[。！？\n]', content)
            relevant_sentences = []

            query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:
                    sentence_keywords = set(re.findall(r'[\u4e00-\u9fff]+', sentence))
                    if query_keywords.intersection(sentence_keywords):
                        relevant_sentences.append(sentence)

            if relevant_sentences:
                key_points.extend(relevant_sentences[:2])  # 每个来源最多2句

        if key_points:
            return "根据相关资料：\n\n" + "\n\n".join(f"• {point}" for point in key_points[:5])
        else:
            return "根据检索到的资料，找到了相关信息，但需要进一步分析。"

    def _add_suggestions_and_warnings(self, query_type: str) -> str:
        """添加建议和警告"""
        suggestions = {
            "treatment": "\n### 💡 重要提醒\n- 以上信息仅供中医文化学习参考\n- 如有健康问题，请及时咨询专业中医师\n- 不同体质的调理方法可能不同",
            "definition": "\n### 💡 学习建议\n- 建议结合经典中医著作深入学习\n- 理论与实践相结合，加深理解\n- 可以查阅更多相关概念扩展知识",
            "prevention": "\n### 💡 养生提醒\n- 养生方法因人而异，需要个体化调整\n- 坚持规律的生活作息很重要\n- 建议在专业指导下进行养生实践",
            "prescription": "\n### ⚠️ 重要警告\n- 方剂使用必须在专业中医师指导下进行\n- 切勿自行配制和服用中药\n- 不同体质用药差异很大，需要辨证施治",
            "general": "\n### 💡 学习提醒\n- 中医知识博大精深，建议系统学习\n- 理论学习要与实践相结合\n- 如有具体健康问题，请咨询专业医师"
        }

        return suggestions.get(query_type, suggestions["general"])

    def _generate_fallback_response(self, query: str) -> str:
        """生成备用回答"""
        return f"""## 🤖 智能助手回复

感谢您的提问：「{query}」

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库
- 检查网络连接，确保能够访问在线资源
- 尝试切换不同的检索模型

### 📚 系统功能：
- 🔍 **智能检索**: 支持本地文档和在线资源
- 🤖 **多模型**: 可切换不同的AI模型
- 📁 **多格式**: 支持PDF、Word、Excel等文档
- 🎤 **语音交互**: 支持语音输入和输出

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。如有健康问题，请咨询专业医疗机构。"""

# 会话管理器
class SessionManager:
    def __init__(self):
        self.sessions = {}

    def create_session(self, session_id: str = None) -> str:
        """创建新会话"""
        if session_id is None:
            session_id = str(uuid.uuid4())

        self.sessions[session_id] = {
            'id': session_id,
            'created_at': datetime.now().isoformat(),
            'messages': [],
            'settings': {
                'model': 'default',
                'voice_enabled': True,
                'auto_speak': False
            }
        }

        return session_id

    def add_message(self, session_id: str, message_type: str, content: str,
                   sources: List[Dict] = None, metadata: Dict = None):
        """添加消息到会话"""
        if session_id not in self.sessions:
            self.create_session(session_id)

        message = {
            'type': message_type,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'sources': sources or [],
            'metadata': metadata or {}
        }

        self.sessions[session_id]['messages'].append(message)

        # 保存到文件
        self._save_session(session_id)

    def get_session(self, session_id: str) -> Dict:
        """获取会话"""
        if session_id in self.sessions:
            return self.sessions[session_id]

        # 尝试从文件加载
        return self._load_session(session_id)

    def export_session(self, session_id: str, format: str = 'json') -> str:
        """导出会话"""
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"会话 {session_id} 不存在")

        if format == 'json':
            return json.dumps(session, ensure_ascii=False, indent=2)
        elif format == 'txt':
            return self._export_as_text(session)
        elif format == 'md':
            return self._export_as_markdown(session)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def _save_session(self, session_id: str):
        """保存会话到文件"""
        try:
            session_file = config.SESSIONS_DIR / f"{session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.sessions[session_id], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

    def _load_session(self, session_id: str) -> Dict:
        """从文件加载会话"""
        try:
            session_file = config.SESSIONS_DIR / f"{session_id}.json"
            if session_file.exists():
                with open(session_file, 'r', encoding='utf-8') as f:
                    session = json.load(f)
                    self.sessions[session_id] = session
                    return session
        except Exception as e:
            logger.error(f"加载会话失败: {e}")

        return {}

    def _export_as_text(self, session: Dict) -> str:
        """导出为文本格式"""
        lines = [
            f"中医智能助手对话记录",
            f"会话ID: {session['id']}",
            f"创建时间: {session['created_at']}",
            f"消息数量: {len(session['messages'])}",
            "=" * 50
        ]

        for msg in session['messages']:
            timestamp = msg['timestamp']
            msg_type = "用户" if msg['type'] == 'user' else "助手"
            content = msg['content']

            lines.append(f"\n[{timestamp}] {msg_type}:")
            lines.append(content)

            if msg.get('sources'):
                lines.append("\n参考来源:")
                for i, source in enumerate(msg['sources'], 1):
                    lines.append(f"{i}. {source.get('source', '未知来源')}")

        return "\n".join(lines)

    def _export_as_markdown(self, session: Dict) -> str:
        """导出为Markdown格式"""
        lines = [
            f"# 中医智能助手对话记录",
            f"",
            f"**会话ID**: {session['id']}",
            f"**创建时间**: {session['created_at']}",
            f"**消息数量**: {len(session['messages'])}",
            f"",
            "---"
        ]

        for msg in session['messages']:
            timestamp = msg['timestamp']
            msg_type = "👤 用户" if msg['type'] == 'user' else "🤖 助手"
            content = msg['content']

            lines.append(f"\n## {msg_type}")
            lines.append(f"*时间: {timestamp}*")
            lines.append(f"\n{content}")

            if msg.get('sources'):
                lines.append(f"\n### 📚 参考来源")
                for i, source in enumerate(msg['sources'], 1):
                    source_name = source.get('source', '未知来源')
                    score = source.get('score', 0)
                    lines.append(f"{i}. **{source_name}** (相关度: {score:.2f})")

        return "\n".join(lines)

# 全局实例
doc_processor = AdvancedDocumentProcessor()
crawler = OnlineResourceCrawler()
vector_system = MultiModelVectorSystem()
response_generator = IntelligentResponseGenerator(vector_system, crawler)
session_manager = SessionManager()

# 启动时初始化
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("🚀 启动终极版中医RAG系统...")

    # 加载现有向量数据库
    if vector_system.load_vector_database():
        logger.info("✅ 向量数据库加载成功")
    else:
        logger.info("⚠️ 未找到现有向量数据库")

    # 检查上传目录中的文档
    uploaded_files = list(config.UPLOAD_DIR.glob("*"))
    if uploaded_files:
        logger.info(f"📁 发现 {len(uploaded_files)} 个已上传文档，开始处理...")

        for file_path in uploaded_files:
            if file_path.suffix.lower() in doc_processor.supported_formats:
                try:
                    chunks = doc_processor.process_document(str(file_path))
                    vector_system.add_documents(chunks)
                    logger.info(f"✅ 处理文档: {file_path.name}")
                except Exception as e:
                    logger.error(f"❌ 处理文档失败 {file_path.name}: {e}")

        # 保存更新的向量数据库
        vector_system.save_vector_database()

    logger.info("🎉 系统初始化完成！")

# API路由
@app.get("/")
async def read_root():
    """返回主页"""
    return HTMLResponse(content=get_ultimate_html())

@app.get("/api/health")
async def health_check():
    """健康检查"""
    stats = vector_system.get_stats()

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "3.0.0",
        "features": [
            "多模型向量检索",
            "在线资源爬取",
            "多文档格式支持",
            "语音交互",
            "会话导出"
        ],
        "stats": stats,
        "advanced_ml": ADVANCED_ML_AVAILABLE
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """智能聊天接口"""
    try:
        start_time = time.time()

        # 创建或获取会话
        session_id = chat_message.session_id or session_manager.create_session()

        # 记录用户消息
        session_manager.add_message(
            session_id, 'user', chat_message.message,
            metadata={'use_voice': chat_message.use_voice}
        )

        # 生成回答
        response, sources, processing_time = await response_generator.generate_response(
            chat_message.message, chat_message.model_name or "default"
        )

        # 记录助手回答
        session_manager.add_message(
            session_id, 'assistant', response, sources,
            metadata={
                'model_used': chat_message.model_name or "default",
                'processing_time': processing_time
            }
        )

        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat(),
            model_used=chat_message.model_name or "default",
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"聊天处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.post("/api/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    results = []

    for file in files:
        try:
            # 检查文件大小
            if file.size > config.MAX_FILE_SIZE:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": f"文件过大 ({file.size / 1024 / 1024:.1f}MB > 50MB)"
                })
                continue

            # 检查文件类型
            file_suffix = Path(file.filename).suffix.lower()
            if file_suffix not in doc_processor.supported_formats:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": f"不支持的文件类型: {file_suffix}"
                })
                continue

            # 保存文件
            file_path = config.UPLOAD_DIR / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 处理文档
            chunks = doc_processor.process_document(str(file_path))

            # 添加到向量系统
            vector_system.add_documents(chunks)

            results.append({
                "filename": file.filename,
                "size": len(content),
                "chunks": len(chunks),
                "status": "success"
            })

            logger.info(f"✅ 文档上传成功: {file.filename} ({len(chunks)} 块)")

        except Exception as e:
            logger.error(f"❌ 文档上传失败 {file.filename}: {e}")
            results.append({
                "filename": file.filename,
                "status": "error",
                "error": str(e)
            })

    # 保存向量数据库
    try:
        vector_system.save_vector_database()
        logger.info("✅ 向量数据库更新完成")
    except Exception as e:
        logger.error(f"❌ 向量数据库保存失败: {e}")

    return {
        "results": results,
        "total_files": len(files),
        "success_count": len([r for r in results if r["status"] == "success"])
    }

@app.get("/api/models")
async def get_models():
    """获取可用模型列表"""
    stats = vector_system.get_stats()
    return {
        "available_models": stats['available_models'],
        "current_model": stats['current_model'],
        "model_info": {
            "default": "通用多语言模型",
            "chinese": "中文优化模型",
            "medical": "医学专用模型",
            "fast": "快速响应模型"
        }
    }

@app.post("/api/switch_model")
async def switch_model(model_name: str):
    """切换模型"""
    if vector_system.switch_model(model_name):
        return {"status": "success", "current_model": model_name}
    else:
        raise HTTPException(status_code=400, detail=f"模型 {model_name} 不可用")

@app.get("/api/sessions/{session_id}")
async def get_session(session_id: str):
    """获取会话"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    return session

@app.get("/api/sessions/{session_id}/export")
async def export_session(session_id: str, format: str = "json"):
    """导出会话"""
    try:
        content = session_manager.export_session(session_id, format)

        if format == "json":
            filename = f"session_{session_id}.json"
            media_type = "application/json"
        elif format == "txt":
            filename = f"session_{session_id}.txt"
            media_type = "text/plain"
        elif format == "md":
            filename = f"session_{session_id}.md"
            media_type = "text/markdown"
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

        # 保存临时文件
        temp_file = config.CACHE_DIR / filename
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)

        return FileResponse(
            path=str(temp_file),
            filename=filename,
            media_type=media_type
        )

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/documents")
async def list_documents():
    """列出已上传的文档"""
    documents = []

    for file_path in config.UPLOAD_DIR.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in doc_processor.supported_formats:
            stat = file_path.stat()
            documents.append({
                "filename": file_path.name,
                "size": stat.st_size,
                "type": file_path.suffix.lower(),
                "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "status": "processed"
            })

    stats = vector_system.get_stats()

    return {
        "documents": documents,
        "total_documents": len(documents),
        "total_chunks": stats['total_chunks'],
        "vector_stores": stats['vector_stores']
    }

@app.delete("/api/documents/{filename}")
async def delete_document(filename: str):
    """删除文档"""
    file_path = config.UPLOAD_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="文档不存在")

    try:
        file_path.unlink()

        # 重建向量数据库
        vector_system.document_chunks = []
        vector_system.vector_stores = {}

        # 重新处理所有文档
        for remaining_file in config.UPLOAD_DIR.iterdir():
            if remaining_file.is_file() and remaining_file.suffix.lower() in doc_processor.supported_formats:
                try:
                    chunks = doc_processor.process_document(str(remaining_file))
                    vector_system.add_documents(chunks)
                except Exception as e:
                    logger.error(f"重新处理文档失败 {remaining_file.name}: {e}")

        vector_system.save_vector_database()

        return {"message": f"文档 {filename} 删除成功"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@app.get("/api/stats")
async def get_system_stats():
    """获取系统统计信息"""
    stats = vector_system.get_stats()

    # 计算文档统计
    doc_stats = {}
    for file_path in config.UPLOAD_DIR.iterdir():
        if file_path.is_file():
            ext = file_path.suffix.lower()
            doc_stats[ext] = doc_stats.get(ext, 0) + 1

    # 计算会话统计
    session_count = len(list(config.SESSIONS_DIR.glob("*.json")))

    return {
        "system": {
            "version": "3.0.0",
            "advanced_ml": ADVANCED_ML_AVAILABLE,
            "jieba_available": JIEBA_AVAILABLE
        },
        "documents": {
            "total_files": len(list(config.UPLOAD_DIR.iterdir())),
            "by_type": doc_stats,
            "total_chunks": stats['total_chunks']
        },
        "models": {
            "available": stats['available_models'],
            "current": stats['current_model'],
            "vector_stores": stats['vector_stores']
        },
        "sessions": {
            "total": session_count
        }
    }

# 前端HTML
def get_ultimate_html() -> str:
    """获取终极版前端HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 终极版中医RAG系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; overflow: hidden;
        }
        .main-container {
            height: 100vh; display: flex; flex-direction: column;
            max-width: 1400px; margin: 0 auto; background: white;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 15px 20px; display: flex;
            justify-content: space-between; align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 { font-size: 20px; }
        .header-controls {
            display: flex; gap: 15px; align-items: center;
        }
        .model-selector select {
            padding: 5px 10px; border: none; border-radius: 5px;
            background: rgba(255,255,255,0.9); font-size: 14px;
        }
        .stats-badge {
            background: rgba(255,255,255,0.2); padding: 5px 10px;
            border-radius: 15px; font-size: 12px;
        }
        .main-content { flex: 1; display: flex; overflow: hidden; }
        .sidebar {
            width: 300px; background: #f8f9fa; border-right: 1px solid #e9ecef;
            display: flex; flex-direction: column; overflow-y: auto;
        }
        .chat-area { flex: 1; display: flex; flex-direction: column; }
        .messages-container {
            flex: 1; overflow-y: auto; padding: 20px; background: #fafafa;
        }
        .message { margin-bottom: 20px; display: flex; align-items: flex-start; }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 70%; padding: 12px 16px; border-radius: 18px;
            word-wrap: break-word; position: relative;
        }
        .message.user .message-content {
            background: #2E8B57; color: white; border-bottom-right-radius: 4px;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #e9ecef; border-bottom-left-radius: 4px;
        }
        .message-avatar {
            width: 40px; height: 40px; border-radius: 50%; margin: 0 10px;
            display: flex; align-items: center; justify-content: center;
            font-size: 18px;
        }
        .user-avatar { background: #2E8B57; color: white; }
        .assistant-avatar { background: #667eea; color: white; }
        .voice-controls {
            position: absolute; top: 5px; right: 5px; display: flex; gap: 5px;
        }
        .voice-btn {
            width: 24px; height: 24px; border: none; border-radius: 50%;
            background: #f0f0f0; cursor: pointer; display: flex;
            align-items: center; justify-content: center; font-size: 12px;
        }
        .voice-btn:hover { background: #e0e0e0; }
        .input-container {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; align-items: flex-end; }
        .input-textarea { flex: 1; position: relative; }
        .input-textarea textarea {
            width: 100%; padding: 12px 50px 12px 12px; border: 1px solid #ddd;
            border-radius: 12px; resize: none; font-size: 16px;
            transition: border-color 0.3s;
        }
        .input-textarea textarea:focus {
            outline: none; border-color: #2E8B57;
        }
        .voice-input-btn {
            position: absolute; right: 10px; top: 50%; transform: translateY(-50%);
            width: 36px; height: 36px; border: none; border-radius: 50%;
            background: #2E8B57; color: white; cursor: pointer;
            display: flex; align-items: center; justify-content: center;
            font-size: 16px; transition: all 0.3s;
        }
        .voice-input-btn:hover { background: #228B22; transform: translateY(-50%) scale(1.05); }
        .voice-input-btn.recording {
            background: #dc3545; animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.1); }
            100% { transform: translateY(-50%) scale(1); }
        }
        .send-btn {
            padding: 12px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 12px; cursor: pointer; font-size: 16px;
            transition: background 0.3s;
        }
        .send-btn:hover { background: #228B22; }
        .send-btn:disabled { background: #ccc; cursor: not-allowed; }
        .sidebar-section {
            padding: 15px; border-bottom: 1px solid #e9ecef;
        }
        .sidebar-section h3 { font-size: 14px; color: #666; margin-bottom: 10px; }
        .quick-btn, .action-btn {
            width: 100%; margin-bottom: 8px; padding: 10px; background: white;
            border: 1px solid #ddd; border-radius: 8px; cursor: pointer;
            text-align: left; font-size: 14px; transition: all 0.3s;
        }
        .quick-btn:hover, .action-btn:hover { background: #f0f0f0; border-color: #2E8B57; }
        .upload-area {
            border: 2px dashed #ddd; border-radius: 8px; padding: 15px;
            text-align: center; margin-bottom: 10px;
        }
        .upload-area.dragover { border-color: #2E8B57; background: #f0f8f0; }
        .file-input { display: none; }
        .typing { color: #666; font-style: italic; }
        .typing-dots { display: inline-flex; gap: 3px; }
        .typing-dot {
            width: 6px; height: 6px; border-radius: 50%; background: #666;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        .notification {
            position: fixed; top: 20px; right: 20px; padding: 15px 20px;
            background: #2E8B57; color: white; border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2); z-index: 1000;
            transform: translateX(400px); transition: transform 0.3s;
        }
        .notification.show { transform: translateX(0); }
        .notification.error { background: #dc3545; }
        .notification.warning { background: #ffc107; color: #000; }
        @media (max-width: 768px) {
            .sidebar { display: none; }
            .message-content { max-width: 85%; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部 -->
        <div class="header">
            <h1>🏥 终极版中医RAG系统</h1>
            <div class="header-controls">
                <div class="model-selector">
                    <select id="modelSelect" onchange="switchModel()">
                        <option value="default">通用模型</option>
                        <option value="chinese">中文模型</option>
                        <option value="medical">医学模型</option>
                        <option value="fast">快速模型</option>
                    </select>
                </div>
                <div class="stats-badge" id="statsBadge">
                    📊 加载中...
                </div>
                <div class="stats-badge">
                    🎤 语音就绪
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 快捷查询 -->
                <div class="sidebar-section">
                    <h3>💡 快捷查询</h3>
                    <button class="quick-btn" onclick="sendQuickMessage('脾胃虚弱的症状和调理方法')">
                        🌿 脾胃调理
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('肾阳虚和肾阴虚的区别及治疗')">
                        🫖 肾虚调养
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('失眠多梦的中医治疗方案')">
                        😴 失眠治疗
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('高血压的中医调理方法')">
                        💓 血压调理
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('糖尿病的中医预防和治疗')">
                        🍯 糖尿病
                    </button>
                </div>

                <!-- 文档上传 -->
                <div class="sidebar-section">
                    <h3>📁 文档上传</h3>
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                        <div>📄 点击或拖拽上传文档</div>
                        <small>支持PDF、Word、Excel、TXT</small>
                    </div>
                    <input type="file" id="fileInput" class="file-input" multiple
                           accept=".pdf,.doc,.docx,.txt,.xlsx,.xls" onchange="uploadFiles()">
                    <button class="action-btn" onclick="viewDocuments()">
                        📚 查看已上传文档
                    </button>
                </div>

                <!-- 会话管理 -->
                <div class="sidebar-section">
                    <h3>💬 会话管理</h3>
                    <button class="action-btn" onclick="exportSession('json')">
                        📄 导出为JSON
                    </button>
                    <button class="action-btn" onclick="exportSession('md')">
                        📝 导出为Markdown
                    </button>
                    <button class="action-btn" onclick="exportSession('txt')">
                        📋 导出为文本
                    </button>
                    <button class="action-btn" onclick="clearSession()">
                        🗑️ 清空会话
                    </button>
                </div>

                <!-- 系统信息 -->
                <div class="sidebar-section">
                    <h3>📊 系统信息</h3>
                    <div id="systemInfo" style="font-size: 12px; color: #666;">
                        加载中...
                    </div>
                </div>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area">
                <!-- 消息容器 -->
                <div class="messages-container" id="messagesContainer">
                    <div class="message assistant">
                        <div class="message-avatar assistant-avatar">🤖</div>
                        <div class="message-content">
                            <strong>🤖 终极助手:</strong> 您好！我是终极版中医RAG系统。我具备以下强大功能：<br><br>
                            🔍 <strong>智能检索</strong>: 同时搜索您上传的PDF文档和在线医学资源<br>
                            🤖 <strong>多模型切换</strong>: 支持通用、中文、医学、快速等多种AI模型<br>
                            📁 <strong>多格式支持</strong>: PDF、Word、Excel、TXT等文档格式<br>
                            🎤 <strong>语音交互</strong>: 语音输入和语音输出功能<br>
                            💾 <strong>会话导出</strong>: 支持JSON、Markdown、文本格式导出<br><br>
                            请问有什么可以帮助您的吗？
                            <div class="voice-controls">
                                <button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-row">
                        <div class="input-textarea">
                            <textarea id="messageInput" rows="2" placeholder="请输入您的中医问题，或点击麦克风使用语音输入..." onkeydown="handleKeyDown(event)"></textarea>
                            <button class="voice-input-btn" id="voiceInputBtn" onclick="toggleVoiceRecording()" title="语音输入">
                                🎤
                            </button>
                        </div>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification" id="notification">
        <div id="notificationText"></div>
    </div>

    <script>
        // 全局变量
        let currentSessionId = null;
        let currentModel = 'default';
        let isTyping = false;
        let recognition = null;
        let synthesis = null;
        let isRecording = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            initializeVoice();
            initializeDragDrop();
            loadSystemStats();
        });

        // 系统初始化
        async function initializeSystem() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                updateStatsBadge(data.stats);
                showNotification('✅ 系统初始化完成', 'success');

            } catch (error) {
                showNotification('❌ 系统初始化失败: ' + error.message, 'error');
            }
        }

        // 语音功能初始化
        function initializeVoice() {
            // 语音识别
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    isRecording = true;
                    updateVoiceButton();
                    showNotification('🎤 正在录音，请说话...', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    showNotification('🎤 语音识别完成: ' + transcript, 'success');
                };

                recognition.onerror = function(event) {
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    updateVoiceButton();
                };
            }

            // 语音合成
            if ('speechSynthesis' in window) {
                synthesis = window.speechSynthesis;
            }
        }

        // 拖拽上传初始化
        function initializeDragDrop() {
            const uploadArea = document.getElementById('uploadArea');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files);
                }
            });
        }

        // 加载系统统计
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                const infoDiv = document.getElementById('systemInfo');
                infoDiv.innerHTML = `
                    <div><strong>文档:</strong> ${data.documents.total_files} 个</div>
                    <div><strong>文本块:</strong> ${data.documents.total_chunks} 个</div>
                    <div><strong>当前模型:</strong> ${data.models.current}</div>
                    <div><strong>会话数:</strong> ${data.sessions.total} 个</div>
                    <div><strong>ML支持:</strong> ${data.system.advanced_ml ? '✅' : '❌'}</div>
                `;

            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 更新统计徽章
        function updateStatsBadge(stats) {
            const badge = document.getElementById('statsBadge');
            badge.textContent = `📊 ${stats.total_chunks} 块文档`;
        }

        // 切换模型
        async function switchModel() {
            const select = document.getElementById('modelSelect');
            const modelName = select.value;

            try {
                const response = await fetch('/api/switch_model', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ model_name: modelName })
                });

                if (response.ok) {
                    currentModel = modelName;
                    showNotification(`✅ 已切换到${select.options[select.selectedIndex].text}`, 'success');
                } else {
                    throw new Error('模型切换失败');
                }

            } catch (error) {
                showNotification('❌ 模型切换失败: ' + error.message, 'error');
                select.value = currentModel; // 恢复原选择
            }
        }

        // 语音录音切换
        function toggleVoiceRecording() {
            if (!recognition) {
                showNotification('⚠️ 浏览器不支持语音识别', 'warning');
                return;
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 更新语音按钮
        function updateVoiceButton() {
            const btn = document.getElementById('voiceInputBtn');
            if (isRecording) {
                btn.classList.add('recording');
                btn.textContent = '🛑';
            } else {
                btn.classList.remove('recording');
                btn.textContent = '🎤';
            }
        }

        // 朗读消息
        function speakMessage(button) {
            if (!synthesis) {
                showNotification('⚠️ 浏览器不支持语音合成', 'warning');
                return;
            }

            const messageContent = button.closest('.message-content');
            const text = messageContent.textContent.replace(/🤖 终极助手:|👤 您:/g, '').trim();

            synthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 1.0;
            utterance.pitch = 1;

            const voices = synthesis.getVoices();
            const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
            if (chineseVoice) {
                utterance.voice = chineseVoice;
            }

            synthesis.speak(utterance);
            showNotification('🔊 开始朗读', 'info');
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在智能检索中...', 'typing');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId,
                        model_name: currentModel,
                        use_voice: false
                    })
                });

                const data = await response.json();

                // 更新会话ID
                currentSessionId = data.session_id;

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 显示处理时间
                showNotification(`⚡ 处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
                showNotification('❌ 请求失败: ' + error.message, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${type}-avatar`;
            avatarDiv.textContent = type === 'user' ? '👤' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const prefix = type === 'user' ? '👤 您:' : '🤖 终极助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\\n/g, '<br>')}`;

            // 为助手消息添加语音控制
            if (type === 'assistant' && !className.includes('typing')) {
                const voiceControls = document.createElement('div');
                voiceControls.className = 'voice-controls';
                voiceControls.innerHTML = '<button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>';
                contentDiv.appendChild(voiceControls);
            }

            if (type === 'user') {
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(avatarDiv);
            } else {
                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(contentDiv);
            }

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }

        // 文件上传
        function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;

            if (files.length === 0) {
                showNotification('⚠️ 请选择文件', 'warning');
                return;
            }

            handleFileUpload(files);
        }

        // 处理文件上传
        async function handleFileUpload(files) {
            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }

            try {
                showNotification('📁 正在上传和处理文档...', 'info');

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                let successCount = data.success_count;
                let totalCount = data.total_files;

                if (successCount === totalCount) {
                    showNotification(`✅ ${successCount} 个文档上传成功！`, 'success');
                } else {
                    showNotification(`⚠️ ${successCount}/${totalCount} 个文档上传成功`, 'warning');
                }

                // 刷新统计信息
                loadSystemStats();

                // 清空文件输入
                document.getElementById('fileInput').value = '';

            } catch (error) {
                showNotification('❌ 文档上传失败: ' + error.message, 'error');
            }
        }

        // 查看文档
        async function viewDocuments() {
            try {
                const response = await fetch('/api/documents');
                const data = await response.json();

                let message = `📚 已上传 ${data.total_documents} 个文档:\\n\\n`;
                data.documents.forEach((doc, index) => {
                    message += `${index + 1}. ${doc.filename} (${(doc.size / 1024).toFixed(1)}KB)\\n`;
                });

                alert(message);

            } catch (error) {
                showNotification('❌ 获取文档列表失败: ' + error.message, 'error');
            }
        }

        // 导出会话
        async function exportSession(format) {
            if (!currentSessionId) {
                showNotification('⚠️ 当前没有活跃会话', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/sessions/${currentSessionId}/export?format=${format}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `session_${currentSessionId}.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showNotification(`✅ 会话已导出为 ${format.toUpperCase()} 格式`, 'success');
                } else {
                    throw new Error('导出失败');
                }

            } catch (error) {
                showNotification('❌ 导出失败: ' + error.message, 'error');
            }
        }

        // 清空会话
        function clearSession() {
            if (confirm('确定要清空当前会话吗？')) {
                document.getElementById('messagesContainer').innerHTML = '';
                currentSessionId = null;
                showNotification('✅ 会话已清空', 'success');

                // 添加欢迎消息
                addMessage('assistant', '您好！我是终极版中医RAG系统。请问有什么可以帮助您的吗？');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');

            text.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
"""

if __name__ == "__main__":
    print("🚀 启动终极版中医RAG系统...")
    print("🎯 功能特色:")
    print("  🔍 智能检索: PDF文档 + 在线医学资源")
    print("  🤖 多模型: 通用/中文/医学/快速模型切换")
    print("  📁 多格式: PDF/Word/Excel/TXT文档支持")
    print("  🎤 语音交互: 语音输入和输出")
    print("  💾 会话导出: JSON/Markdown/文本格式")
    print("  🌐 在线爬取: 自动获取最新医学资料")
    print()
    print("🌐 访问地址: http://localhost:8004")
    print("📚 API文档: http://localhost:8004/docs")
    print("🔍 健康检查: http://localhost:8004/api/health")
    print()
    print("💡 使用提示:")
    print("  - 上传PDF文档扩充知识库")
    print("  - 切换不同AI模型获得最佳效果")
    print("  - 使用语音输入提高交互体验")
    print("  - 导出会话记录保存重要对话")

    uvicorn.run(
        "ultimate_tcm_system:app",
        host="0.0.0.0",
        port=8004,
        reload=True,
        log_level="info"
    )
