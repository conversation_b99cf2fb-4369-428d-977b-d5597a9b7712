# 🏥 家庭私人医生小帮手 - TCM RAG系统

> **您的专属健康顾问，基于AI和医学知识库的智能问答助手**

## 🌟 系统概述

家庭私人医生小帮手是一个基于先进AI技术的中医智能问答系统，结合了传统中医理论与现代人工智能技术，为用户提供专业、准确、个性化的健康咨询服务。

### 🎯 核心特色

- **🧠 DeepSeek-R1智能引擎** - 采用最新的DeepSeek-R1-0528-Qwen3-8B模型
- **📚 本地m3e-base向量化** - 使用本地m3e-base中文嵌入模型，无网络依赖
- **🚀 MCP智能检索** - 基于Model Control Protocol的多领域智能检索
- **📜 古代医书检索** - 专属GitHub仓库的古代医书知识库
- **🎤 语音交互** - 支持语音输入和语音播放
- **🌐 远程访问** - 支持Ngrok隧道的远程访问功能
- **🔄 自动化初始化** - 系统启动时自动初始化所有功能

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    家庭私人医生小帮手                          │
├─────────────────────────────────────────────────────────────┤
│  🎨 前端界面 (Streamlit)                                     │
│  ├── 智能对话界面                                            │
│  ├── 文档上传管理                                            │
│  ├── 语音交互控制                                            │
│  └── 远程访问管理                                            │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI推理引擎                                               │
│  ├── DeepSeek-R1-0528-Qwen3-8B (本地LM Studio)              │
│  ├── 智能回答生成                                            │
│  ├── 对话上下文管理                                          │
│  └── 症状分析与建议                                          │
├─────────────────────────────────────────────────────────────┤
│  🔍 智能检索系统                                             │
│  ├── 📚 向量数据库 (FAISS + m3e-base)                       │
│  ├── 🚀 MCP检索器 (FastMCP + Elasticsearch)                 │
│  ├── 📜 古代医书检索 (GitHub仓库)                            │
│  └── 🎯 多领域智能匹配                                       │
├─────────────────────────────────────────────────────────────┤
│  💾 数据存储层                                               │
│  ├── PDF文档向量化存储                                       │
│  ├── 对话历史记录                                            │
│  ├── 用户画像分析                                            │
│  └── 系统配置管理                                            │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 📋 系统要求

- **操作系统**: Windows 10/11, macOS, Linux
- **Python版本**: 3.8+
- **内存**: 建议16GB以上
- **存储**: 至少10GB可用空间
- **网络**: 首次安装需要网络连接

### 🔧 安装步骤

#### 1️⃣ 克隆项目
```bash
git clone <项目地址>
cd RAG-2025
```

#### 2️⃣ 安装依赖
```bash
python install_dependencies.py
```

#### 3️⃣ 下载m3e模型
```bash
python download_m3e_model.py
```

#### 4️⃣ 配置LM Studio（可选）
1. 下载并安装 [LM Studio](https://lmstudio.ai/)
2. 下载DeepSeek-R1-0528-Qwen3-8B模型
3. 启动Local Server (端口1234)

#### 5️⃣ 启动系统
```bash
python start_tcm_system.py
```
或直接运行：
```bash
streamlit run ultimate_final_tcm_system.py
```

### 🌐 访问地址
- **本地访问**: http://localhost:8507
- **远程访问**: 通过Ngrok隧道（系统内启动）

## 🎛️ 功能详解

### 🧠 AI智能问答

#### 核心功能
- **智能症状分析** - 基于中医理论的症状识别和分析
- **个性化治疗建议** - 根据用户体质和症状提供定制化建议
- **古今结合** - 融合古代医书智慧与现代医学知识
- **安全提醒** - 自动添加医疗安全警告和就医建议

#### 使用方法
1. 在对话框中输入症状描述
2. 系统自动分析并检索相关资料
3. 获得专业的中医分析和建议
4. 可选择语音播放回答内容

### 📚 文档管理系统

#### 支持格式
- **PDF文档** - 医学教材、论文、病例
- **Word文档** - 临床记录、治疗方案
- **Excel表格** - 药物配伍、症状统计
- **PowerPoint** - 医学课件、培训资料
- **文本文件** - 医案记录、经验总结

#### 智能处理
- **自动分块** - 智能分割长文档为合适的检索单元
- **向量化存储** - 使用m3e-base模型进行中文向量化
- **语义检索** - 基于语义相似度的精准检索
- **领域分类** - 自动识别文档所属医学领域

### 🚀 MCP智能检索

#### 检索领域
- **医学领域** - 中医、现代医学、药学
- **法律领域** - 医疗法规、医患关系
- **教育领域** - 医学教育、培训资料
- **商业领域** - 医疗管理、健康产业
- **技术领域** - 医疗设备、信息化
- **文学领域** - 医学典籍、文献研究

#### 检索模式
- **快速检索** - 3秒内返回核心结果
- **综合检索** - 多源并行检索，结果全面
- **深度检索** - 扩展查询词，挖掘深层关联

### 📜 古代医书检索

#### 收录典籍
- **医宗金鉴** - 清代官修医学全书
- **黄帝内经** - 中医理论基础经典
- **伤寒论** - 外感病治疗经典
- **金匮要略** - 内科杂病治疗经典
- **本草纲目** - 中药学集大成之作
- **中医药学** - 现代中医药理论
- **针灸学** - 针灸理论与实践
- **温病学** - 温病理论与治疗

#### 检索特色
- **专属仓库** - 使用个人GitHub仓库，稳定可靠
- **无限检索** - 不受第三方网站限制
- **智能匹配** - 基于症状和治法的智能匹配
- **原文引用** - 提供古籍原文和现代解释

### 🎤 语音交互

#### 语音输入
- **实时识别** - 支持中文语音实时转文字
- **噪音过滤** - 智能过滤环境噪音
- **方言支持** - 支持多种中文方言
- **长语音处理** - 支持长时间连续语音输入

#### 语音播放
- **自然发音** - 使用高质量TTS引擎
- **语速控制** - 可调节播放语速
- **情感表达** - 根据内容调整语调
- **后台播放** - 支持后台连续播放

### 🌐 远程访问

#### Ngrok隧道
- **一键启动** - 系统内一键启动远程访问
- **安全认证** - 密码保护：MVP168918
- **稳定连接** - 自动重连机制
- **状态监控** - 实时监控隧道状态

#### 移动适配
- **响应式设计** - 自适应手机、平板屏幕
- **触控优化** - 针对触控设备优化交互
- **网络优化** - 针对移动网络优化传输
- **离线缓存** - 支持部分内容离线访问

## ⚙️ 配置说明

### 🔧 系统配置

```python
CONFIG = {
    'EMBEDDING_MODEL': './models/m3e-base',  # 本地m3e模型路径
    'VECTOR_DB_PATH': './ultimate_final_vector_db',  # 向量数据库路径
    'CONVERSATION_PATH': './conversations',  # 对话记录路径
    'BATCH_SIZE': 16,  # 批处理大小
    'TOP_K': 5,  # 检索结果数量
    'MIN_RELEVANCE_SCORE': 0.05,  # 最小相关度阈值
    'EXHAUSTIVE_SEARCH': True,  # 启用穷尽搜索
    'REFLECTION_SCORING': True,  # 启用反思打分
}
```

### 🎯 模型配置

#### DeepSeek模型
- **模型路径**: `C:/Users/<USER>/.lmstudio/models/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/`
- **API端点**: `http://localhost:1234/v1/chat/completions`
- **上下文长度**: 32K tokens
- **温度参数**: 0.7

#### m3e-base模型
- **模型路径**: `./models/m3e-base`
- **嵌入维度**: 768
- **支持语言**: 中文、英文
- **最大序列长度**: 512

### 🌐 网络配置

#### MCP服务
- **服务端口**: 8003
- **健康检查**: `/health`
- **服务信息**: `/info`
- **MCP端点**: `/mcp`

#### Ngrok配置
- **认证密码**: MVP168918
- **隧道类型**: HTTP
- **自动重连**: 启用
- **状态监控**: 启用

## 📊 性能优化

### 🚀 启动优化
- **自动初始化** - 系统启动时自动初始化所有组件
- **并行加载** - 多组件并行初始化，减少启动时间
- **缓存机制** - 智能缓存已加载的模型和数据
- **增量更新** - 支持增量更新向量数据库

### 💾 存储优化
- **压缩存储** - 向量数据压缩存储，节省空间
- **分片管理** - 大文档分片处理，提高效率
- **清理机制** - 自动清理过期数据和临时文件
- **备份恢复** - 支持数据备份和恢复功能

### 🔍 检索优化
- **多级缓存** - 查询结果多级缓存机制
- **智能预取** - 预测性加载相关内容
- **并行检索** - 多源并行检索，提高速度
- **结果去重** - 智能去重和排序算法

## 🛡️ 安全与隐私

### 🔒 数据安全
- **本地存储** - 所有数据本地存储，不上传云端
- **加密传输** - 网络传输数据加密保护
- **访问控制** - 远程访问密码保护
- **日志审计** - 完整的操作日志记录

### 🏥 医疗安全
- **免责声明** - 明确系统仅供参考，不替代医生诊断
- **安全提醒** - 自动添加就医建议和安全警告
- **内容审核** - 智能过滤不当医疗建议
- **专业标注** - 明确标注信息来源和可信度

## 🔧 故障排除

### ❌ 常见问题

#### 1. 向量数据库初始化失败
**问题**: 显示"向量数据库初始化失败"
**解决方案**:
```bash
# 检查m3e模型是否存在
ls ./models/m3e-base/

# 重新下载模型
python download_m3e_model.py

# 清理缓存重启
rm -rf ./ultimate_final_vector_db/
streamlit run ultimate_final_tcm_system.py
```

#### 2. DeepSeek模型连接失败
**问题**: 显示"DeepSeek模型未就绪"
**解决方案**:
1. 确保LM Studio已启动
2. 在LM Studio中加载DeepSeek模型
3. 启动Local Server (端口1234)
4. 检查防火墙设置

#### 3. MCP检索器初始化失败
**问题**: 显示"MCP检索器初始化失败"
**解决方案**:
```bash
# 检查端口占用
netstat -an | findstr 8003

# 重启MCP服务
python fastmcp_elasticsearch_service.py

# 检查依赖
pip install fastapi uvicorn
```

#### 4. 语音功能不可用
**问题**: 语音输入或播放失败
**解决方案**:
```bash
# 安装语音依赖
pip install pyttsx3 SpeechRecognition pyaudio

# Windows用户可能需要
pip install pipwin
pipwin install pyaudio
```

### 📞 技术支持

如遇到其他问题，请：
1. 查看系统日志文件
2. 检查网络连接状态
3. 确认所有依赖已正确安装
4. 重启系统尝试解决

## 🔄 更新日志

### v2.0.0 (最新版本)
- ✅ 集成本地m3e-base中文嵌入模型
- ✅ 删除TF-IDF备选方案，专注向量检索
- ✅ 更新古代医书检索到专属GitHub仓库
- ✅ 实现系统自动化初始化
- ✅ 优化MCP智能检索性能
- ✅ 增强语音交互体验
- ✅ 完善远程访问功能

### v1.0.0
- 🎉 首次发布
- 📚 基础PDF文档检索
- 🧠 DeepSeek模型集成
- 🎤 语音交互功能
- 🌐 Web界面设计

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎贡献代码和建议！请：
1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 发起 Pull Request

## 📧 联系方式

- **项目维护者**: BillHCM7777779
- **GitHub**: https://github.com/BillHCM7777779/gudaiyishu
- **问题反馈**: 通过GitHub Issues

---

**⚠️ 重要声明**: 本系统仅供学习和参考使用，不能替代专业医生的诊断和治疗。如有健康问题，请及时就医。
