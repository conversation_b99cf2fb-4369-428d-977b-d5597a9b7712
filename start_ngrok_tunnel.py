#!/usr/bin/env python3
"""
自动启动ngrok隧道的脚本
"""
import subprocess
import sys
import time
import requests
import re
import json
import os

def check_server_running():
    """检查本地服务器是否运行"""
    try:
        response = requests.get('http://localhost:8006/api/health', 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 中医RAG系统运行正常")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
            return True
        else:
            print("❌ 服务器响应异常")
            return False
    except Exception as e:
        print(f"❌ 无法连接到本地服务器: {e}")
        print("💡 请确保中医RAG系统正在运行")
        print("   运行命令: python simple_ultimate_tcm.py")
        return False

def check_ngrok_installed():
    """检查ngrok是否已安装"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ ngrok已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ ngrok命令执行失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到ngrok命令")
        print("💡 请确保:")
        print("   1. ngrok已下载并解压")
        print("   2. ngrok.exe在当前目录或PATH中")
        return False
    except Exception as e:
        print(f"❌ 检查ngrok时出错: {e}")
        return False

def setup_ngrok_auth():
    """设置ngrok认证"""
    print("\n🔧 配置ngrok认证")
    print("=" * 40)
    
    # 检查是否已配置
    try:
        result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True, timeout=10)
        if "valid" in result.stdout.lower():
            print("✅ ngrok认证已配置")
            return True
    except:
        pass
    
    print("⚠️ 需要配置ngrok认证令牌")
    print("\n📋 请按以下步骤操作:")
    print("1. 访问: https://dashboard.ngrok.com/")
    print("2. 登录您的ngrok账户")
    print("3. 找到 'Your Authtoken' 部分")
    print("4. 复制您的authtoken")
    
    authtoken = input("\n🔑 请输入您的ngrok authtoken: ").strip()
    
    if not authtoken:
        print("❌ 未输入authtoken")
        return False
    
    try:
        result = subprocess.run(['ngrok', 'config', 'add-authtoken', authtoken], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ ngrok认证配置成功")
            return True
        else:
            print(f"❌ 认证配置失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 配置认证时出错: {e}")
        return False

def start_ngrok_tunnel():
    """启动ngrok隧道"""
    print("\n🚀 启动ngrok隧道")
    print("=" * 40)
    
    try:
        # 启动ngrok隧道
        print("🌐 正在启动隧道到端口8006...")
        process = subprocess.Popen(['ngrok', 'http', '8006'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, 
                                 text=True)
        
        # 等待隧道启动
        print("⏳ 等待隧道建立...")
        time.sleep(5)
        
        # 获取隧道信息
        tunnel_url = get_tunnel_url()
        
        if tunnel_url:
            print(f"\n🎉 ngrok隧道启动成功！")
            print("=" * 60)
            print(f"🏥 中医RAG智能诊疗系统")
            print(f"🌐 公网访问地址: {tunnel_url}")
            print(f"🔐 登录凭据:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            print("=" * 60)
            
            # 生成分享信息
            generate_share_info(tunnel_url)
            
            # 测试隧道访问
            test_tunnel_access(tunnel_url)
            
            # 保持隧道运行
            keep_tunnel_running(process, tunnel_url)
            
        else:
            print("❌ 无法获取隧道URL")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"❌ 启动隧道失败: {e}")
        return False

def get_tunnel_url():
    """获取ngrok隧道URL"""
    try:
        # 尝试从ngrok API获取隧道信息
        response = requests.get('http://127.0.0.1:4040/api/tunnels', timeout=10)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
                    
        return None
    except Exception as e:
        print(f"⚠️ 无法从API获取隧道信息: {e}")
        return None

def generate_share_info(tunnel_url):
    """生成分享信息"""
    share_info = f"""
📱 分享给朋友的完整信息:
=" * 60
🏥 【中医RAG智能诊疗系统】

🌐 访问地址: {tunnel_url}

🔐 登录信息:
   用户名: tcm_user
   密码: MVP168918

💡 使用说明:
1. 点击上述网址打开系统
2. 输入用户名和密码登录
3. 即可使用中医智能诊疗功能

🎯 系统功能:
• DeepSeek-R1智能诊疗分析 ✅
• 在线医学资源检索 ✅
• PDF文档上传分析 ✅
• 语音交互支持 ✅
• 会话历史管理 ✅

⚠️ 注意事项:
- 请妥善保管登录凭据
- 仅供家人朋友使用
- 系统24小时可用
=" * 60
"""
    
    print(share_info)
    
    # 保存到文件
    try:
        with open('分享信息.txt', 'w', encoding='utf-8') as f:
            f.write(share_info)
        print("💾 分享信息已保存到 '分享信息.txt' 文件")
    except Exception as e:
        print(f"⚠️ 保存分享信息失败: {e}")

def test_tunnel_access(tunnel_url):
    """测试隧道访问"""
    try:
        print("\n🧪 测试隧道访问...")
        
        # 测试健康检查
        health_url = f"{tunnel_url}/api/health"
        response = requests.get(health_url, auth=('tcm_user', 'MVP168918'), timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 隧道访问测试成功！")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
            return True
        else:
            print(f"❌ 隧道访问测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 隧道访问测试异常: {e}")
        return False

def keep_tunnel_running(process, tunnel_url):
    """保持隧道运行"""
    try:
        print(f"\n⏳ 隧道保持运行中... (按Ctrl+C停止)")
        print("💡 请保持此窗口打开，关闭后隧道将断开")
        print(f"🌐 监控面板: http://127.0.0.1:4040")
        print("-" * 60)
        
        while True:
            if process.poll() is not None:
                print("❌ ngrok进程意外退出")
                break
            
            time.sleep(60)
            current_time = time.strftime('%H:%M:%S')
            print(f"🔄 隧道运行中: {tunnel_url} ({current_time})")
            
    except KeyboardInterrupt:
        print("\n🛑 正在停止隧道...")
        process.terminate()
        process.wait()
        print("🛑 隧道已停止")
        print("👋 感谢使用中医RAG智能诊疗系统！")

def main():
    """主函数"""
    print("🚀 中医RAG系统 - ngrok隧道自动设置")
    print("=" * 50)
    
    # 1. 检查本地服务器
    if not check_server_running():
        return
    
    # 2. 检查ngrok安装
    if not check_ngrok_installed():
        return
    
    # 3. 配置ngrok认证
    if not setup_ngrok_auth():
        return
    
    # 4. 启动隧道
    start_ngrok_tunnel()

if __name__ == "__main__":
    main()
