#!/usr/bin/env python3
"""
🚀 完美统一中医智能助手 - 启动脚本
自动启动所有必要服务，确保系统完美运行
"""

import subprocess
import sys
import time
import webbrowser
import requests
from pathlib import Path
import json

def check_and_start_mcp_service():
    """检查并启动智能MCP服务"""
    print("🔍 检查智能MCP服务状态...")
    
    # 检查服务是否已运行
    try:
        response = requests.get('http://localhost:8006/health', timeout=3)
        if response.status_code == 200:
            print("✅ 智能MCP服务已运行")
            return True
    except:
        pass
    
    print("🚀 启动智能MCP服务...")
    try:
        # 检查服务文件是否存在
        mcp_service_file = Path("intelligent_mcp_service.py")
        if not mcp_service_file.exists():
            print("⚠️ intelligent_mcp_service.py 不存在，跳过MCP服务启动")
            return False
        
        process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        
        print(f"✅ 智能MCP服务已启动 (PID: {process.pid})")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        for i in range(10):
            time.sleep(1)
            try:
                response = requests.get('http://localhost:8006/health', timeout=2)
                if response.status_code == 200:
                    print("✅ 智能MCP服务启动成功")
                    return True
            except:
                continue
        
        print("⚠️ 智能MCP服务启动超时，但可能仍在启动中")
        return True
        
    except Exception as e:
        print(f"❌ 启动智能MCP服务失败: {e}")
        return False

def test_system_functionality():
    """测试系统功能"""
    print("\n🧪 测试系统功能...")
    
    test_cases = [
        "失眠多梦怎么办",
        "肚子疼湿气重怎么治疗"
    ]
    
    for query in test_cases:
        print(f"   测试: {query}")
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": "medical",
                    "max_results": 1
                },
                "id": "test"
            }
            
            response = requests.post(
                'http://localhost:8006/mcp',
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and result['result'].get('results'):
                    first_result = result['result']['results'][0]
                    title = first_result.get('title', '')
                    print(f"   ✅ 返回: {title}")
                else:
                    print("   ❌ 无返回结果")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def check_dependencies():
    """检查依赖"""
    print("📦 检查系统依赖...")
    
    required_packages = [
        'streamlit',
        'requests',
        'pathlib',
        'datetime',
        'json',
        'logging'
    ]
    
    optional_packages = [
        ('pyttsx3', '语音播放'),
        ('speech_recognition', '语音识别'),
        ('PyPDF2', 'PDF处理'),
        ('python-docx', 'Word文档处理'),
        ('faiss-cpu', '向量搜索'),
        ('sentence-transformers', '文本嵌入')
    ]
    
    missing_required = []
    missing_optional = []
    
    # 检查必需包
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"   ❌ {package} (必需)")
    
    # 检查可选包
    for package, description in optional_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package} ({description})")
        except ImportError:
            missing_optional.append((package, description))
            print(f"   ⚠️ {package} ({description}) - 可选")
    
    if missing_required:
        print(f"\n❌ 缺少必需依赖: {', '.join(missing_required)}")
        print("请运行: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print(f"\n⚠️ 缺少可选依赖，某些功能可能不可用:")
        for package, description in missing_optional:
            print(f"   - {package}: {description}")
        print("可运行: pip install " + " ".join([p[0] for p in missing_optional]))
    
    return True

def create_directories():
    """创建必要的目录"""
    print("📁 创建必要目录...")
    
    directories = [
        'documents',
        'conversations', 
        'uploads',
        'perfect_vector_db',
        'models'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")

def main():
    """主函数"""
    print("🏥 完美统一中医智能助手 - 启动程序")
    print("🎯 确保所有功能完美运行")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = Path.cwd()
    main_file = current_dir / "perfect_unified_tcm_system.py"
    
    if not main_file.exists():
        print("❌ 未找到主程序文件 perfect_unified_tcm_system.py")
        print(f"当前目录: {current_dir}")
        input("按回车键退出...")
        return False
    
    print("✅ 找到主程序文件")
    
    # 1. 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return False
    
    # 2. 创建目录
    create_directories()
    
    # 3. 启动智能MCP服务
    mcp_started = check_and_start_mcp_service()
    
    # 4. 测试系统功能
    if mcp_started:
        test_system_functionality()
    
    # 5. 启动主系统
    print("\n🌐 启动完美统一中医智能助手...")
    print("💡 系统将在浏览器中自动打开")
    print("🔗 访问地址: http://localhost:8501")
    print()
    print("🎯 功能特色:")
    print("   ✅ 语音对话功能")
    print("   ✅ 智能RAG检索")
    print("   ✅ Elasticsearch检索")
    print("   ✅ 聊天历史管理")
    print("   ✅ 文档上传处理")
    print("   ✅ 多端访问支持")
    print()
    print("🧪 测试建议:")
    print("   1. 输入: 失眠多梦怎么办")
    print("   2. 输入: 肚子疼湿气重怎么治疗")
    print("   3. 尝试语音输入功能")
    print("   4. 上传PDF文档测试")
    print()
    
    try:
        # 自动打开浏览器
        time.sleep(2)
        webbrowser.open('http://localhost:8501')
        
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "perfect_unified_tcm_system.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("📋 使用说明:")
    print("1. 此脚本会自动检查依赖和启动服务")
    print("2. 然后启动完美统一中医智能助手")
    print("3. 支持语音对话、智能检索、文档上传等功能")
    print("4. 按 Ctrl+C 停止系统")
    print()
    
    success = main()
    
    if not success:
        print("\n❌ 启动失败")
        print("💡 故障排除:")
        print("1. 检查Python环境和依赖")
        print("2. 确保端口8501和8006未被占用")
        print("3. 检查文件权限")
        print("4. 查看错误日志")
        input("按回车键退出...")
    
    sys.exit(0 if success else 1)
