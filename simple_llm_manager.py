#!/usr/bin/env python3
"""
简单的LLM模型管理器
适合笔记本电脑的轻量级模型
"""
import logging
import re
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class SimpleLLMManager:
    """简单的LLM管理器 - 基于模板和规则的智能回答"""
    
    def __init__(self):
        self.tcm_knowledge = self._load_tcm_knowledge()
        self.response_templates = self._load_response_templates()
    
    def _load_tcm_knowledge(self) -> Dict[str, Dict]:
        """加载中医基础知识库"""
        return {
            "栀子甘草豉汤": {
                "组成": "栀子、甘草、豆豉",
                "功效": "清热除烦，和中降逆",
                "主治": "热病后虚烦不眠，胸中窒闷，心中懊憹",
                "方解": "栀子清热泻火，豆豉宣发郁热，甘草调和诸药",
                "出处": "《伤寒论》",
                "类别": "清热剂"
            },
            "四君子汤": {
                "组成": "人参、白术、茯苓、甘草",
                "功效": "益气健脾",
                "主治": "脾胃气虚证",
                "方解": "人参大补元气，白术健脾燥湿，茯苓渗湿健脾，甘草调和诸药",
                "出处": "《太平惠民和剂局方》",
                "类别": "补益剂"
            },
            "六味地黄丸": {
                "组成": "熟地黄、山茱萸、山药、泽泻、茯苓、牡丹皮",
                "功效": "滋阴补肾",
                "主治": "肾阴虚证",
                "方解": "熟地黄滋阴补肾，山茱萸补肝肾，山药健脾补肾，三泻配三补",
                "出处": "《小儿药证直诀》",
                "类别": "补益剂"
            },
            "脾胃虚弱": {
                "症状": "食欲不振、腹胀、便溏、倦怠乏力、面色萎黄",
                "病因": "饮食不节、劳倦过度、思虑伤脾",
                "治法": "健脾益气",
                "方药": "四君子汤、参苓白术散",
                "调护": "饮食规律、适量运动、情志调畅"
            },
            "肾阳虚": {
                "症状": "畏寒肢冷、腰膝酸软、夜尿频多、阳痿早泄、精神萎靡",
                "病因": "先天不足、房劳过度、久病伤肾",
                "治法": "温补肾阳",
                "方药": "肾气丸、右归丸",
                "调护": "节制房事、适当运动、温补饮食"
            },
            "肾阴虚": {
                "症状": "腰膝酸软、头晕耳鸣、失眠多梦、潮热盗汗、口干咽燥",
                "病因": "先天不足、房劳过度、热病伤阴",
                "治法": "滋补肾阴",
                "方药": "六味地黄丸、左归丸",
                "调护": "节制房事、清淡饮食、充足睡眠"
            }
        }
    
    def _load_response_templates(self) -> Dict[str, str]:
        """加载回答模板"""
        return {
            "方剂": """## 🔍 关于「{query}」的中医知识

### 📋 方剂信息
**组成**: {组成}
**功效**: {功效}
**主治**: {主治}
**出处**: {出处}

### 🔬 方解分析
{方解}

### 📚 参考资料
{sources_info}

⚠️ **重要提醒**: 以上内容仅供学习参考，具体用药请咨询专业中医师。""",

            "证候": """## 🔍 关于「{query}」的中医知识

### 🎯 证候特点
**主要症状**: {症状}
**病因病机**: {病因}
**治疗原则**: {治法}

### 💊 常用方药
{方药}

### 🏥 调护建议
{调护}

### 📚 参考资料
{sources_info}

⚠️ **重要提醒**: 以上内容仅供学习参考，具体诊疗请咨询专业中医师。""",

            "通用": """## 🔍 关于「{query}」的中医知识

### 📚 相关资料
{content}

### 📖 参考来源
{sources_info}

⚠️ **重要提醒**: 以上内容仅供学习参考，具体诊疗请咨询专业中医师。"""
        }
    
    def generate_intelligent_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成智能回答 - 优先使用在线检索结果"""
        try:
            # 1. 优先处理在线检索结果
            online_sources = [s for s in sources if s.get('type') == 'online']
            local_sources = [s for s in sources if s.get('type') == 'pdf']

            # 2. 如果有在线资源，优先使用在线资源生成回答
            if online_sources:
                logger.info(f"🌐 使用在线资源生成回答: {len(online_sources)}条")
                return self._generate_online_based_response(query, online_sources, local_sources)

            # 3. 检查是否有直接匹配的知识
            direct_match = self._find_direct_match(query)
            if direct_match:
                return self._format_direct_response(query, direct_match, sources)

            # 4. 基于本地检索结果生成回答
            if local_sources:
                return self._format_source_response(query, local_sources)

            # 5. 生成通用回答
            return self._generate_fallback_response(query)

        except Exception as e:
            logger.error(f"生成回答失败: {e}")
            return self._generate_error_response(query, str(e))
    
    def _find_direct_match(self, query: str) -> Dict[str, Any]:
        """查找直接匹配的知识"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        
        best_match = None
        best_score = 0
        
        for key, knowledge in self.tcm_knowledge.items():
            key_keywords = set(re.findall(r'[\u4e00-\u9fff]+', key))
            intersection = query_keywords.intersection(key_keywords)
            
            if intersection:
                score = len(intersection) / len(key_keywords.union(query_keywords))
                if score > best_score and score > 0.3:
                    best_score = score
                    best_match = {
                        'key': key,
                        'knowledge': knowledge,
                        'score': score
                    }
        
        return best_match

    def _generate_online_based_response(self, query: str, online_sources: List[Dict], local_sources: List[Dict] = None) -> str:
        """基于在线资源生成智能回答"""
        if not online_sources:
            return self._generate_fallback_response(query)

        # 分析查询类型
        query_type = self._analyze_query_type(query)

        # 整合在线资源内容
        online_content = self._integrate_online_content(online_sources, query)

        # 整合本地资源内容（如果有）
        local_content = ""
        if local_sources:
            local_content = self._integrate_local_content(local_sources, query)

        # 根据查询类型生成回答
        if query_type == "方剂":
            return self._generate_formula_response(query, online_content, local_content, online_sources + (local_sources or []))
        elif query_type == "症状":
            return self._generate_symptom_response(query, online_content, local_content, online_sources + (local_sources or []))
        elif query_type == "治疗":
            return self._generate_treatment_response(query, online_content, local_content, online_sources + (local_sources or []))
        else:
            return self._generate_comprehensive_response(query, online_content, local_content, online_sources + (local_sources or []))

    def _analyze_query_type(self, query: str) -> str:
        """分析查询类型"""
        if any(keyword in query for keyword in ['汤', '散', '丸', '方', '剂']):
            return "方剂"
        elif any(keyword in query for keyword in ['症状', '表现', '症候', '证候']):
            return "症状"
        elif any(keyword in query for keyword in ['治疗', '调理', '疗法', '怎么治', '如何治']):
            return "治疗"
        else:
            return "综合"

    def _integrate_online_content(self, online_sources: List[Dict], query: str) -> str:
        """整合在线资源内容"""
        content_parts = []

        for i, source in enumerate(online_sources[:3], 1):  # 最多使用3个最相关的源
            content = source.get('content', '').strip()
            if content:
                # 清理和格式化内容
                cleaned_content = self._clean_content(content)
                if len(cleaned_content) > 50:  # 只使用有意义的内容
                    content_parts.append(f"【来源{i}】{cleaned_content}")

        return '\n\n'.join(content_parts)

    def _integrate_local_content(self, local_sources: List[Dict], query: str) -> str:
        """整合本地资源内容"""
        content_parts = []

        for i, source in enumerate(local_sources[:2], 1):  # 最多使用2个本地源
            content = source.get('content', '').strip()
            if content:
                cleaned_content = self._clean_content(content)
                if len(cleaned_content) > 50:
                    content_parts.append(f"【本地文档{i}】{cleaned_content}")

        return '\n\n'.join(content_parts)

    def _clean_content(self, content: str) -> str:
        """清理内容"""
        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', content)
        # 移除特殊字符
        content = re.sub(r'[^\u4e00-\u9fff\w\s，。；：！？、（）【】《》""'']+', '', content)
        # 限制长度
        if len(content) > 300:
            content = content[:300] + "..."
        return content.strip()

    def _generate_formula_response(self, query: str, online_content: str, local_content: str, all_sources: List[Dict]) -> str:
        """生成方剂相关回答"""
        response_parts = [f"## 🔍 关于「{query}」的中医方剂知识\n"]

        if online_content:
            response_parts.append("### 🌐 权威医学资源")
            response_parts.append(online_content)
            response_parts.append("")

        if local_content:
            response_parts.append("### 📁 本地文档资料")
            response_parts.append(local_content)
            response_parts.append("")

        # 添加专业分析
        response_parts.append("### 💡 专业分析")
        response_parts.append("根据以上资料，该方剂的特点和应用需要结合具体的临床情况来判断。")
        response_parts.append("")

        # 添加来源信息
        response_parts.append("### 📚 参考来源")
        response_parts.append(self._format_sources_info(all_sources))
        response_parts.append("")
        response_parts.append("⚠️ **重要提醒**: 以上内容仅供学习参考，具体用药请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _generate_symptom_response(self, query: str, online_content: str, local_content: str, all_sources: List[Dict]) -> str:
        """生成症状相关回答"""
        response_parts = [f"## 🔍 关于「{query}」的中医症候知识\n"]

        if online_content:
            response_parts.append("### 🌐 症候分析")
            response_parts.append(online_content)
            response_parts.append("")

        if local_content:
            response_parts.append("### 📁 文献资料")
            response_parts.append(local_content)
            response_parts.append("")

        response_parts.append("### 💡 中医理论")
        response_parts.append("中医认为症状的出现与脏腑功能失调、气血运行不畅等因素相关，需要辨证论治。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        response_parts.append(self._format_sources_info(all_sources))
        response_parts.append("")
        response_parts.append("⚠️ **重要提醒**: 以上内容仅供学习参考，具体诊疗请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _generate_treatment_response(self, query: str, online_content: str, local_content: str, all_sources: List[Dict]) -> str:
        """生成治疗相关回答 - 智能分析具体问题"""
        # 提取问题关键词
        problem_keywords = self._extract_problem_keywords(query)

        response_parts = [f"## 🔍 关于「{query}」的中医治疗方案\n"]

        # 智能分析问题
        problem_analysis = self._analyze_specific_problem(query, problem_keywords)
        if problem_analysis:
            response_parts.append("### 🎯 问题分析")
            response_parts.append(problem_analysis)
            response_parts.append("")

        if online_content:
            response_parts.append("### 🌐 权威治疗方案")
            # 智能提取关键治疗信息
            key_treatments = self._extract_key_treatments(online_content, problem_keywords)
            response_parts.append(key_treatments)
            response_parts.append("")

        if local_content:
            response_parts.append("### 📁 文献治疗经验")
            # 智能提取本地文档中的相关治疗方法
            local_treatments = self._extract_local_treatments(local_content, problem_keywords)
            response_parts.append(local_treatments)
            response_parts.append("")

        # 生成具体的治疗建议
        specific_advice = self._generate_specific_treatment_advice(query, problem_keywords, online_content, local_content)
        if specific_advice:
            response_parts.append("### 💡 具体治疗建议")
            response_parts.append(specific_advice)
            response_parts.append("")

        # 生成注意事项
        precautions = self._generate_precautions(query, problem_keywords)
        if precautions:
            response_parts.append("### ⚠️ 注意事项")
            response_parts.append(precautions)
            response_parts.append("")

        response_parts.append("### 📚 参考来源")
        response_parts.append(self._format_sources_info(all_sources))
        response_parts.append("")
        response_parts.append("⚠️ **重要提醒**: 以上内容仅供学习参考，具体治疗请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _extract_problem_keywords(self, query: str) -> List[str]:
        """提取问题关键词"""
        # 身体部位关键词
        body_parts = ['腰', '头', '胃', '肚子', '腹', '背', '肩', '颈', '膝', '脚', '手', '胸', '心', '肝', '肾', '脾', '肺']
        # 症状关键词
        symptoms = ['疼', '痛', '酸', '胀', '闷', '麻', '木', '冷', '热', '痒', '肿', '咳', '喘', '泻', '便秘', '失眠', '多梦']
        # 程度关键词
        degrees = ['严重', '轻微', '偶尔', '经常', '持续', '间歇']

        keywords = []
        for word in body_parts + symptoms + degrees:
            if word in query:
                keywords.append(word)

        return keywords

    def _analyze_specific_problem(self, query: str, keywords: List[str]) -> str:
        """分析具体问题"""
        if not keywords:
            return ""

        analysis_parts = []

        # 分析身体部位
        body_parts = ['腰', '头', '胃', '肚子', '腹', '背', '肩', '颈', '膝', '脚', '手', '胸']
        affected_parts = [kw for kw in keywords if kw in body_parts]

        if affected_parts:
            part = affected_parts[0]
            part_analysis = {
                '腰': '腰部疼痛在中医理论中多与肾虚、寒湿、瘀血等因素相关。腰为肾之府，肾虚则腰痛；寒湿阻滞经络，气血不通则痛；跌打损伤导致瘀血阻络也会引起腰痛。',
                '头': '头痛在中医分为外感头痛和内伤头痛。外感多因风寒风热侵袭；内伤多因肝阳上亢、痰浊上扰、瘀血阻络等引起。',
                '胃': '胃痛多因饮食不节、情志不畅、脾胃虚弱等引起。中医认为"胃喜润恶燥，喜降恶逆"，治疗需要理气和胃。',
                '肚子': '腹痛的病因复杂，可能涉及脾胃、肝胆、肠道等多个脏腑，需要根据疼痛部位、性质、时间等进行辨证。'
            }

            if part in part_analysis:
                analysis_parts.append(part_analysis[part])

        # 分析症状性质
        pain_types = ['疼', '痛', '酸', '胀', '闷']
        pain_symptoms = [kw for kw in keywords if kw in pain_types]

        if pain_symptoms:
            symptom_analysis = {
                '疼': '疼痛多为实证，常因气滞血瘀、寒湿阻络所致。',
                '痛': '疼痛的性质需要细分：刺痛多为瘀血，胀痛多为气滞，冷痛多为寒证，灼痛多为热证。',
                '酸': '酸痛多为虚证，常见于肝肾不足、筋骨失养。',
                '胀': '胀痛多为气滞，常因情志不畅、肝气郁结引起。',
                '闷': '闷痛多为痰湿阻滞，气机不畅所致。'
            }

            for symptom in pain_symptoms:
                if symptom in symptom_analysis:
                    analysis_parts.append(symptom_analysis[symptom])

        return '\n'.join(analysis_parts) if analysis_parts else ""

    def _extract_key_treatments(self, content: str, keywords: List[str]) -> str:
        """从在线内容中提取关键治疗信息"""
        if not content:
            return ""

        # 查找治疗相关的句子
        treatment_keywords = ['治疗', '方剂', '用药', '针灸', '按摩', '调理', '疗法', '方法']
        sentences = re.split(r'[。！？]', content)

        relevant_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:
                # 检查是否包含治疗关键词和问题关键词
                has_treatment = any(kw in sentence for kw in treatment_keywords)
                has_problem = any(kw in sentence for kw in keywords) if keywords else True

                if has_treatment and has_problem:
                    relevant_sentences.append(sentence)

        if relevant_sentences:
            return '。'.join(relevant_sentences[:3]) + '。'
        else:
            # 如果没有找到特定的治疗句子，返回前面的内容
            return content[:200] + "..." if len(content) > 200 else content

    def _extract_local_treatments(self, content: str, keywords: List[str]) -> str:
        """从本地内容中提取治疗信息"""
        return self._extract_key_treatments(content, keywords)

    def _generate_specific_treatment_advice(self, query: str, keywords: List[str], online_content: str, local_content: str) -> str:
        """生成具体的治疗建议"""
        advice_parts = []

        # 根据关键词生成针对性建议
        if '腰' in keywords and ('疼' in keywords or '痛' in keywords):
            advice_parts.extend([
                "**辨证治疗**：",
                "• 肾阳虚腰痛：可用右归丸温补肾阳",
                "• 肾阴虚腰痛：可用左归丸滋补肾阴",
                "• 寒湿腰痛：可用独活寄生汤祛寒除湿",
                "• 瘀血腰痛：可用身痛逐瘀汤活血化瘀",
                "",
                "**非药物治疗**：",
                "• 针灸：肾俞、腰阳关、委中等穴位",
                "• 推拿：腰部按摩，配合热敷",
                "• 功能锻炼：适当的腰部运动，避免久坐久站"
            ])

        elif '头' in keywords and ('疼' in keywords or '痛' in keywords):
            advice_parts.extend([
                "**辨证治疗**：",
                "• 风寒头痛：可用川芎茶调散疏风散寒",
                "• 风热头痛：可用桑菊饮疏风清热",
                "• 肝阳头痛：可用天麻钩藤饮平肝潜阳",
                "• 血瘀头痛：可用血府逐瘀汤活血化瘀",
                "",
                "**日常调理**：",
                "• 保持规律作息，避免熬夜",
                "• 适当按摩太阳穴、百会穴",
                "• 避免情绪激动，保持心情舒畅"
            ])

        elif '胃' in keywords and ('疼' in keywords or '痛' in keywords):
            advice_parts.extend([
                "**辨证治疗**：",
                "• 寒邪犯胃：可用良附丸温胃散寒",
                "• 肝气犯胃：可用柴胡疏肝散疏肝理气",
                "• 脾胃虚寒：可用理中汤温中健脾",
                "• 胃阴不足：可用益胃汤滋养胃阴",
                "",
                "**饮食调理**：",
                "• 规律饮食，定时定量",
                "• 避免生冷、辛辣、油腻食物",
                "• 可食用小米粥、山药等养胃食品"
            ])

        elif '失眠' in keywords or '睡眠' in keywords:
            advice_parts.extend([
                "**辨证治疗**：",
                "• 心肾不交：可用交泰丸交通心肾",
                "• 肝郁化火：可用龙胆泻肝汤清肝泻火",
                "• 心脾两虚：可用归脾汤补益心脾",
                "",
                "**生活调理**：",
                "• 建立规律的睡眠时间",
                "• 睡前避免剧烈运动和刺激性食物",
                "• 可以适当按摩神门、三阴交等安神穴位"
            ])

        return '\n'.join(advice_parts) if advice_parts else ""

    def _generate_precautions(self, query: str, keywords: List[str]) -> str:
        """生成注意事项"""
        precautions = []

        if '腰' in keywords:
            precautions.extend([
                "• 避免长时间保持同一姿势",
                "• 注意腰部保暖，避免受寒",
                "• 适当进行腰部功能锻炼",
                "• 如疼痛持续加重，及时就医"
            ])

        elif '头' in keywords:
            precautions.extend([
                "• 保持充足睡眠，避免过度疲劳",
                "• 避免情绪激动和精神紧张",
                "• 注意颈部保暖",
                "• 如伴有发热、呕吐等症状，应立即就医"
            ])

        elif '胃' in keywords:
            precautions.extend([
                "• 饮食要规律，避免暴饮暴食",
                "• 避免过冷、过热、辛辣刺激性食物",
                "• 保持心情愉快，避免情绪波动",
                "• 如出现呕血、黑便等症状，立即就医"
            ])

        else:
            precautions.extend([
                "• 注意观察症状变化",
                "• 保持良好的生活习惯",
                "• 如症状持续或加重，及时就医",
                "• 遵医嘱用药，不可自行增减药量"
            ])

        return '\n'.join(precautions)

    def _generate_comprehensive_response(self, query: str, online_content: str, local_content: str, all_sources: List[Dict]) -> str:
        """生成综合回答"""
        response_parts = [f"## 🔍 关于「{query}」的中医知识\n"]

        if online_content:
            response_parts.append("### 🌐 权威资料")
            response_parts.append(online_content)
            response_parts.append("")

        if local_content:
            response_parts.append("### 📁 文档资料")
            response_parts.append(local_content)
            response_parts.append("")

        response_parts.append("### 💡 中医观点")
        response_parts.append("中医学是一门综合性的医学体系，注重整体观念和辨证论治。")
        response_parts.append("")

        response_parts.append("### 📚 参考来源")
        response_parts.append(self._format_sources_info(all_sources))
        response_parts.append("")
        response_parts.append("⚠️ **重要提醒**: 以上内容仅供学习参考，具体应用请咨询专业中医师。")

        return '\n'.join(response_parts)

    def _format_direct_response(self, query: str, match: Dict, sources: List[Dict]) -> str:
        """格式化直接匹配的回答"""
        knowledge = match['knowledge']
        
        # 判断是方剂还是证候
        if '组成' in knowledge:
            template = self.response_templates['方剂']
            return template.format(
                query=query,
                组成=knowledge.get('组成', ''),
                功效=knowledge.get('功效', ''),
                主治=knowledge.get('主治', ''),
                出处=knowledge.get('出处', ''),
                方解=knowledge.get('方解', ''),
                sources_info=self._format_sources_info(sources)
            )
        elif '症状' in knowledge:
            template = self.response_templates['证候']
            return template.format(
                query=query,
                症状=knowledge.get('症状', ''),
                病因=knowledge.get('病因', ''),
                治法=knowledge.get('治法', ''),
                方药=knowledge.get('方药', ''),
                调护=knowledge.get('调护', ''),
                sources_info=self._format_sources_info(sources)
            )
        else:
            return self._format_source_response(query, sources)
    
    def _format_source_response(self, query: str, sources: List[Dict]) -> str:
        """基于检索源格式化回答"""
        if not sources:
            return self._generate_fallback_response(query)
        
        # 合并所有源的内容
        content_parts = []
        for i, source in enumerate(sources[:3], 1):
            source_type = "📁 本地文档" if source.get('type') == 'pdf' else "🌐 在线资源"
            content = source.get('content', '')
            if len(content) > 200:
                content = content[:200] + "..."
            
            content_parts.append(f"**{i}. {source_type}: {source.get('source', '未知来源')}**\n{content}")
        
        template = self.response_templates['通用']
        return template.format(
            query=query,
            content='\n\n'.join(content_parts),
            sources_info=self._format_sources_info(sources)
        )
    
    def _format_sources_info(self, sources: List[Dict]) -> str:
        """格式化来源信息"""
        if not sources:
            return "📚 基于中医基础理论知识"
        
        info_parts = []
        for i, source in enumerate(sources[:3], 1):
            source_type = "📁 本地文档" if source.get('type') == 'pdf' else "🌐 在线资源"
            score = source.get('score', 0)
            info_parts.append(f"{i}. {source_type}: {source.get('source', '未知来源')} (相关度: {score:.2f})")
        
        return '\n'.join(info_parts)
    
    def _generate_fallback_response(self, query: str) -> str:
        """生成备用回答"""
        return f"""## 🤖 关于「{query}」的回复

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库
- 检查网络连接，确保能够访问在线资源

### 📚 您可以咨询的常见问题：
- 脾胃虚弱的调理方法
- 肾阳虚和肾阴虚的区别
- 常用中医方剂的功效
- 中医养生保健知识

⚠️ **重要提醒**: 本系统仅供中医文化学习参考，不构成医疗建议。"""
    
    def _generate_error_response(self, query: str, error: str) -> str:
        """生成错误回答"""
        return f"""## ❌ 处理「{query}」时发生错误

很抱歉，在处理您的问题时发生了技术错误。

**错误信息**: {error}

### 💡 建议：
- 请稍后重试
- 尝试简化您的问题
- 检查网络连接

⚠️ 如果问题持续存在，请联系技术支持。"""

# 全局实例
simple_llm = SimpleLLMManager()
