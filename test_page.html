<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
</head>
<body>
    <h1>系统状态测试</h1>
    <div id="status">加载中...</div>
    <button onclick="loadStatus()">刷新状态</button>
    
    <script>
        async function loadStatus() {
            try {
                console.log('开始加载状态...');
                const response = await fetch('http://localhost:8006/api/health');
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('数据:', data);
                
                document.getElementById('status').innerHTML = `
                    <div>状态: ${data.status}</div>
                    <div>版本: ${data.version}</div>
                    <div>文档: ${data.documents} 个</div>
                    <div>功能: ${data.features.length} 项</div>
                `;
                
            } catch (error) {
                console.error('错误:', error);
                document.getElementById('status').innerHTML = `错误: ${error.message}`;
            }
        }
        
        // 页面加载完成后自动加载状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            loadStatus();
        });
    </script>
</body>
</html>
