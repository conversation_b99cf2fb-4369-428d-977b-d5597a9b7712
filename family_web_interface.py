"""
家庭中医智能助手 - Web界面
专为家人朋友设计的简洁易用界面
"""
import streamlit as st
import time
import json
from datetime import datetime
from pathlib import Path
from family_tcm_system import family_tcm_system

# 页面配置
st.set_page_config(
    page_title="家庭中医智能助手",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem;
        background: linear-gradient(90deg, #4CAF50, #45a049);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .user-card {
        background: #f0f8f0;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #4CAF50;
        margin-bottom: 1rem;
    }
    .answer-card {
        background: #fff;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #ddd;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .stats-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
        margin: 0.5rem;
    }
    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_system():
    """初始化系统"""
    if 'system_initialized' not in st.session_state:
        with st.spinner("🏥 正在初始化家庭中医智能助手..."):
            success = family_tcm_system.initialize_system()
            st.session_state.system_initialized = success
            if success:
                st.success("✅ 系统初始化成功！")
            else:
                st.error("❌ 系统初始化失败，请检查配置")
    return st.session_state.system_initialized

def main():
    """主界面"""
    # 系统标题
    st.markdown("""
    <div class="main-header">
        <h1>🏥 家庭中医智能助手</h1>
        <p>24小时为您和家人提供中医知识查询服务</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 初始化系统
    if not initialize_system():
        st.stop()
    
    # 侧边栏
    with st.sidebar:
        st.header("👤 用户信息")
        
        # 用户身份选择
        user_options = ["爸爸", "妈妈", "爷爷", "奶奶", "自己", "朋友", "其他"]
        user_id = st.selectbox("请选择您的身份：", user_options)
        
        if user_id == "其他":
            custom_user = st.text_input("请输入您的称呼：")
            if custom_user:
                user_id = custom_user
        
        st.markdown(f"""
        <div class="user-card">
            <h4>👋 欢迎，{user_id}！</h4>
            <p>有什么中医问题想要了解吗？</p>
        </div>
        """, unsafe_allow_html=True)
        
        # 系统状态
        st.header("📊 系统状态")
        stats = family_tcm_system.get_system_stats()
        
        if "error" not in stats:
            col1, col2 = st.columns(2)
            with col1:
                st.metric("知识库文档", stats["knowledge_base"]["total_documents"])
                st.metric("今日查询", stats["usage_stats"]["today_queries"])
            with col2:
                st.metric("知识块数", stats["knowledge_base"]["total_chunks"])
                st.metric("活跃用户", stats["usage_stats"]["active_users"])
            
            # 健康状态
            health = stats["system_health"]
            if health["status"] == "healthy":
                st.success("🟢 系统运行正常")
            elif health["status"] == "warning":
                st.warning(f"🟡 系统警告: {', '.join(health['issues'])}")
            else:
                st.error(f"🔴 系统错误: {', '.join(health['issues'])}")
    
    # 主要内容区域
    tab1, tab2, tab3 = st.tabs(["💬 智能问答", "📚 知识库管理", "📈 使用统计"])
    
    with tab1:
        st.header("💬 中医智能问答")
        
        # 常见问题快捷按钮
        st.subheader("🔥 常见问题")
        col1, col2, col3, col4 = st.columns(4)
        
        common_questions = [
            "感冒了怎么办？",
            "失眠如何调理？",
            "脾胃虚弱的症状？",
            "如何补气血？"
        ]
        
        for i, question in enumerate(common_questions):
            with [col1, col2, col3, col4][i]:
                if st.button(question, key=f"common_{i}"):
                    st.session_state.user_question = question
        
        # 问题输入
        st.subheader("❓ 请输入您的问题")
        user_question = st.text_area(
            "您可以询问任何中医相关问题，比如症状、治疗方法、中药等：",
            value=st.session_state.get('user_question', ''),
            height=100,
            placeholder="例如：最近总是感觉疲劳，中医认为是什么原因？"
        )
        
        col1, col2, col3 = st.columns([1, 1, 3])
        with col1:
            ask_button = st.button("🔍 立即咨询", type="primary")
        with col2:
            clear_button = st.button("🗑️ 清空")
        
        if clear_button:
            st.session_state.user_question = ""
            st.rerun()
        
        # 处理问题
        if ask_button and user_question.strip():
            with st.spinner("🤔 正在分析您的问题..."):
                result = family_tcm_system.query_tcm_knowledge(user_question, user_id)
                
                if "error" in result:
                    st.error(f"❌ 查询失败: {result['error']}")
                else:
                    # 显示回答
                    st.markdown(f"""
                    <div class="answer-card">
                        <h4>💡 中医智能回答</h4>
                        <p>{result['answer']}</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # 显示参考来源
                    if result.get('sources'):
                        with st.expander("📖 参考文献来源"):
                            for i, source in enumerate(result['sources'], 1):
                                st.write(f"**来源 {i}:** {source['source']}")
                                st.write(f"**相关度:** {source['similarity']:.3f}")
                                st.write(f"**内容摘要:** {source['content']}")
                                st.divider()
                    
                    # 免责声明
                    st.markdown("""
                    <div class="warning-box">
                        <strong>⚠️ 重要提醒：</strong><br>
                        本系统提供的信息仅供参考，不能替代专业医生的诊断和治疗建议。
                        如有严重症状或疑虑，请及时就医咨询专业医生。
                    </div>
                    """, unsafe_allow_html=True)
        
        elif ask_button and not user_question.strip():
            st.warning("请先输入您的问题")
    
    with tab2:
        st.header("📚 知识库管理")
        
        # 当前知识库状态
        stats = family_tcm_system.get_system_stats()
        if "error" not in stats:
            col1, col2, col3 = st.columns(3)
            with col1:
                st.markdown(f"""
                <div class="stats-card">
                    <h3>{stats["knowledge_base"]["total_documents"]}</h3>
                    <p>已处理文档</p>
                </div>
                """, unsafe_allow_html=True)
            with col2:
                st.markdown(f"""
                <div class="stats-card">
                    <h3>{stats["knowledge_base"]["total_chunks"]}</h3>
                    <p>知识块数量</p>
                </div>
                """, unsafe_allow_html=True)
            with col3:
                st.markdown(f"""
                <div class="stats-card">
                    <h3>24/7</h3>
                    <p>服务时间</p>
                </div>
                """, unsafe_allow_html=True)
        
        st.divider()
        
        # 文档上传
        st.subheader("📄 添加中医文档")
        st.info("💡 您可以上传中医经典、医案、方剂等PDF文档来扩充知识库")
        
        uploaded_files = st.file_uploader(
            "选择PDF文档",
            type=['pdf'],
            accept_multiple_files=True,
            help="支持上传多个PDF文件，系统会自动处理并添加到知识库中"
        )
        
        if uploaded_files:
            if st.button("🚀 开始处理文档", type="primary"):
                # 保存上传的文件
                docs_dir = Path("family_tcm_knowledge/documents")
                docs_dir.mkdir(exist_ok=True)
                
                saved_files = []
                for uploaded_file in uploaded_files:
                    file_path = docs_dir / uploaded_file.name
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    saved_files.append(str(file_path))
                
                # 处理文档
                with st.spinner("📚 正在处理文档，请稍候..."):
                    results = family_tcm_system.add_tcm_documents(saved_files)
                
                # 显示结果
                if results["success"]:
                    st.success(f"✅ 成功处理 {len(results['success'])} 个文档")
                    st.info(f"📊 知识库现在包含 {results['total_chunks']} 个知识块")
                
                if results["failed"]:
                    st.error(f"❌ {len(results['failed'])} 个文档处理失败")
                    for failed_file in results["failed"]:
                        st.write(f"- {failed_file}")
        
        # 备份功能
        st.divider()
        st.subheader("💾 数据备份")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📦 创建备份"):
                with st.spinner("正在创建备份..."):
                    success = family_tcm_system.backup_knowledge_base()
                if success:
                    st.success("✅ 备份创建成功")
                else:
                    st.error("❌ 备份创建失败")
    
    with tab3:
        st.header("📈 使用统计")
        
        stats = family_tcm_system.get_system_stats()
        if "error" not in stats:
            # 今日统计
            st.subheader("📅 今日统计")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("今日查询", stats["usage_stats"]["today_queries"])
            with col2:
                st.metric("活跃用户", stats["usage_stats"]["active_users"])
            with col3:
                st.metric("系统状态", "正常运行" if stats["system_health"]["status"] == "healthy" else "需要关注")
            with col4:
                st.metric("运行时间", stats["usage_stats"]["uptime"])
            
            # 系统信息
            st.subheader("🖥️ 系统信息")
            info_col1, info_col2 = st.columns(2)
            
            with info_col1:
                st.write(f"**系统名称:** {stats['system_name']}")
                st.write(f"**版本:** {stats['version']}")
                st.write(f"**初始化状态:** {'✅ 已初始化' if stats['initialized'] else '❌ 未初始化'}")
            
            with info_col2:
                st.write(f"**知识库文档:** {stats['knowledge_base']['total_documents']} 个")
                st.write(f"**知识块数量:** {stats['knowledge_base']['total_chunks']} 个")
                st.write(f"**健康状态:** {stats['system_health']['status']}")
        
        else:
            st.error(f"❌ 无法获取统计信息: {stats['error']}")

if __name__ == "__main__":
    main()
