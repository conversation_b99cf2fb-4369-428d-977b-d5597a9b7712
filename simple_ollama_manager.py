#!/usr/bin/env python3
"""
简化的Ollama DeepSeek管理器
使用ollama库直接管理
"""

import ollama
import subprocess
import time
import os
import streamlit as st

class SimpleOllamaManager:
    """简化的Ollama管理器"""
    
    def __init__(self):
        self.model_name = "deepseek-r1:8b"
        self.initialized = False
        
    def check_ollama_installed(self):
        """检查Ollama是否安装"""
        try:
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except:
            return False
    
    def start_ollama_service(self):
        """启动Ollama服务"""
        try:
            st.info("🚀 启动Ollama服务...")
            
            # 在后台启动Ollama服务
            if os.name == 'nt':
                subprocess.Popen(
                    ["ollama", "serve"],
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            
            # 等待服务启动
            for i in range(30):
                try:
                    ollama.list()
                    st.success("✅ Ollama服务启动成功!")
                    return True
                except:
                    time.sleep(1)
            
            return False
            
        except Exception as e:
            st.error(f"启动服务失败: {e}")
            return False
    
    def check_model_available(self):
        """检查模型是否可用"""
        try:
            models = ollama.list()
            for model in models.get('models', []):
                if 'deepseek-r1' in model.get('name', '').lower():
                    return True, model['name']
            return False, None
        except:
            return False, None
    
    def pull_deepseek_model(self):
        """拉取DeepSeek模型"""
        st.info("📥 正在下载DeepSeek-R1模型...")
        st.info("💡 这可能需要几分钟时间，请耐心等待")
        
        try:
            # 使用ollama库拉取模型
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 开始拉取
            status_text.text("开始下载模型...")
            
            # 使用ollama.pull()
            result = ollama.pull(self.model_name)
            
            if result:
                progress_bar.progress(1.0)
                st.success("✅ DeepSeek-R1模型下载完成!")
                return True
            else:
                st.error("❌ 模型下载失败")
                return False
                
        except Exception as e:
            st.error(f"下载异常: {e}")
            return False
    
    def test_model(self):
        """测试模型"""
        try:
            response = ollama.generate(
                model=self.model_name,
                prompt="你好，请简单介绍一下中医。",
                options={
                    "num_predict": 50
                }
            )
            
            if response and response.get('response'):
                return True, response['response']
            else:
                return False, "测试失败"
                
        except Exception as e:
            return False, f"测试异常: {e}"
    
    def initialize(self):
        """初始化Ollama和DeepSeek模型"""
        st.info("🤖 初始化Ollama DeepSeek引擎...")
        
        # 1. 检查Ollama安装
        if not self.check_ollama_installed():
            st.error("❌ Ollama未安装")
            st.info("💡 请先安装Ollama:")
            st.info("1. 访问 https://ollama.ai/download")
            st.info("2. 下载Windows版本")
            st.info("3. 运行安装程序")
            st.info("4. 重启此应用")
            return False
        
        st.success("✅ Ollama已安装")
        
        # 2. 检查服务运行
        try:
            ollama.list()
            st.success("✅ Ollama服务运行中")
        except:
            st.info("🔄 Ollama服务未运行，正在启动...")
            if not self.start_ollama_service():
                st.error("❌ 无法启动Ollama服务")
                return False
        
        # 3. 检查模型
        model_available, model_name = self.check_model_available()
        if not model_available:
            st.info("📥 DeepSeek模型未找到，开始下载...")
            if not self.pull_deepseek_model():
                st.error("❌ 模型下载失败")
                return False
            
            # 重新检查
            model_available, model_name = self.check_model_available()
            if model_available:
                self.model_name = model_name
        else:
            self.model_name = model_name
            st.success(f"✅ 找到模型: {model_name}")
        
        # 4. 测试模型
        st.info("🧪 测试模型...")
        success, result = self.test_model()
        if success:
            st.success("✅ DeepSeek-R1模型测试通过!")
            st.info(f"🎯 测试回答: {result[:100]}...")
            self.initialized = True
            return True
        else:
            st.error(f"❌ 模型测试失败: {result}")
            return False
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成回答"""
        if not self.initialized:
            return "DeepSeek模型未初始化"
        
        try:
            st.info("🧠 DeepSeek-R1正在思考...")
            
            response = ollama.generate(
                model=self.model_name,
                prompt=prompt,
                options={
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            )
            
            if response and response.get('response'):
                content = response['response']
                st.success("✅ DeepSeek-R1生成完成")
                return content
            else:
                return "DeepSeek生成为空，请重试"
                
        except Exception as e:
            return f"生成异常: {str(e)}"

def main():
    """测试函数"""
    manager = SimpleOllamaManager()
    
    print("🤖 简化Ollama DeepSeek管理器测试")
    print("=" * 40)
    
    if manager.initialize():
        print("✅ 初始化成功!")
        
        # 测试生成
        response = manager.generate_response("什么是中医？", max_tokens=100)
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
