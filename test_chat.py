#!/usr/bin/env python3
"""
测试聊天功能
验证在线检索、PDF检索和DeepSeek模型是否正常工作
"""
import requests
import json
import time

def test_chat_functionality():
    """测试聊天功能"""
    print("🧪 测试聊天功能")
    print("=" * 50)
    
    # 测试问题
    test_query = "小女孩晚上老是鼻子怎么回事"
    
    print(f"📝 测试问题: {test_query}")
    print("-" * 30)
    
    # 构建请求
    url = "http://localhost:8008/api/chat"
    payload = {
        "message": test_query,
        "session_id": None
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("🚀 发送请求...")
        start_time = time.time()
        
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"📡 响应状态: {response.status_code}")
        print(f"⏱️ 处理时间: {processing_time:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            
            print("\n✅ 请求成功！")
            print("=" * 50)
            
            # 分析回答
            answer = data.get('response', '')
            sources = data.get('sources', [])
            session_id = data.get('session_id', '')
            timestamp = data.get('timestamp', '')
            api_processing_time = data.get('processing_time', 0)
            
            print(f"📝 回答长度: {len(answer)} 字符")
            print(f"📚 来源数量: {len(sources)} 条")
            print(f"🆔 会话ID: {session_id}")
            print(f"⏰ API处理时间: {api_processing_time:.2f}秒")
            print(f"📅 时间戳: {timestamp}")
            
            # 检查功能启用情况
            print("\n🔍 功能检查:")
            print("-" * 30)
            
            # 1. 检查在线检索
            online_sources = [s for s in sources if s.get('type') == 'online' or s.get('source_type') == 'online']
            print(f"🌐 在线检索: {'✅ 启用' if online_sources else '❌ 未启用'} ({len(online_sources)}条)")
            
            # 2. 检查PDF检索
            pdf_sources = [s for s in sources if s.get('type') == 'pdf' or s.get('source_type') == 'pdf']
            print(f"📄 PDF检索: {'✅ 启用' if pdf_sources else '❌ 未启用'} ({len(pdf_sources)}条)")
            
            # 3. 检查DeepSeek模型
            deepseek_indicators = [
                "DeepSeek" in answer,
                "🧠" in answer,
                "智能分析" in answer,
                len(answer) > 200,  # 足够详细
                "##" in answer  # 结构化
            ]
            deepseek_score = sum(deepseek_indicators)
            print(f"🤖 DeepSeek模型: {'✅ 启用' if deepseek_score >= 2 else '❌ 未启用'} (评分: {deepseek_score}/5)")
            
            # 显示回答内容
            print("\n💬 回答内容:")
            print("-" * 30)
            print(answer[:500] + "..." if len(answer) > 500 else answer)
            
            # 显示来源信息
            if sources:
                print(f"\n📚 来源详情 ({len(sources)}条):")
                print("-" * 30)
                for i, source in enumerate(sources[:5], 1):
                    source_type = source.get('type', source.get('source_type', 'unknown'))
                    source_name = source.get('source', source.get('filename', 'unknown'))
                    content_preview = source.get('content', '')[:100]
                    score = source.get('score', 0)
                    
                    print(f"{i}. 类型: {source_type}")
                    print(f"   来源: {source_name}")
                    print(f"   评分: {score:.3f}")
                    print(f"   内容: {content_preview}...")
                    print()
            
            # 总体评估
            print("\n🎯 总体评估:")
            print("-" * 30)
            
            total_features = 3  # 在线检索、PDF检索、DeepSeek模型
            enabled_features = sum([
                len(online_sources) > 0,
                len(pdf_sources) > 0,
                deepseek_score >= 2
            ])
            
            print(f"功能启用率: {enabled_features}/{total_features} ({enabled_features/total_features*100:.1f}%)")
            
            if enabled_features == total_features:
                print("🎉 所有功能都已正常启用！")
            elif enabled_features >= 2:
                print("⚠️ 大部分功能已启用，但仍有改进空间")
            else:
                print("❌ 多个功能未正常启用，需要检查配置")
                
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_chat_functionality()
