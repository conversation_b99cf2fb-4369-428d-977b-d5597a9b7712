#!/usr/bin/env python3
"""
测试超级智能LLM系统
验证去重功能和回答质量
"""
import asyncio
import sys
import time
from typing import List, Dict
from simple_ultimate_tcm import SimpleResponseGenerator, SimpleDocumentProcessor, SimpleOnlineCrawler

async def test_ultra_intelligent_system():
    """测试超级智能系统"""
    print("🚀 测试超级智能LLM系统")
    print("=" * 60)
    
    # 初始化系统组件
    print("🚀 初始化超级智能中医RAG系统...")
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    tcm_system = SimpleResponseGenerator(doc_processor, crawler)
    
    # 测试问题列表
    test_questions = [
        "有什么偏方可以治疗头痛吗",  # 之前重复回答的问题
        "腰疼怎么办",
        "膝盖疼痛如何治疗",
        "胸闷气短的原因",
        "失眠多梦怎么调理"
    ]
    
    print(f"📝 测试问题数量: {len(test_questions)}个")
    print("-" * 50)
    
    results = []
    
    for i, query in enumerate(test_questions, 1):
        print(f"\n🔍 测试问题 {i}: {query}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # 生成回答
            response, sources, processing_time = await tcm_system.generate_response(query)
            
            # 分析回答质量
            quality_analysis = analyze_response_quality(query, response, sources)
            
            results.append({
                'question': query,
                'response': response,
                'sources': sources,
                'processing_time': processing_time,
                'quality': quality_analysis
            })
            
            print(f"✅ 处理成功 (耗时: {processing_time:.2f}秒)")
            print(f"📚 来源数量: {len(sources)}条")
            print(f"📝 回答长度: {len(response)}字符")
            
            # 检查重复内容
            duplicate_check = check_for_duplicates(response)
            if duplicate_check['has_duplicates']:
                print(f"❌ 发现重复内容: {duplicate_check['duplicate_count']}处")
            else:
                print("✅ 无重复内容")
            
            # 显示质量评分
            print(f"🎯 质量评分: {quality_analysis['total_score']:.1f}/10")
            
            # 显示回答摘要
            response_summary = response[:200] + "..." if len(response) > 200 else response
            print(f"💬 回答摘要: {response_summary}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results.append({
                'question': query,
                'error': str(e),
                'processing_time': time.time() - start_time
            })
        
        print()
    
    # 生成测试报告
    generate_test_report(results)

def analyze_response_quality(query: str, response: str, sources: List[dict]) -> dict:
    """分析回答质量"""
    analysis = {
        'relevance_score': 0,      # 相关性评分 (0-3)
        'completeness_score': 0,   # 完整性评分 (0-3)
        'professionalism_score': 0, # 专业性评分 (0-2)
        'structure_score': 0,      # 结构性评分 (0-2)
        'total_score': 0
    }
    
    # 1. 相关性评分
    query_keywords = extract_keywords(query)
    response_keywords = extract_keywords(response)
    
    relevance = len(set(query_keywords).intersection(set(response_keywords)))
    if relevance >= 3:
        analysis['relevance_score'] = 3
    elif relevance >= 2:
        analysis['relevance_score'] = 2
    elif relevance >= 1:
        analysis['relevance_score'] = 1
    
    # 2. 完整性评分
    completeness_indicators = ['治疗', '方剂', '症状', '病因', '预防', '注意']
    found_indicators = sum(1 for indicator in completeness_indicators if indicator in response)
    
    if found_indicators >= 4:
        analysis['completeness_score'] = 3
    elif found_indicators >= 3:
        analysis['completeness_score'] = 2
    elif found_indicators >= 2:
        analysis['completeness_score'] = 1
    
    # 3. 专业性评分
    professional_terms = ['辨证论治', '四诊合参', '中医', '方剂', '穴位', '经络']
    found_terms = sum(1 for term in professional_terms if term in response)
    
    if found_terms >= 3:
        analysis['professionalism_score'] = 2
    elif found_terms >= 1:
        analysis['professionalism_score'] = 1
    
    # 4. 结构性评分
    structure_indicators = ['##', '###', '**', '•', '⚠️']
    found_structure = sum(1 for indicator in structure_indicators if indicator in response)
    
    if found_structure >= 3:
        analysis['structure_score'] = 2
    elif found_structure >= 1:
        analysis['structure_score'] = 1
    
    # 计算总分
    analysis['total_score'] = (
        analysis['relevance_score'] + 
        analysis['completeness_score'] + 
        analysis['professionalism_score'] + 
        analysis['structure_score']
    )
    
    return analysis

def extract_keywords(text: str) -> list:
    """提取关键词"""
    import re
    # 提取中文词汇
    keywords = re.findall(r'[\u4e00-\u9fff]{2,}', text)
    return list(set(keywords))

def check_for_duplicates(text: str) -> dict:
    """检查重复内容"""
    import re
    
    # 分句
    sentences = re.split(r'[。！？；]', text)
    sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
    
    # 检查重复句子
    seen_sentences = set()
    duplicate_count = 0
    
    for sentence in sentences:
        if sentence in seen_sentences:
            duplicate_count += 1
        else:
            seen_sentences.add(sentence)
    
    return {
        'has_duplicates': duplicate_count > 0,
        'duplicate_count': duplicate_count,
        'total_sentences': len(sentences),
        'unique_sentences': len(seen_sentences)
    }

def generate_test_report(results: list):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 超级智能系统测试报告")
    print("=" * 60)
    
    successful_tests = [r for r in results if 'error' not in r]
    failed_tests = [r for r in results if 'error' in r]
    
    print(f"✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print(f"❌ 失败测试: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        # 计算平均质量评分
        avg_quality = sum(r['quality']['total_score'] for r in successful_tests) / len(successful_tests)
        avg_processing_time = sum(r['processing_time'] for r in successful_tests) / len(successful_tests)
        avg_response_length = sum(len(r['response']) for r in successful_tests) / len(successful_tests)
        
        print(f"\n📈 质量指标:")
        print(f"• 平均质量评分: {avg_quality:.1f}/10")
        print(f"• 平均处理时间: {avg_processing_time:.2f}秒")
        print(f"• 平均回答长度: {avg_response_length:.0f}字符")
        
        # 检查重复问题
        print(f"\n🔍 重复内容检查:")
        duplicate_found = False
        for result in successful_tests:
            duplicate_check = check_for_duplicates(result['response'])
            if duplicate_check['has_duplicates']:
                print(f"❌ 问题「{result['question']}」存在 {duplicate_check['duplicate_count']} 处重复")
                duplicate_found = True
        
        if not duplicate_found:
            print("✅ 所有回答均无重复内容")
        
        # 质量分析
        print(f"\n🎯 详细质量分析:")
        for result in successful_tests:
            quality = result['quality']
            print(f"• {result['question'][:20]}... - 总分: {quality['total_score']}/10 "
                  f"(相关性: {quality['relevance_score']}/3, "
                  f"完整性: {quality['completeness_score']}/3, "
                  f"专业性: {quality['professionalism_score']}/2, "
                  f"结构性: {quality['structure_score']}/2)")
    
    if failed_tests:
        print(f"\n❌ 失败测试详情:")
        for result in failed_tests:
            print(f"• {result['question']}: {result['error']}")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    print("🚀 开始超级智能系统测试...")
    asyncio.run(test_ultra_intelligent_system())
