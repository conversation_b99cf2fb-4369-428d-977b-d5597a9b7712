#!/usr/bin/env python3
"""
本地DeepSeek模型API
直接使用您的本地GGUF模型文件
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uvicorn
import os
import sys
import time
import streamlit as st

# 检查llama-cpp-python
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False

# 请求模型
class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 2048
    temperature: Optional[float] = 0.7

class ChatResponse(BaseModel):
    response: str
    model: str
    status: str

# FastAPI应用
app = FastAPI(
    title="本地DeepSeek-R1 API",
    description="使用本地GGUF模型文件的DeepSeek-R1 API",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class LocalDeepSeekAPI:
    """本地DeepSeek API管理器"""
    
    def __init__(self):
        # 您的本地模型路径
        self.model_paths = [
            r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
            r"C:\Users\<USER>\.lmstudio\models\bartowski\deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF\deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf"
        ]
        self.model = None
        self.model_path = None
        self.initialized = False
        
    def find_best_model(self):
        """找到最好的可用模型"""
        for path in self.model_paths:
            if os.path.exists(path):
                size = os.path.getsize(path) / (1024 * 1024 * 1024)
                print(f"找到模型: {path} ({size:.2f} GB)")
                
                # 优先使用较大的模型（更完整）
                if size > 2.0:  # 大于2GB认为是完整模型
                    return path
        
        # 如果没有大模型，使用任何可用的
        for path in self.model_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def load_model(self):
        """加载模型"""
        if not LLAMA_CPP_AVAILABLE:
            raise Exception("llama-cpp-python未安装，请运行: pip install llama-cpp-python")
        
        self.model_path = self.find_best_model()
        if not self.model_path:
            raise Exception("未找到可用的DeepSeek模型文件")
        
        print(f"加载模型: {self.model_path}")
        
        try:
            # 使用保守的参数加载
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=2048,
                n_threads=4,
                n_gpu_layers=0,  # 使用CPU
                n_batch=256,
                verbose=False,
                use_mmap=True,
                use_mlock=False
            )
            
            print("✅ 模型加载成功")
            return True
            
        except Exception as e:
            print(f"模型加载失败，尝试降级配置: {e}")
            
            # 尝试更保守的配置
            try:
                self.model = Llama(
                    model_path=self.model_path,
                    n_ctx=1024,
                    n_threads=2,
                    n_gpu_layers=0,
                    n_batch=128,
                    verbose=False,
                    use_mmap=True,
                    use_mlock=False
                )
                
                print("✅ 模型降级加载成功")
                return True
                
            except Exception as e2:
                raise Exception(f"模型加载完全失败: {e2}")
    
    def initialize(self):
        """初始化API"""
        if self.initialized:
            return True
        
        try:
            if self.load_model():
                # 测试生成
                test_response = self.generate_response("你好", max_tokens=10)
                if test_response and "异常" not in test_response:
                    self.initialized = True
                    print("✅ API初始化成功")
                    return True
                else:
                    raise Exception("模型测试失败")
            else:
                raise Exception("模型加载失败")
                
        except Exception as e:
            print(f"❌ API初始化失败: {e}")
            return False
    
    def generate_response(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7):
        """生成回答"""
        if not self.initialized:
            if not self.initialize():
                raise HTTPException(status_code=500, detail="模型初始化失败")
        
        try:
            # 构建中医专业提示词
            system_prompt = "你是一位专业的中医医生，请用专业、温和的语气回答用户的中医相关问题。"
            full_prompt = f"{system_prompt}\n\n用户问题: {prompt}\n\n回答:"
            
            response = self.model(
                full_prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                repeat_penalty=1.1,
                stop=["用户:", "助手:", "Human:", "Assistant:", "\n\n---"],
                echo=False
            )
            
            if response and response.get('choices'):
                content = response['choices'][0]['text'].strip()
                
                # 清理回答
                content = self._clean_response(content)
                
                return {
                    "response": content,
                    "model": os.path.basename(self.model_path),
                    "status": "success"
                }
            else:
                raise HTTPException(status_code=500, detail="模型返回空结果")
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"生成异常: {str(e)}")
    
    def _clean_response(self, text: str) -> str:
        """清理生成的回答"""
        import re
        
        # 移除多余的换行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 移除可能的提示词残留
        text = re.sub(r'^(用户|助手|Human|Assistant|回答)[:：]\s*', '', text, flags=re.MULTILINE)
        
        return text.strip()

# 全局API实例
deepseek_api = LocalDeepSeekAPI()

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    print("🚀 启动本地DeepSeek API服务...")
    if deepseek_api.initialize():
        print("✅ 本地DeepSeek API初始化成功")
    else:
        print("⚠️ 本地DeepSeek API初始化失败，将在首次请求时重试")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "本地DeepSeek-R1 API服务",
        "model": deepseek_api.model_path if deepseek_api.model_path else "未加载",
        "status": "running" if deepseek_api.initialized else "initializing"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy" if deepseek_api.initialized else "initializing",
        "model_path": deepseek_api.model_path,
        "model_loaded": deepseek_api.model is not None
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    try:
        result = deepseek_api.generate_response(
            prompt=request.message,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        return ChatResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate")
async def generate(request: ChatRequest):
    """生成接口（兼容性）"""
    return await chat(request)

@app.post("/initialize")
async def initialize_model():
    """手动初始化模型"""
    try:
        if deepseek_api.initialize():
            return {"status": "success", "message": "模型初始化成功"}
        else:
            raise HTTPException(status_code=500, detail="模型初始化失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🤖 启动本地DeepSeek-R1 FastAPI服务")
    print("📋 API文档: http://localhost:8001/docs")
    print("🔗 健康检查: http://localhost:8001/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
