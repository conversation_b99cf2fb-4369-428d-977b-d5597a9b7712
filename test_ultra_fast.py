#!/usr/bin/env python3
"""
测试超快版本的性能
"""
import time
import pickle
import re
from pathlib import Path

def load_knowledge_base():
    """加载知识库"""
    vector_db_dir = Path('vector_db')
    
    with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
        chunks = pickle.load(f)
    
    with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
        metadata = pickle.load(f)
    
    return chunks, metadata

def simple_text_search(query, chunks, metadata, top_k=3):
    """简单文本搜索"""
    # 简单关键词匹配
    query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query.lower()))
    
    results = []
    for i, chunk in enumerate(chunks):
        # 计算关键词匹配度
        chunk_keywords = set(re.findall(r'[\u4e00-\u9fff]+', chunk.lower()))
        
        # 计算交集
        common_keywords = query_keywords.intersection(chunk_keywords)
        
        if common_keywords:
            score = len(common_keywords) / max(len(query_keywords), 1)
            results.append({
                'content': chunk,
                'metadata': metadata[i],
                'score': score,
                'matched_keywords': list(common_keywords)
            })
    
    # 按分数排序
    results.sort(key=lambda x: x['score'], reverse=True)
    return results[:top_k]

def main():
    print("🚀 测试超快版本性能")
    print("=" * 50)
    
    # 测试加载时间
    start_time = time.time()
    chunks, metadata = load_knowledge_base()
    load_time = time.time() - start_time
    
    print(f"📚 知识库加载时间: {load_time:.3f} 秒")
    print(f"📊 知识块数量: {len(chunks)}")
    print(f"📊 元数据数量: {len(metadata)}")
    
    # 测试查询
    test_queries = [
        "栀子甘草豉汤方",
        "防己黄芪汤",
        "甘草汤的作用",
        "栀子的功效",
        "桂枝附子汤"
    ]
    
    print("\n🔍 测试查询性能:")
    total_search_time = 0
    
    for query in test_queries:
        start_time = time.time()
        results = simple_text_search(query, chunks, metadata, top_k=3)
        search_time = time.time() - start_time
        total_search_time += search_time
        
        print(f"\n查询: {query}")
        print(f"  搜索时间: {search_time:.3f} 秒")
        print(f"  结果数量: {len(results)}")
        
        if results:
            best_result = results[0]
            print(f"  最佳匹配: {best_result['score']:.3f}")
            print(f"  匹配关键词: {', '.join(best_result['matched_keywords'][:5])}")
            print(f"  内容预览: {best_result['content'][:100]}...")
    
    avg_search_time = total_search_time / len(test_queries)
    print(f"\n📈 性能总结:")
    print(f"  平均搜索时间: {avg_search_time:.3f} 秒")
    print(f"  总查询时间: {total_search_time:.3f} 秒")
    print(f"  加载时间: {load_time:.3f} 秒")
    
    if avg_search_time < 0.1:
        print("🎉 性能评级: 优秀 (< 0.1秒)")
    elif avg_search_time < 0.5:
        print("✅ 性能评级: 良好 (< 0.5秒)")
    else:
        print("⚠️ 性能评级: 一般 (> 0.5秒)")

if __name__ == "__main__":
    main()
