"""
RAG系统性能监控工具
"""
import psutil
import time
import os
from datetime import datetime

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def get_system_info():
    """获取系统信息"""
    # 内存信息
    memory = psutil.virtual_memory()
    
    # CPU信息
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    
    return {
        'memory': {
            'total': memory.total / (1024**3),
            'available': memory.available / (1024**3),
            'used': memory.used / (1024**3),
            'percent': memory.percent
        },
        'cpu': {
            'percent': cpu_percent,
            'count': psutil.cpu_count()
        },
        'disk': {
            'total': disk.total / (1024**3),
            'free': disk.free / (1024**3),
            'used': disk.used / (1024**3),
            'percent': (disk.used / disk.total) * 100
        }
    }

def get_python_processes():
    """获取Python进程信息"""
    python_procs = []
    for proc in psutil.process_iter(['pid', 'name', 'memory_percent', 'cpu_percent', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'][:2]) if proc.info['cmdline'] else 'Unknown'
                python_procs.append({
                    'pid': proc.info['pid'],
                    'memory': proc.info['memory_percent'],
                    'cpu': proc.info['cpu_percent'],
                    'cmd': cmdline
                })
        except:
            continue
    
    return sorted(python_procs, key=lambda x: x['memory'], reverse=True)

def display_status(info, python_procs):
    """显示状态信息"""
    clear_screen()
    
    print("🔍 RAG系统性能监控")
    print("=" * 60)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 内存状态
    memory = info['memory']
    memory_color = "🔴" if memory['percent'] > 90 else "🟡" if memory['percent'] > 80 else "🟢"
    print(f"💾 内存状态 {memory_color}")
    print(f"   总内存: {memory['total']:.1f} GB")
    print(f"   已使用: {memory['used']:.1f} GB ({memory['percent']:.1f}%)")
    print(f"   可用内存: {memory['available']:.1f} GB")
    
    # CPU状态
    cpu = info['cpu']
    cpu_color = "🔴" if cpu['percent'] > 90 else "🟡" if cpu['percent'] > 70 else "🟢"
    print(f"\n🖥️ CPU状态 {cpu_color}")
    print(f"   使用率: {cpu['percent']:.1f}%")
    print(f"   核心数: {cpu['count']}")
    
    # 磁盘状态
    disk = info['disk']
    disk_color = "🔴" if disk['percent'] > 90 else "🟡" if disk['percent'] > 80 else "🟢"
    print(f"\n💿 磁盘状态 {disk_color}")
    print(f"   总空间: {disk['total']:.1f} GB")
    print(f"   已使用: {disk['used']:.1f} GB ({disk['percent']:.1f}%)")
    print(f"   可用空间: {disk['free']:.1f} GB")
    
    # Python进程
    print(f"\n🐍 Python进程 (前5个)")
    print("-" * 60)
    print(f"{'PID':<8} {'内存%':<8} {'CPU%':<8} {'命令'}")
    print("-" * 60)
    
    for proc in python_procs[:5]:
        cmd_short = proc['cmd'][:35] + "..." if len(proc['cmd']) > 35 else proc['cmd']
        print(f"{proc['pid']:<8} {proc['memory']:<8.1f} {proc['cpu']:<8.1f} {cmd_short}")
    
    # 性能建议
    print(f"\n💡 性能建议:")
    if memory['percent'] > 85:
        print("   ⚠️ 内存使用过高，建议关闭不必要的应用")
    elif memory['percent'] < 50:
        print("   ✅ 内存使用正常")
    else:
        print("   🟡 内存使用适中")
    
    if cpu['percent'] > 80:
        print("   ⚠️ CPU使用过高，可能影响响应速度")
    elif cpu['percent'] < 30:
        print("   ✅ CPU使用正常")
    
    print(f"\n📊 RAG系统状态:")
    if memory['percent'] < 80 and cpu['percent'] < 70:
        print("   🎉 系统运行状态良好，PDF处理速度应该正常")
    elif memory['percent'] < 90:
        print("   🟡 系统运行状态一般，可能有轻微延迟")
    else:
        print("   ⚠️ 系统资源紧张，建议优化")
    
    print("\n" + "=" * 60)
    print("按 Ctrl+C 退出监控")

def main():
    """主监控循环"""
    print("🚀 启动RAG系统性能监控...")
    print("💡 将每5秒更新一次系统状态")
    time.sleep(2)
    
    try:
        while True:
            # 获取系统信息
            info = get_system_info()
            python_procs = get_python_processes()
            
            # 显示状态
            display_status(info, python_procs)
            
            # 等待5秒
            time.sleep(5)
            
    except KeyboardInterrupt:
        clear_screen()
        print("\n👋 性能监控已停止")
        print("📊 最终状态:")
        
        final_info = get_system_info()
        memory = final_info['memory']
        print(f"   内存使用: {memory['percent']:.1f}% ({memory['available']:.1f} GB 可用)")
        print(f"   CPU使用: {final_info['cpu']['percent']:.1f}%")
        
        if memory['percent'] < 80:
            print("✅ 系统状态良好，可以正常使用RAG系统")
        else:
            print("⚠️ 建议进一步优化内存使用")

if __name__ == "__main__":
    main()
