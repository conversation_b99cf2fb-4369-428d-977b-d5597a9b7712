#!/usr/bin/env python3
"""
家庭中医智能助手 - 一键启动脚本
专为家人朋友设计的简单启动方式
"""
import subprocess
import sys
import socket
import webbrowser
import time
from pathlib import Path

def get_network_info():
    """获取网络信息"""
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        return hostname, local_ip
    except:
        return "未知", "未知"

def check_dependencies():
    """检查基本依赖"""
    try:
        import streamlit
        import pickle
        return True
    except ImportError:
        return False

def main():
    """主启动函数"""
    print("🏥 家庭中医智能助手 - 一键启动")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 缺少必要依赖，请先安装：")
        print("   pip install streamlit")
        input("按回车键退出...")
        return
    
    # 检查知识库
    if not Path("vector_db/chunks.pkl").exists():
        print("❌ 知识库文件不存在")
        print("💡 请先运行 emergency_fix.py 生成知识库")
        input("按回车键退出...")
        return
    
    # 获取网络信息
    hostname, local_ip = get_network_info()
    
    print("✅ 系统检查通过")
    print(f"🖥️ 主机名: {hostname}")
    print(f"🌐 IP地址: {local_ip}")
    print()
    
    print("🚀 正在启动中医智能助手...")
    print("⏳ 请稍候，首次启动可能需要几秒钟...")
    
    try:
        # 启动服务
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "enhanced_ultra_fast_tcm.py",
            "--server.port", "8517",
            "--server.address", "0.0.0.0",
            "--server.headless", "true",
            "--server.maxUploadSize", "200"
        ]
        
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待启动
        time.sleep(3)
        
        print("✅ 启动成功！")
        print()
        print("📱 访问地址：")
        print(f"   本机访问: http://localhost:8517")
        print(f"   手机访问: http://{local_ip}:8517")
        print()
        print("👨‍👩‍👧‍👦 家人朋友使用方法：")
        print(f"   1. 确保连接到同一WiFi网络")
        print(f"   2. 在浏览器中输入: http://{local_ip}:8517")
        print(f"   3. 即可开始使用中医智能助手")
        print()
        print("💡 功能特点：")
        print("   - ⚡ 秒速启动，无需等待")
        print("   - 📚 支持上传PDF扩充知识库")
        print("   - 🔍 智能搜索中医方剂")
        print("   - 📱 支持手机、平板访问")
        print()
        print("⚠️ 重要提醒：")
        print("   - 本系统仅供中医学习参考")
        print("   - 不能替代专业医生诊断")
        print("   - 如有疑虑请咨询专业医师")
        print()
        
        # 询问是否打开浏览器
        try:
            response = input("是否自动打开浏览器？(y/n，默认y): ").lower().strip()
            if response != 'n':
                webbrowser.open(f"http://localhost:8517")
                print("🌐 浏览器已打开")
        except KeyboardInterrupt:
            pass
        
        print()
        print("🔧 控制说明：")
        print("   - 按 Ctrl+C 停止服务")
        print("   - 关闭此窗口也会停止服务")
        print("   - 服务运行期间请保持此窗口开启")
        print()
        
        # 等待用户停止
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n\n👋 正在停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")
            print("💾 所有数据已保存")
            print("🏥 感谢使用家庭中医智能助手！")
    
    except FileNotFoundError:
        print("❌ 找不到启动文件")
        print("💡 请确保在正确的目录中运行此脚本")
        input("按回车键退出...")
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查系统配置或联系技术支持")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
