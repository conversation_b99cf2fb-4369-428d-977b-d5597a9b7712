#!/usr/bin/env python3
"""
优化版中医RAG系统 - 主启动文件
解决卡顿问题 + 语音对话 + 连续对话记忆
"""

import streamlit as st
import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import PyPDF2
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
import requests
from bs4 import BeautifulSoup
import time
import hashlib
import logging
from typing import Dict, List, Any
import threading
import queue
import gc  # 垃圾回收

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    SPEECH_RECOGNITION_AVAILABLE = False

# 多格式文档处理
try:
    import docx
    import pandas as pd
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="🧙‍♂️ 智者·中医AI助手 - 优化版",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 优化配置 - 减少内存使用
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './optimized_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CHUNK_SIZE': 300,  # 减小块大小
    'CHUNK_OVERLAP': 30,  # 减小重叠
    'TOP_K': 3,  # 减少检索数量
    'MAX_WORKERS': 2,  # 减少并发数
    'BATCH_SIZE': 16,  # 减小批处理大小
    'MAX_FILE_SIZE': 10 * 1024 * 1024,  # 10MB限制
    'MAX_PAGES': 50  # 最大页数限制
}

class OptimizedVoiceManager:
    """优化的语音管理器 - 支持语音输入和输出"""
    
    def __init__(self):
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.voice_available = VOICE_AVAILABLE
        self.speech_recognition_available = SPEECH_RECOGNITION_AVAILABLE
        
        # 初始化TTS
        if self.voice_available:
            try:
                self.tts_engine = pyttsx3.init()
                self.tts_engine.setProperty('rate', 150)
                self.tts_engine.setProperty('volume', 0.8)
                logger.info("TTS引擎初始化成功")
            except Exception as e:
                logger.error(f"TTS引擎初始化失败: {e}")
                self.voice_available = False
        
        # 初始化语音识别
        if self.speech_recognition_available:
            try:
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                # 调整环境噪音
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                logger.info("语音识别初始化成功")
            except Exception as e:
                logger.error(f"语音识别初始化失败: {e}")
                self.speech_recognition_available = False
    
    def speak_text(self, text):
        """朗读文本"""
        if not self.voice_available or not self.tts_engine:
            return False
        
        try:
            # 清理文本
            clean_text = re.sub(r'[#*`\[\]()]', '', text)
            clean_text = re.sub(r'https?://\S+', '', clean_text)
            clean_text = clean_text.replace('\n', ' ').strip()
            
            # 限制长度
            if len(clean_text) > 200:
                clean_text = clean_text[:200] + "..."
            
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            return True
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
            return False
    
    def listen_for_speech(self, timeout=5, phrase_time_limit=10):
        """监听语音输入"""
        if not self.speech_recognition_available:
            return None
        
        try:
            with self.microphone as source:
                st.info("🎤 正在监听，请说话...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
            
            st.info("🔄 正在识别语音...")
            # 使用Google语音识别
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            return text
            
        except sr.WaitTimeoutError:
            st.warning("⏰ 语音输入超时")
            return None
        except sr.UnknownValueError:
            st.warning("🤷 无法识别语音内容")
            return None
        except sr.RequestError as e:
            st.error(f"❌ 语音识别服务错误: {e}")
            return None
        except Exception as e:
            st.error(f"❌ 语音识别失败: {e}")
            return None

class ConversationManager:
    """对话管理器 - 管理连续对话和记忆"""
    
    def __init__(self):
        self.conversation_history = []
        self.max_history = 10  # 最多保存10轮对话
        self.user_profile = {}
        
    def add_conversation(self, user_input, assistant_response):
        """添加对话记录"""
        conversation = {
            'timestamp': datetime.now().isoformat(),
            'user': user_input,
            'assistant': assistant_response,
            'turn': len(self.conversation_history) + 1
        }
        
        self.conversation_history.append(conversation)
        
        # 保持历史记录在限制范围内
        if len(self.conversation_history) > self.max_history:
            self.conversation_history.pop(0)
        
        # 更新用户画像
        self.update_user_profile(user_input)
    
    def update_user_profile(self, user_input):
        """更新用户画像"""
        # 提取关键信息
        if "我" in user_input:
            if "湿气" in user_input:
                self.user_profile['symptoms'] = self.user_profile.get('symptoms', [])
                if '湿气' not in self.user_profile['symptoms']:
                    self.user_profile['symptoms'].append('湿气')
            
            if "失眠" in user_input:
                self.user_profile['symptoms'] = self.user_profile.get('symptoms', [])
                if '失眠' not in self.user_profile['symptoms']:
                    self.user_profile['symptoms'].append('失眠')
            
            if "头痛" in user_input:
                self.user_profile['symptoms'] = self.user_profile.get('symptoms', [])
                if '头痛' not in self.user_profile['symptoms']:
                    self.user_profile['symptoms'].append('头痛')
    
    def get_context_for_query(self, current_query):
        """为当前查询获取上下文"""
        if not self.conversation_history:
            return current_query
        
        # 构建上下文
        context_parts = []
        
        # 添加用户画像
        if self.user_profile.get('symptoms'):
            context_parts.append(f"用户已知症状: {', '.join(self.user_profile['symptoms'])}")
        
        # 添加最近的对话历史
        recent_conversations = self.conversation_history[-3:]  # 最近3轮对话
        for conv in recent_conversations:
            context_parts.append(f"之前问题: {conv['user']}")
        
        # 当前问题
        context_parts.append(f"当前问题: {current_query}")
        
        return "\n".join(context_parts)
    
    def get_conversation_summary(self):
        """获取对话摘要"""
        if not self.conversation_history:
            return "暂无对话记录"
        
        summary_parts = []
        summary_parts.append(f"对话轮数: {len(self.conversation_history)}")
        
        if self.user_profile.get('symptoms'):
            summary_parts.append(f"关注症状: {', '.join(self.user_profile['symptoms'])}")
        
        return " | ".join(summary_parts)

class OptimizedDocumentProcessor:
    """优化的文档处理器 - 防止卡顿"""
    
    def __init__(self):
        self.supported_formats = ['.pdf', '.txt']
        if MULTI_FORMAT_AVAILABLE:
            self.supported_formats.extend(['.docx', '.doc'])
    
    def process_file_safely(self, file_path, file_name):
        """安全处理文件，防止卡顿"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > CONFIG['MAX_FILE_SIZE']:
                st.warning(f"⚠️ 文件 {file_name} 过大 ({file_size/1024/1024:.1f}MB)，跳过处理")
                return [], []
            
            # 根据文件类型处理
            extension = Path(file_name).suffix.lower()
            
            if extension == '.pdf':
                return self._process_pdf_safely(file_path, file_name)
            elif extension == '.txt':
                return self._process_txt_safely(file_path, file_name)
            elif extension in ['.docx', '.doc'] and MULTI_FORMAT_AVAILABLE:
                return self._process_word_safely(file_path, file_name)
            else:
                st.warning(f"⚠️ 不支持的文件格式: {extension}")
                return [], []
                
        except Exception as e:
            st.error(f"❌ 处理文件失败 {file_name}: {e}")
            return [], []
    
    def _process_pdf_safely(self, file_path, file_name):
        """安全处理PDF文件"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                # 限制页数
                max_pages = min(total_pages, CONFIG['MAX_PAGES'])
                if total_pages > CONFIG['MAX_PAGES']:
                    st.warning(f"⚠️ PDF文件 {file_name} 有 {total_pages} 页，只处理前 {CONFIG['MAX_PAGES']} 页")
                
                text_parts = []
                
                # 分批处理页面，避免内存溢出
                batch_size = 5
                for i in range(0, max_pages, batch_size):
                    batch_end = min(i + batch_size, max_pages)
                    
                    # 显示进度
                    progress = (batch_end) / max_pages
                    st.progress(progress, text=f"处理页面 {i+1}-{batch_end}/{max_pages}")
                    
                    # 处理当前批次
                    for page_num in range(i, batch_end):
                        try:
                            page_text = pdf_reader.pages[page_num].extract_text()
                            if page_text.strip():
                                text_parts.append(page_text.strip())
                        except Exception as e:
                            logger.warning(f"页面 {page_num+1} 处理失败: {e}")
                            continue
                    
                    # 强制垃圾回收
                    gc.collect()
                
                full_text = '\n'.join(text_parts)
                
                # 分割文本
                chunks = self._split_text_optimized(full_text)
                
                # 创建元数据
                metadata = []
                for i, chunk in enumerate(chunks):
                    meta = {
                        'source': file_name,
                        'chunk_id': f"{file_name}_{i}",
                        'chunk_index': i,
                        'content': chunk,
                        'upload_time': datetime.now().isoformat(),
                        'file_type': '.pdf',
                        'total_pages': total_pages,
                        'processed_pages': max_pages
                    }
                    metadata.append(meta)
                
                return chunks, metadata
                
        except Exception as e:
            st.error(f"PDF处理失败: {e}")
            return [], []
    
    def _process_txt_safely(self, file_path, file_name):
        """安全处理文本文件"""
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            text = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if text is None:
                st.error(f"❌ 无法读取文本文件 {file_name}")
                return [], []
            
            # 限制文本长度
            max_length = CONFIG['MAX_FILE_SIZE'] // 2  # 字符数限制
            if len(text) > max_length:
                text = text[:max_length]
                st.warning(f"⚠️ 文本文件 {file_name} 过长，已截取前 {max_length} 个字符")
            
            chunks = self._split_text_optimized(text)
            
            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.txt'
                }
                metadata.append(meta)
            
            return chunks, metadata
            
        except Exception as e:
            st.error(f"文本文件处理失败: {e}")
            return [], []
    
    def _process_word_safely(self, file_path, file_name):
        """安全处理Word文档"""
        try:
            doc = docx.Document(file_path)
            text_parts = []
            
            # 限制段落数量
            max_paragraphs = 200
            paragraph_count = 0
            
            for paragraph in doc.paragraphs:
                if paragraph_count >= max_paragraphs:
                    st.warning(f"⚠️ Word文档 {file_name} 段落过多，只处理前 {max_paragraphs} 段")
                    break
                
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
                    paragraph_count += 1
            
            full_text = '\n'.join(text_parts)
            chunks = self._split_text_optimized(full_text)
            
            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.docx'
                }
                metadata.append(meta)
            
            return chunks, metadata
            
        except Exception as e:
            st.error(f"Word文档处理失败: {e}")
            return [], []
    
    def _split_text_optimized(self, text):
        """优化的文本分割"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']
        
        # 按段落分割
        paragraphs = text.split('\n\n')
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前块加上新段落不超过限制
            if len(current_chunk) + len(paragraph) <= chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                # 保存当前块
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                
                # 开始新块
                if len(paragraph) <= chunk_size:
                    current_chunk = paragraph + "\n\n"
                else:
                    # 段落太长，需要进一步分割
                    sub_chunks = self._split_long_paragraph(paragraph, chunk_size, overlap)
                    chunks.extend(sub_chunks)
                    current_chunk = ""
        
        # 添加最后一块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        # 过滤太短的块
        return [chunk for chunk in chunks if len(chunk.strip()) > 20]
    
    def _split_long_paragraph(self, paragraph, chunk_size, overlap):
        """分割长段落"""
        chunks = []
        start = 0
        
        while start < len(paragraph):
            end = start + chunk_size
            if end > len(paragraph):
                end = len(paragraph)
            
            chunk = paragraph[start:end]
            
            # 在句号处分割
            if end < len(paragraph) and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = paragraph[start:end]
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(paragraph):
                break
        
        return chunks

class OptimizedRAGSystem:
    """优化的RAG系统"""
    
    def __init__(self):
        self.embedding_model = None
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        self.initialized = False
        self.doc_processor = OptimizedDocumentProcessor()
        
    def initialize(self):
        """初始化系统"""
        if self.initialized:
            return True
            
        try:
            with st.spinner("🚀 正在初始化优化系统..."):
                # 加载嵌入模型
                st.write("📥 加载嵌入模型...")
                self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
                st.success("✅ 嵌入模型加载成功")
                
                # 加载向量数据库
                st.write("📚 加载向量数据库...")
                self.load_vector_database()
                
                self.initialized = True
                st.success("🎉 优化系统初始化完成！")
                return True

        except Exception as e:
            st.error(f"❌ 系统初始化失败: {e}")
            return False

    def load_vector_database(self):
        """加载向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])

            if vector_db_path.exists():
                index_file = vector_db_path / "index.faiss"
                chunks_file = vector_db_path / "chunks.pkl"
                metadata_file = vector_db_path / "metadata.pkl"

                if all(f.exists() for f in [index_file, chunks_file, metadata_file]):
                    self.vector_index = faiss.read_index(str(index_file))

                    with open(chunks_file, 'rb') as f:
                        self.document_chunks = pickle.load(f)

                    with open(metadata_file, 'rb') as f:
                        self.chunk_metadata = pickle.load(f)

                    st.success(f"✅ 已加载 {len(self.document_chunks)} 个文档块")
                else:
                    st.warning("⚠️ 向量数据库文件不完整，请重新处理文档")
            else:
                st.warning("⚠️ 向量数据库不存在，请先上传文档")

        except Exception as e:
            st.warning(f"⚠️ 加载向量数据库失败: {e}")

    def process_documents_optimized(self, uploaded_files):
        """优化的文档处理 - 防止卡顿"""
        if not uploaded_files:
            return False

        # 检查文件数量
        if len(uploaded_files) > 5:
            st.warning("⚠️ 一次最多处理5个文件，防止系统卡顿")
            uploaded_files = uploaded_files[:5]

        try:
            with st.spinner("⚡ 正在优化处理文档..."):
                all_chunks = []
                all_metadata = []

                # 逐个处理文件，避免并发导致的内存问题
                for i, uploaded_file in enumerate(uploaded_files):
                    st.write(f"📄 处理文件 {i+1}/{len(uploaded_files)}: {uploaded_file.name}")

                    # 保存文件
                    documents_path = Path(CONFIG['DOCUMENTS_PATH'])
                    documents_path.mkdir(exist_ok=True)

                    file_path = documents_path / uploaded_file.name
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # 安全处理文件
                    chunks, metadata = self.doc_processor.process_file_safely(file_path, uploaded_file.name)

                    if chunks:
                        all_chunks.extend(chunks)
                        all_metadata.extend(metadata)
                        st.success(f"✅ 从 {uploaded_file.name} 提取了 {len(chunks)} 个文本块")
                    else:
                        st.warning(f"⚠️ 文件 {uploaded_file.name} 处理失败")

                    # 强制垃圾回收
                    gc.collect()

                if not all_chunks:
                    st.error("❌ 没有提取到任何文本内容")
                    return False

                # 优化的向量索引创建
                st.write("🔍 创建向量索引...")
                success = self._create_vector_index_optimized(all_chunks)

                if success:
                    # 保存数据
                    self.document_chunks = all_chunks
                    self.chunk_metadata = all_metadata
                    self.save_vector_database()

                    st.success(f"🎉 成功处理 {len(uploaded_files)} 个文档，共 {len(all_chunks)} 个文本块！")
                    return True
                else:
                    st.error("❌ 向量索引创建失败")
                    return False

        except Exception as e:
            st.error(f"❌ 处理文档失败: {e}")
            return False

    def _create_vector_index_optimized(self, chunks):
        """优化的向量索引创建"""
        try:
            embeddings = []
            batch_size = CONFIG['BATCH_SIZE']

            progress_bar = st.progress(0)

            # 分批处理，避免内存溢出
            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]

                try:
                    batch_embeddings = self.embedding_model.encode(batch, show_progress_bar=False)
                    embeddings.extend(batch_embeddings)

                    progress = min((i + batch_size) / len(chunks), 1.0)
                    progress_bar.progress(progress, text=f"向量化进度: {i+1}-{min(i+batch_size, len(chunks))}/{len(chunks)}")

                    # 强制垃圾回收
                    gc.collect()

                except Exception as e:
                    st.error(f"批次 {i//batch_size + 1} 处理失败: {e}")
                    continue

            if not embeddings:
                st.error("❌ 没有生成任何向量")
                return False

            embeddings = np.array(embeddings).astype('float32')

            # 创建FAISS索引
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)
            faiss.normalize_L2(embeddings)
            self.vector_index.add(embeddings)

            st.success(f"✅ 向量索引创建成功，维度: {dimension}")
            return True

        except Exception as e:
            st.error(f"创建向量索引失败: {e}")
            return False

    def save_vector_database(self):
        """保存向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])
            vector_db_path.mkdir(exist_ok=True)

            faiss.write_index(self.vector_index, str(vector_db_path / "index.faiss"))

            with open(vector_db_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)

            with open(vector_db_path / "metadata.pkl", 'wb') as f:
                pickle.dump(self.chunk_metadata, f)

            return True
        except Exception as e:
            st.error(f"保存向量数据库失败: {e}")
            return False

    def search_documents(self, query, top_k=None):
        """搜索文档"""
        if top_k is None:
            top_k = CONFIG['TOP_K']

        if self.vector_index is None or not self.document_chunks:
            return []

        try:
            query_embedding = self.embedding_model.encode([query])[0]
            query_vector = np.array([query_embedding]).astype('float32')
            faiss.normalize_L2(query_vector)

            scores, indices = self.vector_index.search(query_vector, top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.chunk_metadata):
                    result = self.chunk_metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)

            return results

        except Exception as e:
            st.error(f"文档搜索失败: {e}")
            return []

# 全局实例
voice_manager = OptimizedVoiceManager()
conversation_manager = ConversationManager()

# 初始化系统
if 'optimized_rag_system' not in st.session_state:
    st.session_state.optimized_rag_system = OptimizedRAGSystem()

# 初始化对话管理器
if 'conversation_manager' not in st.session_state:
    st.session_state.conversation_manager = ConversationManager()

def main():
    """主界面"""
    st.title("🧙‍♂️ 智者·中医AI助手 - 优化版")
    st.markdown("### 🚀 解决卡顿 + 语音对话 + 连续记忆")

    # 功能状态显示
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("PDF检索", "✅ 优化")
    with col2:
        st.metric("语音对话", "✅ 可用" if VOICE_AVAILABLE and SPEECH_RECOGNITION_AVAILABLE else "❌ 不可用")
    with col3:
        st.metric("连续记忆", "✅ 可用")
    with col4:
        st.metric("防卡顿", "✅ 优化")

    # 侧边栏
    with st.sidebar:
        st.header("📋 系统控制")

        # 系统初始化
        if st.button("🚀 初始化系统", type="primary"):
            st.session_state.optimized_rag_system.initialize()

        # 系统状态
        st.subheader("📊 系统状态")
        if st.session_state.optimized_rag_system.initialized:
            st.success("✅ 系统已就绪")
            st.metric("文档块数量", len(st.session_state.optimized_rag_system.document_chunks))
        else:
            st.warning("⚠️ 系统未初始化")

        # 对话状态
        st.subheader("💬 对话状态")
        conversation_summary = st.session_state.conversation_manager.get_conversation_summary()
        st.info(conversation_summary)

        if st.button("🗑️ 清空对话记录"):
            st.session_state.conversation_manager = ConversationManager()
            st.success("✅ 对话记录已清空")

        st.divider()

        # 文档上传
        st.subheader("📄 文档管理")
        st.write("⚠️ 优化版限制：")
        st.write("- 最多5个文件")
        st.write("- 单文件≤10MB")
        st.write("- PDF≤50页")

        uploaded_files = st.file_uploader(
            "上传文档",
            type=['pdf', 'txt', 'docx'],
            accept_multiple_files=True,
            help="优化版，防止卡顿"
        )

        if uploaded_files and st.button("⚡ 优化处理"):
            st.session_state.optimized_rag_system.process_documents_optimized(uploaded_files)

        st.divider()

        # 语音设置
        st.subheader("🎛️ 语音设置")
        voice_input_enabled = st.checkbox("🎤 启用语音输入", value=True, disabled=not SPEECH_RECOGNITION_AVAILABLE)
        voice_output_enabled = st.checkbox("🔊 启用语音输出", value=True, disabled=not VOICE_AVAILABLE)

        st.session_state.voice_input_enabled = voice_input_enabled
        st.session_state.voice_output_enabled = voice_output_enabled

    # 主要内容区域
    if not st.session_state.optimized_rag_system.initialized:
        st.info("👆 请先点击侧边栏的'初始化系统'按钮")
        return

    # 对话历史显示
    if st.session_state.conversation_manager.conversation_history:
        with st.expander("📜 对话历史", expanded=False):
            for conv in st.session_state.conversation_manager.conversation_history[-5:]:  # 显示最近5轮
                st.markdown(f"**第{conv['turn']}轮 - {conv['timestamp'][:19]}**")
                st.markdown(f"🙋 **用户**: {conv['user']}")
                st.markdown(f"🧙‍♂️ **智者**: {conv['assistant'][:200]}...")
                st.divider()

    # 问答界面
    st.subheader("💬 智能对话")

    # 语音输入按钮
    col1, col2 = st.columns([4, 1])

    with col2:
        if st.session_state.get('voice_input_enabled', False) and SPEECH_RECOGNITION_AVAILABLE:
            if st.button("🎤 语音输入"):
                voice_text = voice_manager.listen_for_speech()
                if voice_text:
                    st.session_state.voice_question = voice_text
                    st.success(f"🎤 识别到: {voice_text}")

    # 问题输入
    with col1:
        question = st.text_input(
            "请输入您的问题:",
            value=st.session_state.get('voice_question', ''),
            placeholder="例如：我最近湿气很重，应该怎么调理？",
            key="question_input"
        )

    # 提问按钮
    if st.button("🧙‍♂️ 智者分析", type="primary") and question:
        handle_conversation(question)

def handle_conversation(question):
    """处理对话"""
    with st.spinner("🧙‍♂️ 智者正在深度分析..."):
        # 1. 获取带上下文的查询
        contextual_query = st.session_state.conversation_manager.get_context_for_query(question)

        # 2. PDF文档检索
        pdf_results = st.session_state.optimized_rag_system.search_documents(contextual_query)

        # 3. 生成智能回答
        answer = generate_contextual_answer(question, pdf_results, st.session_state.conversation_manager)

        # 4. 添加到对话历史
        st.session_state.conversation_manager.add_conversation(question, answer)

        # 5. 显示结果
        display_conversation_results(question, answer, pdf_results)

        # 6. 语音播放
        if st.session_state.get('voice_output_enabled', False) and VOICE_AVAILABLE:
            with st.spinner("🔊 正在播放回答..."):
                # 提取主要内容进行播放
                clean_answer = answer.split('### 📚')[0]  # 只播放主要分析部分
                clean_answer = re.sub(r'[#*`\[\]()]', '', clean_answer)
                voice_manager.speak_text(clean_answer[:300])

def generate_contextual_answer(question, pdf_results, conversation_manager):
    """生成带上下文的智能回答"""
    answer_parts = []

    # 检查是否是连续对话
    is_follow_up = len(conversation_manager.conversation_history) > 0

    # 标题
    if is_follow_up:
        answer_parts.append(f"## 🧙‍♂️ 智者·中医AI助手 (第{len(conversation_manager.conversation_history)+1}轮对话)")
    else:
        answer_parts.append(f"## 🧙‍♂️ 智者·中医AI助手")

    answer_parts.append(f"**您的咨询**: {question}")
    answer_parts.append("")

    # 连续对话上下文
    if is_follow_up:
        answer_parts.append("### 🔗 结合之前对话")
        if conversation_manager.user_profile.get('symptoms'):
            answer_parts.append(f"根据您之前提到的症状：{', '.join(conversation_manager.user_profile['symptoms'])}")
        answer_parts.append("")

    # 中医分析
    answer_parts.append("### 🔍 中医辨证分析")

    # 根据关键词和上下文生成分析
    if "湿气" in question or "湿气" in conversation_manager.user_profile.get('symptoms', []):
        answer_parts.extend(generate_dampness_analysis(question, is_follow_up))
    elif "气血" in question or any(s in question for s in ["气虚", "血虚", "气血不足"]):
        answer_parts.extend(generate_qiblood_analysis(question, is_follow_up))
    elif "失眠" in question or "失眠" in conversation_manager.user_profile.get('symptoms', []):
        answer_parts.extend(generate_sleep_analysis(question, is_follow_up))
    elif "头痛" in question or "头痛" in conversation_manager.user_profile.get('symptoms', []):
        answer_parts.extend(generate_headache_analysis(question, is_follow_up))
    else:
        answer_parts.extend(generate_general_analysis(question, is_follow_up))

    # 检索结果
    if pdf_results:
        answer_parts.append("")
        answer_parts.append("### 📚 文献检索结果")
        for i, result in enumerate(pdf_results[:2], 1):
            answer_parts.append(f"**{i}. 《{result['source']}》** (相似度: {result['similarity_score']:.3f})")
            answer_parts.append(f"   {result['content'][:150]}...")
            answer_parts.append("")

    # 个性化建议
    if is_follow_up:
        answer_parts.append("### 💡 个性化建议")
        answer_parts.append("基于您的持续咨询，建议：")
        answer_parts.append("- 保持规律的作息时间")
        answer_parts.append("- 注意饮食调理")
        answer_parts.append("- 适当运动锻炼")
        answer_parts.append("- 如症状持续，及时就医")
        answer_parts.append("")

    # 安全提醒
    answer_parts.append("### ⚠️ 重要提醒")
    answer_parts.append("- 以上分析基于中医理论，仅供参考")
    answer_parts.append("- 具体治疗需专业中医师诊断")
    answer_parts.append("- 如有疑问，欢迎继续咨询")

    return "\n".join(answer_parts)

def generate_dampness_analysis(question, is_follow_up):
    """生成湿气分析"""
    parts = []
    if is_follow_up:
        parts.append("结合您之前的咨询，湿气问题需要综合调理：")
    else:
        parts.append("**病因病机**: 湿为阴邪，其性重浊、黏腻、趋下。")

    parts.append("")
    parts.append("**主要表现**:")
    parts.append("- 身体困重，头昏如裹")
    parts.append("- 胸闷腹胀，食欲不振")
    parts.append("- 大便黏腻，小便短赤")
    parts.append("")
    parts.append("**调理建议**:")
    parts.append("- 饮食：薏米、红豆、冬瓜利湿")
    parts.append("- 运动：适当运动，促进代谢")
    parts.append("- 环境：保持干燥通风")

    return parts

def generate_qiblood_analysis(question, is_follow_up):
    """生成气血分析"""
    parts = []
    if is_follow_up:
        parts.append("继续您关于气血的咨询：")

    parts.append("**病因病机**: 气为血之帅，血为气之母。")
    parts.append("")
    parts.append("**调理要点**:")
    parts.append("- 补气：黄芪、党参、白术")
    parts.append("- 养血：当归、熟地、白芍")
    parts.append("- 运动：太极拳、八段锦")

    return parts

def generate_sleep_analysis(question, is_follow_up):
    """生成失眠分析"""
    parts = []
    if is_follow_up:
        parts.append("关于您的睡眠问题，进一步分析：")

    parts.append("**病因病机**: 心主神明，心神不安则不寐。")
    parts.append("")
    parts.append("**调理方法**:")
    parts.append("- 安神：酸枣仁、龙骨、牡蛎")
    parts.append("- 穴位：神门、三阴交、百会")
    parts.append("- 作息：规律睡眠，避免熬夜")

    return parts

def generate_headache_analysis(question, is_follow_up):
    """生成头痛分析"""
    parts = []
    if is_follow_up:
        parts.append("结合您之前的头痛咨询：")

    parts.append("**病因病机**: 头为诸阳之会，清窍之府。")
    parts.append("")
    parts.append("**治疗要点**:")
    parts.append("- 风寒头痛：川芎茶调散")
    parts.append("- 风热头痛：芎芷石膏汤")
    parts.append("- 肝阳头痛：天麻钩藤饮")

    return parts

def generate_general_analysis(question, is_follow_up):
    """生成通用分析"""
    parts = []
    if is_follow_up:
        parts.append("基于我们的持续对话：")

    parts.append("中医强调整体观念和辨证论治。")
    parts.append("需要结合具体症状、体质进行个性化分析。")

    return parts

def display_conversation_results(question, answer, pdf_results):
    """显示对话结果"""
    # 用户问题
    st.markdown(f"""
    <div style="background-color: #E3F2FD; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #2196F3;">
        <strong>🙋 用户问题：</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)

    # 智者回答
    st.markdown(answer)

    # 详细检索结果
    if pdf_results:
        with st.expander("🔍 详细检索结果", expanded=False):
            for i, result in enumerate(pdf_results, 1):
                st.write(f"**{i}. {result['source']}** (相似度: {result['similarity_score']:.3f})")
                with st.expander(f"查看内容 {i}"):
                    st.write(result['content'])

if __name__ == "__main__":
    main()
