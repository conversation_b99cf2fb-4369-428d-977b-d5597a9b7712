#!/usr/bin/env python3
"""
检查API是否就绪的脚本
只有在API完全就绪时才返回成功
"""

import requests
import subprocess
import time
import sys

def check_ollama_models():
    """检查Ollama模型列表"""
    try:
        result = subprocess.run([
            r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe", 
            "list"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"Ollama模型列表:\n{output}")
            
            lines = output.split('\n')
            if len(lines) > 1:
                models = []
                for line in lines[1:]:
                    if line.strip():
                        parts = line.split()
                        if parts:
                            models.append(parts[0])
                return models
            return []
        else:
            print(f"获取模型列表失败: {result.stderr}")
            return []
    except Exception as e:
        print(f"检查模型列表异常: {e}")
        return []

def check_api_status():
    """检查API状态"""
    try:
        print("检查Ollama API状态...")
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            print(f"API返回模型: {models}")
            return True, models
        else:
            print(f"API响应异常: {response.status_code}")
            return False, []
    except requests.exceptions.ConnectionError:
        print("无法连接到Ollama API")
        return False, []
    except Exception as e:
        print(f"API检查异常: {e}")
        return False, []

def test_model_generation():
    """测试模型生成"""
    try:
        print("测试模型生成...")
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "deepseek-r1:8b",
                "prompt": "你好",
                "stream": False,
                "options": {"num_predict": 5}
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('response', '')
            print(f"模型测试成功，回答: {content}")
            return True
        else:
            print(f"模型测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"模型测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查API就绪状态")
    print("=" * 40)
    
    # 1. 检查本地模型
    models = check_ollama_models()
    deepseek_local = any('deepseek' in model.lower() for model in models)
    
    # 2. 检查API状态
    api_ok, api_models = check_api_status()
    deepseek_api = any('deepseek' in model.lower() for model in api_models)
    
    print(f"\n📊 检查结果:")
    print(f"本地模型包含DeepSeek: {'✅' if deepseek_local else '❌'}")
    print(f"API模型包含DeepSeek: {'✅' if deepseek_api else '❌'}")
    print(f"API服务状态: {'✅' if api_ok else '❌'}")
    
    # 3. 如果都正常，测试生成
    if deepseek_local and deepseek_api and api_ok:
        print("\n🧪 进行模型生成测试...")
        if test_model_generation():
            print("\n🎉 API完全就绪！可以启动RAG系统")
            return True
        else:
            print("\n❌ 模型生成测试失败")
            return False
    else:
        print("\n⏳ API尚未就绪，请等待模型下载完成")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
