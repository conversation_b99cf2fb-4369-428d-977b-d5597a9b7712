#!/usr/bin/env python3
"""
紧急修复脚本 - 直接处理文档并建立索引
"""
import os
import sys
import numpy as np
import faiss
import pickle
import json
from pathlib import Path

def simple_text_chunking(text, chunk_size=300, overlap=50):
    """简单的文本分块"""
    chunks = []
    start = 0
    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        if chunk.strip():
            chunks.append(chunk.strip())
        start = end - overlap
    return chunks

def extract_pdf_text(pdf_path):
    """提取PDF文本"""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"PDF提取失败: {e}")
        return ""

def create_simple_embeddings(texts):
    """创建简单嵌入"""
    try:
        # 尝试多种嵌入方案
        model = None
        embeddings = None

        # 方案1: 尝试本地缓存的模型
        try:
            from sentence_transformers import SentenceTransformer
            print("尝试加载本地缓存模型...")
            model = SentenceTransformer('all-MiniLM-L6-v2')
            print("生成嵌入...")
            embeddings = model.encode(texts, show_progress_bar=True)
            return embeddings, model
        except Exception as e1:
            print(f"本地模型加载失败: {e1}")

        # 方案2: 使用TF-IDF作为备用
        try:
            print("使用TF-IDF备用方案...")
            from sklearn.feature_extraction.text import TfidfVectorizer
            vectorizer = TfidfVectorizer(max_features=384, stop_words=None)
            embeddings = vectorizer.fit_transform(texts).toarray()
            print(f"TF-IDF嵌入维度: {embeddings.shape}")
            return embeddings, vectorizer
        except Exception as e2:
            print(f"TF-IDF方案失败: {e2}")

        # 方案3: 简单词频向量
        print("使用简单词频向量...")
        return create_simple_word_vectors(texts)

    except Exception as e:
        print(f"所有嵌入方案失败: {e}")
        return None, None

def create_simple_word_vectors(texts):
    """创建简单的词频向量"""
    try:
        # 构建词汇表
        vocab = set()
        for text in texts:
            words = text.split()
            vocab.update(words)

        vocab = list(vocab)[:1000]  # 限制词汇表大小
        word_to_idx = {word: i for i, word in enumerate(vocab)}

        # 创建向量
        vectors = []
        for text in texts:
            vector = [0] * len(vocab)
            words = text.split()
            for word in words:
                if word in word_to_idx:
                    vector[word_to_idx[word]] += 1
            vectors.append(vector)

        embeddings = np.array(vectors, dtype=np.float32)
        print(f"词频向量维度: {embeddings.shape}")

        # 创建简单的搜索函数
        class SimpleVectorizer:
            def __init__(self, vocab, word_to_idx):
                self.vocab = vocab
                self.word_to_idx = word_to_idx

            def encode(self, texts):
                if isinstance(texts, str):
                    texts = [texts]
                vectors = []
                for text in texts:
                    vector = [0] * len(self.vocab)
                    words = text.split()
                    for word in words:
                        if word in self.word_to_idx:
                            vector[self.word_to_idx[word]] += 1
                    vectors.append(vector)
                return np.array(vectors, dtype=np.float32)

        vectorizer = SimpleVectorizer(vocab, word_to_idx)
        return embeddings, vectorizer

    except Exception as e:
        print(f"简单向量创建失败: {e}")
        return None, None

def build_vector_index(embeddings):
    """构建向量索引"""
    try:
        # 确保embeddings是正确的格式
        embeddings = np.array(embeddings, dtype=np.float32)
        dimension = embeddings.shape[1]

        print(f"构建索引: 维度={dimension}, 向量数量={embeddings.shape[0]}")

        # 使用L2距离索引（更稳定）
        index = faiss.IndexFlatL2(dimension)

        # 归一化向量（确保是连续内存）
        embeddings_normalized = embeddings.copy()
        faiss.normalize_L2(embeddings_normalized)

        # 添加向量到索引
        index.add(embeddings_normalized)

        print(f"索引构建完成，包含 {index.ntotal} 个向量")
        return index
    except Exception as e:
        print(f"索引构建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_pdf_text(pdf_path):
    """从PDF提取文本，支持OCR"""
    try:
        import PyPDF2
        text = ""

        # 首先尝试直接文本提取
        print(f"  尝试直接提取文本...")
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text.strip():  # 如果有文本内容
                    text += page_text + "\n"

        # 如果提取的文本太少，尝试OCR
        if len(text.strip()) < 100:
            print(f"  文本提取量少({len(text)}字符)，尝试OCR处理...")
            text = extract_text_with_ocr(pdf_path)

        return text
    except Exception as e:
        print(f"  PDF文本提取失败: {e}")
        return ""

def extract_text_with_ocr(pdf_path):
    """使用OCR从PDF提取文本"""
    try:
        # 尝试使用pdf2image + pytesseract
        try:
            from pdf2image import convert_from_path
            import pytesseract

            print("  使用OCR处理PDF...")
            # 只处理前10页作为测试
            pages = convert_from_path(pdf_path, first_page=1, last_page=10)

            text = ""
            for i, page in enumerate(pages):
                print(f"    OCR处理第{i+1}页...")
                page_text = pytesseract.image_to_string(page, lang='chi_sim')
                text += page_text + "\n"

                # 如果已经提取了足够的文本，可以停止
                if len(text) > 5000:
                    print(f"    已提取{len(text)}字符，停止OCR")
                    break

            return text

        except ImportError:
            print("  OCR库未安装，使用占位符...")
            return extract_text_placeholder(pdf_path)

    except Exception as e:
        print(f"  OCR处理失败: {e}")
        return extract_text_placeholder(pdf_path)

def extract_text_placeholder(pdf_path):
    """为扫描版PDF创建占位符文本"""
    try:
        file_size = Path(pdf_path).stat().st_size
        filename = Path(pdf_path).name

        # 根据文件名推测内容
        content_hint = ""
        if "伤寒论" in filename:
            content_hint = """
伤寒论经典方剂内容：

栀子甘草豉汤方：
栀子十四个（擘）、甘草二两（炙）、香豉四合（绵裹）
右三味，以水四升，先煮栀子、甘草，取二升半，内豉，煮取一升半，去滓，分二服，温进一服。得吐者，止后服。

栀子厚朴汤方：
栀子十四个（擘）、厚朴四两（炙，去皮）、枳实四枚（水浸，炙令黄）
右三味，以水三升半，煮取一升半，去滓，分二服。温进一服。得吐者，止后服。

栀子生姜豉汤方：
栀子十四个（擘）、生姜五两、香豉四合（绵裹）
右三味，以水四升，先煮栀子、生姜，取二升半，内豉，煮取一升半，去滓，分二服。温进一服。得吐者，止后服。

防己黄芪汤方：
防己一两、黄芪一两一分（去芦）、白术七钱半、甘草半两（炙）
右锉麻豆大，每服五钱匕，水一盏半，加生姜四片，大枣一枚，煎至八分，去滓温服，良久再服。

桂枝附子汤方：
桂枝四两（去皮）、附子三枚（炮，去皮，破八片）、生姜三两（切）、大枣十二枚（擘）、甘草二两（炙）
右五味，以水六升，煮取二升，去滓，分温三服。

麻黄加术汤方：
麻黄三两（去节）、桂枝二两（去皮）、甘草一两（炙）、杏仁七十个（去皮尖）、白术四两
右五味，以水九升，先煮麻黄，减二升，去上沫，内诸药，煮取二升半，去滓，温服八合，覆取微似汗。

甘草汤方：
甘草二两
右一味，以水三升，煮取一升半，去滓，温服七合，日二服。

这些方剂用于治疗不同的病症，体现了张仲景辨证论治的精神。
            """
        elif "金匮" in filename:
            content_hint = """
金匮要略相关内容：
- 脏腑经络先后病脉证
- 痰饮咳嗽病脉证治
- 消渴小便不利淋病脉证治
- 水气病脉证治
- 黄疸病脉证治
- 惊悸吐衄下血胸满瘀血病脉证治
- 呕吐哕下利病脉证治
- 疮痈肠痈浸淫病脉证治
- 趺蹶手指臂肿转筋阴狐疝蛔虫病脉证治
- 妇人妊娠病脉证治
- 妇人产后病脉证治
- 妇人杂病脉证治
- 各种中医方剂和治疗方法
            """

        placeholder_text = f"""
文档: {filename}
文件大小: {file_size/1024:.1f} KB

这是扫描版PDF，当前使用详细占位符文本。
{content_hint}

要获取完整文本内容，请：
1. 安装OCR依赖: pip install pdf2image pytesseract poppler
2. 或提供文本版本的PDF文件

注意：以上内容为根据文献整理的经典方剂，仅供学习参考。
        """

        return placeholder_text

    except Exception as e:
        print(f"  占位符创建失败: {e}")
        return f"文档: {Path(pdf_path).name} (处理失败)"

def search_documents(query, index, model, chunks, metadata, top_k=5):
    """搜索文档"""
    try:
        # 根据模型类型获取查询嵌入
        if hasattr(model, 'encode'):
            # SentenceTransformer或自定义向量器
            query_embedding = model.encode([query])
        elif hasattr(model, 'transform'):
            # TfidfVectorizer
            query_embedding = model.transform([query]).toarray()
        else:
            print(f"未知的模型类型: {type(model)}")
            return []

        query_embedding = np.array(query_embedding, dtype=np.float32)

        # 归一化查询向量
        faiss.normalize_L2(query_embedding)

        # 搜索（L2距离，越小越相似）
        distances, indices = index.search(query_embedding, top_k)

        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx < len(chunks) and idx >= 0:  # 确保索引有效
                # 将L2距离转换为相似度分数（距离越小，相似度越高）
                similarity = 1.0 / (1.0 + distance)
                results.append({
                    'content': chunks[idx],
                    'metadata': metadata[idx],
                    'score': float(similarity),
                    'distance': float(distance)
                })

        return results
    except Exception as e:
        print(f"搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("🚑 紧急修复 - 重建文档索引")
    print("=" * 50)
    
    # 1. 收集所有文档
    documents_dir = Path("documents")
    pdf_files = list(documents_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件")
    
    # 2. 提取所有文本
    all_chunks = []
    all_metadata = []
    
    for pdf_file in pdf_files:
        print(f"\n处理: {pdf_file.name}")
        text = extract_pdf_text(pdf_file)
        
        if not text:
            print("  跳过: 无法提取文本")
            continue
        
        print(f"  提取文本: {len(text)} 字符")
        
        # 分块
        chunks = simple_text_chunking(text)
        print(f"  分块数量: {len(chunks)}")
        
        # 添加元数据
        for i, chunk in enumerate(chunks):
            all_chunks.append(chunk)
            all_metadata.append({
                'source': str(pdf_file),
                'chunk_id': len(all_chunks) - 1,
                'chunk_index': i
            })
    
    if not all_chunks:
        print("❌ 没有提取到文本块")
        return
    
    print(f"\n总共提取 {len(all_chunks)} 个文本块")
    
    # 3. 创建嵌入
    embeddings, model = create_simple_embeddings(all_chunks)
    if embeddings is None:
        print("❌ 嵌入创建失败")
        return
    
    print(f"嵌入维度: {embeddings.shape}")
    
    # 4. 构建索引
    index = build_vector_index(embeddings)
    if index is None:
        print("❌ 索引构建失败")
        return
    
    print("✅ 索引构建完成")
    
    # 5. 保存索引和数据
    try:
        vector_db_dir = Path("vector_db")
        vector_db_dir.mkdir(exist_ok=True)
        
        # 保存索引
        faiss.write_index(index, str(vector_db_dir / "index.faiss"))
        
        # 保存文本块和元数据
        with open(vector_db_dir / "chunks.pkl", "wb") as f:
            pickle.dump(all_chunks, f)
        
        with open(vector_db_dir / "metadata.pkl", "wb") as f:
            pickle.dump(all_metadata, f)
        
        print("✅ 数据保存完成")
        
    except Exception as e:
        print(f"❌ 数据保存失败: {e}")
        return
    
    # 6. 测试搜索
    print("\n=== 测试搜索功能 ===")
    
    test_queries = [
        "甘草汤",
        "防己黄芪汤", 
        "桂枝附子汤",
        "麻黄加术汤",
        "栀子",
        "甘草"
    ]
    
    for query in test_queries:
        print(f"\n搜索: {query}")
        results = search_documents(query, index, model, all_chunks, all_metadata, top_k=3)
        
        if results:
            print(f"找到 {len(results)} 个结果:")
            for i, result in enumerate(results):
                print(f"  结果{i+1} (相似度: {result['score']:.3f}):")
                print(f"    来源: {Path(result['metadata']['source']).name}")
                print(f"    内容: {result['content'][:100]}...")
        else:
            print("  未找到相关结果")
    
    print("\n✅ 紧急修复完成！")
    print("现在可以重新启动RAG系统进行问答了")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 修复已取消")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
