#!/usr/bin/env python3
"""
手机端PWA应用生成器
将中医智能助手打包成可安装的手机应用
"""
import json
import base64
from pathlib import Path

def create_pwa_manifest():
    """创建PWA清单文件"""
    manifest = {
        "name": "中医智能助手",
        "short_name": "中医助手",
        "description": "专业的中医知识查询助手",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#ffffff",
        "theme_color": "#667eea",
        "orientation": "portrait",
        "scope": "/",
        "lang": "zh-CN",
        "icons": [
            {
                "src": "/static/icon-192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "any maskable"
            },
            {
                "src": "/static/icon-512.png", 
                "sizes": "512x512",
                "type": "image/png",
                "purpose": "any maskable"
            }
        ],
        "categories": ["medical", "education", "health"],
        "screenshots": [
            {
                "src": "/static/screenshot1.png",
                "sizes": "540x720",
                "type": "image/png"
            }
        ]
    }
    
    return manifest

def create_service_worker():
    """创建Service Worker"""
    sw_content = """
// 中医智能助手 Service Worker
const CACHE_NAME = 'tcm-assistant-v1.0';
const urlsToCache = [
    '/',
    '/static/css/index.css',
    '/static/js/index.js',
    '/static/icon-192.png',
    '/static/icon-512.png'
];

// 安装事件
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// 获取事件
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // 如果缓存中有，返回缓存版本
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});

// 激活事件
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
"""
    return sw_content

def create_mobile_optimized_html():
    """创建移动端优化的HTML"""
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="中医助手">
    
    <title>中医智能助手</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icon-192.png">
    <link rel="apple-touch-icon" href="/static/icon-192.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .quick-access {
            margin: 30px 0;
        }
        
        .access-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.2s;
        }
        
        .access-button:hover {
            transform: translateY(-2px);
        }
        
        .features {
            margin: 30px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 500;
            color: #333;
        }
        
        .feature-desc {
            font-size: 0.8rem;
            color: #666;
        }
        
        .install-prompt {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .install-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin-top: 10px;
            cursor: pointer;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏥</div>
            <div class="title">中医智能助手</div>
            <div class="subtitle">专业 · 智能 · 便捷</div>
        </div>
        
        <div class="quick-access">
            <a href="http://localhost:8519" class="access-button">
                🚀 立即使用
            </a>
        </div>
        
        <div class="features">
            <div class="feature-item">
                <div class="feature-icon">🌐</div>
                <div class="feature-text">
                    <div class="feature-title">双重数据源</div>
                    <div class="feature-desc">本地知识库 + 在线医学资源</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">
                    <div class="feature-title">合规保障</div>
                    <div class="feature-desc">严格内容审核，安全可靠</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📱</div>
                <div class="feature-text">
                    <div class="feature-title">移动优化</div>
                    <div class="feature-desc">专为手机端设计优化</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">
                    <div class="feature-title">快速响应</div>
                    <div class="feature-desc">毫秒级查询，即时获得答案</div>
                </div>
            </div>
        </div>
        
        <div class="install-prompt" id="installPrompt" style="display: none;">
            <div>📱 添加到主屏幕</div>
            <div style="font-size: 0.8rem; margin: 5px 0;">获得更好的使用体验</div>
            <button class="install-button" id="installButton">安装应用</button>
        </div>
        
        <div class="footer">
            <div>⚠️ 仅供中医文化学习参考</div>
            <div>如有健康问题请咨询专业医师</div>
        </div>
    </div>
    
    <script>
        // PWA 安装提示
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('installPrompt').style.display = 'block';
        });
        
        document.getElementById('installButton').addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                deferredPrompt = null;
                document.getElementById('installPrompt').style.display = 'none';
            }
        });
        
        // 注册 Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
"""
    return html_content

def create_app_icons():
    """创建应用图标（Base64编码的简单图标）"""
    # 192x192 图标 (简化的SVG转Base64)
    icon_192 = """data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB4PSI0OCIgeT0iNDgiIHdpZHRoPSI5NiIgaGVpZ2h0PSI5NiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xOS4xNCwyMC45NUgxNi4yOVYxOS4wN0gxOS4xNFYyMC45NVpNMTYuMjksMTcuMTlIMTkuMTRWMTUuMzFIMTYuMjlWMTcuMTlaTTE5LjE0LDEzLjQzSDE2LjI5VjExLjU1SDE5LjE0VjEzLjQzWk0xNi4yOSw5LjY3SDE5LjE0VjcuNzlIMTYuMjlWOS42N1pNMTkuMTQsNS45MUgxNi4yOVY0LjAzSDE5LjE0VjUuOTFaTTEzLjQzLDIwLjk1SDEwLjU3VjE5LjA3SDEzLjQzVjIwLjk1Wk0xMC41NywxNy4xOUgxMy40M1YxNS4zMUgxMC41N1YxNy4xOVpNMTMuNDMsMTMuNDNIMTAuNTdWMTEuNTVIMTMuNDNWMTMuNDNaTTEwLjU3LDkuNjdIMTMuNDNWNy43OUgxMC41N1Y5LjY3Wk0xMy40Myw1LjkxSDEwLjU3VjQuMDNIMTMuNDNWNS45MVpNNy43MSwyMC45NUg0Ljg2VjE5LjA3SDcuNzFWMjAuOTVaTTQuODYsMTcuMTlINy43MVYxNS4zMUg0Ljg2VjE3LjE5Wk03LjcxLDEzLjQzSDQuODZWMTEuNTVINy43MVYxMy40M1pNNC44Niw5LjY3SDcuNzFWNy43OUg0Ljg2VjkuNjdaTTcuNzEsNS45MUg0Ljg2VjQuMDNINy43MVY1LjkxWiIvPgo8L3N2Zz4KPC9zdmc+"""
    
    # 512x512 图标
    icon_512 = icon_192.replace("192", "512").replace("48", "128").replace("96", "256")
    
    return icon_192, icon_512

def setup_pwa_files():
    """设置PWA相关文件"""
    # 创建静态文件目录
    static_dir = Path("static")
    static_dir.mkdir(exist_ok=True)
    
    # 创建manifest.json
    manifest = create_pwa_manifest()
    with open("manifest.json", "w", encoding="utf-8") as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    # 创建service worker
    sw_content = create_service_worker()
    with open("sw.js", "w", encoding="utf-8") as f:
        f.write(sw_content)
    
    # 创建移动端HTML
    html_content = create_mobile_optimized_html()
    with open("mobile_app.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    # 创建图标文件（这里使用占位符，实际应用中需要真实图标）
    icon_192, icon_512 = create_app_icons()
    
    print("✅ PWA文件创建完成！")
    print("📁 生成的文件：")
    print("   - manifest.json (PWA清单)")
    print("   - sw.js (Service Worker)")
    print("   - mobile_app.html (移动端界面)")
    print("   - static/ (静态资源目录)")
    print()
    print("📱 使用方法：")
    print("   1. 在手机浏览器中访问应用")
    print("   2. 点击浏览器菜单中的'添加到主屏幕'")
    print("   3. 应用将像原生APP一样安装到手机")

def create_deployment_guide():
    """创建部署指南"""
    guide = """
# 📱 手机端PWA部署指南

## 🎯 PWA应用特点

### ✨ 原生应用体验
- 📱 可安装到手机主屏幕
- 🚀 离线缓存，快速启动
- 🔔 支持推送通知（可选）
- 📊 全屏显示，无浏览器界面

### 🌐 跨平台兼容
- ✅ iOS Safari (iOS 11.3+)
- ✅ Android Chrome (Chrome 70+)
- ✅ 微信内置浏览器
- ✅ 其他现代浏览器

## 🚀 部署步骤

### 1. 本地测试
```bash
# 启动商用版系统
python commercial_tcm_system.py

# 在手机浏览器访问
http://你的IP:8519
```

### 2. 云端部署选项

#### 选项A：免费云平台
- **Vercel**: 免费，支持PWA
- **Netlify**: 免费，自动HTTPS
- **GitHub Pages**: 免费，简单易用

#### 选项B：VPS服务器
- **阿里云**: 按量付费，国内访问快
- **腾讯云**: 新用户优惠，稳定可靠
- **AWS**: 全球部署，功能强大

#### 选项C：专业云服务
- **华为云**: 企业级，安全可靠
- **百度云**: AI集成，智能优化

### 3. 域名和HTTPS
```bash
# PWA要求HTTPS，建议：
1. 申请域名（如：tcm-assistant.com）
2. 配置SSL证书（Let's Encrypt免费）
3. 设置CDN加速
```

## 📱 用户安装指南

### iOS设备
1. 用Safari浏览器打开应用
2. 点击底部分享按钮
3. 选择"添加到主屏幕"
4. 确认安装

### Android设备
1. 用Chrome浏览器打开应用
2. 点击右上角菜单
3. 选择"安装应用"或"添加到主屏幕"
4. 确认安装

### 微信内使用
1. 在微信中打开链接
2. 点击右上角"..."
3. 选择"在浏览器中打开"
4. 按上述步骤安装

## 💰 商用化建议

### 1. 技术架构
- **前端**: PWA + Streamlit
- **后端**: Python + FastAPI
- **数据库**: PostgreSQL + Redis
- **部署**: Docker + Kubernetes

### 2. 功能扩展
- 👤 用户注册登录
- 💳 付费订阅模式
- 📊 使用统计分析
- 🔔 消息推送
- 🎨 个性化定制

### 3. 合规要求
- 📋 医疗器械备案
- 🔒 数据安全认证
- ⚖️ 法律合规审查
- 🏥 医疗免责声明

### 4. 运营策略
- 📈 SEO优化
- 📱 应用商店上架
- 🎯 精准营销
- 🤝 合作推广

## 🔧 技术优化

### 性能优化
```javascript
// Service Worker缓存策略
const CACHE_STRATEGY = {
    static: 'cache-first',    // 静态资源
    api: 'network-first',     // API请求
    images: 'cache-first'     // 图片资源
};
```

### 用户体验
- ⚡ 预加载关键资源
- 🔄 智能缓存更新
- 📱 响应式设计
- 🎨 Material Design

## 📊 监控分析

### 关键指标
- 📈 日活跃用户(DAU)
- 📱 安装转化率
- ⏱️ 页面加载时间
- 🔍 查询成功率

### 分析工具
- Google Analytics
- 百度统计
- 友盟统计
- 自定义埋点

---

**🎉 恭喜！您现在拥有了一个专业的商用级中医智能助手PWA应用！**
"""
    
    with open("PWA部署指南.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 PWA部署指南已生成：PWA部署指南.md")

def create_cloud_deployment_configs():
    """创建云端部署配置"""

    # Vercel配置
    vercel_config = {
        "version": 2,
        "builds": [
            {
                "src": "commercial_tcm_system.py",
                "use": "@vercel/python"
            }
        ],
        "routes": [
            {
                "src": "/(.*)",
                "dest": "/commercial_tcm_system.py"
            }
        ]
    }

    with open("vercel.json", "w") as f:
        json.dump(vercel_config, f, indent=2)

    # Docker配置
    dockerfile = """
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080

CMD ["streamlit", "run", "commercial_tcm_system.py", "--server.port=8080", "--server.address=0.0.0.0"]
"""

    with open("Dockerfile", "w") as f:
        f.write(dockerfile)

    # requirements.txt
    requirements = """
streamlit>=1.28.0
requests>=2.31.0
beautifulsoup4>=4.12.0
PyPDF2>=3.0.0
"""

    with open("requirements.txt", "w") as f:
        f.write(requirements)

    print("☁️ 云端部署配置已生成：")
    print("   - vercel.json (Vercel部署)")
    print("   - Dockerfile (Docker容器)")
    print("   - requirements.txt (Python依赖)")

def main():
    """主函数"""
    print("📱 中医智能助手 - PWA应用生成器")
    print("=" * 50)

    # 设置PWA文件
    setup_pwa_files()

    # 创建部署指南
    create_deployment_guide()

    # 创建云端部署配置
    create_cloud_deployment_configs()

    print()
    print("🎉 PWA应用生成完成！")
    print("💡 接下来您可以：")
    print("   1. 测试移动端体验")
    print("   2. 部署到云平台")
    print("   3. 配置域名和HTTPS")
    print("   4. 推广给用户使用")

if __name__ == "__main__":
    main()
