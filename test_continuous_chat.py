#!/usr/bin/env python3
"""
测试连续对话功能
"""
import requests
import json
import time

def test_continuous_chat():
    """测试连续对话"""
    url = "http://localhost:8006/api/chat"
    session_id = None
    
    questions = [
        "栀子甘草豉汤的功效是什么？",
        "这个方剂的组成有哪些？",
        "它主要治疗什么病症？",
        "有什么使用注意事项吗？",
        "脾胃虚弱怎么调理？"
    ]
    
    print("🔄 测试连续对话功能")
    print("=" * 50)
    
    for i, question in enumerate(questions, 1):
        print(f"\n📝 问题 {i}: {question}")
        print("-" * 30)
        
        try:
            payload = {"message": question}
            if session_id:
                payload["session_id"] = session_id
            
            start_time = time.time()
            response = requests.post(url, 
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get('session_id')  # 保存会话ID
                
                print(f"✅ 响应成功 (耗时: {end_time - start_time:.2f}s)")
                print(f"🔗 会话ID: {session_id}")
                print(f"📊 处理时间: {data.get('processing_time', 0):.2f}s")
                
                # 显示回答摘要
                answer = data.get('response', '')
                print(f"🤖 回答摘要: {answer[:200]}...")
                
                # 显示来源数量
                sources = data.get('sources', [])
                print(f"📚 来源数量: {len(sources)}")
                
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n🎉 连续对话测试完成！")
    print(f"📊 最终会话ID: {session_id}")

def test_voice_support():
    """测试语音功能支持"""
    print("\n🎤 测试语音功能支持")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8006", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查语音相关功能
            voice_features = [
                "voice-btn",
                "toggleVoiceInput",
                "SpeechRecognition",
                "🎤"
            ]
            
            found_features = []
            for feature in voice_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 语音功能检查:")
            print(f"  发现功能: {len(found_features)}/{len(voice_features)}")
            for feature in found_features:
                print(f"  ✓ {feature}")
            
            missing = set(voice_features) - set(found_features)
            if missing:
                print(f"  缺失功能: {missing}")
            
        else:
            print(f"❌ 无法访问主页: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 语音功能检查失败: {e}")

def test_session_management():
    """测试会话管理"""
    print("\n💬 测试会话管理功能")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8006", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查会话管理相关功能
            session_features = [
                "会话管理",
                "currentSession",
                "messageCount",
                "startNewSession",
                "clearChat"
            ]
            
            found_features = []
            for feature in session_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 会话管理功能检查:")
            print(f"  发现功能: {len(found_features)}/{len(session_features)}")
            for feature in found_features:
                print(f"  ✓ {feature}")
            
            missing = set(session_features) - set(found_features)
            if missing:
                print(f"  缺失功能: {missing}")
            
        else:
            print(f"❌ 无法访问主页: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 会话管理功能检查失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试修复后的功能")
    print("=" * 60)
    
    # 1. 测试会话管理
    test_session_management()
    
    # 2. 测试语音功能
    test_voice_support()
    
    # 3. 测试连续对话
    test_continuous_chat()
    
    print("\n🎉 所有测试完成！")
    print("=" * 60)
    print("💡 现在您可以在浏览器中测试:")
    print("   1. 连续提问多个问题")
    print("   2. 使用语音输入功能")
    print("   3. 管理会话（新建/清空）")
    print("   4. 查看会话信息和消息计数")
