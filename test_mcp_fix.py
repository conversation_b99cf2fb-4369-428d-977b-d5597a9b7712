#!/usr/bin/env python3
"""
测试MCP检索修复
"""

import asyncio
import sys
import os

async def test_mcp_search():
    """测试MCP搜索功能"""
    try:
        # 导入MCP引擎
        from fastmcp_elasticsearch_service import MCPElasticsearchEngine, SearchRequest
        
        print("🧪 测试MCP检索修复")
        print("=" * 50)
        
        # 创建搜索引擎
        engine = MCPElasticsearchEngine()
        
        # 测试查询列表
        test_queries = [
            "肾虚脾虚怎么治疗",
            "湿气重怎么办", 
            "失眠多梦",
            "胃痛",
            "肩周炎",
            "老人保健"
        ]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: {query}")
            
            # 创建搜索请求
            request = SearchRequest(
                query=query,
                domain='medical',
                max_results=3,
                search_type='comprehensive'
            )
            
            # 执行搜索
            results = await engine.search_knowledge(request)
            
            print(f"   找到结果: {len(results)} 条")
            
            # 显示结果
            for i, result in enumerate(results, 1):
                print(f"   {i}. {result.title} (评分: {result.score:.2f})")
                print(f"      内容: {result.content[:80]}...")
                print(f"      来源: {result.source}")
        
        print("\n" + "=" * 50)
        print("🎉 MCP检索测试完成！")
        
        # 验证肾虚脾虚查询是否有针对性结果
        kidney_spleen_request = SearchRequest(
            query="肾虚脾虚怎么治疗",
            domain='medical',
            max_results=5,
            search_type='comprehensive'
        )
        
        kidney_spleen_results = await engine.search_knowledge(kidney_spleen_request)
        
        print(f"\n✅ 肾虚脾虚查询验证:")
        print(f"   查询结果数量: {len(kidney_spleen_results)}")
        
        # 检查是否有针对性的结果
        has_kidney_spleen_result = any(
            '肾虚脾虚' in result.content or '肾脾双补' in result.content 
            for result in kidney_spleen_results
        )
        
        if has_kidney_spleen_result:
            print("   ✅ 找到了针对肾虚脾虚的专门内容")
        else:
            print("   ⚠️ 未找到针对肾虚脾虚的专门内容")
        
        # 检查结果是否不再固定
        different_queries_results = []
        for query in ["肾虚脾虚", "湿气重", "失眠"]:
            req = SearchRequest(query=query, domain='medical', max_results=2, search_type='comprehensive')
            res = await engine.search_knowledge(req)
            different_queries_results.append([r.title for r in res])
        
        # 检查结果是否有差异
        all_same = all(results == different_queries_results[0] for results in different_queries_results)
        
        if not all_same:
            print("   ✅ 不同查询返回不同结果（修复成功）")
        else:
            print("   ❌ 不同查询返回相同结果（仍有问题）")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_threshold():
    """测试向量检索阈值"""
    try:
        from ultimate_final_tcm_system import CONFIG
        
        print(f"\n📊 向量检索配置检查:")
        print(f"   阈值: {CONFIG['MIN_RELEVANCE_SCORE']}")
        print(f"   TOP_K: {CONFIG['TOP_K']}")
        print(f"   穷尽搜索: {CONFIG['EXHAUSTIVE_SEARCH']}")
        
        if CONFIG['MIN_RELEVANCE_SCORE'] <= 0.5:
            print("   ✅ 阈值已降低，有利于提高召回率")
        else:
            print("   ⚠️ 阈值较高，可能影响召回率")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 开始测试MCP和向量检索修复")
    
    # 测试向量阈值
    vector_ok = test_vector_threshold()
    
    # 测试MCP搜索
    mcp_ok = await test_mcp_search()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   向量检索配置: {'✅ 正常' if vector_ok else '❌ 异常'}")
    print(f"   MCP检索功能: {'✅ 正常' if mcp_ok else '❌ 异常'}")
    
    if vector_ok and mcp_ok:
        print("🎉 所有测试通过！系统修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
