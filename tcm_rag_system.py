#!/usr/bin/env python3
"""
中医RAG系统
结合Elasticsearch、向量数据库和ChatGLM3-6B的完整RAG系统
"""

import asyncio
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
import requests
from datetime import datetime
import sqlite3
import pickle

# Elasticsearch相关
try:
    from elasticsearch import Elasticsearch
    ES_AVAILABLE = True
except ImportError:
    ES_AVAILABLE = False
    print("❌ Elasticsearch未安装，请运行: pip install elasticsearch")

# 向量相关
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("❌ FAISS未安装，请运行: pip install faiss-cpu")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TCMRAGSystem:
    """中医RAG系统"""
    
    def __init__(self):
        self.chatglm_api_url = "http://127.0.0.1:8004"
        
        # Elasticsearch配置
        self.es_client = None
        self.es_index = "tcm_knowledge"
        
        # 向量数据库配置
        self.vector_db_path = "./data/tcm_vectors.db"
        self.faiss_index = None
        self.document_store = {}
        
        # 中医知识库
        self.tcm_knowledge_base = self.load_tcm_knowledge()
        
        # 初始化组件
        self.initialize_components()
    
    def load_tcm_knowledge(self) -> List[Dict[str, Any]]:
        """加载中医知识库"""
        knowledge_base = [
            {
                "id": "tcm_001",
                "title": "中医基础理论概述",
                "category": "基础理论",
                "content": "中医学是以阴阳五行学说为理论基础，以脏腑经络学说为核心，通过望闻问切四诊合参的方法诊断疾病，运用中药、针灸、推拿等方法治疗疾病的医学体系。中医强调整体观念和辨证论治，认为人体是一个有机整体。",
                "keywords": ["中医", "基础理论", "阴阳", "五行", "脏腑", "经络", "四诊", "辨证论治"],
                "source": "中医基础理论教材"
            },
            {
                "id": "tcm_002",
                "title": "阴阳学说详解",
                "category": "基础理论",
                "content": "阴阳学说认为宇宙间一切事物都存在着相互对立统一的阴阳两个方面。阴阳既对立又统一，既相互依存又相互转化。在人体，阴阳的相对平衡是维持正常生理功能的基础，阴阳失调则会导致疾病的发生。",
                "keywords": ["阴阳", "对立统一", "相互依存", "相互转化", "平衡", "失调"],
                "source": "中医基础理论"
            },
            {
                "id": "tcm_003",
                "title": "五行学说应用",
                "category": "基础理论",
                "content": "五行学说以木、火、土、金、水五种物质的特性来说明脏腑的生理功能和相互关系。五行之间存在相生相克的关系：木生火，火生土，土生金，金生水，水生木；木克土，土克水，水克火，火克金，金克木。",
                "keywords": ["五行", "木火土金水", "相生相克", "脏腑", "生理功能"],
                "source": "中医基础理论"
            },
            {
                "id": "tcm_004",
                "title": "气血津液理论",
                "category": "基础理论",
                "content": "气血津液是构成人体和维持人体生命活动的基本物质。气为血之帅，血为气之母，气血相互依存。津液是人体一切正常水液的总称，包括各脏腑组织器官的内在体液及其正常的分泌物。",
                "keywords": ["气血", "津液", "基本物质", "生命活动", "气为血帅", "血为气母"],
                "source": "中医基础理论"
            },
            {
                "id": "tcm_005",
                "title": "中医四诊方法",
                "category": "诊断方法",
                "content": "中医四诊包括望、闻、问、切四种诊断方法。望诊是通过观察患者的神色、形态、舌象等；闻诊包括听声音和嗅气味；问诊是询问症状、病史等；切诊主要是脉诊和按诊。四诊合参，综合分析。",
                "keywords": ["四诊", "望闻问切", "望诊", "闻诊", "问诊", "切诊", "脉诊", "舌诊"],
                "source": "中医诊断学"
            },
            {
                "id": "tcm_006",
                "title": "中药治疗原则",
                "category": "治疗方法",
                "content": "中药治疗遵循君臣佐使的配伍原则。君药是方剂中针对主病或主症起主要治疗作用的药物；臣药是辅助君药加强治疗主病或主症的药物；佐药是佐助君臣药治疗兼病兼症的药物；使药是引经药或调和诸药的药物。",
                "keywords": ["中药", "君臣佐使", "配伍", "君药", "臣药", "佐药", "使药", "方剂"],
                "source": "中药学"
            },
            {
                "id": "tcm_007",
                "title": "针灸治疗机理",
                "category": "治疗方法",
                "content": "针灸通过刺激人体特定的穴位，调节经络气血，平衡阴阳，从而达到治疗疾病的目的。针灸具有疏通经络、调和阴阳、扶正祛邪的作用。现代研究表明，针灸可以调节神经系统、内分泌系统和免疫系统。",
                "keywords": ["针灸", "穴位", "经络", "气血", "阴阳", "疏通经络", "扶正祛邪"],
                "source": "针灸学"
            },
            {
                "id": "tcm_008",
                "title": "中医养生理念",
                "category": "养生保健",
                "content": "中医养生强调预防为主，治未病。养生的基本原则包括：顺应自然、调养精神、饮食有节、起居有常、不妄作劳、房事有度。通过调节生活方式，保持身心健康，预防疾病的发生。",
                "keywords": ["养生", "治未病", "预防", "顺应自然", "调养精神", "饮食有节", "起居有常"],
                "source": "中医养生学"
            }
        ]
        
        return knowledge_base
    
    def initialize_components(self):
        """初始化系统组件"""
        logger.info("初始化中医RAG系统...")
        
        # 初始化Elasticsearch
        self.initialize_elasticsearch()
        
        # 初始化向量数据库
        self.initialize_vector_database()
        
        # 索引知识库
        self.index_knowledge_base()
        
        logger.info("中医RAG系统初始化完成")
    
    def initialize_elasticsearch(self):
        """初始化Elasticsearch"""
        if not ES_AVAILABLE:
            logger.warning("Elasticsearch不可用，使用内存搜索")
            return
        
        try:
            # 尝试连接本地Elasticsearch
            self.es_client = Elasticsearch([{"host": "localhost", "port": 9200}])
            
            # 测试连接
            if self.es_client.ping():
                logger.info("Elasticsearch连接成功")
                
                # 创建索引
                if not self.es_client.indices.exists(index=self.es_index):
                    mapping = {
                        "mappings": {
                            "properties": {
                                "title": {"type": "text", "analyzer": "ik_max_word"},
                                "content": {"type": "text", "analyzer": "ik_max_word"},
                                "category": {"type": "keyword"},
                                "keywords": {"type": "keyword"},
                                "source": {"type": "keyword"}
                            }
                        }
                    }
                    self.es_client.indices.create(index=self.es_index, body=mapping)
                    logger.info(f"创建Elasticsearch索引: {self.es_index}")
            else:
                logger.warning("Elasticsearch连接失败，使用内存搜索")
                self.es_client = None
                
        except Exception as e:
            logger.warning(f"Elasticsearch初始化失败: {e}，使用内存搜索")
            self.es_client = None
    
    def initialize_vector_database(self):
        """初始化向量数据库"""
        if not FAISS_AVAILABLE:
            logger.warning("FAISS不可用，跳过向量搜索")
            return
        
        try:
            # 创建数据目录
            Path("./data").mkdir(exist_ok=True)
            
            # 初始化FAISS索引 (假设384维向量)
            dimension = 384
            self.faiss_index = faiss.IndexFlatIP(dimension)  # 内积相似度
            
            logger.info("向量数据库初始化成功")
            
        except Exception as e:
            logger.warning(f"向量数据库初始化失败: {e}")
            self.faiss_index = None
    
    def index_knowledge_base(self):
        """索引知识库到Elasticsearch和向量数据库"""
        logger.info("开始索引中医知识库...")
        
        for doc in self.tcm_knowledge_base:
            # 索引到Elasticsearch
            if self.es_client:
                try:
                    self.es_client.index(
                        index=self.es_index,
                        id=doc["id"],
                        body=doc
                    )
                except Exception as e:
                    logger.warning(f"Elasticsearch索引失败: {e}")
            
            # 生成向量并索引到FAISS
            if self.faiss_index is not None:
                try:
                    # 简单的文本向量化（实际应用中应使用专业的embedding模型）
                    text = f"{doc['title']} {doc['content']}"
                    vector = self.simple_text_to_vector(text)
                    
                    # 添加到FAISS索引
                    self.faiss_index.add(np.array([vector], dtype=np.float32))
                    
                    # 保存文档映射
                    self.document_store[len(self.document_store)] = doc
                    
                except Exception as e:
                    logger.warning(f"向量索引失败: {e}")
        
        logger.info(f"知识库索引完成，共 {len(self.tcm_knowledge_base)} 条记录")
    
    def simple_text_to_vector(self, text: str, dimension: int = 384) -> np.ndarray:
        """简单的文本向量化（演示用）"""
        # 这是一个简化的向量化方法，实际应用中应使用专业的embedding模型
        import hashlib
        
        # 使用文本哈希生成伪向量
        hash_obj = hashlib.md5(text.encode())
        hash_bytes = hash_obj.digest()
        
        # 扩展到指定维度
        vector = np.zeros(dimension)
        for i in range(min(len(hash_bytes), dimension)):
            vector[i] = hash_bytes[i] / 255.0
        
        # 添加一些随机性
        np.random.seed(sum(hash_bytes))
        noise = np.random.normal(0, 0.1, dimension)
        vector = vector + noise
        
        # 归一化
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector
    
    async def search_knowledge(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索知识库"""
        results = []
        
        # Elasticsearch搜索
        if self.es_client:
            try:
                es_results = await self.elasticsearch_search(query, top_k)
                results.extend(es_results)
            except Exception as e:
                logger.warning(f"Elasticsearch搜索失败: {e}")
        
        # 向量搜索
        if self.faiss_index is not None:
            try:
                vector_results = await self.vector_search(query, top_k)
                results.extend(vector_results)
            except Exception as e:
                logger.warning(f"向量搜索失败: {e}")
        
        # 如果没有其他搜索方法，使用简单的关键词匹配
        if not results:
            results = await self.simple_keyword_search(query, top_k)
        
        # 去重和排序
        unique_results = {}
        for result in results:
            doc_id = result.get("id", result.get("_id"))
            if doc_id not in unique_results:
                unique_results[doc_id] = result
        
        return list(unique_results.values())[:top_k]
    
    async def elasticsearch_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Elasticsearch搜索"""
        search_body = {
            "query": {
                "multi_match": {
                    "query": query,
                    "fields": ["title^2", "content", "keywords^1.5"],
                    "type": "best_fields"
                }
            },
            "size": top_k
        }
        
        response = self.es_client.search(index=self.es_index, body=search_body)
        
        results = []
        for hit in response["hits"]["hits"]:
            result = hit["_source"]
            result["score"] = hit["_score"]
            result["search_type"] = "elasticsearch"
            results.append(result)
        
        return results
    
    async def vector_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """向量搜索"""
        # 将查询转换为向量
        query_vector = self.simple_text_to_vector(query)
        
        # 搜索最相似的向量
        scores, indices = self.faiss_index.search(
            np.array([query_vector], dtype=np.float32), 
            top_k
        )
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx in self.document_store:
                result = self.document_store[idx].copy()
                result["score"] = float(score)
                result["search_type"] = "vector"
                results.append(result)
        
        return results
    
    async def simple_keyword_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """简单关键词搜索"""
        query_lower = query.lower()
        results = []
        
        for doc in self.tcm_knowledge_base:
            score = 0
            
            # 标题匹配
            if query_lower in doc["title"].lower():
                score += 2
            
            # 内容匹配
            if query_lower in doc["content"].lower():
                score += 1
            
            # 关键词匹配
            for keyword in doc["keywords"]:
                if query_lower in keyword.lower():
                    score += 1.5
            
            if score > 0:
                result = doc.copy()
                result["score"] = score
                result["search_type"] = "keyword"
                results.append(result)
        
        # 按分数排序
        results.sort(key=lambda x: x["score"], reverse=True)
        return results[:top_k]
    
    async def generate_rag_response(self, query: str, max_tokens: int = 800) -> Dict[str, Any]:
        """生成RAG回答"""
        try:
            # 1. 搜索相关知识
            logger.info(f"搜索相关知识: {query}")
            relevant_docs = await self.search_knowledge(query, top_k=3)
            
            # 2. 构建上下文
            context = self.build_context(relevant_docs)
            
            # 3. 构建增强提示
            enhanced_prompt = self.build_enhanced_prompt(query, context)
            
            # 4. 调用ChatGLM3-6B生成回答
            logger.info("调用ChatGLM3-6B生成回答")
            response = await self.call_chatglm3(enhanced_prompt, max_tokens)
            
            # 5. 构建完整响应
            rag_response = {
                "query": query,
                "answer": response.get("answer", ""),
                "relevant_documents": relevant_docs,
                "context_used": context,
                "model": "ChatGLM3-6B",
                "timestamp": datetime.now().isoformat()
            }
            
            return rag_response
            
        except Exception as e:
            logger.error(f"RAG回答生成失败: {e}")
            return {
                "query": query,
                "answer": f"抱歉，在处理您的问题时遇到了错误: {str(e)}",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def build_context(self, relevant_docs: List[Dict[str, Any]]) -> str:
        """构建上下文"""
        if not relevant_docs:
            return "暂无相关资料。"
        
        context = "相关中医知识：\n\n"
        for i, doc in enumerate(relevant_docs, 1):
            context += f"{i}. 【{doc['title']}】\n"
            context += f"   {doc['content']}\n\n"
        
        return context
    
    def build_enhanced_prompt(self, query: str, context: str) -> str:
        """构建增强提示"""
        prompt = f"""作为一位专业的中医师，请基于以下中医知识回答用户的问题。

{context}

用户问题：{query}

请根据上述中医知识，结合您的专业理解，为用户提供准确、专业的回答。回答应该：
1. 基于传统中医理论
2. 结合提供的相关知识
3. 语言专业但易懂
4. 在适当时候提醒用户咨询专业医师

回答："""
        
        return prompt
    
    async def call_chatglm3(self, prompt: str, max_tokens: int = 800) -> Dict[str, Any]:
        """调用ChatGLM3-6B API"""
        try:
            chat_data = {
                "model": "chatglm3-6b",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7,
                "max_tokens": max_tokens
            }
            
            response = requests.post(
                f"{self.chatglm_api_url}/v1/chat/completions",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "answer": result["choices"][0]["message"]["content"],
                    "usage": result.get("usage", {}),
                    "model": "ChatGLM3-6B"
                }
            else:
                return {
                    "answer": f"API调用失败: {response.status_code}",
                    "error": response.text
                }
                
        except Exception as e:
            return {
                "answer": f"ChatGLM3-6B调用失败: {str(e)}",
                "error": str(e)
            }

# 全局RAG系统实例
tcm_rag = TCMRAGSystem()

async def main():
    """测试RAG系统"""
    logger.info("测试中医RAG系统...")
    
    test_queries = [
        "什么是阴阳学说？",
        "中医四诊包括哪些方法？",
        "针灸的治疗机理是什么？",
        "中医养生的基本原则有哪些？"
    ]
    
    for query in test_queries:
        print(f"\n{'='*50}")
        print(f"问题: {query}")
        print('='*50)
        
        response = await tcm_rag.generate_rag_response(query)
        print(f"回答: {response['answer']}")
        
        if 'relevant_documents' in response:
            print(f"\n参考文档: {len(response['relevant_documents'])} 条")

if __name__ == "__main__":
    asyncio.run(main())
