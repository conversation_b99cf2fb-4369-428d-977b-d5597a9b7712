#!/usr/bin/env python3
"""
优化启动器 - 专门解决回答质量和文档处理问题
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def print_optimization_banner():
    """打印优化横幅"""
    print("=" * 100)
    print("🔧 终极中医RAG系统 - 优化版启动器")
    print("=" * 100)
    print("🎯 专门解决您提到的两个关键问题:")
    print()
    print("❌ 问题1: 回答质量差")
    print("✅ 解决方案: 强化PDF检索 + 古籍检索 + DeepSeek模型优化")
    print("   - 增加检索结果数量 (5→8条)")
    print("   - 优化提示词，强制使用检索资料")
    print("   - 实时显示检索状态和结果")
    print("   - 增强备用回答机制")
    print()
    print("❌ 问题2: 文档上传卡顿")
    print("✅ 解决方案: 异步处理 + 进度显示 + 内存优化")
    print("   - 分批处理文档，避免UI阻塞")
    print("   - 实时进度显示和状态更新")
    print("   - 内存优化，支持大文件处理")
    print("   - 错误处理和恢复机制")
    print("=" * 100)
    print()

def check_optimization_requirements():
    """检查优化需求"""
    print("🔍 检查优化环境...")
    
    issues = []
    
    # 检查主系统文件
    if not os.path.exists("ultimate_final_tcm_system.py"):
        issues.append("主系统文件不存在")
    else:
        print("✅ 优化版系统文件存在")
    
    # 检查DeepSeek模型
    model_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    if os.path.exists(model_path):
        model_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ DeepSeek模型: {model_size:.2f} GB")
    else:
        issues.append("DeepSeek模型文件不存在")
    
    # 检查关键依赖
    critical_packages = ['streamlit', 'sentence_transformers', 'faiss', 'PyPDF2']
    missing_critical = []
    
    for package in critical_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_critical.append(package)
            issues.append(f"缺失关键依赖: {package}")
    
    # 检查网络连接
    try:
        import requests
        response = requests.get("https://chinesebooks.github.io/gudaiyishu/", timeout=5)
        if response.status_code == 200:
            print("✅ 古代医书网站可访问")
        else:
            issues.append("古代医书网站访问异常")
    except:
        issues.append("网络连接问题，可能影响古籍检索")
    
    if issues:
        print("\n⚠️ 发现问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    
    print("✅ 优化环境检查通过")
    return True

def install_missing_dependencies():
    """安装缺失依赖"""
    print("📦 安装/更新关键依赖...")
    
    # 关键依赖包
    critical_packages = [
        "streamlit>=1.28.0",
        "sentence-transformers>=2.2.2", 
        "faiss-cpu>=1.7.4",
        "PyPDF2>=3.0.0",
        "numpy>=1.24.0",
        "pandas>=2.0.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0"
    ]
    
    # 可选依赖包
    optional_packages = [
        "python-docx>=0.8.11",
        "openpyxl>=3.1.0", 
        "python-pptx>=0.6.21",
        "pyttsx3>=2.90",
        "SpeechRecognition>=3.10.0",
        "llama-cpp-python>=0.2.0"
    ]
    
    print("🔧 安装关键依赖...")
    for package in critical_packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade", "--quiet"
            ])
            print(f"✅ {package.split('>=')[0]}")
        except:
            print(f"❌ {package.split('>=')[0]} 安装失败")
    
    print("\n🔧 安装可选依赖...")
    for package in optional_packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade", "--quiet"
            ])
            print(f"✅ {package.split('>=')[0]}")
        except:
            print(f"⚠️ {package.split('>=')[0]} 安装失败（可选功能）")
    
    print("✅ 依赖安装完成")

def run_system_test():
    """运行系统测试"""
    print("🧪 运行优化测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_optimized_system.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
            return True
        else:
            print("⚠️ 系统测试发现问题")
            print(result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 测试超时，但系统可能正常")
        return True
    except Exception as e:
        print(f"⚠️ 测试失败: {e}")
        return True  # 继续启动

def start_optimized_system():
    """启动优化系统"""
    print("🚀 启动优化版终极中医RAG系统...")
    
    # 创建必要目录
    dirs_to_create = [
        "ultimate_final_vector_db",
        "documents", 
        "conversations",
        "logs",
        "cache",
        "temp"
    ]
    
    for dir_name in dirs_to_create:
        os.makedirs(dir_name, exist_ok=True)
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "ultimate_final_tcm_system.py",
        "--server.port=8507",
        "--server.address=0.0.0.0",
        "--theme.base=light",
        "--server.maxUploadSize=500"  # 支持500MB上传
    ]
    
    print("💡 启动命令:")
    print(f"   {' '.join(cmd)}")
    print()
    print("🌐 访问地址:")
    print("   本地: http://localhost:8507")
    print("   局域网: http://[您的IP]:8507")
    print()
    print("🎯 优化功能说明:")
    print("   ✅ 回答质量优化 - 强化检索和提示词")
    print("   ✅ 文档处理优化 - 异步处理，不卡顿")
    print("   ✅ 实时状态显示 - 检索过程可视化")
    print("   ✅ 错误处理增强 - 更好的用户体验")
    print()
    print("📝 使用建议:")
    print("   1. 首次使用请点击'初始化系统'")
    print("   2. 上传PDF文档提高回答质量")
    print("   3. 使用具体的中医术语提问")
    print("   4. 观察检索状态确保功能正常")
    print()
    print("⏹️ 按 Ctrl+C 停止服务")
    print("=" * 100)
    
    # 自动打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8507")
            print("🌐 已自动打开浏览器")
        except:
            pass
    
    import threading
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 优化版系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_optimization_menu():
    """显示优化菜单"""
    while True:
        print("\n" + "=" * 60)
        print("🔧 优化版系统控制菜单")
        print("=" * 60)
        print("1. 🚀 启动优化版系统 (推荐)")
        print("2. 📦 安装/更新依赖")
        print("3. 🧪 运行系统测试")
        print("4. 🔍 环境检查")
        print("5. 📖 查看优化说明")
        print("0. 👋 退出")
        print("=" * 60)
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == "1":
                start_optimized_system()
            elif choice == "2":
                install_missing_dependencies()
            elif choice == "3":
                run_system_test()
            elif choice == "4":
                check_optimization_requirements()
            elif choice == "5":
                show_optimization_details()
            elif choice == "0":
                print("👋 感谢使用优化版系统！")
                break
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 感谢使用！")
            break

def show_optimization_details():
    """显示优化详情"""
    details = """
🔧 系统优化详情

📈 回答质量优化:
1. **强化检索逻辑**
   - PDF检索结果从5条增加到8条
   - 古籍检索结果从3条增加到8条
   - 实时显示检索状态和相似度

2. **优化提示词**
   - 强制要求基于检索资料回答
   - 增加专业角色定义和回答格式
   - 提高DeepSeek模型输出质量

3. **增强备用机制**
   - 智能分析检索内容
   - 基于症状关键词提供建议
   - 确保即使检索失败也有质量回答

⚡ 文档处理优化:
1. **异步处理机制**
   - 分文件逐个处理，避免UI阻塞
   - 实时进度显示和状态更新
   - 支持大文件(>500MB)处理

2. **内存优化**
   - 分批向量化，避免内存溢出
   - 自动垃圾回收
   - 临时文件管理

3. **用户体验改进**
   - 详细的处理状态显示
   - 错误处理和恢复
   - 处理完成后自动刷新

🎯 使用建议:
- 上传中医相关PDF文档提高回答质量
- 使用具体的中医术语提问
- 观察检索状态确保功能正常
- 大文件上传时请耐心等待
"""
    print(details)

def main():
    """主函数"""
    print_optimization_banner()
    
    # 检查环境
    if not check_optimization_requirements():
        print("❌ 环境检查失败")
        print("💡 建议先运行'安装/更新依赖'")
    else:
        print("🎉 环境检查通过，系统已优化！")
    
    # 显示菜单
    show_optimization_menu()

if __name__ == "__main__":
    main()
