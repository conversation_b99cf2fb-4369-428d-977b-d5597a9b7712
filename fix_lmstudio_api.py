#!/usr/bin/env python3
"""
修复LM Studio API问题
真正解决API服务器启动问题
"""

import requests
import time
import subprocess
import sys
import json
from pathlib import Path
import psutil

class LMStudioAPIFixer:
    """LM Studio API修复器"""
    
    def __init__(self):
        self.api_base = "http://localhost:1234/v1"
        
    def check_lmstudio_process(self):
        """检查LM Studio进程"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'lm studio' in proc.info['name'].lower() or 'lmstudio' in proc.info['name'].lower():
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': proc.info['cmdline']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def check_port_1234(self):
        """检查端口1234是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == 1234:
                    return True, conn.pid
            return False, None
        except:
            return False, None
    
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = requests.get(f"{self.api_base}/models", timeout=3)
            if response.status_code == 200:
                data = response.json()
                models = [model['id'] for model in data.get('data', [])]
                return True, models
            return False, []
        except:
            return False, []
    
    def diagnose_problem(self):
        """诊断问题"""
        print("🔍 诊断LM Studio API问题...")
        
        # 1. 检查LM Studio进程
        processes = self.check_lmstudio_process()
        print(f"LM Studio进程: {len(processes)} 个")
        for proc in processes:
            print(f"  - PID {proc['pid']}: {proc['name']}")
        
        # 2. 检查端口
        port_used, port_pid = self.check_port_1234()
        print(f"端口1234占用: {'是' if port_used else '否'}")
        if port_used:
            print(f"  - 占用进程PID: {port_pid}")
        
        # 3. 测试API
        api_ok, models = self.test_api_connection()
        print(f"API连接: {'✅ 成功' if api_ok else '❌ 失败'}")
        if models:
            print(f"  - 可用模型: {models}")
        
        return {
            'lmstudio_running': len(processes) > 0,
            'port_occupied': port_used,
            'api_working': api_ok,
            'models': models
        }
    
    def try_restart_lmstudio_api(self):
        """尝试重启LM Studio API服务"""
        print("🔄 尝试重启LM Studio API服务...")
        
        # 方法1: 尝试通过HTTP请求激活
        activation_urls = [
            f"{self.api_base}/models",
            "http://localhost:1234/models",
            "http://localhost:1234/api/models",
            "http://localhost:1234/health",
            "http://localhost:1234/status"
        ]
        
        for url in activation_urls:
            try:
                print(f"尝试访问: {url}")
                response = requests.get(url, timeout=5)
                print(f"响应: {response.status_code}")
                
                # 等待一下再检查
                time.sleep(2)
                api_ok, models = self.test_api_connection()
                if api_ok:
                    print("✅ API已激活!")
                    return True
                    
            except Exception as e:
                print(f"失败: {e}")
                continue
        
        return False
    
    def provide_manual_instructions(self):
        """提供手动操作指导"""
        print("\n" + "="*50)
        print("📋 手动操作指导")
        print("="*50)
        
        print("🎯 **关键问题**: LM Studio中的API服务器没有启动")
        print()
        print("✅ **解决步骤**:")
        print("1. 在LM Studio界面中找到 'Local Server' 或 'Server' 标签页")
        print("2. 确认您的DeepSeek模型已经选中/加载")
        print("3. 找到 'Start Server' 按钮并点击")
        print("4. 确认端口设置为 1234")
        print("5. 等待显示 'Server is running' 状态")
        print()
        print("🔍 **如何确认成功**:")
        print("- LM Studio显示 'Server is running'")
        print("- 端口显示为 'localhost:1234'")
        print("- 可以看到API服务状态")
        print()
        print("⚠️ **重要提醒**:")
        print("- 仅仅加载模型是不够的")
        print("- 必须同时启动API服务器")
        print("- 这是两个独立的步骤")
    
    def wait_for_manual_fix(self, timeout=120):
        """等待手动修复"""
        print(f"\n⏳ 等待您手动启动API服务器 (最多{timeout}秒)...")
        
        for i in range(timeout):
            api_ok, models = self.test_api_connection()
            if api_ok and models:
                print(f"\n✅ 检测到API已启动! 模型: {models}")
                return True
            
            if i % 10 == 0 and i > 0:
                print(f"   仍在等待... ({i}/{timeout}秒)")
            
            time.sleep(1)
        
        print(f"\n⏰ 等待超时 ({timeout}秒)")
        return False
    
    def full_fix_process(self):
        """完整修复流程"""
        print("🛠️ LM Studio API修复器")
        print("="*40)
        
        # 1. 诊断问题
        status = self.diagnose_problem()
        
        # 2. 如果API已经工作，直接返回
        if status['api_working']:
            print("✅ API已经正常工作!")
            return True
        
        # 3. 如果LM Studio没运行，提示启动
        if not status['lmstudio_running']:
            print("❌ LM Studio未运行，请先启动LM Studio")
            return False
        
        # 4. 尝试自动激活API
        print("\n🔄 尝试自动激活API...")
        if self.try_restart_lmstudio_api():
            return True
        
        # 5. 提供手动指导
        self.provide_manual_instructions()
        
        # 6. 等待手动修复
        return self.wait_for_manual_fix()

def main():
    """主函数"""
    fixer = LMStudioAPIFixer()
    
    try:
        success = fixer.full_fix_process()
        
        if success:
            print("\n🎉 LM Studio API修复成功!")
            print("现在可以在RAG系统中正常使用DeepSeek了!")
            return True
        else:
            print("\n❌ 修复失败，请检查LM Studio设置")
            return False
            
    except Exception as e:
        print(f"\n💥 修复过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
