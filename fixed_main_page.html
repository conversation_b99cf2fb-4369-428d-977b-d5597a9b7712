<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 简化版终极中医RAG系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            width: 100%;
        }
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 20px; text-align: center;
        }
        .main-content {
            flex: 1;
            display: flex;
            min-height: 0; /* 重要：允许flex子元素收缩 */
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            padding: 20px;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            flex-shrink: 0; /* 防止侧边栏收缩 */
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0; /* 重要：允许聊天区域收缩 */
        }
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
            min-height: 200px; /* 确保最小高度 */
        }
        .message { margin-bottom: 15px; }
        .message.user { text-align: right; }
        .message-content {
            display: inline-block; max-width: 70%; padding: 10px 15px;
            border-radius: 15px; word-wrap: break-word;
        }
        .message.user .message-content {
            background: #2E8B57; color: white;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #ddd;
        }
        .input-area {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; }
        .input-row textarea {
            flex: 1; padding: 10px; border: 1px solid #ddd;
            border-radius: 8px; resize: none; font-size: 16px;
        }
        .send-btn, .voice-btn {
            padding: 10px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 8px; cursor: pointer;
        }
        .send-btn:hover, .voice-btn:hover { background: #228B22; }
        .send-btn:disabled, .voice-btn:disabled { background: #ccc; cursor: not-allowed; }
        .voice-btn {
            background: #6c757d;
            padding: 10px 15px;
            min-width: 50px;
            font-size: 16px;
        }
        .voice-btn:hover { background: #5a6268; }
        .voice-btn.recording {
            background: #dc3545;
            animation: pulse 1s infinite;
            color: white;
        }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .section { margin-bottom: 20px; }
        .section h3 { margin-bottom: 10px; color: #666; }
        .btn {
            width: 100%; margin-bottom: 8px; padding: 10px;
            background: white; border: 1px solid #ddd; border-radius: 5px;
            cursor: pointer; text-align: left;
        }
        .btn:hover { background: #f0f0f0; }
        .notification {
            position: fixed; top: 20px; right: 20px; padding: 15px;
            background: #2E8B57; color: white; border-radius: 5px;
            transform: translateX(400px); transition: transform 0.3s;
        }
        .notification.show { transform: translateX(0); }
        .notification.error { background: #dc3545; }
        .notification.success { background: #28a745; }
        .typing { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 简化版终极中医RAG系统</h1>
            <p>集成PDF检索 + 在线医学爬取 + 智能问答</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="section">
                    <h3>💡 快捷查询</h3>
                    <button class="btn" onclick="sendQuickMessage('脾胃虚弱的症状和调理方法')">
                        🌿 脾胃调理
                    </button>
                    <button class="btn" onclick="sendQuickMessage('肾阳虚和肾阴虚的区别及治疗')">
                        🫖 肾虚调养
                    </button>
                    <button class="btn" onclick="sendQuickMessage('失眠多梦的中医治疗方案')">
                        😴 失眠治疗
                    </button>
                    <button class="btn" onclick="sendQuickMessage('栀子甘草豉汤的功效和应用')">
                        💊 经典方剂
                    </button>
                </div>

                <div class="section">
                    <h3>💬 会话管理</h3>
                    <div id="sessionInfo">
                        <div>当前会话: <span id="currentSession">新会话</span></div>
                        <div>消息数量: <span id="messageCount">0</span></div>
                    </div>
                    <button class="btn" onclick="startNewSession()" style="margin-top: 10px; font-size: 12px;">
                        🆕 新建会话
                    </button>
                    <button class="btn" onclick="clearChat()" style="font-size: 12px;">
                        🗑️ 清空聊天
                    </button>
                </div>

                <div class="section">
                    <h3>📊 系统状态</h3>
                    <div id="systemStatus">
                        <div>状态: <span style="color: green;">✅ healthy</span></div>
                        <div>版本: 3.0.0-simple</div>
                        <div>文档: <span id="docCount">0</span> 个</div>
                        <div>功能: 4 项</div>
                        <div style="font-size: 11px; color: #888;">启动时间: <span id="startTime"></span></div>
                    </div>
                    <button class="btn" onclick="loadSystemStatus()" style="margin-top: 10px; font-size: 12px;">
                        🔄 刷新状态
                    </button>
                </div>
            </div>

            <div class="chat-area">
                <div class="messages" id="messages">
                    <div class="message assistant">
                        <div class="message-content">
                            <strong>🤖 助手:</strong> 您好！我是简化版终极中医RAG系统。我可以：<br>
                            🔍 搜索您上传的PDF文档<br>
                            🌐 优先爬取医宗金鉴等在线资源<br>
                            💬 提供智能中医问答<br><br>
                            请问有什么可以帮助您的吗？
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <div class="input-row">
                        <textarea id="messageInput" rows="2"
                                placeholder="请输入您的中医问题..."
                                onkeydown="handleKeyDown(event)"></textarea>
                        <button class="voice-btn" onclick="toggleVoiceInput()" id="voiceBtn" title="语音输入">🎤</button>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification">
        <div id="notificationText"></div>
    </div>

    <script>
        let currentSessionId = null;
        let isTyping = false;
        let recognition = null;
        let isRecording = false;
        let messageCount = 0;
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');

            if (notification && text) {
                text.textContent = message;
                notification.className = `notification ${type} show`;

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }

        // 加载系统状态
        async function loadSystemStatus() {
            console.log('🔍 开始加载系统状态...');
            
            try {
                const response = await fetch('/api/health?t=' + Date.now());
                console.log('📡 响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('📊 系统数据:', data);
                    
                    // 更新文档数量
                    const docCountElement = document.getElementById('docCount');
                    if (docCountElement) {
                        docCountElement.textContent = data.documents || 0;
                    }
                    
                    // 更新完整状态
                    const statusElement = document.getElementById('systemStatus');
                    if (statusElement) {
                        statusElement.innerHTML = `
                            <div>状态: <span style="color: green;">✅ ${data.status}</span></div>
                            <div>版本: ${data.version}</div>
                            <div>文档: ${data.documents} 个</div>
                            <div>功能: ${data.features ? data.features.length : 0} 项</div>
                            <div style="font-size: 11px; color: #888;">更新: ${new Date().toLocaleTimeString()}</div>
                        `;
                    }
                    
                    console.log('✅ 系统状态加载成功');
                    showNotification('✅ 系统状态已更新', 'success');
                } else {
                    throw new Error('HTTP ' + response.status);
                }

            } catch (error) {
                console.error('❌ 系统状态加载失败:', error);
                const statusElement = document.getElementById('systemStatus');
                if (statusElement) {
                    statusElement.innerHTML = `
                        <div style="color: red;">❌ 加载失败: ${error.message}</div>
                        <div style="font-size: 11px; color: #888;">时间: ${new Date().toLocaleTimeString()}</div>
                    `;
                }
                
                showNotification('❌ 系统状态加载失败', 'error');
            }
        }

        // 会话管理功能
        function startNewSession() {
            currentSessionId = null;
            messageCount = 0;
            updateSessionInfo();
            showNotification('🆕 已开始新会话', 'success');
        }

        function clearChat() {
            const container = document.getElementById('messages');
            // 保留欢迎消息
            const welcomeMessage = container.querySelector('.message.assistant');
            container.innerHTML = '';
            if (welcomeMessage) {
                container.appendChild(welcomeMessage);
            }
            messageCount = 0;
            updateSessionInfo();
            showNotification('🗑️ 聊天记录已清空', 'success');
        }

        function updateSessionInfo() {
            const sessionElement = document.getElementById('currentSession');
            const countElement = document.getElementById('messageCount');

            if (sessionElement) {
                sessionElement.textContent = currentSessionId ?
                    currentSessionId.substring(0, 8) + '...' : '新会话';
            }

            if (countElement) {
                countElement.textContent = messageCount;
            }
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在智能检索中...', 'typing');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                // 更新会话ID
                currentSessionId = data.session_id;

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 更新消息计数和会话信息
                messageCount += 2; // 用户消息 + 助手回复
                updateSessionInfo();

                // 显示处理时间
                showNotification(`⚡ 处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
                showNotification('❌ 请求失败: ' + error.message, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\n/g, '<br>')}`;

            messageDiv.appendChild(contentDiv);
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }

        // 语音识别功能
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.lang = 'zh-CN';
                recognition.continuous = false;
                recognition.interimResults = false;

                recognition.onstart = function() {
                    isRecording = true;
                    document.getElementById('voiceBtn').classList.add('recording');
                    showNotification('🎤 正在录音...', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    showNotification('✅ 语音识别完成', 'success');
                };

                recognition.onerror = function(event) {
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    document.getElementById('voiceBtn').classList.remove('recording');
                };

                return true;
            }
            return false;
        }

        function toggleVoiceInput() {
            if (!recognition) {
                if (!initSpeechRecognition()) {
                    showNotification('❌ 您的浏览器不支持语音识别', 'error');
                    return;
                }
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 设置启动时间
            document.getElementById('startTime').textContent = new Date().toLocaleTimeString();

            // 初始化语音识别
            initSpeechRecognition();

            // 初始化会话信息
            updateSessionInfo();

            // 延迟加载系统状态
            setTimeout(() => {
                loadSystemStatus();
            }, 1000);
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('JavaScript错误:', e.message, '在', e.filename, ':', e.lineno);
            showNotification('JavaScript错误: ' + e.message, 'error');
        });

        window.addEventListener('unhandledrejection', function(e) {
            console.error('Promise错误:', e.reason);
            showNotification('Promise错误: ' + e.reason, 'error');
        });
    </script>
</body>
</html>
