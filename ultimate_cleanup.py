#!/usr/bin/env python3
"""
终极清理脚本 - 彻底清理RAG 2025目录结构
只保留核心文件和必要目录
"""

import os
import shutil
from pathlib import Path
import json

def ultimate_cleanup():
    print('🧹 开始终极清理RAG 2025目录...')
    
    # 核心文件列表 - 必须保留的文件
    core_files = {
        # 主系统文件
        'perfect_unified_tcm_system.py',
        'intelligent_rag_retriever.py', 
        'intelligent_mcp_service.py',
        'deepseek_ollama_api.py',
        'perfect_launcher.py',
        'config.py',
        
        # 文档和说明
        'README_PERFECT.md',
        'SYSTEM_OPTIMIZATION_REPORT.md',
        
        # 配置文件
        'requirements_perfect.txt',
        
        # 核心测试文件
        'test_vector_db.py',
        'test_mcp_service.py',
        'test_system_integration.py',
        'test_deepseek_integration.py',
        'test_deepseek_simple.py',
        'check_vector_db.py',
        
        # 清理脚本
        'ultimate_cleanup.py'
    }
    
    # 核心目录列表 - 必须保留的目录
    core_directories = {
        'documents',
        'perfect_vector_db', 
        'conversations',
        'uploads',
        'models',
        'logs',
        'cache'
    }
    
    # 获取当前目录下的所有文件和目录
    current_dir = Path('.')
    all_items = list(current_dir.iterdir())
    
    deleted_files = []
    deleted_dirs = []
    
    print('📋 分析文件结构...')
    
    # 删除冗余文件
    for item in all_items:
        if item.name.startswith('.'):
            continue  # 跳过隐藏文件
            
        if item.is_file():
            if item.name not in core_files:
                try:
                    item.unlink()
                    deleted_files.append(item.name)
                    print(f'   🗑️ 删除文件: {item.name}')
                except Exception as e:
                    print(f'   ❌ 删除失败: {item.name} - {e}')
        
        elif item.is_dir():
            if item.name not in core_directories:
                # 特殊处理：保留有用的子目录内容
                if item.name == 'backend':
                    # 删除backend目录
                    try:
                        shutil.rmtree(item)
                        deleted_dirs.append(item.name)
                        print(f'   🗑️ 删除目录: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
                elif item.name in ['__pycache__', 'temp', '.pytest_cache', '.git']:
                    try:
                        shutil.rmtree(item)
                        deleted_dirs.append(item.name)
                        print(f'   🗑️ 删除目录: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
                elif item.name.startswith('vector_db') and item.name != 'perfect_vector_db':
                    # 删除其他版本的向量数据库
                    try:
                        shutil.rmtree(item)
                        deleted_dirs.append(item.name)
                        print(f'   🗑️ 删除目录: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
    
    # 创建必要的目录
    print('\n📁 创建必要目录...')
    for dir_name in core_directories:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f'   ✅ 创建目录: {dir_name}')
    
    # 清理缓存目录
    print('\n🧹 清理缓存目录...')
    cache_dirs = ['cache', 'logs']
    for cache_dir in cache_dirs:
        cache_path = Path(cache_dir)
        if cache_path.exists():
            for file in cache_path.glob('*'):
                if file.is_file() and file.suffix in ['.log', '.tmp', '.cache']:
                    try:
                        file.unlink()
                        print(f'   🗑️ 清理缓存: {file.name}')
                    except:
                        pass
    
    # 生成清理报告
    cleanup_report = {
        'timestamp': str(datetime.now()),
        'deleted_files_count': len(deleted_files),
        'deleted_directories_count': len(deleted_dirs),
        'deleted_files': deleted_files[:20],  # 只记录前20个
        'deleted_directories': deleted_dirs,
        'core_files_preserved': list(core_files),
        'core_directories_preserved': list(core_directories),
        'cleanup_summary': {
            'total_deleted_files': len(deleted_files),
            'total_deleted_dirs': len(deleted_dirs),
            'core_files_count': len(core_files),
            'core_dirs_count': len(core_directories)
        }
    }
    
    # 保存清理报告
    with open('ultimate_cleanup_report.json', 'w', encoding='utf-8') as f:
        json.dump(cleanup_report, f, ensure_ascii=False, indent=2)
    
    print(f'\n📊 终极清理完成统计:')
    print(f'   - 删除文件: {len(deleted_files)} 个')
    print(f'   - 删除目录: {len(deleted_dirs)} 个')
    print(f'   - 保留核心文件: {len(core_files)} 个')
    print(f'   - 保留核心目录: {len(core_directories)} 个')
    
    if deleted_files:
        print(f'\n🗑️ 主要删除的文件类型:')
        file_types = {}
        for file in deleted_files:
            ext = Path(file).suffix or 'no_ext'
            file_types[ext] = file_types.get(ext, 0) + 1
        
        for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f'   - {ext}: {count} 个')
    
    print('\n✅ 终极清理完成！')
    print('📋 详细报告已保存到 ultimate_cleanup_report.json')
    print('\n🎯 清理后的目录结构:')
    print('   📁 核心文件: perfect_unified_tcm_system.py (主系统)')
    print('   📁 核心文件: intelligent_rag_retriever.py (RAG检索)')
    print('   📁 核心文件: intelligent_mcp_service.py (MCP服务)')
    print('   📁 核心文件: deepseek_ollama_api.py (AI模型)')
    print('   📁 核心文件: perfect_launcher.py (启动器)')
    print('   📁 数据目录: perfect_vector_db/ (向量数据库)')
    print('   📁 数据目录: documents/ (文档)')
    print('   📁 数据目录: conversations/ (对话历史)')
    
    return True

if __name__ == "__main__":
    print('🏥 完美统一中医智能助手 - 终极清理工具')
    print('🎯 删除所有冗余文件，只保留核心系统')
    print('=' * 60)
    
    confirm = input('⚠️  警告：这将删除大量文件！确认开始清理？(y/N): ')
    if confirm.lower() in ['y', 'yes']:
        from datetime import datetime
        ultimate_cleanup()
    else:
        print('❌ 清理已取消')
