#!/usr/bin/env python3
"""
修复DeepSeek兼容性问题
下载兼容的模型版本或使用替代方案
"""

import os
import sys
import requests
from pathlib import Path

def test_current_model():
    """测试当前模型"""
    print("🧪 测试当前DeepSeek模型...")
    
    model_path = './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf'
    
    if not os.path.exists(model_path):
        print("❌ 模型文件不存在")
        return False
    
    try:
        from llama_cpp import Llama
        
        # 尝试不同的加载参数
        configs = [
            {"n_ctx": 128, "n_threads": 1, "verbose": False},
            {"n_ctx": 256, "n_threads": 1, "verbose": True},
            {"n_ctx": 512, "n_threads": 2, "verbose": True}
        ]
        
        for i, config in enumerate(configs, 1):
            try:
                print(f"   🔄 尝试配置{i}...")
                model = Llama(
                    model_path=model_path,
                    n_gpu_layers=0,
                    use_mmap=False,
                    use_mlock=False,
                    **config
                )
                print(f"   ✅ 配置{i}成功!")
                
                # 简单测试
                response = model("Hello", max_tokens=3, echo=False)
                print(f"   ✅ 测试响应: {response}")
                return True
                
            except Exception as e:
                print(f"   ❌ 配置{i}失败: {str(e)[:50]}...")
                continue
        
        return False
        
    except ImportError:
        print("❌ llama-cpp-python未安装")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def download_compatible_model():
    """下载兼容的模型"""
    print("\n📥 下载兼容的DeepSeek模型...")
    
    # 使用更兼容的模型版本
    compatible_models = [
        {
            "name": "DeepSeek-Coder-6.7B-Q4_K_M",
            "url": "https://huggingface.co/TheBloke/deepseek-coder-6.7b-instruct-GGUF/resolve/main/deepseek-coder-6.7b-instruct.Q4_K_M.gguf",
            "size": "3.8GB",
            "description": "DeepSeek Coder 6.7B，兼容性极佳"
        },
        {
            "name": "DeepSeek-Coder-1.3B-Q4_K_M",
            "url": "https://huggingface.co/TheBloke/deepseek-coder-1.3b-instruct-GGUF/resolve/main/deepseek-coder-1.3b-instruct.Q4_K_M.gguf",
            "size": "0.8GB",
            "description": "DeepSeek Coder 1.3B，小巧快速"
        }
    ]
    
    print("📋 可用的兼容模型:")
    for i, model in enumerate(compatible_models, 1):
        print(f"{i}. {model['name']}")
        print(f"   大小: {model['size']}")
        print(f"   描述: {model['description']}")
        print()
    
    choice = input("选择要下载的模型 (1-2) 或按回车跳过: ").strip()
    
    if not choice:
        return False
    
    try:
        selected = compatible_models[int(choice) - 1]
        return download_model(selected)
    except (ValueError, IndexError):
        print("❌ 无效选择")
        return False

def download_model(model_info):
    """下载模型"""
    model_dir = Path("./models")
    model_dir.mkdir(exist_ok=True)
    
    filename = f"{model_info['name']}.gguf"
    model_path = model_dir / filename
    
    print(f"📥 下载 {model_info['name']}...")
    print(f"📍 URL: {model_info['url']}")
    print(f"💾 保存到: {model_path}")
    
    try:
        response = requests.get(model_info['url'], stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = downloaded / total_size * 100
                        print(f"\r📥 下载进度: {progress:.1f}%", end="")
        
        print(f"\n✅ 下载完成!")
        
        # 测试新模型
        if test_model(str(model_path)):
            print("🎉 新模型测试成功!")
            update_system_config(str(model_path))
            return True
        else:
            print("⚠️ 新模型测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def test_model(model_path):
    """测试模型"""
    print(f"\n🧪 测试模型: {Path(model_path).name}")
    
    try:
        from llama_cpp import Llama
        
        model = Llama(
            model_path=model_path,
            n_ctx=256,
            n_threads=1,
            n_gpu_layers=0,
            verbose=False,
            use_mmap=False,
            use_mlock=False
        )
        
        response = model("Hello", max_tokens=5, echo=False)
        print(f"✅ 测试成功: {response}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def update_system_config(model_path):
    """更新系统配置"""
    print("\n🔧 更新系统配置...")
    
    try:
        # 更新主系统文件
        with open("ultimate_final_tcm_system.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换模型路径
        old_path = "'DEEPSEEK_MODEL_PATH': './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf'"
        new_path = f"'DEEPSEEK_MODEL_PATH': '{model_path.replace(chr(92), '/')}'"
        
        new_content = content.replace(old_path, new_path)
        
        with open("ultimate_final_tcm_system.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("✅ 系统配置已更新")
        
    except Exception as e:
        print(f"⚠️ 更新配置失败: {e}")

def create_fallback_solution():
    """创建备用解决方案"""
    print("\n🔄 创建备用解决方案...")
    
    # 修改系统以支持无模型运行
    fallback_code = '''
# 备用DeepSeek管理器
class FallbackDeepSeekManager:
    """备用DeepSeek管理器 - 智能回答模式"""
    
    def __init__(self):
        self.initialized = True
        self.fallback_mode = True
    
    def initialize(self):
        st.info("🤖 启用智能备用回答模式")
        st.success("✅ 系统已就绪（备用模式）")
        return True
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成智能回答"""
        # 基于关键词的智能回答
        if "中医" in prompt or "中药" in prompt:
            return self._generate_tcm_response(prompt)
        elif "方剂" in prompt or "药方" in prompt:
            return self._generate_prescription_response(prompt)
        else:
            return self._generate_general_response(prompt)
    
    def _generate_tcm_response(self, prompt):
        return """中医是中华民族传统医学，具有数千年历史。
        
## 中医基本理论
- **阴阳学说**: 认为人体是阴阳对立统一的整体
- **五行学说**: 木、火、土、金、水五行相生相克
- **脏腑学说**: 五脏六腑功能协调
- **经络学说**: 气血运行的通道

## 诊断方法
- **望诊**: 观察面色、舌象等
- **闻诊**: 听声音、嗅气味
- **问诊**: 询问症状、病史
- **切诊**: 脉诊、按诊

## 治疗方法
- **中药治疗**: 汤剂、丸剂、散剂等
- **针灸治疗**: 针刺、艾灸
- **推拿按摩**: 手法治疗
- **食疗**: 药食同源

请提供更具体的问题，我可以给出更详细的回答。"""
    
    def _generate_prescription_response(self, prompt):
        return """中医方剂是根据中医理论组方的药物配伍。

## 经典方剂举例
- **四君子汤**: 人参、白术、茯苓、甘草 - 补气健脾
- **四物汤**: 当归、川芎、白芍、熟地 - 补血调经
- **小柴胡汤**: 柴胡、黄芩、人参、半夏等 - 和解少阳
- **麻黄汤**: 麻黄、桂枝、杏仁、甘草 - 发汗解表

## 方剂配伍原则
- **君药**: 主要治疗作用
- **臣药**: 辅助君药
- **佐药**: 制约副作用
- **使药**: 调和诸药

请注意：具体用药需要专业中医师诊断后开具，不可自行用药。"""
    
    def _generate_general_response(self, prompt):
        return f"""感谢您的咨询。

基于您的问题："{prompt[:100]}..."

我建议：
1. 如需专业中医诊疗，请咨询执业中医师
2. 可以上传相关医学文档，我可以帮助检索信息
3. 对于具体症状，建议结合现代医学检查

如果您有具体的中医药问题，请提供更详细的信息，我会尽力为您解答。

**免责声明**: 本回答仅供参考，不能替代专业医疗建议。"""

# 使用备用管理器
if 'deepseek_manager' not in st.session_state:
    st.session_state.deepseek_manager = FallbackDeepSeekManager()
'''
    
    with open("fallback_deepseek.py", "w", encoding="utf-8") as f:
        f.write(fallback_code)
    
    print("✅ 备用解决方案已创建: fallback_deepseek.py")

def main():
    """主函数"""
    print("🔧 DeepSeek兼容性修复工具")
    print("=" * 50)
    
    # 测试当前模型
    if test_current_model():
        print("\n🎉 当前模型工作正常!")
        return
    
    print("\n❌ 当前模型无法正常工作")
    
    # 尝试下载兼容模型
    print("\n💡 解决方案:")
    print("1. 下载兼容的DeepSeek模型")
    print("2. 使用智能备用模式")
    
    choice = input("\n选择解决方案 (1/2): ").strip()
    
    if choice == "1":
        if download_compatible_model():
            print("\n🎉 兼容模型安装成功!")
        else:
            print("\n⚠️ 模型下载失败，启用备用模式")
            create_fallback_solution()
    else:
        create_fallback_solution()
    
    print("\n💡 下一步:")
    print("重启系统: streamlit run ultimate_final_tcm_system.py --server.port=8507")

if __name__ == "__main__":
    main()
