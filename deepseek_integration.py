
# DeepSeek-R1 Transformers集成到RAG系统
# 将此代码添加到您的ultimate_final_tcm_system.py中

from deepseek_r1_transformers import DeepSeekR1Manager
import streamlit as st

class TransformersDeepSeekManager:
    """Transformers版本的DeepSeek管理器"""
    
    def __init__(self):
        self.deepseek = DeepSeekR1Manager()
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化"""
        if self.initialized:
            return True
        
        st.info("🚀 正在加载DeepSeek-R1模型（Transformers版本）...")
        
        # 根据系统配置选择量化选项
        load_in_8bit = st.checkbox("使用8bit量化（节省内存）", value=True)
        
        if self.deepseek.initialize(load_in_8bit=load_in_8bit):
            self.initialized = True
            st.success("✅ DeepSeek-R1模型加载成功！")
            st.info("🎯 使用Transformers引擎，完全兼容Qwen3架构")
            return True
        else:
            st.error("❌ DeepSeek-R1模型加载失败")
            return False
    
    def generate_response(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
        """生成回答"""
        if not self.initialized:
            return "DeepSeek模型未初始化"
        
        st.info("🧠 DeepSeek-R1正在思考...")
        
        try:
            response = self.deepseek.generate(
                prompt=prompt,
                max_length=max_tokens,
                temperature=temperature
            )
            
            if response:
                st.success("✅ DeepSeek-R1生成完成")
                return response
            else:
                return "DeepSeek生成失败，请重试"
                
        except Exception as e:
            st.error(f"❌ DeepSeek调用失败: {e}")
            return f"DeepSeek调用失败: {str(e)}"

# 使用方法：
# 1. 将TransformersDeepSeekManager替换原来的UltimateDeepSeekManager
# 2. 在系统初始化时调用initialize()
# 3. 使用generate_response()生成回答
