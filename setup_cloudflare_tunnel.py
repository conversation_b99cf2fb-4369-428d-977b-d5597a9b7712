#!/usr/bin/env python3
"""
使用Cloudflare隧道 - 免费的ngrok替代方案
"""
import subprocess
import sys
import time
import requests
import re
import threading

def download_cloudflared():
    """下载cloudflared"""
    try:
        print("🔧 正在下载cloudflared...")
        
        # Windows版本下载链接
        url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        
        import urllib.request
        urllib.request.urlretrieve(url, "cloudflared.exe")
        
        print("✅ cloudflared下载成功")
        return True
    except Exception as e:
        print(f"❌ cloudflared下载失败: {e}")
        return False

def setup_cloudflare_tunnel():
    """设置Cloudflare隧道"""
    try:
        print("🌩️ 启动Cloudflare隧道...")
        print("🎯 目标端口: 8006")
        print("🔐 认证密码: MVP168918")
        
        # 启动隧道
        cmd = ['cloudflared.exe', 'tunnel', '--url', 'http://localhost:8006']
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, text=True)
        
        # 等待隧道启动并获取URL
        public_url = None
        for _ in range(60):  # 等待60秒
            if process.poll() is not None:
                break
            
            # 检查输出中的URL
            try:
                line = process.stderr.readline()
                if line:
                    print(f"📝 {line.strip()}")
                    
                    # 查找隧道URL
                    if 'https://' in line and 'trycloudflare.com' in line:
                        # 提取URL
                        url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', line)
                        if url_match:
                            public_url = url_match.group(0)
                            break
            except:
                pass
            
            time.sleep(1)
        
        if public_url:
            print(f"\n🎉 Cloudflare隧道启动成功！")
            print("=" * 60)
            print(f"🏥 中医RAG智能诊疗系统")
            print(f"🌐 公网访问地址: {public_url}")
            print(f"🔐 登录凭据:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            print("=" * 60)
            
            print(f"\n📱 分享给异地朋友的完整信息:")
            print("=" * 60)
            print(f"🏥 【中医RAG智能诊疗系统】")
            print(f"")
            print(f"🌐 访问地址: {public_url}")
            print(f"")
            print(f"🔐 登录信息:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            print(f"")
            print(f"💡 使用说明:")
            print(f"1. 点击上述网址打开系统")
            print(f"2. 输入用户名和密码登录")
            print(f"3. 即可使用中医智能诊疗功能")
            print(f"")
            print(f"🎯 系统功能:")
            print(f"• DeepSeek-R1智能诊疗分析")
            print(f"• 在线医学资源检索")
            print(f"• PDF文档上传分析")
            print(f"• 语音交互支持")
            print(f"• 会话历史管理")
            print("=" * 60)
            
            return public_url, process
        else:
            print("❌ 未能获取隧道URL")
            process.terminate()
            return None, None
        
    except FileNotFoundError:
        print("❌ cloudflared未找到，正在下载...")
        if download_cloudflared():
            return setup_cloudflare_tunnel()
        else:
            return None, None
    except Exception as e:
        print(f"❌ Cloudflare隧道启动失败: {e}")
        return None, None

def test_tunnel_access(url):
    """测试隧道访问"""
    try:
        print("\n🧪 测试隧道访问...")
        
        # 等待一下让隧道完全启动
        time.sleep(5)
        
        # 测试健康检查接口
        health_url = f"{url}/api/health"
        response = requests.get(health_url, auth=('tcm_user', 'MVP168918'), timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 隧道访问测试成功！")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
            return True
        else:
            print(f"❌ 隧道访问测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 隧道访问测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 中医RAG系统 - Cloudflare隧道设置")
    print("=" * 50)
    
    # 检查本地服务器是否运行
    try:
        response = requests.get("http://localhost:8006/api/health", 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 本地服务器运行正常")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
        else:
            print("❌ 本地服务器响应异常")
            return
    except Exception as e:
        print("❌ 本地服务器未运行，请先启动服务器")
        print("💡 运行命令: python simple_ultimate_tcm.py")
        print(f"错误详情: {e}")
        return
    
    # 设置Cloudflare隧道
    public_url, process = setup_cloudflare_tunnel()
    
    if public_url and process:
        # 测试隧道访问
        if test_tunnel_access(public_url):
            print("\n🎉 Cloudflare隧道设置完成！")
            print("📱 现在可以将访问信息分享给异地朋友了")
            
            # 保持隧道运行
            try:
                print("\n⏳ 隧道保持运行中... (按Ctrl+C停止)")
                print("💡 请保持此窗口打开，关闭后隧道将断开")
                print("-" * 50)
                
                while True:
                    if process.poll() is not None:
                        print("❌ 隧道进程意外退出")
                        break
                    
                    time.sleep(60)
                    print(f"🔄 隧道运行中: {public_url} ({time.strftime('%H:%M:%S')})")
                    
            except KeyboardInterrupt:
                print("\n🛑 正在停止隧道...")
                process.terminate()
                process.wait()
                print("🛑 隧道已停止")
                print("👋 感谢使用中医RAG智能诊疗系统！")
        else:
            print("❌ 隧道设置失败")
            process.terminate()
    else:
        print("❌ 无法设置Cloudflare隧道")
        print("\n💡 替代方案:")
        print("1. 使用局域网访问 (同一WiFi)")
        print("2. 尝试ngrok等其他隧道服务")
        print("3. 部署到云服务器")

if __name__ == "__main__":
    main()
