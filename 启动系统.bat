@echo off
chcp 65001 >nul
title 终极中医RAG系统启动器

echo ===============================================
echo 🧙‍♂️ 终极中医RAG系统启动器
echo ===============================================
echo.

echo 🚀 正在启动系统...
echo.

REM 方法1: 使用simple_start.py
if exist "simple_start.py" (
    echo 📝 使用简单启动脚本...
    python simple_start.py
    goto :end
)

REM 方法2: 直接启动
if exist "working_tcm_system_api.py" (
    echo 📝 直接启动主系统...
    python -m streamlit run working_tcm_system_api.py --server.port=8507 --server.address=0.0.0.0
    goto :end
)

REM 如果都找不到
echo ❌ 找不到系统文件
echo 💡 请确保以下文件存在:
echo    - working_tcm_system_api.py
echo    - simple_start.py
echo.

:end
echo.
echo 👋 感谢使用！
pause
