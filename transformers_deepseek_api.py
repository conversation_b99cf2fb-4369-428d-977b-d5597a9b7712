#!/usr/bin/env python3
"""
基于Transformers的DeepSeek API
使用HuggingFace Transformers库加载DeepSeek-R1模型
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uvicorn
import torch
import gc
import time

# 检查transformers
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# 请求模型
class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 2048
    temperature: Optional[float] = 0.7

class ChatResponse(BaseModel):
    response: str
    model: str
    status: str

# FastAPI应用
app = FastAPI(
    title="DeepSeek-R1 Transformers API",
    description="基于Transformers的DeepSeek-R1 API",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TransformersDeepSeekAPI:
    """基于Transformers的DeepSeek API"""
    
    def __init__(self):
        self.model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
        self.model = None
        self.tokenizer = None
        self.device = None
        self.initialized = False
        
    def load_model(self):
        """加载模型"""
        if not TRANSFORMERS_AVAILABLE:
            raise Exception("transformers未安装，请运行: pip install transformers torch")
        
        print(f"🔄 加载模型: {self.model_name}")
        
        try:
            # 检测设备
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"🎯 使用设备: {self.device}")
            
            # 加载分词器
            print("📥 加载分词器...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True
            )
            
            # 加载模型
            print("📥 加载模型（这可能需要几分钟）...")
            
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "low_cpu_mem_usage": True,
            }
            
            # CPU优化
            if self.device == "cpu":
                model_kwargs["torch_dtype"] = torch.float32
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                **model_kwargs
            )
            
            self.model = self.model.to(self.device)
            self.model.eval()
            
            print("✅ 模型加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def initialize(self):
        """初始化API"""
        if self.initialized:
            return True
        
        try:
            if self.load_model():
                # 测试生成
                test_response = self.generate_response("你好", max_tokens=10)
                if test_response and test_response.get('status') == 'success':
                    self.initialized = True
                    print("✅ API初始化成功")
                    return True
                else:
                    print("❌ 模型测试失败")
                    return False
            else:
                print("❌ 模型加载失败")
                return False
                
        except Exception as e:
            print(f"❌ API初始化失败: {e}")
            return False
    
    def generate_response(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7):
        """生成回答"""
        if not self.initialized:
            if not self.initialize():
                raise HTTPException(status_code=500, detail="模型初始化失败")
        
        try:
            # 构建中医专业提示词
            system_prompt = "你是一位专业的中医医生，请用专业、温和的语气回答用户的中医相关问题。"
            full_prompt = f"{system_prompt}\n\n用户问题: {prompt}\n\n回答:"
            
            # 编码输入
            inputs = self.tokenizer(
                full_prompt,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=2048
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成参数
            generation_kwargs = {
                "max_length": len(inputs["input_ids"][0]) + max_tokens,
                "temperature": temperature,
                "do_sample": True,
                "top_p": 0.9,
                "pad_token_id": self.tokenizer.pad_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "repetition_penalty": 1.1,
            }
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs["input_ids"],
                    attention_mask=inputs.get("attention_mask"),
                    **generation_kwargs
                )
            
            # 解码
            generated_text = self.tokenizer.decode(
                outputs[0],
                skip_special_tokens=True
            )
            
            # 提取新生成的部分
            response = generated_text[len(full_prompt):].strip()
            
            # 清理GPU内存
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            # 清理回答
            response = self._clean_response(response)
            
            if response:
                return {
                    "response": response,
                    "model": self.model_name,
                    "status": "success"
                }
            else:
                raise HTTPException(status_code=500, detail="生成为空")
                
        except Exception as e:
            # 清理内存
            if self.device == "cuda":
                torch.cuda.empty_cache()
            gc.collect()
            
            raise HTTPException(status_code=500, detail=f"生成异常: {str(e)}")
    
    def _clean_response(self, text: str) -> str:
        """清理生成的回答"""
        import re
        
        # 移除多余的换行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 移除可能的提示词残留
        text = re.sub(r'^(用户|助手|Human|Assistant|回答|问题)[:：]\s*', '', text, flags=re.MULTILINE)
        
        return text.strip()

# 全局API实例
deepseek_api = TransformersDeepSeekAPI()

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    print("🚀 启动Transformers DeepSeek API服务...")
    # 不在启动时初始化，避免阻塞
    print("💡 模型将在首次请求时加载")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "DeepSeek-R1 Transformers API服务",
        "model": deepseek_api.model_name,
        "status": "running" if deepseek_api.initialized else "not_initialized"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy" if deepseek_api.initialized else "not_initialized",
        "model": deepseek_api.model_name,
        "device": deepseek_api.device,
        "model_loaded": deepseek_api.model is not None
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    try:
        result = deepseek_api.generate_response(
            prompt=request.message,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        return ChatResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate")
async def generate(request: ChatRequest):
    """生成接口（兼容性）"""
    return await chat(request)

@app.post("/initialize")
async def initialize_model():
    """手动初始化模型"""
    try:
        if deepseek_api.initialize():
            return {"status": "success", "message": "模型初始化成功"}
        else:
            raise HTTPException(status_code=500, detail="模型初始化失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🤖 启动DeepSeek-R1 Transformers FastAPI服务")
    print("📋 API文档: http://localhost:8002/docs")
    print("🔗 健康检查: http://localhost:8002/health")
    print("💡 首次请求时会自动下载和加载模型")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        log_level="info"
    )
