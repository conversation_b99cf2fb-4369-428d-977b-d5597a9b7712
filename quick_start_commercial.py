#!/usr/bin/env python3
"""
快速启动商业化中医RAG系统
一键安装依赖并启动系统
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🏥 商业级中医智能助手 - 快速启动")
    print("=" * 60)
    print("🚀 确保每个功能都真正工作")
    print("📚 真正的PDF检索 + 智能回答生成")
    print("🌐 在线资源整合 + 向量相似度匹配")
    print("⚡ 超越简单问答的商业级体验")
    print("=" * 60)

def check_python():
    """检查Python版本"""
    print("🔍 检查Python环境...")
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装必要依赖...")
    
    # 核心依赖列表
    dependencies = [
        "streamlit>=1.28.0",
        "sentence-transformers>=2.2.0", 
        "faiss-cpu>=1.7.0",
        "PyPDF2>=3.0.0",
        "numpy>=1.21.0",
        "requests>=2.28.0",
        "beautifulsoup4>=4.11.0",
        "torch>=1.13.0"
    ]
    
    failed_packages = []
    
    for package in dependencies:
        package_name = package.split(">=")[0]
        try:
            # 检查是否已安装
            __import__(package_name.replace("-", "_"))
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"📥 安装 {package}...")
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"✅ {package} 安装成功")
                else:
                    print(f"❌ {package} 安装失败: {result.stderr}")
                    failed_packages.append(package)
            except subprocess.TimeoutExpired:
                print(f"⏰ {package} 安装超时")
                failed_packages.append(package)
            except Exception as e:
                print(f"❌ {package} 安装异常: {e}")
                failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️ 以下包安装失败: {', '.join(failed_packages)}")
        print("💡 请手动安装这些包或检查网络连接")
        return False
    
    print("✅ 所有依赖安装完成")
    return True

def create_directories():
    """创建必要目录"""
    print("\n📁 创建系统目录...")
    
    directories = [
        "./working_vector_db",
        "./documents", 
        "./uploads",
        "./online_cache",
        "./logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}")

def check_existing_data():
    """检查现有数据"""
    print("\n🔍 检查现有数据...")
    
    # 检查PDF文档
    documents_dir = Path("./documents")
    if documents_dir.exists():
        pdf_files = list(documents_dir.glob("*.pdf"))
        if pdf_files:
            print(f"📚 发现 {len(pdf_files)} 个PDF文档:")
            for pdf in pdf_files[:5]:  # 只显示前5个
                print(f"   - {pdf.name}")
            if len(pdf_files) > 5:
                print(f"   ... 还有 {len(pdf_files) - 5} 个文档")
        else:
            print("📄 documents目录为空，请上传PDF文档")
    
    # 检查向量数据库
    vector_db_dir = Path("./working_vector_db")
    if vector_db_dir.exists():
        required_files = ["index.faiss", "chunks.pkl", "metadata.pkl"]
        existing_files = [f for f in required_files if (vector_db_dir / f).exists()]
        
        if len(existing_files) == len(required_files):
            print("🗄️ 向量数据库完整，可直接使用")
        elif existing_files:
            print(f"⚠️ 向量数据库不完整，缺少: {set(required_files) - set(existing_files)}")
        else:
            print("🆕 向量数据库为空，需要处理PDF文档")

def test_core_functionality():
    """测试核心功能"""
    print("\n🧪 测试核心功能...")
    
    try:
        # 测试嵌入模型
        print("📊 测试嵌入模型...")
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('moka-ai/m3e-base')
        test_embedding = model.encode(["测试文本"])[0]
        print(f"✅ 嵌入模型正常 (维度: {len(test_embedding)})")
        
        # 测试FAISS
        print("🔍 测试FAISS...")
        import faiss
        import numpy as np
        test_vectors = np.random.random((10, len(test_embedding))).astype('float32')
        index = faiss.IndexFlatIP(len(test_embedding))
        index.add(test_vectors)
        print(f"✅ FAISS正常 (索引大小: {index.ntotal})")
        
        # 测试PDF处理
        print("📄 测试PDF处理...")
        import PyPDF2
        print("✅ PDF处理模块正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def launch_application():
    """启动应用"""
    print("\n🚀 启动应用...")
    
    try:
        # 检查应用文件是否存在
        app_file = "working_commercial_tcm.py"
        if not Path(app_file).exists():
            print(f"❌ 应用文件不存在: {app_file}")
            return False
        
        print(f"📱 启动 {app_file}...")
        print("🌐 应用将在浏览器中打开: http://localhost:8501")
        print("⏹️ 按 Ctrl+C 停止应用")
        print("-" * 50)
        
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_file,
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--theme.base=light",
            "--server.headless=false"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 1. 检查Python环境
    if not check_python():
        input("按回车键退出...")
        return
    
    # 2. 安装依赖
    if not install_dependencies():
        print("\n⚠️ 依赖安装不完整，但可以尝试启动应用")
        response = input("是否继续启动？(y/N): ")
        if response.lower() != 'y':
            return
    
    # 3. 创建目录
    create_directories()
    
    # 4. 检查现有数据
    check_existing_data()
    
    # 5. 测试核心功能
    if not test_core_functionality():
        print("\n⚠️ 核心功能测试失败，但可以尝试启动应用")
        response = input("是否继续启动？(y/N): ")
        if response.lower() != 'y':
            return
    
    print("\n" + "=" * 60)
    print("🎉 系统准备完成！")
    print("💡 使用说明:")
    print("   1. 应用启动后，点击'初始化系统'")
    print("   2. 上传中医PDF文档到知识库")
    print("   3. 开始智能问答体验")
    print("=" * 60)
    
    # 6. 启动应用
    input("按回车键启动应用...")
    launch_application()

if __name__ == "__main__":
    main()
