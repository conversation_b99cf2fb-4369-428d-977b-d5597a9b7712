<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单聊天测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .chat-container { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 10px; margin-bottom: 10px; background: #f9f9f9; }
        .message { margin-bottom: 10px; padding: 8px; border-radius: 5px; }
        .user { background: #e3f2fd; text-align: right; }
        .assistant { background: #f1f8e9; }
        .input-area { display: flex; gap: 10px; }
        .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .input-area button { padding: 10px 20px; background: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .input-area button:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin-bottom: 10px; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
    </style>
</head>
<body>
    <h1>🏥 简单聊天测试</h1>
    
    <div id="status" class="status">准备就绪</div>
    
    <div id="chatContainer" class="chat-container">
        <div class="message assistant">
            <strong>🤖 助手:</strong> 您好！我是中医RAG系统，请输入您的问题。
        </div>
    </div>
    
    <div class="input-area">
        <input type="text" id="messageInput" placeholder="请输入您的问题..." onkeydown="handleKeyDown(event)">
        <button id="sendBtn" onclick="sendMessage()">发送</button>
        <button onclick="testAPI()">测试API</button>
        <button onclick="clearChat()">清空</button>
    </div>

    <script>
        let isTyping = false;
        let currentSessionId = null;
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            console.log(`[${new Date().toLocaleTimeString()}] ${type.toUpperCase()}: ${message}`);
        }
        
        function addMessage(type, content) {
            const container = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            messageDiv.innerHTML = `<strong>${prefix}</strong> ${content}`;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }
        
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) {
                updateStatus('请输入消息或等待当前请求完成', 'error');
                return;
            }
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 设置加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            updateStatus('正在发送请求...', 'info');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });
                
                updateStatus(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                // 更新会话ID
                currentSessionId = data.session_id;
                
                // 显示助手回复
                addMessage('assistant', data.response);
                
                updateStatus(`处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');
                
            } catch (error) {
                console.error('发送失败:', error);
                addMessage('assistant', `抱歉，发生了错误: ${error.message}`);
                updateStatus(`发送失败: ${error.message}`, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }
        
        async function testAPI() {
            updateStatus('测试API连接...', 'info');
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                updateStatus(`API测试成功 - 状态: ${data.status}, 文档: ${data.documents}个`, 'success');
                console.log('API测试结果:', data);
                
            } catch (error) {
                updateStatus(`API测试失败: ${error.message}`, 'error');
                console.error('API测试失败:', error);
            }
        }
        
        function clearChat() {
            const container = document.getElementById('chatContainer');
            container.innerHTML = `
                <div class="message assistant">
                    <strong>🤖 助手:</strong> 您好！我是中医RAG系统，请输入您的问题。
                </div>
            `;
            currentSessionId = null;
            updateStatus('聊天记录已清空', 'success');
        }
        
        // 页面加载完成后自动测试API
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，正在测试API...', 'info');
            setTimeout(testAPI, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            updateStatus(`JavaScript错误: ${e.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            updateStatus(`Promise错误: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
