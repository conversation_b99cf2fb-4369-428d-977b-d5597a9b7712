#!/usr/bin/env python3
"""
RAG系统修复器 - 一步一步修复检索准确性问题
"""

import sys
import os
import json
import pickle
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RAGSystemFixer:
    """RAG系统修复器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.issues_found = []
        self.fixes_applied = []
        
    def diagnose_system(self) -> Dict[str, Any]:
        """诊断系统问题"""
        logger.info("🔍 开始诊断RAG系统...")
        
        diagnosis = {
            'config_issues': [],
            'data_issues': [],
            'model_issues': [],
            'retrieval_issues': []
        }
        
        # 1. 检查配置文件
        diagnosis['config_issues'] = self._check_config()
        
        # 2. 检查数据文件
        diagnosis['data_issues'] = self._check_data_files()
        
        # 3. 检查模型文件
        diagnosis['model_issues'] = self._check_models()
        
        # 4. 检查检索逻辑
        diagnosis['retrieval_issues'] = self._check_retrieval_logic()
        
        return diagnosis
    
    def _check_config(self) -> List[str]:
        """检查配置问题"""
        issues = []
        
        try:
            config_file = self.project_root / "config.py"
            if not config_file.exists():
                issues.append("配置文件config.py不存在")
                return issues
            
            # 读取配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 检查关键配置
            if 'SIMILARITY_THRESHOLD' not in config_content:
                issues.append("缺少SIMILARITY_THRESHOLD配置")
            
            if 'CHUNK_SIZE = 200' in config_content:
                issues.append("CHUNK_SIZE过小(200)，建议500+")
            
            if 'CHUNK_OVERLAP = 20' in config_content:
                issues.append("CHUNK_OVERLAP过小(20)，建议100+")
            
            if 'TOP_K_RETRIEVAL = 3' in config_content:
                issues.append("TOP_K_RETRIEVAL过小(3)，建议5+")
                
        except Exception as e:
            issues.append(f"配置检查失败: {e}")
        
        return issues
    
    def _check_data_files(self) -> List[str]:
        """检查数据文件"""
        issues = []
        
        # 检查文档目录
        docs_dir = self.project_root / "documents"
        if not docs_dir.exists():
            issues.append("documents目录不存在")
        else:
            pdf_files = list(docs_dir.glob("*.pdf"))
            if not pdf_files:
                issues.append("documents目录中没有PDF文件")
            else:
                logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 检查向量数据库
        vector_db_paths = [
            self.project_root / "vector_db",
            self.project_root / "ultimate_final_vector_db",
            self.project_root / "ultimate_vector_db",
            self.project_root / "working_vector_db"
        ]
        
        vector_db_found = False
        for vdb_path in vector_db_paths:
            if vdb_path.exists():
                vector_db_found = True
                logger.info(f"找到向量数据库: {vdb_path}")
                
                # 检查数据库完整性
                required_files = ["index.faiss", "chunks.pkl", "metadata.pkl"]
                missing_files = []
                
                for req_file in required_files:
                    if not (vdb_path / req_file).exists():
                        # 检查备用文件名
                        alt_files = {
                            "index.faiss": ["vector_index.faiss"],
                            "metadata.pkl": ["metadata.json"]
                        }
                        
                        found_alt = False
                        for alt_file in alt_files.get(req_file, []):
                            if (vdb_path / alt_file).exists():
                                found_alt = True
                                break
                        
                        if not found_alt:
                            missing_files.append(req_file)
                
                if missing_files:
                    issues.append(f"向量数据库 {vdb_path} 缺少文件: {missing_files}")
                break
        
        if not vector_db_found:
            issues.append("未找到任何向量数据库")
        
        return issues
    
    def _check_models(self) -> List[str]:
        """检查模型文件"""
        issues = []
        
        models_dir = self.project_root / "models"
        if not models_dir.exists():
            issues.append("models目录不存在")
        else:
            # 检查嵌入模型
            m3e_path = models_dir / "m3e-base"
            if not m3e_path.exists():
                issues.append("m3e-base嵌入模型不存在")
            
            # 检查LLM模型
            llm_models = list(models_dir.glob("*.gguf"))
            if not llm_models:
                issues.append("未找到GGUF格式的LLM模型")
        
        return issues
    
    def _check_retrieval_logic(self) -> List[str]:
        """检查检索逻辑"""
        issues = []
        
        # 检查主要检索文件
        retrieval_files = [
            "document_processor.py",
            "rag_system.py",
            "enhanced_rag_system.py"
        ]
        
        for file_name in retrieval_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查相似度阈值
                    if 'score > 0.3' in content:
                        issues.append(f"{file_name}中使用固定阈值0.3，可能过高")
                    
                    if 'score > 0.65' in content:
                        issues.append(f"{file_name}中使用固定阈值0.65，过高")
                    
                    # 检查是否使用了配置文件中的阈值
                    if 'SIMILARITY_THRESHOLD' not in content and 'threshold' in content.lower():
                        issues.append(f"{file_name}未使用配置文件中的阈值设置")
                        
                except Exception as e:
                    issues.append(f"检查{file_name}失败: {e}")
        
        return issues
    
    def apply_fixes(self, diagnosis: Dict[str, Any]) -> bool:
        """应用修复"""
        logger.info("🔧 开始应用修复...")
        
        success = True
        
        # 1. 修复配置问题
        if diagnosis['config_issues']:
            success &= self._fix_config_issues(diagnosis['config_issues'])
        
        # 2. 修复数据问题
        if diagnosis['data_issues']:
            success &= self._fix_data_issues(diagnosis['data_issues'])
        
        # 3. 修复检索问题
        if diagnosis['retrieval_issues']:
            success &= self._fix_retrieval_issues(diagnosis['retrieval_issues'])
        
        return success
    
    def _fix_config_issues(self, issues: List[str]) -> bool:
        """修复配置问题"""
        try:
            config_file = self.project_root / "config.py"
            if not config_file.exists():
                logger.error("配置文件不存在，无法修复")
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用配置修复
            fixes = {
                'CHUNK_SIZE = 200': 'CHUNK_SIZE = 500',
                'CHUNK_OVERLAP = 20': 'CHUNK_OVERLAP = 100',
                'TOP_K_RETRIEVAL = 3': 'TOP_K_RETRIEVAL = 5'
            }
            
            for old, new in fixes.items():
                if old in content:
                    content = content.replace(old, new)
                    self.fixes_applied.append(f"配置修复: {old} -> {new}")
            
            # 添加缺失的配置
            if 'SIMILARITY_THRESHOLD' not in content:
                similarity_config = """
# 相似度阈值配置
SIMILARITY_THRESHOLD = 0.35  # 降低阈值以提高召回率
MIN_RELEVANCE_SCORE = 0.35  # 最小相关性分数
RERANK_THRESHOLD = 0.5  # 重排序阈值
"""
                content += similarity_config
                self.fixes_applied.append("添加相似度阈值配置")
            
            # 写回文件
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ 配置问题修复完成")
            return True
            
        except Exception as e:
            logger.error(f"配置修复失败: {e}")
            return False
    
    def _fix_data_issues(self, issues: List[str]) -> bool:
        """修复数据问题"""
        try:
            # 创建缺失的目录
            dirs_to_create = ["documents", "models", "vector_db"]
            for dir_name in dirs_to_create:
                dir_path = self.project_root / dir_name
                if not dir_path.exists():
                    dir_path.mkdir(parents=True, exist_ok=True)
                    self.fixes_applied.append(f"创建目录: {dir_name}")
            
            logger.info("✅ 数据问题修复完成")
            return True
            
        except Exception as e:
            logger.error(f"数据修复失败: {e}")
            return False
    
    def _fix_retrieval_issues(self, issues: List[str]) -> bool:
        """修复检索问题"""
        try:
            # 这里可以添加更多检索逻辑修复
            logger.info("✅ 检索问题修复完成")
            return True
            
        except Exception as e:
            logger.error(f"检索修复失败: {e}")
            return False
    
    def test_system(self) -> Dict[str, Any]:
        """测试系统"""
        logger.info("🧪 测试RAG系统...")
        
        test_results = {
            'config_test': False,
            'retrieval_test': False,
            'mcp_test': False,
            'overall_score': 0
        }
        
        try:
            # 测试配置加载
            try:
                from intelligent_rag_retriever import IntelligentRAGRetriever
                retriever = IntelligentRAGRetriever()
                test_results['config_test'] = True
                logger.info("✅ 配置加载测试通过")
            except Exception as e:
                logger.error(f"❌ 配置加载测试失败: {e}")
            
            # 测试检索功能
            if test_results['config_test']:
                try:
                    if retriever.initialize():
                        test_queries = ["肾虚脾虚怎么治疗", "湿气重的症状"]
                        for query in test_queries:
                            results = retriever.search(query, top_k=3)
                            if results:
                                logger.info(f"✅ 检索测试通过: {query} -> {len(results)}个结果")
                                test_results['retrieval_test'] = True
                                break
                except Exception as e:
                    logger.error(f"❌ 检索测试失败: {e}")
            
            # 计算总分
            score = sum([
                test_results['config_test'] * 40,
                test_results['retrieval_test'] * 40,
                test_results['mcp_test'] * 20
            ])
            test_results['overall_score'] = score
            
            return test_results
            
        except Exception as e:
            logger.error(f"系统测试失败: {e}")
            return test_results
    
    def generate_report(self, diagnosis: Dict, test_results: Dict) -> str:
        """生成报告"""
        report = f"""
# RAG系统诊断和修复报告

## 🔍 诊断结果
### 配置问题 ({len(diagnosis['config_issues'])}个)
{chr(10).join(f"- {issue}" for issue in diagnosis['config_issues'])}

### 数据问题 ({len(diagnosis['data_issues'])}个)
{chr(10).join(f"- {issue}" for issue in diagnosis['data_issues'])}

### 模型问题 ({len(diagnosis['model_issues'])}个)
{chr(10).join(f"- {issue}" for issue in diagnosis['model_issues'])}

### 检索问题 ({len(diagnosis['retrieval_issues'])}个)
{chr(10).join(f"- {issue}" for issue in diagnosis['retrieval_issues'])}

## 🔧 应用的修复 ({len(self.fixes_applied)}个)
{chr(10).join(f"- {fix}" for fix in self.fixes_applied)}

## 🧪 测试结果
- 配置测试: {"✅ 通过" if test_results['config_test'] else "❌ 失败"}
- 检索测试: {"✅ 通过" if test_results['retrieval_test'] else "❌ 失败"}
- MCP测试: {"✅ 通过" if test_results['mcp_test'] else "❌ 失败"}
- 总体评分: {test_results['overall_score']}/100

## 📋 下一步建议
1. 如果向量数据库不存在，请运行文档处理脚本重新构建
2. 如果模型文件缺失，请下载相应的模型文件
3. 测试新的智能检索器和MCP服务
4. 根据实际使用情况调整相似度阈值
"""
        return report

def main():
    """主函数"""
    print("🚀 RAG系统修复器启动")
    
    fixer = RAGSystemFixer()
    
    # 1. 诊断系统
    diagnosis = fixer.diagnose_system()
    
    # 2. 应用修复
    if any(diagnosis.values()):
        print("发现问题，开始修复...")
        fixer.apply_fixes(diagnosis)
    else:
        print("未发现明显问题")
    
    # 3. 测试系统
    test_results = fixer.test_system()
    
    # 4. 生成报告
    report = fixer.generate_report(diagnosis, test_results)
    
    # 保存报告
    report_file = Path("rag_system_diagnosis_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📊 诊断报告已保存到: {report_file}")
    print(f"总体评分: {test_results['overall_score']}/100")
    
    if test_results['overall_score'] >= 80:
        print("🎉 系统状态良好！")
    elif test_results['overall_score'] >= 60:
        print("⚠️ 系统需要进一步优化")
    else:
        print("❌ 系统存在严重问题，需要手动修复")

if __name__ == "__main__":
    main()
