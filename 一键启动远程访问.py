#!/usr/bin/env python3
"""
一键启动远程访问 - 自动启动系统并创建Ngrok隧道
"""

import subprocess
import sys
import time
import threading
import webbrowser
import os
from pathlib import Path

def start_intelligent_mcp():
    """启动智能MCP服务"""
    print("🧠 启动智能MCP服务...")
    try:
        process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        print(f"✅ 智能MCP服务已启动 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"⚠️ 智能MCP服务启动失败: {e}")
        return None

def start_streamlit_system():
    """启动Streamlit系统"""
    print("🌐 启动Streamlit系统...")
    
    main_file = "ultimate_final_tcm_system.py"
    if not os.path.exists(main_file):
        print(f"❌ 未找到主文件: {main_file}")
        return None
    
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", 
            main_file,
            "--server.headless", "true",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print(f"✅ Streamlit系统已启动 (PID: {process.pid})")
        return process
    except Exception as e:
        print(f"❌ Streamlit启动失败: {e}")
        return None

def wait_for_service(port=8501, timeout=30):
    """等待服务启动"""
    import requests
    
    print(f"⏳ 等待端口{port}服务启动...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f'http://localhost:{port}', timeout=2)
            if response.status_code == 200:
                print(f"✅ 服务已在端口{port}启动")
                return True
        except:
            pass
        time.sleep(1)
    
    print(f"⚠️ 等待服务启动超时")
    return False

def start_ngrok_tunnel():
    """启动Ngrok隧道"""
    print("🚀 启动Ngrok隧道...")
    
    try:
        # 使用智能Ngrok启动器
        process = subprocess.Popen([
            sys.executable, "智能Ngrok启动器.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 读取输出
        output_lines = []
        while True:
            line = process.stdout.readline()
            if not line:
                break
            print(line.strip())
            output_lines.append(line.strip())
            
            # 检查是否成功获取URL
            if "公网地址:" in line:
                url = line.split("公网地址:")[-1].strip()
                return url, process
        
        return None, process
        
    except Exception as e:
        print(f"❌ Ngrok启动失败: {e}")
        return None, None

def open_browser_and_share_info():
    """打开浏览器和分享信息"""
    try:
        # 打开本地浏览器
        print("🌐 打开本地浏览器...")
        webbrowser.open('http://localhost:8501')
        
        # 打开分享信息文件
        share_file = "ngrok_分享信息.txt"
        if os.path.exists(share_file):
            print(f"📄 打开分享信息: {share_file}")
            if os.name == 'nt':  # Windows
                os.startfile(share_file)
            else:  # Linux/Mac
                subprocess.run(['open', share_file])
    except Exception as e:
        print(f"⚠️ 打开浏览器失败: {e}")

def main():
    """主函数"""
    print("🏥 家庭私人医生小帮手 - 一键启动远程访问")
    print("🎯 自动启动系统并创建Ngrok隧道")
    print("=" * 60)
    
    processes = []
    
    try:
        # 1. 启动智能MCP服务
        mcp_process = start_intelligent_mcp()
        if mcp_process:
            processes.append(mcp_process)
        
        # 等待MCP服务启动
        time.sleep(3)
        
        # 2. 启动Streamlit系统
        streamlit_process = start_streamlit_system()
        if not streamlit_process:
            print("❌ Streamlit启动失败，无法继续")
            return False
        processes.append(streamlit_process)
        
        # 3. 等待Streamlit服务启动
        if not wait_for_service(8501, 30):
            print("❌ Streamlit服务启动超时")
            return False
        
        # 4. 启动Ngrok隧道
        print("\n" + "=" * 40)
        ngrok_url, ngrok_process = start_ngrok_tunnel()
        
        if ngrok_url:
            print(f"\n🎉 系统启动成功!")
            print(f"🌐 远程访问地址: {ngrok_url}")
            print(f"🔐 访问密码: MVP168918")
            print(f"📱 本地访问: http://localhost:8501")
            
            # 5. 打开浏览器和分享信息
            time.sleep(2)
            open_browser_and_share_info()
            
            print("\n" + "=" * 60)
            print("💡 系统运行中...")
            print("📋 朋友可以通过以下方式访问:")
            print(f"   地址: {ngrok_url}")
            print(f"   密码: MVP168918")
            print("\n⚠️ 注意: 每次重启系统，地址会自动更新")
            print("🛑 按 Ctrl+C 停止所有服务")
            
            # 保持运行
            try:
                while True:
                    time.sleep(10)
                    # 检查进程状态
                    if streamlit_process.poll() is not None:
                        print("⚠️ Streamlit进程意外退出")
                        break
            except KeyboardInterrupt:
                print("\n🛑 用户中断，正在停止服务...")
            
        else:
            print("❌ Ngrok隧道创建失败")
            print("💡 您仍可以通过本地地址访问: http://localhost:8501")
            
            # 打开本地浏览器
            time.sleep(2)
            webbrowser.open('http://localhost:8501')
            
            print("\n🛑 按 Ctrl+C 停止服务")
            try:
                while True:
                    time.sleep(10)
                    if streamlit_process.poll() is not None:
                        break
            except KeyboardInterrupt:
                print("\n🛑 用户中断")
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return False
    
    finally:
        # 清理进程
        print("🧹 清理进程...")
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        # 清理ngrok
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/f', '/im', 'ngrok.exe'], 
                             capture_output=True, check=False)
            else:  # Linux/Mac
                subprocess.run(['pkill', 'ngrok'], 
                             capture_output=True, check=False)
        except:
            pass
        
        print("✅ 清理完成")
    
    return True

if __name__ == "__main__":
    print("📋 使用说明:")
    print("1. 此脚本会自动启动所有必要服务")
    print("2. 自动创建Ngrok远程访问隧道")
    print("3. 自动打开浏览器和分享信息")
    print("4. 每次启动都会生成新的访问地址")
    print("5. 按 Ctrl+C 停止所有服务")
    print()
    
    success = main()
    
    if not success:
        print("\n❌ 启动失败")
        print("💡 故障排除:")
        print("1. 检查Python环境和依赖")
        print("2. 检查端口8501是否被占用")
        print("3. 检查Ngrok是否正确安装")
        print("4. 尝试手动启动各个组件")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
