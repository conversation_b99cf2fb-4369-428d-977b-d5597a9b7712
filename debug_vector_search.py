#!/usr/bin/env python3
"""
调试向量检索 - 找出为什么没有结果
"""

import numpy as np
import pickle
import faiss
from pathlib import Path
from sentence_transformers import SentenceTransformer

def debug_vector_search():
    """调试向量搜索"""
    print("🔍 调试向量检索...")
    
    # 1. 检查数据库文件
    db_path = Path('./ultimate_final_vector_db')
    metadata_file = db_path / 'metadata.pkl'
    index_file = db_path / 'index.faiss'
    
    if not metadata_file.exists() or not index_file.exists():
        print("❌ 数据库文件不存在")
        return
    
    # 2. 加载数据
    try:
        with open(metadata_file, 'rb') as f:
            metadata = pickle.load(f)
        
        index = faiss.read_index(str(index_file))
        
        print(f"📊 数据库信息:")
        print(f"   元数据记录: {len(metadata)}")
        print(f"   向量数量: {index.ntotal}")
        print(f"   向量维度: {index.d}")
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    # 3. 加载模型
    try:
        model = SentenceTransformer('./models/m3e-base')
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 4. 测试查询
    test_queries = [
        "肾虚脾虚怎么治疗",
        "黄帝内经",
        "养生",
        "阴阳",
        "治疗"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: {query}")
        
        # 生成查询向量
        query_embedding = model.encode([query])
        query_embedding_normalized = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
        
        # 搜索前20个最相似的
        distances, indices = index.search(query_embedding_normalized.astype('float32'), 20)
        
        print(f"   搜索到 {len(distances[0])} 个结果")
        
        # 分析每个结果
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx < len(metadata) and idx >= 0:
                # 计算余弦相似度
                cosine_similarity = 1 - (distance ** 2 / 2)
                cosine_similarity = max(0.0, min(1.0, cosine_similarity))
                
                source = metadata[idx].get('source', 'unknown')
                content = metadata[idx].get('content', '')
                
                print(f"   {i+1}. 相似度: {cosine_similarity:.4f} | 距离: {distance:.4f}")
                print(f"      来源: {source}")
                print(f"      内容: {content[:80]}...")
                
                # 检查阈值
                if cosine_similarity >= 0.2:
                    print(f"      ✅ 通过阈值 0.2")
                else:
                    print(f"      ❌ 未通过阈值 0.2")
                
                if cosine_similarity >= 0.1:
                    print(f"      ✅ 通过阈值 0.1")
                else:
                    print(f"      ❌ 未通过阈值 0.1")
                
                print()
        
        # 统计通过不同阈值的数量
        passed_02 = sum(1 for distance in distances[0] if (1 - (distance ** 2 / 2)) >= 0.2)
        passed_01 = sum(1 for distance in distances[0] if (1 - (distance ** 2 / 2)) >= 0.1)
        passed_005 = sum(1 for distance in distances[0] if (1 - (distance ** 2 / 2)) >= 0.05)
        
        print(f"   📊 阈值统计:")
        print(f"      阈值 0.2: {passed_02} 个结果")
        print(f"      阈值 0.1: {passed_01} 个结果")
        print(f"      阈值 0.05: {passed_005} 个结果")

def test_content_matching():
    """测试内容匹配"""
    print("\n🧪 测试内容匹配...")
    
    # 加载元数据
    db_path = Path('./ultimate_final_vector_db')
    metadata_file = db_path / 'metadata.pkl'
    
    try:
        with open(metadata_file, 'rb') as f:
            metadata = pickle.load(f)
    except Exception as e:
        print(f"❌ 加载元数据失败: {e}")
        return
    
    # 搜索包含特定关键词的内容
    keywords = ["肾虚", "脾虚", "治疗", "方剂", "黄帝内经"]
    
    for keyword in keywords:
        print(f"\n🔍 搜索关键词: {keyword}")
        matches = []
        
        for i, meta in enumerate(metadata):
            content = meta.get('content', '')
            source = meta.get('source', 'unknown')
            
            if keyword in content:
                matches.append({
                    'index': i,
                    'source': source,
                    'content': content[:100] + '...'
                })
        
        print(f"   找到 {len(matches)} 个匹配")
        for match in matches[:3]:  # 显示前3个
            print(f"   - 索引 {match['index']}: {match['source']}")
            print(f"     内容: {match['content']}")

def fix_vector_search():
    """修复向量搜索 - 临时降低阈值"""
    print("\n🔧 修复向量搜索...")
    
    # 直接修改配置文件中的阈值
    config_updates = {
        'MIN_RELEVANCE_SCORE': 0.01,  # 极低阈值
        'EXHAUSTIVE_SEARCH': True,
        'TOP_K': 50
    }
    
    print("修改配置:")
    for key, value in config_updates.items():
        print(f"   {key}: {value}")
    
    # 这里可以直接修改配置或者生成修复代码
    fix_code = f"""
# 在 ultimate_final_tcm_system.py 中修改 CONFIG
CONFIG.update({config_updates})
"""
    
    print(f"\n修复代码:\n{fix_code}")

if __name__ == "__main__":
    debug_vector_search()
    test_content_matching()
    fix_vector_search()
