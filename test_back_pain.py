#!/usr/bin/env python3
"""
测试腰疼问题的智能回答
"""
import asyncio
import sys
import time
from simple_ultimate_tcm import SimpleResponseGenerator, SimpleDocumentProcessor, SimpleOnlineCrawler

async def test_back_pain_query():
    """测试腰疼怎么办的查询"""
    print("🧪 测试腰疼问题的智能回答")
    print("=" * 60)

    # 初始化系统组件
    print("🚀 初始化中医RAG系统...")
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    tcm_system = SimpleResponseGenerator(doc_processor, crawler)
    
    # 测试查询
    query = "腰疼怎么办"
    print(f"📝 用户问题: {query}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 生成回答
        response, sources, processing_time = await tcm_system.generate_response(query)
        
        print(f"✅ 处理成功 (耗时: {processing_time:.2f}秒)")
        print(f"📚 来源数量: {len(sources)}条")
        
        # 分析来源类型
        online_sources = [s for s in sources if s.get('type') == 'online']
        local_sources = [s for s in sources if s.get('type') == 'pdf']
        
        print(f"🌐 在线资源: {len(online_sources)}条")
        print(f"📁 本地资源: {len(local_sources)}条")
        print()
        
        print("🤖 智能回答:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
        # 分析回答质量
        print("\n📊 回答质量分析:")
        print(f"• 回答长度: {len(response)} 字符")
        print(f"• 是否包含具体建议: {'是' if '建议' in response or '治疗' in response else '否'}")
        print(f"• 是否包含中医理论: {'是' if '中医' in response or '辨证' in response else '否'}")
        print(f"• 是否包含注意事项: {'是' if '注意' in response or '提醒' in response else '否'}")
        
        # 检查是否针对腰疼问题
        back_pain_keywords = ['腰', '肾', '寒湿', '瘀血', '腰痛', '腰部']
        relevant_keywords = [kw for kw in back_pain_keywords if kw in response]
        print(f"• 腰疼相关关键词: {relevant_keywords}")
        
        if len(relevant_keywords) >= 2:
            print("✅ 回答针对性强，包含腰疼相关内容")
        else:
            print("❌ 回答针对性不足，缺乏腰疼专门内容")
        
        # 显示来源信息
        if sources:
            print("\n📚 参考来源详情:")
            for i, source in enumerate(sources[:5], 1):
                source_type = "🌐 在线" if source.get('type') == 'online' else "📁 本地"
                score = source.get('score', 0)
                print(f"{i}. {source_type} {source.get('source', '未知来源')} (相关度: {score:.2f})")
                content_preview = source.get('content', '')[:100]
                print(f"   内容预览: {content_preview}...")
                print()
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_back_pain_query())
