
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智能助手 - 测试版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2E8B57;
        }
        .chat-area {
            border: 1px solid #ddd;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        .user-message {
            background: #2E8B57;
            color: white;
            margin-left: 20%;
        }
        .assistant-message {
            background: #e3f2fd;
            margin-right: 20%;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        .input-area input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        .input-area button {
            padding: 12px 20px;
            background: #2E8B57;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .input-area button:hover {
            background: #228B22;
        }
        .quick-buttons {
            margin-bottom: 20px;
            text-align: center;
        }
        .quick-btn {
            margin: 5px;
            padding: 8px 15px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            display: inline-block;
        }
        .quick-btn:hover {
            background: #e0e0e0;
        }
        .upload-area {
            margin-bottom: 20px;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 中医智能助手</h1>
            <p>现代化聊天界面 - 测试版</p>
        </div>
        
        <div class="upload-area">
            <h3>📁 文档上传</h3>
            <input type="file" id="fileInput" accept=".pdf,.txt,.doc,.docx" multiple>
            <button onclick="uploadFiles()">上传文档</button>
        </div>
        
        <div class="quick-buttons">
            <div class="quick-btn" onclick="sendQuickMessage('湿气重有什么表现？')">🌿 湿气问题</div>
            <div class="quick-btn" onclick="sendQuickMessage('气血不足如何调理？')">🫖 气血调理</div>
            <div class="quick-btn" onclick="sendQuickMessage('什么是阴阳平衡？')">☯️ 阴阳平衡</div>
            <div class="quick-btn" onclick="sendQuickMessage('四季养生要点')">🌸 四季养生</div>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message assistant-message">
                <strong>🤖 助手:</strong> 您好！我是中医智能助手。您可以：<br>
                • 点击上方快捷按钮查询常见问题<br>
                • 上传PDF文档扩充知识库<br>
                • 直接输入您的问题<br><br>
                请问有什么可以帮助您的吗？
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 显示加载状态
            addMessage('assistant', '正在思考中...', 'loading');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: 'test_session'
                    })
                });
                
                const data = await response.json();
                
                // 移除加载消息
                removeLoadingMessage();
                
                // 显示助手回复
                addMessage('assistant', data.response);
                
                // 显示来源信息
                if (data.sources && data.sources.length > 0) {
                    let sourcesText = '<br><small><strong>📚 参考来源:</strong><br>';
                    data.sources.forEach((source, index) => {
                        sourcesText += `${index + 1}. ${source.source}<br>`;
                    });
                    sourcesText += '</small>';
                    addMessage('assistant', sourcesText);
                }
                
            } catch (error) {
                removeLoadingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
            }
        }
        
        function addMessage(type, content, className = '') {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message ${className}`;
            
            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            messageDiv.innerHTML = `<strong>${prefix}</strong> ${content}`;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        function removeLoadingMessage() {
            const loadingMessage = document.querySelector('.loading');
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }
        
        async function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }
            
            try {
                addMessage('assistant', '正在上传和处理文档...', 'loading');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                removeLoadingMessage();
                
                let resultText = `📁 文档上传完成！<br>`;
                data.results.forEach(result => {
                    if (result.status === 'success') {
                        resultText += `✅ ${result.filename} (${result.chunks} 个文本块)<br>`;
                    } else {
                        resultText += `❌ ${result.filename}: ${result.error}<br>`;
                    }
                });
                
                addMessage('assistant', resultText);
                fileInput.value = '';
                
            } catch (error) {
                removeLoadingMessage();
                addMessage('assistant', '文档上传失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
