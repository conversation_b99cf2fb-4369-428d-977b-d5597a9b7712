#!/usr/bin/env python3
"""
增强MCP服务 - 解决固定结果问题
基于真实查询内容进行智能匹配和检索
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import asyncio
import logging
import re
import json
from pathlib import Path
import jieba
from intelligent_rag_retriever import IntelligentRAGRetriever

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI应用
app = FastAPI(
    title="Enhanced TCM MCP Service",
    description="增强的中医智能检索MCP服务",
    version="2.0.0"
)

# 请求模型
class MCPRequest(BaseModel):
    method: str
    params: Dict[str, Any]
    id: Optional[str] = None

class MCPResponse(BaseModel):
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    id: Optional[str] = None

class SearchResult(BaseModel):
    title: str
    content: str
    source: str
    score: float
    category: str
    metadata: Dict[str, Any] = {}

class EnhancedMCPService:
    """增强MCP服务"""
    
    def __init__(self):
        self.rag_retriever = IntelligentRAGRetriever()
        self.initialized = False
        
        # 中医知识库
        self.tcm_knowledge = self._build_tcm_knowledge()
        
        # 查询意图分析器
        self.intent_patterns = self._build_intent_patterns()
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            logger.info("初始化增强MCP服务...")
            
            # 初始化RAG检索器
            if self.rag_retriever.initialize():
                logger.info("RAG检索器初始化成功")
            else:
                logger.warning("RAG检索器初始化失败，使用知识库模式")
            
            self.initialized = True
            logger.info("增强MCP服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def _build_tcm_knowledge(self) -> Dict[str, List[Dict]]:
        """构建中医知识库"""
        return {
            '肾虚': [
                {
                    'title': '肾阳虚证治',
                    'content': '肾阳虚者，症见腰膝酸软，畏寒肢冷，阳痿早泄，小便清长，夜尿频多。治宜温补肾阳，方用右归丸加减。',
                    'source': '《金匮要略》',
                    'category': '病证',
                    'formulas': ['右归丸', '肾气丸'],
                    'symptoms': ['腰膝酸软', '畏寒肢冷', '阳痿', '夜尿频']
                },
                {
                    'title': '肾阴虚证治',
                    'content': '肾阴虚者，症见腰膝酸软，头晕耳鸣，失眠多梦，五心烦热，盗汗，口干咽燥。治宜滋补肾阴，方用六味地黄丸加减。',
                    'source': '《医宗金鉴》',
                    'category': '病证',
                    'formulas': ['六味地黄丸', '左归丸'],
                    'symptoms': ['腰膝酸软', '头晕耳鸣', '失眠', '盗汗']
                }
            ],
            '脾虚': [
                {
                    'title': '脾气虚证治',
                    'content': '脾气虚者，症见食少腹胀，食后尤甚，大便溏薄，倦怠乏力，少气懒言，面色萎黄。治宜健脾益气，方用四君子汤加减。',
                    'source': '《脾胃论》',
                    'category': '病证',
                    'formulas': ['四君子汤', '补中益气汤'],
                    'symptoms': ['食少腹胀', '大便溏薄', '倦怠乏力', '面色萎黄']
                },
                {
                    'title': '脾阳虚证治',
                    'content': '脾阳虚者，症见脘腹冷痛，喜温喜按，食少便溏，四肢不温，或肢体浮肿。治宜温中健脾，方用理中汤加减。',
                    'source': '《伤寒论》',
                    'category': '病证',
                    'formulas': ['理中汤', '附子理中汤'],
                    'symptoms': ['脘腹冷痛', '食少便溏', '四肢不温', '肢体浮肿']
                }
            ],
            '肾虚脾虚': [
                {
                    'title': '肾脾双补法',
                    'content': '肾虚脾虚并见者，症见腰膝酸软，食少便溏，倦怠乏力，畏寒肢冷，夜尿频多。治宜补肾健脾，方用附子理中汤合右归丸加减。',
                    'source': '《医宗金鉴》',
                    'category': '治法',
                    'formulas': ['附子理中汤合右归丸', '金匮肾气丸'],
                    'symptoms': ['腰膝酸软', '食少便溏', '倦怠乏力', '畏寒肢冷'],
                    'principle': '脾肾相关，肾为先天之本，脾为后天之本，两脏相互资生'
                }
            ],
            '湿气': [
                {
                    'title': '湿邪致病特点',
                    'content': '湿为阴邪，其性重浊粘腻，易阻遏气机，损伤阳气。湿邪致病，症见头身困重，胸脘痞闷，食欲不振，大便粘腻。',
                    'source': '《黄帝内经》',
                    'category': '病因',
                    'symptoms': ['头身困重', '胸脘痞闷', '食欲不振', '大便粘腻']
                }
            ]
        }
    
    def _build_intent_patterns(self) -> Dict[str, List[str]]:
        """构建查询意图模式"""
        return {
            'treatment': ['怎么治疗', '如何治疗', '治疗方法', '怎么办', '如何调理'],
            'symptoms': ['症状', '表现', '特点', '什么样'],
            'formula': ['方剂', '药方', '处方', '汤方'],
            'cause': ['原因', '病因', '为什么', '怎么回事'],
            'prevention': ['预防', '保养', '养生', '注意事项']
        }
    
    def analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """分析查询意图"""
        intent = {
            'type': 'general',
            'keywords': [],
            'entities': [],
            'intent_score': {}
        }
        
        query_lower = query.lower()
        
        # 提取关键词
        words = list(jieba.cut(query))
        intent['keywords'] = [w for w in words if len(w) >= 2]
        
        # 识别中医实体
        for entity_type, entities in self.tcm_knowledge.items():
            if any(keyword in query_lower for keyword in entity_type.split()):
                intent['entities'].append(entity_type)
        
        # 识别意图类型
        for intent_type, patterns in self.intent_patterns.items():
            score = sum(1 for pattern in patterns if pattern in query_lower)
            if score > 0:
                intent['intent_score'][intent_type] = score
        
        # 确定主要意图
        if intent['intent_score']:
            intent['type'] = max(intent['intent_score'], key=intent['intent_score'].get)
        
        return intent
    
    async def intelligent_search(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """智能搜索"""
        try:
            logger.info(f"智能搜索: {query}")
            
            # 分析查询意图
            intent = self.analyze_query_intent(query)
            logger.info(f"查询意图: {intent}")
            
            results = []
            
            # 1. 使用RAG检索器搜索
            if self.rag_retriever.initialized:
                rag_results = self.rag_retriever.search(query, max_results)
                for result in rag_results:
                    search_result = SearchResult(
                        title=f"RAG检索结果",
                        content=result['content'][:300] + "..." if len(result['content']) > 300 else result['content'],
                        source=result.get('metadata', {}).get('source', 'RAG数据库'),
                        score=result['combined_score'],
                        category='rag_retrieval',
                        metadata=result.get('metadata', {})
                    )
                    results.append(search_result)
            
            # 2. 基于意图的知识库搜索
            kb_results = await self._search_knowledge_base(query, intent, max_results // 2)
            results.extend(kb_results)
            
            # 3. 去重和排序
            unique_results = self._deduplicate_results(results)
            sorted_results = sorted(unique_results, key=lambda x: x.score, reverse=True)
            
            return sorted_results[:max_results]
            
        except Exception as e:
            logger.error(f"智能搜索失败: {e}")
            return []
    
    async def _search_knowledge_base(self, query: str, intent: Dict, max_results: int) -> List[SearchResult]:
        """搜索知识库"""
        results = []
        query_lower = query.lower()
        
        try:
            # 遍历知识库
            for category, items in self.tcm_knowledge.items():
                category_score = 0
                
                # 计算类别匹配分数
                if category in query_lower:
                    category_score = 2.0
                elif any(keyword in category for keyword in intent['keywords']):
                    category_score = 1.0
                
                if category_score > 0:
                    for item in items:
                        # 计算内容匹配分数
                        content_score = 0
                        
                        # 标题匹配
                        if any(keyword in item['title'] for keyword in intent['keywords']):
                            content_score += 1.5
                        
                        # 内容匹配
                        if any(keyword in item['content'] for keyword in intent['keywords']):
                            content_score += 1.0
                        
                        # 症状匹配
                        if 'symptoms' in item:
                            symptom_matches = sum(1 for symptom in item['symptoms'] 
                                                if any(keyword in symptom for keyword in intent['keywords']))
                            content_score += symptom_matches * 0.5
                        
                        total_score = category_score + content_score
                        
                        if total_score > 0:
                            search_result = SearchResult(
                                title=item['title'],
                                content=item['content'],
                                source=item['source'],
                                score=total_score,
                                category=category,
                                metadata={
                                    'formulas': item.get('formulas', []),
                                    'symptoms': item.get('symptoms', []),
                                    'principle': item.get('principle', '')
                                }
                            )
                            results.append(search_result)
            
            return sorted(results, key=lambda x: x.score, reverse=True)[:max_results]
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return []
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重结果"""
        seen_content = set()
        unique_results = []
        
        for result in results:
            content_hash = hash(result.content[:100])  # 使用前100字符作为去重依据
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)
        
        return unique_results

# 全局服务实例
mcp_service = EnhancedMCPService()

@app.on_event("startup")
async def startup_event():
    """启动事件"""
    await mcp_service.initialize()

@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    """MCP协议主端点"""
    try:
        if request.method == "search_knowledge":
            query = request.params.get("query", "")
            max_results = request.params.get("max_results", 5)
            
            if not query:
                return MCPResponse(
                    error={"code": -32602, "message": "查询参数不能为空"},
                    id=request.id
                )
            
            # 执行智能搜索
            results = await mcp_service.intelligent_search(query, max_results)
            
            # 转换为字典格式
            result_dicts = [result.dict() for result in results]
            
            return MCPResponse(
                result={
                    "results": result_dicts,
                    "total": len(result_dicts),
                    "query": query,
                    "search_type": "enhanced_intelligent"
                },
                id=request.id
            )
        
        else:
            return MCPResponse(
                error={"code": -32601, "message": f"方法未找到: {request.method}"},
                id=request.id
            )
            
    except Exception as e:
        logger.error(f"MCP请求处理失败: {e}")
        return MCPResponse(
            error={"code": -32603, "message": f"内部服务器错误: {str(e)}"},
            id=request.id
        )

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "Enhanced MCP Service",
        "initialized": mcp_service.initialized,
        "rag_initialized": mcp_service.rag_retriever.initialized if mcp_service.rag_retriever else False
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8001)
