#!/usr/bin/env python3
"""
ChatGLM3-6B FastAPI服务器
使用本地下载的ChatGLM3-6B模型提供OpenAI兼容的API服务
"""

import asyncio
import logging
import time
import torch
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
import uuid

# FastAPI相关
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# ChatGLM3模型加载
try:
    from transformers import AutoTokenizer, AutoModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("❌ transformers未安装，请运行: pip install transformers")

# 嵌入模型
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False
    print("❌ sentence-transformers未安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API模型定义
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    max_tokens: int = 2048
    stream: bool = False

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class ChatGLM3APIServer:
    """ChatGLM3-6B API服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="ChatGLM3-6B API Server", version="1.0.0")
        self.setup_cors()
        self.setup_routes()
        
        # 模型配置
        self.model_config = {
            'chatglm3_model_path': './models/chatglm3-6b-hub',
            'device': 'cpu',
            'torch_dtype': torch.float16,
            'trust_remote_code': True
        }
        
        # 模型实例
        self.chatglm_model = None
        self.chatglm_tokenizer = None
        self.initialized = False
    
    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            return {
                "status": "healthy" if self.initialized else "initializing",
                "model": "ChatGLM3-6B",
                "model_loaded": self.chatglm_model is not None
            }
        
        @self.app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
        async def chat_completions(request: ChatCompletionRequest):
            if not self.chatglm_model or not self.chatglm_tokenizer:
                raise HTTPException(status_code=503, detail="ChatGLM3-6B模型未加载")
            
            try:
                # 构建对话历史
                history = []
                current_query = ""
                
                for message in request.messages:
                    if message.role == "user":
                        current_query = message.content
                    elif message.role == "assistant":
                        if current_query:
                            history.append([current_query, message.content])
                            current_query = ""
                
                # 获取最后一个用户消息
                if not current_query and request.messages:
                    last_message = request.messages[-1]
                    if last_message.role == "user":
                        current_query = last_message.content
                
                if not current_query:
                    raise HTTPException(status_code=400, detail="未找到用户查询")
                
                # 使用ChatGLM3的chat方法
                response, updated_history = self.chatglm_model.chat(
                    self.chatglm_tokenizer,
                    current_query,
                    history=history,
                    max_length=min(request.max_tokens + len(current_query.split()), 8192),
                    temperature=request.temperature
                )
                
                # 构建响应
                completion_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
                
                return ChatCompletionResponse(
                    id=completion_id,
                    created=int(time.time()),
                    model=request.model,
                    choices=[{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response
                        },
                        "finish_reason": "stop"
                    }],
                    usage={
                        "prompt_tokens": len(current_query.split()),
                        "completion_tokens": len(response.split()),
                        "total_tokens": len(current_query.split()) + len(response.split())
                    }
                )
                
            except Exception as e:
                logger.error(f"聊天完成失败: {e}")
                raise HTTPException(status_code=500, detail=f"生成回答失败: {str(e)}")
    
    async def initialize_models(self):
        """初始化模型"""
        try:
            logger.info("开始初始化ChatGLM3-6B API服务器...")
            
            if not TRANSFORMERS_AVAILABLE:
                logger.error("transformers未安装")
                return False
            
            # 检查模型路径
            model_path = Path(self.model_config['chatglm3_model_path'])
            if not model_path.exists():
                logger.error(f"ChatGLM3-6B模型路径不存在: {model_path}")
                return False
            
            logger.info(f"加载ChatGLM3-6B模型: {model_path}")
            
            # 加载tokenizer
            logger.info("加载tokenizer...")
            self.chatglm_tokenizer = AutoTokenizer.from_pretrained(
                str(model_path),
                trust_remote_code=self.model_config['trust_remote_code']
            )
            logger.info("tokenizer加载成功")
            
            # 加载模型
            logger.info("加载ChatGLM3-6B模型...")
            self.chatglm_model = AutoModel.from_pretrained(
                str(model_path),
                torch_dtype=self.model_config['torch_dtype'],
                device_map=self.model_config['device'],
                trust_remote_code=self.model_config['trust_remote_code'],
                low_cpu_mem_usage=True
            )
            
            self.chatglm_model.eval()
            logger.info("ChatGLM3-6B模型加载成功")
            
            # 测试模型
            logger.info("测试ChatGLM3-6B模型...")
            test_response, test_history = self.chatglm_model.chat(
                self.chatglm_tokenizer,
                "Hello",
                history=[],
                max_length=100
            )
            
            logger.info(f"测试回答: {test_response[:50]}...")
            
            self.initialized = True
            logger.info("ChatGLM3-6B API服务器初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            return False
    
    async def start_server(self, host: str = "127.0.0.1", port: int = 8003):
        """启动服务器"""
        if not await self.initialize_models():
            logger.error("模型初始化失败，无法启动服务器")
            return
        
        logger.info(f"启动ChatGLM3-6B API服务器: http://{host}:{port}")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

# 全局服务器实例
chatglm3_server = ChatGLM3APIServer()

async def main():
    """主函数"""
    await chatglm3_server.start_server()

if __name__ == "__main__":
    asyncio.run(main())
