"""
RAG系统自动设置和启动脚本
"""
import os
import sys
import subprocess
import requests
from pathlib import Path
import config

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def download_sample_documents():
    """下载示例文档"""
    print("正在下载示例中医文档...")
    
    # 创建文档目录
    config.DOCUMENTS_DIR.mkdir(parents=True, exist_ok=True)
    
    # 示例文档URLs（这里使用一些公开的中医文档链接）
    sample_docs = {
        "黄帝内经_素问_节选.pdf": "https://example.com/huangdi_neijing.pdf",  # 实际使用时需要替换为真实链接
        "伤寒论_节选.pdf": "https://example.com/shanghanlun.pdf"  # 实际使用时需要替换为真实链接
    }
    
    # 由于示例链接不可用，我们创建一个提示文件
    readme_content = """
# 文档上传说明

请将您的中医经典PDF文档放置在此目录中，支持的文档包括：

1. 《黄帝内经》
2. 《伤寒论》
3. 其他中医经典文献

## 使用方法：
1. 将PDF文件复制到此目录
2. 启动Web界面
3. 在侧边栏中点击"处理文档"按钮

## 注意事项：
- 支持的文件格式：PDF
- 建议文件大小不超过100MB
- 确保PDF文件可以正常提取文本
"""
    
    readme_path = config.DOCUMENTS_DIR / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 文档目录已创建: {config.DOCUMENTS_DIR}")
    print("📝 请手动将PDF文档放置到documents目录中")
    return True

def create_sample_pdf():
    """创建示例PDF文档"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 创建示例PDF
        sample_pdf_path = config.DOCUMENTS_DIR / "中医基础理论_示例.pdf"
        
        c = canvas.Canvas(str(sample_pdf_path), pagesize=letter)
        
        # 添加示例内容
        sample_text = """
中医基础理论示例文档

第一章 阴阳学说

阴阳学说是中医理论的重要组成部分。阴阳是中国古代哲学的重要概念，
用来说明事物的对立统一关系。

在中医学中，阴阳学说主要体现在以下几个方面：

1. 人体的阴阳
人体是一个有机的整体，各个组织器官都有阴阳属性。
一般来说，上为阳，下为阴；外为阳，内为阴；背为阳，腹为阴。

2. 脏腑的阴阳
五脏属阴，六腑属阳。五脏主藏精气，六腑主传化物质。

3. 气血的阴阳
气属阳，血属阴。气为血之帅，血为气之母。

第二章 五行学说

五行学说是中医理论的另一重要组成部分。
五行即木、火、土、金、水五种基本物质。

五行与脏腑的对应关系：
- 木对应肝胆
- 火对应心小肠
- 土对应脾胃
- 金对应肺大肠
- 水对应肾膀胱

五行相生：木生火，火生土，土生金，金生水，水生木。
五行相克：木克土，土克水，水克火，火克金，金克木。
"""
        
        # 写入文本（简化版本，实际需要处理中文字体）
        y_position = 750
        for line in sample_text.split('\n'):
            if line.strip():
                c.drawString(50, y_position, line.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore'))
                y_position -= 20
                if y_position < 50:
                    c.showPage()
                    y_position = 750
        
        c.save()
        print(f"✅ 创建示例PDF: {sample_pdf_path}")
        return True
        
    except ImportError:
        print("📝 未安装reportlab，跳过PDF创建")
        return True
    except Exception as e:
        print(f"⚠️ 创建示例PDF失败: {e}")
        return True

def check_system_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查可用内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        print(f"可用内存: {available_gb:.1f} GB")
        
        if available_gb < 4:
            print("⚠️ 警告: 可用内存不足4GB，可能影响模型运行")
        else:
            print("✅ 内存充足")
    except ImportError:
        print("📝 未安装psutil，跳过内存检查")
    
    return True

def initialize_system():
    """初始化系统"""
    print("正在初始化RAG系统...")
    
    try:
        from rag_system import rag_system
        success = rag_system.initialize()
        
        if success:
            print("✅ RAG系统初始化成功")
        else:
            print("❌ RAG系统初始化失败")
        
        return success
    except Exception as e:
        print(f"❌ 系统初始化错误: {e}")
        return False

def start_web_interface():
    """启动Web界面"""
    print("正在启动Web界面...")
    print("🌐 Web界面将在浏览器中打开")
    print("📝 如果浏览器没有自动打开，请访问: http://localhost:8501")
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 感谢使用RAG系统！")
    except Exception as e:
        print(f"❌ 启动Web界面失败: {e}")

def main():
    """主函数"""
    print("🚀 RAG系统自动设置程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装依赖
    if not install_requirements():
        return
    
    # 检查系统要求
    check_system_requirements()
    
    # 下载示例文档
    download_sample_documents()
    
    # 创建示例PDF
    create_sample_pdf()
    
    print("\n" + "=" * 50)
    print("✅ 系统设置完成！")
    print("\n📋 接下来的步骤：")
    print("1. 将您的PDF文档放置到 'documents' 目录中")
    print("2. 启动Web界面")
    print("3. 在Web界面中上传并处理文档")
    print("4. 开始使用RAG问答系统")
    
    # 询问是否立即启动
    response = input("\n是否现在启动Web界面？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是', '']:
        start_web_interface()
    else:
        print("\n💡 要启动Web界面，请运行: streamlit run app.py")
        print("👋 再见！")

if __name__ == "__main__":
    main()
