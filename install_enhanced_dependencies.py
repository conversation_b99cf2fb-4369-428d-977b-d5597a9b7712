#!/usr/bin/env python3
"""
安装增强版中医RAG系统的所有依赖
包括语音功能、多格式文档处理、DeepSeek模型支持等
"""

import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    """打印安装横幅"""
    print("=" * 80)
    print("🔧 增强版中医RAG系统 - 依赖安装器")
    print("=" * 80)
    print("📦 将安装以下功能的依赖:")
    print("   🧠 DeepSeek模型支持 (llama-cpp-python)")
    print("   🔊 语音功能 (pyttsx3)")
    print("   📄 多格式文档处理 (docx, pptx, pandas)")
    print("   🔍 向量检索 (faiss, sentence-transformers)")
    print("   🌐 在线爬取 (requests, beautifulsoup4)")
    print("   🖥️ 界面框架 (streamlit)")
    print("=" * 80)

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"📥 安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def install_core_dependencies():
    """安装核心依赖"""
    print("\n🔧 安装核心依赖...")
    
    core_packages = [
        ("streamlit>=1.28.0", "Web界面框架"),
        ("numpy>=1.21.0", "数值计算"),
        ("requests>=2.28.0", "HTTP请求"),
        ("beautifulsoup4>=4.11.0", "HTML解析"),
    ]
    
    success_count = 0
    for package, description in core_packages:
        if install_package(package, description):
            success_count += 1
    
    return success_count == len(core_packages)

def install_ai_dependencies():
    """安装AI相关依赖"""
    print("\n🧠 安装AI相关依赖...")
    
    ai_packages = [
        ("sentence-transformers>=2.2.0", "文本嵌入模型"),
        ("faiss-cpu>=1.7.0", "向量相似度搜索"),
        ("torch>=1.13.0", "深度学习框架"),
    ]
    
    success_count = 0
    for package, description in ai_packages:
        if install_package(package, description):
            success_count += 1
    
    return success_count == len(ai_packages)

def install_document_processing():
    """安装文档处理依赖"""
    print("\n📄 安装文档处理依赖...")
    
    doc_packages = [
        ("PyPDF2>=3.0.0", "PDF文档解析"),
        ("python-docx", "Word文档处理"),
        ("python-pptx", "PowerPoint文档处理"),
        ("pandas>=1.5.0", "Excel文档处理"),
        ("openpyxl", "Excel文件读写"),
    ]
    
    success_count = 0
    for package, description in doc_packages:
        if install_package(package, description):
            success_count += 1
    
    return success_count == len(doc_packages)

def install_voice_support():
    """安装语音支持"""
    print("\n🔊 安装语音功能依赖...")
    
    try:
        # 检测操作系统
        if sys.platform.startswith('win'):
            # Windows系统
            if install_package("pyttsx3", "文本转语音引擎"):
                print("✅ Windows语音支持安装成功")
                return True
        elif sys.platform.startswith('darwin'):
            # macOS系统
            if install_package("pyttsx3", "文本转语音引擎"):
                print("✅ macOS语音支持安装成功")
                return True
        elif sys.platform.startswith('linux'):
            # Linux系统
            print("🐧 检测到Linux系统，安装额外依赖...")
            # Linux需要额外的系统依赖
            if install_package("pyttsx3", "文本转语音引擎"):
                print("✅ Linux语音支持安装成功")
                print("💡 如果语音功能不工作，请安装系统依赖:")
                print("   Ubuntu/Debian: sudo apt-get install espeak espeak-data libespeak1 libespeak-dev")
                print("   CentOS/RHEL: sudo yum install espeak espeak-devel")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ 语音功能安装失败: {e}")
        return False

def install_deepseek_support():
    """安装DeepSeek模型支持"""
    print("\n🧠 安装DeepSeek模型支持...")
    
    try:
        # 检查是否有GPU
        try:
            import torch
            if torch.cuda.is_available():
                print("🎮 检测到CUDA GPU，安装GPU版本...")
                package = "llama-cpp-python[cuda]"
            else:
                print("💻 使用CPU版本...")
                package = "llama-cpp-python"
        except ImportError:
            print("💻 使用CPU版本...")
            package = "llama-cpp-python"
        
        if install_package(package, "DeepSeek模型推理引擎"):
            print("✅ DeepSeek模型支持安装成功")
            return True
        else:
            print("⚠️ DeepSeek模型支持安装失败，系统将使用智能模板")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek模型支持安装失败: {e}")
        return False

def check_model_file():
    """检查DeepSeek模型文件"""
    print("\n🔍 检查DeepSeek模型文件...")
    
    model_path = r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf'
    
    if os.path.exists(model_path):
        print(f"✅ DeepSeek模型文件存在")
        print(f"📁 路径: {model_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)  # GB
        print(f"📊 文件大小: {file_size:.2f} GB")
        
        return True
    else:
        print(f"❌ DeepSeek模型文件不存在: {model_path}")
        print("💡 请确保模型文件路径正确，或在配置中修改路径")
        return False

def create_directories():
    """创建必要目录"""
    print("\n📁 创建系统目录...")
    
    directories = [
        "./enhanced_vector_db",
        "./documents",
        "./uploads",
        "./online_cache",
        "./logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}")

def test_installations():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    tests = [
        ("streamlit", "Streamlit界面框架"),
        ("sentence_transformers", "文本嵌入模型"),
        ("faiss", "向量搜索"),
        ("PyPDF2", "PDF处理"),
        ("docx", "Word处理"),
        ("pptx", "PowerPoint处理"),
        ("pandas", "Excel处理"),
        ("requests", "HTTP请求"),
        ("bs4", "HTML解析"),
    ]
    
    success_count = 0
    
    for module, description in tests:
        try:
            __import__(module)
            print(f"✅ {description}")
            success_count += 1
        except ImportError:
            print(f"❌ {description} - 导入失败")
    
    # 测试可选功能
    print("\n🔍 测试可选功能...")
    
    # 测试语音功能
    try:
        import pyttsx3
        print("✅ 语音功能可用")
    except ImportError:
        print("❌ 语音功能不可用")
    
    # 测试DeepSeek支持
    try:
        from llama_cpp import Llama
        print("✅ DeepSeek模型支持可用")
    except ImportError:
        print("❌ DeepSeek模型支持不可用")
    
    print(f"\n📊 测试结果: {success_count}/{len(tests)} 核心功能可用")
    
    return success_count >= len(tests) * 0.8  # 80%以上成功率

def main():
    """主安装流程"""
    print_banner()
    
    print("🚀 开始安装依赖...")
    
    # 1. 安装核心依赖
    if not install_core_dependencies():
        print("❌ 核心依赖安装失败，无法继续")
        return False
    
    # 2. 安装AI依赖
    if not install_ai_dependencies():
        print("❌ AI依赖安装失败，无法继续")
        return False
    
    # 3. 安装文档处理依赖
    if not install_document_processing():
        print("⚠️ 文档处理依赖安装不完整，但可以继续")
    
    # 4. 安装语音支持
    if not install_voice_support():
        print("⚠️ 语音功能安装失败，但不影响主要功能")
    
    # 5. 安装DeepSeek支持
    if not install_deepseek_support():
        print("⚠️ DeepSeek模型支持安装失败，将使用智能模板")
    
    # 6. 检查模型文件
    check_model_file()
    
    # 7. 创建目录
    create_directories()
    
    # 8. 测试安装
    if test_installations():
        print("\n" + "=" * 80)
        print("🎉 依赖安装完成！")
        print("✅ 系统已准备就绪，可以启动增强版中医RAG系统")
        print("=" * 80)
        return True
    else:
        print("\n" + "=" * 80)
        print("⚠️ 部分依赖安装失败")
        print("💡 建议检查网络连接和Python环境")
        print("=" * 80)
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 现在可以运行: python start_enhanced_system.py")
    else:
        print("\n🔧 请解决依赖问题后重新运行安装脚本")
    
    input("\n按回车键退出...")
