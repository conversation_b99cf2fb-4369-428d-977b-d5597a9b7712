
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="中医助手">
    
    <title>中医智能助手</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icon-192.png">
    <link rel="apple-touch-icon" href="/static/icon-192.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .quick-access {
            margin: 30px 0;
        }
        
        .access-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.2s;
        }
        
        .access-button:hover {
            transform: translateY(-2px);
        }
        
        .features {
            margin: 30px 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 500;
            color: #333;
        }
        
        .feature-desc {
            font-size: 0.8rem;
            color: #666;
        }
        
        .install-prompt {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .install-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin-top: 10px;
            cursor: pointer;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏥</div>
            <div class="title">中医智能助手</div>
            <div class="subtitle">专业 · 智能 · 便捷</div>
        </div>
        
        <div class="quick-access">
            <a href="http://localhost:8519" class="access-button">
                🚀 立即使用
            </a>
        </div>
        
        <div class="features">
            <div class="feature-item">
                <div class="feature-icon">🌐</div>
                <div class="feature-text">
                    <div class="feature-title">双重数据源</div>
                    <div class="feature-desc">本地知识库 + 在线医学资源</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">
                    <div class="feature-title">合规保障</div>
                    <div class="feature-desc">严格内容审核，安全可靠</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📱</div>
                <div class="feature-text">
                    <div class="feature-title">移动优化</div>
                    <div class="feature-desc">专为手机端设计优化</div>
                </div>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">
                    <div class="feature-title">快速响应</div>
                    <div class="feature-desc">毫秒级查询，即时获得答案</div>
                </div>
            </div>
        </div>
        
        <div class="install-prompt" id="installPrompt" style="display: none;">
            <div>📱 添加到主屏幕</div>
            <div style="font-size: 0.8rem; margin: 5px 0;">获得更好的使用体验</div>
            <button class="install-button" id="installButton">安装应用</button>
        </div>
        
        <div class="footer">
            <div>⚠️ 仅供中医文化学习参考</div>
            <div>如有健康问题请咨询专业医师</div>
        </div>
    </div>
    
    <script>
        // PWA 安装提示
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('installPrompt').style.display = 'block';
        });
        
        document.getElementById('installButton').addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                deferredPrompt = null;
                document.getElementById('installPrompt').style.display = 'none';
            }
        });
        
        // 注册 Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
