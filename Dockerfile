
# 增强版中医RAG系统 Docker镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    cmake \
    git \
    wget \
    curl \
    espeak \
    espeak-data \
    libespeak1 \
    libespeak-dev \
    alsa-utils \
    pulseaudio \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY enhanced_requirements.txt requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p enhanced_vector_db documents uploads online_cache logs

# 设置权限
RUN chmod +x start_enhanced_system.py

# 暴露端口
EXPOSE 8503

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8503/_stcore/health || exit 1

# 启动命令
CMD ["python", "-m", "streamlit", "run", "enhanced_ultimate_tcm_system.py", "--server.port=8503", "--server.address=0.0.0.0", "--theme.base=light", "--server.headless=true"]
