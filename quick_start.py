"""
RAG系统快速启动脚本 - 自动安装依赖并启动
"""
import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except:
        return False

def main():
    print("🚀 RAG系统快速启动")
    print("=" * 40)
    
    # 核心依赖列表
    core_packages = [
        "streamlit",
        "torch",
        "transformers", 
        "sentence-transformers",
        "faiss-cpu",
        "PyPDF2",
        "numpy",
        "pandas",
        "requests"
    ]
    
    print("📦 正在安装核心依赖...")
    for package in core_packages:
        print(f"安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
        else:
            print(f"⚠️ {package} 安装失败，继续...")
    
    print("\n🌐 启动Web界面...")
    print("📝 浏览器将打开 http://localhost:8501")
    print("⚠️ 首次启动需要下载模型，请耐心等待...")
    print("💡 如果遇到问题，请检查网络连接")
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请尝试手动运行: streamlit run app.py")

if __name__ == "__main__":
    main()
