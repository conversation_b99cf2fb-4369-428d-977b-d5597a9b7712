#!/usr/bin/env python3
"""
Ollama DeepSeek管理器
使用Ollama运行DeepSeek-R1模型
"""

import requests
import json
import subprocess
import sys
import time
import os
from pathlib import Path
import streamlit as st
import ollama

class OllamaDeepSeekManager:
    """Ollama DeepSeek管理器"""
    
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.model_name = "deepseek-r1:8b"
        self.initialized = False
        
    def check_ollama_installed(self):
        """检查Ollama是否安装"""
        try:
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def install_ollama(self):
        """安装Ollama"""
        st.info("🔄 正在安装Ollama...")
        
        try:
            # Windows安装命令
            if os.name == 'nt':
                st.info("💡 请手动安装Ollama:")
                st.info("1. 访问 https://ollama.ai/download")
                st.info("2. 下载Windows版本")
                st.info("3. 运行安装程序")
                st.info("4. 重启此应用")
                return False
            else:
                # Linux/Mac自动安装
                result = subprocess.run(
                    ["curl", "-fsSL", "https://ollama.ai/install.sh"],
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode == 0:
                    subprocess.run(["sh"], input=result.stdout, text=True)
                    return True
                else:
                    return False
                    
        except Exception as e:
            st.error(f"安装失败: {e}")
            return False
    
    def check_ollama_running(self):
        """检查Ollama服务是否运行"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_ollama_service(self):
        """启动Ollama服务"""
        try:
            st.info("🚀 启动Ollama服务...")
            
            # 在后台启动Ollama服务
            if os.name == 'nt':
                # Windows
                subprocess.Popen(
                    ["ollama", "serve"],
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                # Linux/Mac
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            
            # 等待服务启动
            for i in range(30):
                if self.check_ollama_running():
                    st.success("✅ Ollama服务启动成功!")
                    return True
                time.sleep(1)
            
            return False
            
        except Exception as e:
            st.error(f"启动服务失败: {e}")
            return False
    
    def check_model_available(self):
        """检查模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                for model in models:
                    if 'deepseek-r1' in model.get('name', '').lower():
                        return True, model['name']
            return False, None
        except:
            return False, None
    
    def pull_deepseek_model(self):
        """拉取DeepSeek模型"""
        st.info("📥 正在下载DeepSeek-R1模型...")
        st.info("💡 这可能需要几分钟时间，请耐心等待")
        
        try:
            # 使用流式请求监控下载进度
            response = requests.post(
                f"{self.ollama_base_url}/api/pull",
                json={"name": self.model_name},
                stream=True,
                timeout=1800  # 30分钟超时
            )
            
            if response.status_code == 200:
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            status = data.get('status', '')
                            
                            if 'completed' in data and 'total' in data:
                                progress = data['completed'] / data['total']
                                progress_bar.progress(progress)
                                status_text.text(f"下载进度: {progress*100:.1f}% - {status}")
                            else:
                                status_text.text(status)
                                
                            if status == 'success':
                                st.success("✅ DeepSeek-R1模型下载完成!")
                                return True
                                
                        except json.JSONDecodeError:
                            continue
                
                return True
            else:
                st.error(f"下载失败: {response.status_code}")
                return False
                
        except Exception as e:
            st.error(f"下载异常: {e}")
            return False
    
    def test_model(self):
        """测试模型"""
        try:
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "你好，请简单介绍一下中医。",
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return True, result.get('response', '')
            else:
                return False, f"测试失败: {response.status_code}"
                
        except Exception as e:
            return False, f"测试异常: {e}"
    
    def initialize(self):
        """初始化Ollama和DeepSeek模型"""
        st.info("🤖 初始化Ollama DeepSeek引擎...")
        
        # 1. 检查Ollama安装
        if not self.check_ollama_installed():
            st.warning("⚠️ Ollama未安装")
            if not self.install_ollama():
                return False
        
        st.success("✅ Ollama已安装")
        
        # 2. 检查服务运行
        if not self.check_ollama_running():
            st.info("🔄 Ollama服务未运行，正在启动...")
            if not self.start_ollama_service():
                st.error("❌ 无法启动Ollama服务")
                return False
        
        st.success("✅ Ollama服务运行中")
        
        # 3. 检查模型
        model_available, model_name = self.check_model_available()
        if not model_available:
            st.info("📥 DeepSeek模型未找到，开始下载...")
            if not self.pull_deepseek_model():
                st.error("❌ 模型下载失败")
                return False
            
            # 重新检查
            model_available, model_name = self.check_model_available()
            if model_available:
                self.model_name = model_name
        else:
            self.model_name = model_name
            st.success(f"✅ 找到模型: {model_name}")
        
        # 4. 测试模型
        st.info("🧪 测试模型...")
        success, result = self.test_model()
        if success:
            st.success("✅ DeepSeek-R1模型测试通过!")
            st.info(f"🎯 测试回答: {result[:100]}...")
            self.initialized = True
            return True
        else:
            st.error(f"❌ 模型测试失败: {result}")
            return False
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成回答"""
        if not self.initialized:
            return "DeepSeek模型未初始化"
        
        try:
            st.info("🧠 DeepSeek-R1正在思考...")
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens
                    }
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                
                if content:
                    st.success("✅ DeepSeek-R1生成完成")
                    return content
                else:
                    return "DeepSeek生成为空，请重试"
            else:
                return f"生成失败: {response.status_code}"
                
        except Exception as e:
            return f"生成异常: {str(e)}"

def main():
    """测试函数"""
    manager = OllamaDeepSeekManager()
    
    print("🤖 Ollama DeepSeek管理器测试")
    print("=" * 40)
    
    if manager.initialize():
        print("✅ 初始化成功!")
        
        # 测试生成
        response = manager.generate_response("什么是中医？", max_tokens=100)
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
