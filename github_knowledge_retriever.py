#!/usr/bin/env python3
"""
GitHub知识库检索器
从指定的GitHub仓库检索线上知识
"""

import requests
import json
import base64
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import re
from urllib.parse import urljoin
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GitHubKnowledgeRetriever:
    """GitHub知识库检索器"""
    
    def __init__(self, repo_url: str, github_token: Optional[str] = None):
        """
        初始化GitHub检索器
        
        Args:
            repo_url: GitHub仓库URL，例如 "https://github.com/user/repo"
            github_token: GitHub访问令牌（可选，用于提高API限制）
        """
        self.repo_url = repo_url.rstrip('/')
        self.github_token = github_token
        
        # 解析仓库信息
        self.owner, self.repo = self._parse_repo_url(repo_url)
        
        # API配置
        self.api_base = "https://api.github.com"
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "TCM-RAG-System/1.0"
        }
        
        if github_token:
            self.headers["Authorization"] = f"token {github_token}"
        
        # 缓存
        self.file_cache = {}
        self.content_cache = {}
        
        # 中医相关文件扩展名
        self.relevant_extensions = {'.md', '.txt', '.json', '.csv', '.xml'}
        
        # 中医关键词
        self.tcm_keywords = [
            '中医', '中药', '方剂', '针灸', '经络', '穴位', '脏腑', '气血', '阴阳', '五行',
            '本草', '伤寒', '金匮', '黄帝内经', '神农本草经', '医宗金鉴', '温病',
            '肾虚', '脾虚', '肝郁', '血瘀', '痰湿', '湿热', '阳虚', '阴虚',
            '四君子汤', '四物汤', '六味地黄丸', '逍遥散', '补中益气汤'
        ]
    
    def _parse_repo_url(self, url: str) -> tuple:
        """解析GitHub仓库URL"""
        # 移除协议和域名
        path = url.replace('https://github.com/', '').replace('http://github.com/', '')
        parts = path.split('/')
        
        if len(parts) >= 2:
            return parts[0], parts[1]
        else:
            raise ValueError(f"无效的GitHub仓库URL: {url}")
    
    def initialize(self) -> bool:
        """初始化检索器"""
        try:
            logger.info(f"🔄 初始化GitHub知识库检索器: {self.owner}/{self.repo}")
            
            # 测试仓库访问
            repo_info = self._get_repo_info()
            if repo_info:
                logger.info(f"✅ 仓库访问成功: {repo_info.get('full_name')}")
                logger.info(f"📊 仓库描述: {repo_info.get('description', '无描述')}")
                return True
            else:
                logger.error("❌ 仓库访问失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ GitHub检索器初始化失败: {e}")
            return False
    
    def _get_repo_info(self) -> Optional[Dict]:
        """获取仓库信息"""
        try:
            url = f"{self.api_base}/repos/{self.owner}/{self.repo}"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取仓库信息失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取仓库信息异常: {e}")
            return None
    
    def search_repository(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """搜索仓库内容"""
        try:
            logger.info(f"🔍 在GitHub仓库中搜索: {query}")
            
            results = []
            
            # 1. 搜索文件名
            filename_results = self._search_by_filename(query, max_results // 2)
            results.extend(filename_results)
            
            # 2. 搜索文件内容
            content_results = self._search_by_content(query, max_results // 2)
            results.extend(content_results)
            
            # 3. 去重和排序
            unique_results = self._deduplicate_results(results)
            sorted_results = sorted(unique_results, key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            logger.info(f"✅ GitHub搜索完成: 找到 {len(sorted_results)} 个结果")
            return sorted_results[:max_results]
            
        except Exception as e:
            logger.error(f"❌ GitHub搜索失败: {e}")
            return []
    
    def _search_by_filename(self, query: str, max_results: int) -> List[Dict]:
        """按文件名搜索"""
        try:
            # 获取仓库文件树
            files = self._get_repository_files()
            
            results = []
            query_lower = query.lower()
            
            for file_info in files:
                filename = file_info['name'].lower()
                path = file_info['path'].lower()
                
                # 检查文件扩展名
                if not any(path.endswith(ext) for ext in self.relevant_extensions):
                    continue
                
                # 计算相关性分数
                score = 0
                
                # 文件名匹配
                if query_lower in filename:
                    score += 2.0
                
                # 路径匹配
                if query_lower in path:
                    score += 1.0
                
                # 中医关键词匹配
                for keyword in self.tcm_keywords:
                    if keyword.lower() in filename or keyword.lower() in path:
                        score += 0.5
                
                if score > 0:
                    results.append({
                        'type': 'filename_match',
                        'path': file_info['path'],
                        'name': file_info['name'],
                        'relevance_score': score,
                        'url': file_info['html_url'],
                        'download_url': file_info.get('download_url')
                    })
            
            return sorted(results, key=lambda x: x['relevance_score'], reverse=True)[:max_results]
            
        except Exception as e:
            logger.error(f"文件名搜索失败: {e}")
            return []
    
    def _search_by_content(self, query: str, max_results: int) -> List[Dict]:
        """按文件内容搜索"""
        try:
            # 使用GitHub搜索API
            search_url = f"{self.api_base}/search/code"
            params = {
                'q': f'{query} repo:{self.owner}/{self.repo}',
                'per_page': max_results
            }
            
            response = requests.get(search_url, headers=self.headers, params=params, timeout=15)
            
            if response.status_code == 200:
                search_data = response.json()
                results = []
                
                for item in search_data.get('items', []):
                    # 检查文件扩展名
                    if not any(item['name'].endswith(ext) for ext in self.relevant_extensions):
                        continue
                    
                    # 计算相关性分数
                    score = item.get('score', 1.0)
                    
                    # 中医关键词加分
                    content_preview = item.get('text_matches', [])
                    for match in content_preview:
                        fragment = match.get('fragment', '').lower()
                        for keyword in self.tcm_keywords:
                            if keyword.lower() in fragment:
                                score += 0.3
                    
                    results.append({
                        'type': 'content_match',
                        'path': item['path'],
                        'name': item['name'],
                        'relevance_score': score,
                        'url': item['html_url'],
                        'repository': item['repository'],
                        'text_matches': content_preview
                    })
                
                return results
            else:
                logger.warning(f"内容搜索API调用失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"内容搜索失败: {e}")
            return []
    
    def _get_repository_files(self, path: str = "") -> List[Dict]:
        """获取仓库文件列表"""
        try:
            if path in self.file_cache:
                return self.file_cache[path]
            
            url = f"{self.api_base}/repos/{self.owner}/{self.repo}/contents/{path}"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                files = response.json()
                
                # 递归获取子目录文件
                all_files = []
                for item in files:
                    if item['type'] == 'file':
                        all_files.append(item)
                    elif item['type'] == 'dir' and len(path.split('/')) < 3:  # 限制递归深度
                        sub_files = self._get_repository_files(item['path'])
                        all_files.extend(sub_files)
                
                self.file_cache[path] = all_files
                return all_files
            else:
                logger.warning(f"获取文件列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取文件列表异常: {e}")
            return []
    
    def get_file_content(self, file_path: str) -> Optional[str]:
        """获取文件内容"""
        try:
            if file_path in self.content_cache:
                return self.content_cache[file_path]
            
            url = f"{self.api_base}/repos/{self.owner}/{self.repo}/contents/{file_path}"
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                file_data = response.json()
                
                if file_data.get('encoding') == 'base64':
                    content = base64.b64decode(file_data['content']).decode('utf-8', errors='ignore')
                    self.content_cache[file_path] = content
                    return content
                else:
                    logger.warning(f"不支持的文件编码: {file_data.get('encoding')}")
                    return None
            else:
                logger.warning(f"获取文件内容失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取文件内容异常: {e}")
            return None
    
    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """去重结果"""
        seen_paths = set()
        unique_results = []
        
        for result in results:
            path = result.get('path', '')
            if path not in seen_paths:
                seen_paths.add(path)
                unique_results.append(result)
        
        return unique_results
    
    def get_enhanced_results(self, search_results: List[Dict]) -> List[Dict]:
        """获取增强的搜索结果（包含文件内容）"""
        enhanced_results = []
        
        for result in search_results:
            enhanced_result = result.copy()
            
            # 获取文件内容
            file_path = result.get('path', '')
            if file_path:
                content = self.get_file_content(file_path)
                if content:
                    enhanced_result['content'] = content[:2000]  # 限制内容长度
                    enhanced_result['full_content_available'] = True
                else:
                    enhanced_result['content'] = '无法获取文件内容'
                    enhanced_result['full_content_available'] = False
            
            enhanced_results.append(enhanced_result)
        
        return enhanced_results

# 使用示例
if __name__ == "__main__":
    # 使用用户提供的实际GitHub仓库
    repo_url = "https://github.com/BillHCM7777779/gudaiyishu"

    retriever = GitHubKnowledgeRetriever(repo_url)

    if retriever.initialize():
        print("✅ GitHub仓库初始化成功")

        # 测试搜索
        test_queries = ["肾虚脾虚", "古代医术", "中医方剂"]

        for query in test_queries:
            print(f"\n🔍 搜索: {query}")
            results = retriever.search_repository(query, max_results=3)

            if results:
                enhanced_results = retriever.get_enhanced_results(results)
                for result in enhanced_results:
                    print(f"  📄 文件: {result['path']}")
                    print(f"  📊 相关性: {result['relevance_score']:.2f}")
                    print(f"  📝 内容预览: {result.get('content', '无内容')[:100]}...")
                    print("  " + "-" * 40)
            else:
                print("  ❌ 未找到相关结果")
    else:
        print("❌ GitHub仓库初始化失败")
