<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 布局测试页面</title>
    <style>
        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; 
            overflow: hidden;
        }
        
        .container {
            height: 100vh; 
            display: flex; 
            flex-direction: column;
            max-width: 1200px; 
            margin: 0 auto; 
            background: white;
            border: 2px solid red; /* 调试边框 */
        }
        
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; 
            padding: 20px; 
            text-align: center;
            border: 2px solid blue; /* 调试边框 */
        }
        
        .main-content { 
            flex: 1; 
            display: flex;
            border: 2px solid green; /* 调试边框 */
        }
        
        .sidebar {
            width: 300px; 
            background: #f8f9fa; 
            padding: 20px;
            border-right: 1px solid #e9ecef; 
            overflow-y: auto;
            border: 2px solid orange; /* 调试边框 */
        }
        
        .chat-area { 
            flex: 1; 
            display: flex; 
            flex-direction: column;
            border: 2px solid purple; /* 调试边框 */
        }
        
        .messages {
            flex: 1; 
            overflow-y: auto; 
            padding: 20px; 
            background: #fafafa;
            border: 2px solid pink; /* 调试边框 */
        }
        
        .input-area {
            padding: 20px; 
            background: white; 
            border-top: 1px solid #e9ecef;
            border: 2px solid cyan; /* 调试边框 */
        }
        
        .input-row { 
            display: flex; 
            gap: 10px; 
        }
        
        .input-row textarea {
            flex: 1; 
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 8px; 
            resize: none; 
            font-size: 16px;
        }
        
        .voice-btn, .send-btn {
            padding: 10px 20px; 
            background: #2E8B57; 
            color: white;
            border: none; 
            border-radius: 8px; 
            cursor: pointer;
            font-size: 16px;
        }
        
        .voice-btn {
            background: #6c757d;
            min-width: 50px;
        }
        
        .voice-btn:hover { background: #5a6268; }
        .send-btn:hover { background: #228B22; }
        
        .section { 
            margin-bottom: 20px; 
        }
        
        .section h3 { 
            margin-bottom: 10px; 
            color: #666; 
        }
        
        .btn {
            width: 100%; 
            margin-bottom: 8px; 
            padding: 10px;
            background: white; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            cursor: pointer; 
            text-align: left;
        }
        
        .btn:hover { 
            background: #f0f0f0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 布局测试页面</h1>
            <p>测试聊天区域和语音功能是否正常显示</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="section">
                    <h3>💡 快捷查询</h3>
                    <button class="btn" onclick="alert('快捷查询功能正常')">
                        🌿 脾胃调理
                    </button>
                    <button class="btn" onclick="alert('快捷查询功能正常')">
                        💊 经典方剂
                    </button>
                </div>

                <div class="section">
                    <h3>💬 会话管理</h3>
                    <div>当前会话: 新会话</div>
                    <div>消息数量: 0</div>
                    <button class="btn" onclick="alert('会话管理功能正常')">
                        🆕 新建会话
                    </button>
                </div>

                <div class="section">
                    <h3>📊 系统状态</h3>
                    <div>状态: ✅ healthy</div>
                    <div>版本: 3.0.0-test</div>
                    <div>文档: 0 个</div>
                </div>
            </div>

            <div class="chat-area">
                <div class="messages">
                    <div style="margin-bottom: 15px;">
                        <div style="background: white; border: 1px solid #ddd; padding: 10px 15px; border-radius: 15px; display: inline-block;">
                            <strong>🤖 助手:</strong> 您好！这是布局测试页面。如果您能看到这条消息和下面的输入框，说明布局正常。
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <div class="input-row">
                        <textarea rows="2" placeholder="请输入您的中医问题..." onkeydown="if(event.key==='Enter' && !event.shiftKey){event.preventDefault(); testSend();}"></textarea>
                        <button class="voice-btn" onclick="testVoice()" title="语音输入">🎤</button>
                        <button class="send-btn" onclick="testSend()">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testSend() {
            alert('发送功能正常！聊天区域和输入框都能正常显示。');
        }
        
        function testVoice() {
            alert('语音按钮正常！🎤 语音功能可以正常点击。');
        }
        
        console.log('🚀 布局测试页面加载完成');
        console.log('📱 容器高度:', document.querySelector('.container').offsetHeight);
        console.log('📱 主内容高度:', document.querySelector('.main-content').offsetHeight);
        console.log('📱 聊天区域高度:', document.querySelector('.chat-area').offsetHeight);
    </script>
</body>
</html>
