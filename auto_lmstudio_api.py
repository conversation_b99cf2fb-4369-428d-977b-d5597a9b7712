#!/usr/bin/env python3
"""
自动化LM Studio API启动
尝试模拟Ollama的自动API模式
"""

import subprocess
import time
import requests
import psutil
import os
import json
from pathlib import Path
import win32gui
import win32con
import win32api

class AutoLMStudioAPI:
    """自动化LM Studio API管理器"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:1234/v1"
        self.lmstudio_exe = None
        self.find_lmstudio_executable()
        
    def find_lmstudio_executable(self):
        """查找LM Studio可执行文件"""
        possible_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\LM Studio\LM Studio.exe",
            r"C:\Program Files\LM Studio\LM Studio.exe",
            r"C:\Program Files (x86)\LM Studio\LM Studio.exe",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.lmstudio_exe = path
                print(f"找到LM Studio: {path}")
                return True
        
        print("未找到LM Studio可执行文件")
        return False
    
    def check_lmstudio_running(self):
        """检查LM Studio是否运行"""
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                if 'lm studio' in proc.info['name'].lower():
                    return True, proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False, None
    
    def start_lmstudio(self):
        """启动LM Studio"""
        if not self.lmstudio_exe:
            print("❌ 未找到LM Studio可执行文件")
            return False
        
        try:
            print("🚀 启动LM Studio...")
            subprocess.Popen([self.lmstudio_exe])
            
            # 等待启动
            for i in range(30):
                running, pid = self.check_lmstudio_running()
                if running:
                    print(f"✅ LM Studio已启动 (PID: {pid})")
                    return True
                time.sleep(1)
            
            print("⚠️ LM Studio启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动LM Studio失败: {e}")
            return False
    
    def find_lmstudio_window(self):
        """查找LM Studio窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if 'lm studio' in window_text.lower():
                    windows.append((hwnd, window_text))
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows
    
    def try_automate_lmstudio_gui(self):
        """尝试自动化LM Studio GUI操作"""
        try:
            print("🔄 尝试自动化LM Studio界面操作...")
            
            # 查找LM Studio窗口
            windows = self.find_lmstudio_window()
            if not windows:
                print("❌ 未找到LM Studio窗口")
                return False
            
            hwnd, window_text = windows[0]
            print(f"找到窗口: {window_text}")
            
            # 激活窗口
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(1)
            
            # 尝试发送快捷键（如果LM Studio支持）
            # 这里需要知道LM Studio的具体快捷键
            # 由于不知道确切的快捷键，这个方法可能不会成功
            
            print("⚠️ GUI自动化需要知道LM Studio的具体快捷键")
            return False
            
        except Exception as e:
            print(f"❌ GUI自动化失败: {e}")
            return False
    
    def check_api_status(self):
        """检查API状态"""
        try:
            response = requests.get(f"{self.api_base_url}/models", timeout=3)
            if response.status_code == 200:
                data = response.json()
                models = [model['id'] for model in data.get('data', [])]
                return True, models
            return False, []
        except:
            return False, []
    
    def wait_for_manual_setup(self, timeout=300):
        """等待用户手动设置"""
        print("⏳ 等待您手动在LM Studio中:")
        print("   1. 加载DeepSeek模型")
        print("   2. 启动Local Server")
        print("   3. 确认端口为1234")
        print(f"   (最多等待{timeout//60}分钟)")
        
        for i in range(timeout):
            api_ok, models = self.check_api_status()
            if api_ok and models:
                print(f"✅ 检测到API已启动! 模型: {models}")
                return True, models
            
            if i % 30 == 0 and i > 0:
                print(f"   仍在等待... ({i//60}分{i%60}秒)")
            
            time.sleep(1)
        
        print(f"⏰ 等待超时 ({timeout//60}分钟)")
        return False, []
    
    def auto_setup_api(self):
        """自动设置API"""
        print("🤖 自动化LM Studio API设置")
        print("=" * 40)
        
        # 1. 检查LM Studio是否运行
        running, pid = self.check_lmstudio_running()
        if not running:
            print("LM Studio未运行，尝试启动...")
            if not self.start_lmstudio():
                return False, "无法启动LM Studio"
        else:
            print(f"✅ LM Studio已运行 (PID: {pid})")
        
        # 2. 检查API是否已经可用
        api_ok, models = self.check_api_status()
        if api_ok and models:
            print(f"✅ API已就绪! 模型: {models}")
            return True, models
        
        # 3. 尝试GUI自动化（通常会失败）
        if self.try_automate_lmstudio_gui():
            # 等待API启动
            time.sleep(5)
            api_ok, models = self.check_api_status()
            if api_ok and models:
                print(f"✅ GUI自动化成功! 模型: {models}")
                return True, models
        
        # 4. 等待手动操作
        print("🔧 GUI自动化失败，需要手动操作")
        success, models = self.wait_for_manual_setup()
        
        if success:
            return True, models
        else:
            return False, "手动设置超时"

def main():
    """主函数"""
    manager = AutoLMStudioAPI()
    
    try:
        success, result = manager.auto_setup_api()
        
        if success:
            print(f"\n🎉 LM Studio API设置成功!")
            print(f"可用模型: {result}")
            print("现在可以通过API调用DeepSeek模型了!")
            return True
        else:
            print(f"\n❌ 设置失败: {result}")
            return False
            
    except Exception as e:
        print(f"\n💥 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
