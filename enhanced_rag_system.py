"""
增强版中医RAG系统 - 修复问答质量问题
"""
from typing import Dict, List, Generator
from models.model_manager import model_manager
from document_processor import doc_processor
from session_manager import session_manager
from simple_answer_generator import simple_generator

class EnhancedRAGSystem:
    def __init__(self):
        self.initialized = False
        self.max_context_length = 400  # 进一步减少上下文长度
        self.max_retrieved_docs = 2    # 进一步限制检索文档数量
        self.max_prompt_length = 600   # 限制总提示词长度
    
    def initialize(self) -> bool:
        """初始化增强RAG系统"""
        try:
            print("正在初始化增强RAG系统...")
            
            # 初始化模型
            print("开始初始化模型...")
            model_manager.initialize_models()
            print("所有模型初始化完成！")
            
            # 初始化文档处理器
            doc_processor.load_index()
            
            # 初始化会话管理器
            session_manager.initialize()
            print("会话管理器初始化完成")
            
            self.initialized = True
            print("增强RAG系统初始化完成！")
            return True
            
        except Exception as e:
            print(f"增强RAG系统初始化失败: {e}")
            return False
    
    def filter_relevant_content(self, docs: List[Dict]) -> List[Dict]:
        """过滤相关内容，移除版权页等无关信息"""
        filtered_docs = []
        
        # 无关内容关键词
        irrelevant_keywords = [
            "版权页", "图书在版编目", "CIP", "ISBN", "定价", "著作权",
            "出版社", "印刷", "标准书号", "版次", "印次"
        ]
        
        for doc in docs:
            content = doc.get("content", "")
            
            # 检查是否包含无关关键词
            is_irrelevant = any(keyword in content for keyword in irrelevant_keywords)
            
            # 检查内容长度（太短的可能是目录或标题）
            if len(content.strip()) < 30:
                is_irrelevant = True
            
            if not is_irrelevant:
                filtered_docs.append(doc)
        
        return filtered_docs
    
    def create_optimized_prompt(self, query: str, docs: List[Dict], history: List = None) -> str:
        """创建优化的提示词 - 严格控制长度"""
        # 过滤相关内容
        relevant_docs = self.filter_relevant_content(docs)

        # 限制文档数量
        relevant_docs = relevant_docs[:self.max_retrieved_docs]

        # 构建上下文 - 更严格的长度控制
        context_parts = []
        current_length = 0

        for doc in relevant_docs:
            content = doc["content"]
            # 每个文档最多200字符
            content = content[:200] if len(content) > 200 else content

            if current_length + len(content) < self.max_context_length:
                context_parts.append(content)
                current_length += len(content)
            else:
                # 截断内容以适应长度限制
                remaining_length = self.max_context_length - current_length
                if remaining_length > 30:  # 至少保留30字符
                    context_parts.append(content[:remaining_length] + "...")
                break

        context = "\n".join(context_parts)  # 使用单个换行符

        # 不包含历史对话以节省空间

        # 创建简化的提示词
        prompt = f"""基于中医文献回答问题。

文献：{context}

问题：{query}

回答："""

        # 检查并截断提示词
        if len(prompt) > self.max_prompt_length:
            # 重新构建更短的提示词
            short_context = context[:200] + "..." if len(context) > 200 else context
            prompt = f"""基于文献回答：{short_context}

问：{query}
答："""

        return prompt
    
    def retrieve_and_generate(self, query: str, session_id: str = None) -> Dict:
        """检索相关文档并生成回答 - 增强版"""
        if not self.initialized:
            return {"error": "系统未初始化"}
        
        try:
            print(f"处理查询: {query}")
            
            # 检索相关文档
            retrieved_docs = doc_processor.search_similar_chunks(query, top_k=5)
            print(f"检索到 {len(retrieved_docs)} 个相关文档块")
            
            if not retrieved_docs:
                return {
                    "answer": "抱歉，我在中医文献中没有找到相关信息。请尝试重新表述您的问题，或者询问其他中医相关问题。",
                    "sources": [],
                    "session_id": session_id
                }
            
            # 获取历史对话
            history = []
            if session_id:
                history = session_manager.get_session_history(session_id)
            
            # 创建优化提示
            prompt = self.create_optimized_prompt(query, retrieved_docs, history)
            print(f"提示词长度: {len(prompt)} 字符")
            
            # 检查提示词长度
            if len(prompt) > 2000:
                print("⚠️ 提示词过长，进行截断")
                prompt = prompt[:2000] + "...\n\n回答："
            
            # 尝试使用LLM生成回答
            llm_answer = model_manager.generate_response(
                prompt,
                max_new_tokens=150,  # 限制新生成的token数量
                temperature=0.7
            )

            print(f"LLM生成回答长度: {len(llm_answer)} 字符")

            # 如果LLM回答质量不好，使用简单生成器
            if not llm_answer or len(llm_answer.strip()) < 20 or "根据中医理论，这个问题需要结合具体情况" in llm_answer:
                print("🔄 LLM回答质量不佳，使用简单生成器")
                # 构建上下文
                context = "\n".join([doc["content"] for doc in self.filter_relevant_content(retrieved_docs)[:2]])
                answer = simple_generator.generate_answer_from_context(query, context)
            else:
                answer = llm_answer
            
            # 保存到会话历史
            if session_id:
                session_manager.add_to_history(session_id, query, answer)
            
            # 准备源文档信息 - 只显示相关的
            relevant_docs = self.filter_relevant_content(retrieved_docs)
            sources = []
            for doc in relevant_docs[:3]:  # 最多显示3个来源
                sources.append({
                    "source": doc["source"],
                    "content": doc["content"][:150] + "..." if len(doc["content"]) > 150 else doc["content"],
                    "similarity": doc.get("similarity_score", 0.0)
                })
            
            return {
                "answer": answer,
                "sources": sources,
                "session_id": session_id,
                "retrieved_count": len(retrieved_docs),
                "relevant_count": len(relevant_docs)
            }
            
        except Exception as e:
            print(f"增强RAG处理错误: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"处理请求时出错: {str(e)}"}
    
    def stream_generate(self, query: str, session_id: str = None) -> Generator[str, None, None]:
        """流式生成回答 - 增强版"""
        if not self.initialized:
            yield "系统未初始化"
            return
        
        try:
            # 检索相关文档
            retrieved_docs = doc_processor.search_similar_chunks(query, top_k=5)
            
            if not retrieved_docs:
                yield "抱歉，我在中医文献中没有找到相关信息。请尝试重新表述您的问题。"
                return
            
            # 获取历史对话
            history = []
            if session_id:
                history = session_manager.get_session_history(session_id)
            
            # 创建优化提示
            prompt = self.create_optimized_prompt(query, retrieved_docs, history)
            
            # 检查提示词长度
            if len(prompt) > 2000:
                prompt = prompt[:2000] + "...\n\n回答："
            
            # 生成回答
            llm_response = model_manager.generate_response(
                prompt,
                max_new_tokens=150,
                temperature=0.7
            )

            # 如果LLM回答质量不好，使用简单生成器
            if not llm_response or len(llm_response.strip()) < 20:
                context = "\n".join([doc["content"] for doc in self.filter_relevant_content(retrieved_docs)[:2]])
                full_response = simple_generator.generate_answer_from_context(query, context)
            else:
                full_response = llm_response
            
            # 模拟流式输出
            words = full_response.split()
            current_response = ""
            
            for word in words:
                current_response += word + " "
                yield current_response.strip()
                
            # 保存到会话历史
            if session_id:
                session_manager.add_to_history(session_id, query, full_response)
                
        except Exception as e:
            yield f"处理请求时出错: {str(e)}"
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            "initialized": self.initialized,
            "models_loaded": {
                "embedding": model_manager.embedding_model is not None,
                "llm": model_manager.llm_model is not None
            },
            "documents_indexed": len(doc_processor.document_chunks) if doc_processor.document_chunks else 0,
            "device": model_manager.device,
            "max_context_length": self.max_context_length,
            "max_retrieved_docs": self.max_retrieved_docs
        }

# 全局增强RAG系统实例
enhanced_rag_system = EnhancedRAGSystem()
