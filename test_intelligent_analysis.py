#!/usr/bin/env python3
"""
测试智能问题识别系统
"""

import sys
sys.path.append('.')

def test_query_analysis():
    """测试查询分析功能"""
    
    try:
        from ultimate_final_tcm_system import analyze_user_query
        
        # 测试不同类型的问题
        test_queries = [
            "身上肚子很疼，湿气很重怎么治疗，男性",
            "失眠多梦怎么办",
            "头痛头晕，女性，30岁",
            "腰膝酸软，肾虚怎么调理",
            "胃痛吃什么药好",
            "咳嗽有痰，胸闷气短",
            "手脚冰凉，怕冷，脾胃虚弱",
            "心烦失眠，潮热盗汗",
            "大便干燥，便秘严重",
            "食欲不振，腹胀腹泻"
        ]
        
        print("🧠 智能问题识别测试")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. 测试问题: {query}")
            print("-" * 40)
            
            analysis = analyze_user_query(query)
            
            print(f"症状识别: {analysis['symptoms']}")
            print(f"身体部位: {analysis['body_parts']}")
            print(f"人口学信息: {analysis['demographics']}")
            print(f"严重程度: {analysis['severity']}")
            print(f"查询类型: {analysis['query_type']}")
            print(f"关键词: {analysis['keywords'][:5]}")  # 只显示前5个
            
            # 评估识别准确性
            accuracy_score = 0
            if analysis['symptoms']:
                accuracy_score += 2
            if analysis['body_parts']:
                accuracy_score += 1
            if analysis['query_type'] != 'unknown':
                accuracy_score += 2
            if analysis['keywords']:
                accuracy_score += 1
            
            print(f"识别准确性: {accuracy_score}/6 ({'优秀' if accuracy_score >= 5 else '良好' if accuracy_score >= 3 else '需改进'})")
        
        print("\n" + "=" * 60)
        print("✅ 智能问题识别测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_response_generation():
    """测试回答生成"""
    
    try:
        from ultimate_final_tcm_system import generate_content_based_response
        
        # 模拟检索结果
        mock_mcp_results = [
            {
                'title': '《脾胃论》- 祛湿健脾法',
                'content': '湿邪困脾，运化失司，见身重困倦，胸闷腹胀，大便溏薄。治宜健脾祛湿。用平胃散合二陈汤：苍术12g，厚朴10g，陈皮10g，甘草6g，半夏10g，茯苓15g，生姜3片。功效：健脾祛湿，理气和胃。',
                'source': '脾胃论',
                'score': 0.9
            },
            {
                'title': '《金匮要略》- 腹痛治疗',
                'content': '腹痛得温则减，遇寒加重，舌淡苔白，脉沉迟。此为脾胃虚寒证。用理中汤：人参9g，白术9g，干姜9g，炙甘草9g。功效：温中健脾，和胃止痛。',
                'source': '金匮要略',
                'score': 0.85
            }
        ]
        
        test_query = "肚子疼，湿气重怎么治疗"
        
        print("\n🧪 测试回答生成")
        print("=" * 60)
        print(f"测试问题: {test_query}")
        print("-" * 40)
        
        response = generate_content_based_response(
            test_query, 
            [], 
            [], 
            mock_mcp_results
        )
        
        print("生成的回答:")
        print(response)
        
        # 检查回答质量
        quality_checks = [
            ('包含问题分析', '问题分析' in response or '症状' in response),
            ('包含治疗建议', '治疗' in response or '方剂' in response),
            ('包含具体方药', '平胃散' in response or '理中汤' in response),
            ('针对性强', test_query.replace('怎么治疗', '') in response),
            ('结构清晰', '###' in response and '**' in response)
        ]
        
        print("\n回答质量评估:")
        passed = 0
        for check_name, check_result in quality_checks:
            status = "✅" if check_result else "❌"
            print(f"{status} {check_name}")
            if check_result:
                passed += 1
        
        print(f"\n质量评分: {passed}/{len(quality_checks)} ({'优秀' if passed >= 4 else '良好' if passed >= 3 else '需改进'})")
        
        return True
        
    except Exception as e:
        print(f"❌ 回答生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔬 智能分析系统测试")
    
    success1 = test_query_analysis()
    success2 = test_response_generation()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！智能分析系统工作正常")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
