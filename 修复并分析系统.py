#!/usr/bin/env python3
"""
修复Ngrok + 分析数据库容量
"""

import os
import subprocess
import sys
from pathlib import Path

def fix_ngrok_and_restart():
    """修复Ngrok配置并重启系统"""
    print("🔧 修复Ngrok端口配置...")
    
    # 检查系统文件
    main_file = "ultimate_final_tcm_system.py"
    if not os.path.exists(main_file):
        print(f"❌ 未找到主文件: {main_file}")
        return False
    
    print("✅ 找到主系统文件")
    print("🚀 重启系统以应用Ngrok修复...")
    
    try:
        # 启动系统
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            main_file,
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def analyze_database_capacity():
    """分析数据库容量"""
    print("\n📊 数据库容量分析")
    print("=" * 50)
    
    # 检查向量数据库目录
    vector_dirs = [
        'vector_db',
        'ultimate_vector_db', 
        'ultimate_final_vector_db',
        'ultimate_integrated_vector_db',
        'enhanced_vector_db',
        'optimized_vector_db',
        'working_vector_db'
    ]

    print('📊 向量数据库目录:')
    total_vector_size = 0
    active_dbs = 0
    
    for vdir in vector_dirs:
        if os.path.exists(vdir):
            files = list(Path(vdir).glob('*'))
            if files:
                size_mb = sum(f.stat().st_size for f in files if f.is_file()) / (1024 * 1024)
                total_vector_size += size_mb
                active_dbs += 1
                print(f'   ✅ {vdir}: {len(files)} 个文件, {size_mb:.1f} MB')
            else:
                print(f'   📁 {vdir}: 空目录')
        else:
            print(f'   ❌ {vdir}: 不存在')

    # 检查文档目录
    doc_dirs = ['documents', 'uploads']
    print(f'\n📚 文档目录:')
    total_doc_size = 0
    total_pdfs = 0
    
    for ddir in doc_dirs:
        if os.path.exists(ddir):
            files = list(Path(ddir).glob('*.pdf'))
            if files:
                size_mb = sum(f.stat().st_size for f in files) / (1024 * 1024)
                total_doc_size += size_mb
                total_pdfs += len(files)
                print(f'   ✅ {ddir}: {len(files)} 个PDF, {size_mb:.1f} MB')
                for pdf in files[:3]:  # 只显示前3个
                    pdf_size = pdf.stat().st_size / (1024 * 1024)
                    print(f'      - {pdf.name}: {pdf_size:.1f} MB')
                if len(files) > 3:
                    print(f'      ... 还有 {len(files)-3} 个文件')
            else:
                print(f'   📁 {ddir}: 无PDF文件')

    # 总结
    print(f'\n📈 容量总结:')
    print(f'   📊 向量数据库: {active_dbs} 个活跃, 总计 {total_vector_size:.1f} MB')
    print(f'   📚 PDF文档: {total_pdfs} 个文件, 总计 {total_doc_size:.1f} MB')
    print(f'   💾 总存储: {total_vector_size + total_doc_size:.1f} MB')
    
    # 容量估算
    print(f'\n🎯 扩展能力分析:')
    print(f'   当前状态: {"良好" if total_pdfs > 0 else "需要添加文档"}')
    print(f'   理论上限: 数TB级别 (SQLite + FAISS)')
    print(f'   实际建议: 1-10GB 知识库')
    print(f'   文档建议: 100-10000 个PDF文件')
    
    # 性能预估
    if total_pdfs > 0:
        avg_size = total_doc_size / total_pdfs
        estimated_chunks = total_pdfs * 50  # 假设每个PDF 50个文本块
        print(f'\n⚡ 性能预估:')
        print(f'   平均文档大小: {avg_size:.1f} MB')
        print(f'   估计文本块数: {estimated_chunks:,}')
        print(f'   查询响应时间: {"<1秒" if estimated_chunks < 10000 else "1-3秒" if estimated_chunks < 50000 else "3-10秒"}')

def provide_expansion_recommendations():
    """提供扩展建议"""
    print(f'\n🚀 知识库扩展建议:')
    print('=' * 50)
    
    print('📖 中医经典文献:')
    print('   - 四大经典: 黄帝内经、伤寒论、金匮要略、温病条辨')
    print('   - 本草类: 神农本草经、本草纲目、中药学')
    print('   - 方剂类: 方剂学、千金方、太平惠民和剂局方')
    print('   - 诊断类: 中医诊断学、脉经、濒湖脉学')
    
    print('\n🏥 现代医学资料:')
    print('   - 临床指南: 各科诊疗指南和标准')
    print('   - 病例分析: 典型病例和治疗方案')
    print('   - 研究文献: 最新医学研究和论文')
    print('   - 药物信息: 现代药理学和临床用药')
    
    print('\n💡 优化建议:')
    print('   1. 📊 定期清理重复和低质量文档')
    print('   2. 🏷️ 按科室、病症分类管理文档')
    print('   3. 🔍 建立文档质量评估机制')
    print('   4. ⚡ 根据使用频率优化索引')
    print('   5. 🔄 定期更新和维护知识库')
    
    print('\n📋 添加文档步骤:')
    print('   1. 将PDF文件放入 documents/ 或 uploads/ 目录')
    print('   2. 在系统界面点击"重新处理文档"')
    print('   3. 等待向量化处理完成')
    print('   4. 测试新文档的检索效果')

def main():
    """主函数"""
    print("🏥 TCM系统修复与分析")
    print("🎯 修复Ngrok + 分析数据库容量")
    print()
    
    # 分析数据库
    analyze_database_capacity()
    
    # 扩展建议
    provide_expansion_recommendations()
    
    print("\n" + "=" * 60)
    print("✅ 分析完成！")
    print()
    print("🔧 Ngrok已修复 (端口8507 → 8501)")
    print("📊 数据库容量分析完成")
    print("🚀 现在可以重启系统测试远程访问")
    print()
    
    # 询问是否重启
    restart = input("是否现在重启系统？(y/n): ").lower().strip()
    if restart in ['y', 'yes', '是']:
        print("\n🚀 重启系统...")
        return fix_ngrok_and_restart()
    else:
        print("\n💡 手动重启命令:")
        print("python 最终启动脚本.py")
        return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 操作失败")
        input("按回车键退出...")
    sys.exit(0 if success else 1)
