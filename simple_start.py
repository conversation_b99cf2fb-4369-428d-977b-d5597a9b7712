#!/usr/bin/env python3
"""
简单启动脚本 - 终极中医RAG系统
直接启动，无复杂检查
"""

import subprocess
import sys
import webbrowser
import time
import os

def main():
    """主函数"""
    print("=" * 60)
    print("🧙‍♂️ 终极中医RAG系统 - 简单启动")
    print("=" * 60)
    
    # 检查主文件
    if not os.path.exists("working_tcm_system_api.py"):
        print("❌ 找不到主系统文件 working_tcm_system_api.py")
        print("💡 请确保在正确的目录中运行此脚本")
        input("按回车键退出...")
        return
    
    print("✅ 找到主系统文件")
    print("🚀 正在启动系统...")
    print("")
    
    # 启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "working_tcm_system_api.py",
        "--server.port=8507",
        "--server.address=0.0.0.0"
    ]
    
    print("💡 启动命令:")
    print(f"   {' '.join(cmd)}")
    print("")
    print("🌐 访问地址:")
    print("   本地: http://localhost:8507")
    print("   局域网: http://[您的IP]:8507")
    print("")
    print("⏹️ 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    # 等待3秒后自动打开浏览器
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8507")
            print("🌐 已自动打开浏览器")
        except:
            pass
    
    import threading
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        # 启动服务
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查Python环境和Streamlit安装")
        print("💡 可以尝试运行: pip install streamlit")

if __name__ == "__main__":
    main()
