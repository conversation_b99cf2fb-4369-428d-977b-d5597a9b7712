# 🚀 LM Studio + DeepSeek-R1 启动指南

## 📋 快速启动步骤

### 1️⃣ 启动LM Studio
1. 打开 **LM Studio** 应用程序
2. 等待应用完全加载

### 2️⃣ 加载DeepSeek模型
1. 在LM Studio中点击 **"Local Server"** 标签
2. 在模型列表中找到您的 **DeepSeek-R1** 模型
3. 点击模型旁边的 **"Load"** 按钮
4. 等待模型加载完成（显示绿色状态）

### 3️⃣ 启动API服务器
1. 模型加载完成后，点击 **"Start Server"** 按钮
2. 确认服务器地址为：`http://localhost:1234`
3. 等待显示 **"Server is running"** 状态

### 4️⃣ 测试连接
1. 在RAG系统中点击 **"🚀 初始化系统"**
2. 系统会自动检测LM Studio API
3. 看到 **"✅ LM Studio API连接成功!"** 表示成功

## 🔧 故障排除

### ❌ 如果显示"LM Studio未启动"
- 确保LM Studio应用程序已打开
- 确保已点击"Start Server"
- 检查端口1234是否被占用

### ❌ 如果显示"没有加载模型"
- 在LM Studio中重新加载DeepSeek模型
- 确保模型状态显示为绿色（已加载）

### ❌ 如果API调用失败
- 重启LM Studio
- 重新加载模型
- 检查防火墙设置

## 🎯 优势说明

✅ **使用您现有的模型** - 无需重新下载
✅ **稳定可靠** - LM Studio经过充分测试
✅ **图形界面管理** - 易于监控和控制
✅ **完全本地运行** - 数据隐私安全
✅ **支持多种模型** - 可以随时切换

## 💡 使用技巧

1. **保持LM Studio运行** - 一旦启动，可以一直保持运行
2. **监控资源使用** - 在LM Studio中可以看到内存和GPU使用情况
3. **模型切换** - 可以随时在LM Studio中切换不同的模型
4. **性能调优** - 在LM Studio设置中可以调整推理参数

## 🔄 自动化选项

如果您希望更自动化的启动，可以：

1. **使用启动脚本**：
   ```bash
   # 运行自动启动脚本
   start_lmstudio.bat
   ```

2. **设置开机自启**：
   - 将LM Studio添加到Windows启动项
   - 配置自动加载特定模型

## 📞 支持

如果遇到问题：
1. 检查LM Studio官方文档
2. 确保使用最新版本的LM Studio
3. 重启应用程序和系统

---

**🎉 现在您可以享受DeepSeek-R1的强大智能了！**
