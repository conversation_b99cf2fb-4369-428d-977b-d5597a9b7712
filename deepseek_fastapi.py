#!/usr/bin/env python3
"""
DeepSeek FastAPI接口
使用您指定的deepseek-r1-0528-qwen3-8b模型
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import uvicorn
import requests
import json
import subprocess
import time
import os
import sys
from pathlib import Path

# 请求模型
class ChatRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 2048
    temperature: Optional[float] = 0.7
    stream: Optional[bool] = False

class ChatResponse(BaseModel):
    response: str
    model: str
    status: str
    tokens_used: Optional[int] = None

# FastAPI应用
app = FastAPI(
    title="DeepSeek-R1 API",
    description="DeepSeek-R1-0528-Qwen3-8B 模型API接口",
    version="1.0.0"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class DeepSeekAPI:
    """DeepSeek API管理器"""
    
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.model_name = "deepseek-r1:8b"
        self.initialized = False
        
    def check_ollama_running(self):
        """检查Ollama是否运行"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_ollama_service(self):
        """启动Ollama服务"""
        try:
            if os.name == 'nt':
                subprocess.Popen(
                    ["ollama", "serve"],
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            else:
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
            
            # 等待服务启动
            for i in range(30):
                if self.check_ollama_running():
                    return True
                time.sleep(1)
            
            return False
        except:
            return False
    
    def check_model_available(self):
        """检查模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                for model in models:
                    if 'deepseek-r1' in model.get('name', '').lower():
                        return True, model['name']
            return False, None
        except:
            return False, None
    
    def pull_model(self):
        """拉取DeepSeek模型"""
        try:
            response = requests.post(
                f"{self.ollama_base_url}/api/pull",
                json={"name": self.model_name},
                timeout=1800
            )
            return response.status_code == 200
        except:
            return False
    
    def initialize(self):
        """初始化API"""
        if self.initialized:
            return True
        
        # 检查Ollama服务
        if not self.check_ollama_running():
            if not self.start_ollama_service():
                return False
        
        # 检查模型
        model_available, model_name = self.check_model_available()
        if not model_available:
            if not self.pull_model():
                return False
            model_available, model_name = self.check_model_available()
        
        if model_available:
            self.model_name = model_name
            self.initialized = True
            return True
        
        return False
    
    def generate_response(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7):
        """生成回答"""
        if not self.initialized:
            if not self.initialize():
                raise HTTPException(status_code=500, detail="模型初始化失败")
        
        try:
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens
                    }
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "response": result.get('response', ''),
                    "model": self.model_name,
                    "status": "success",
                    "tokens_used": result.get('eval_count', 0)
                }
            else:
                raise HTTPException(status_code=500, detail=f"生成失败: {response.status_code}")
                
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"生成异常: {str(e)}")

# 全局API实例
deepseek_api = DeepSeekAPI()

@app.on_event("startup")
async def startup_event():
    """启动时初始化"""
    print("🚀 启动DeepSeek API服务...")
    if deepseek_api.initialize():
        print("✅ DeepSeek API初始化成功")
    else:
        print("⚠️ DeepSeek API初始化失败，将在首次请求时重试")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "DeepSeek-R1 API服务",
        "model": "deepseek-r1-0528-qwen3-8b",
        "status": "running" if deepseek_api.initialized else "initializing"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy" if deepseek_api.initialized else "initializing",
        "model": deepseek_api.model_name,
        "ollama_running": deepseek_api.check_ollama_running()
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    try:
        result = deepseek_api.generate_response(
            prompt=request.message,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        return ChatResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate")
async def generate(request: ChatRequest):
    """生成接口（兼容性）"""
    return await chat(request)

@app.get("/models")
async def list_models():
    """列出可用模型"""
    try:
        response = requests.get(f"{deepseek_api.ollama_base_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            return {
                "models": [model['name'] for model in models],
                "current_model": deepseek_api.model_name
            }
        else:
            return {"models": [], "current_model": deepseek_api.model_name}
    except:
        return {"models": [], "current_model": deepseek_api.model_name}

@app.post("/initialize")
async def initialize_model():
    """手动初始化模型"""
    try:
        if deepseek_api.initialize():
            return {"status": "success", "message": "模型初始化成功"}
        else:
            raise HTTPException(status_code=500, detail="模型初始化失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🤖 启动DeepSeek-R1 FastAPI服务")
    print("📋 API文档: http://localhost:8000/docs")
    print("🔗 健康检查: http://localhost:8000/health")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
