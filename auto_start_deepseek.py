#!/usr/bin/env python3
"""
DeepSeek-R1 完全自动化启动器
一键启动LM Studio、加载模型、启动服务
"""

import os
import sys
import time
import subprocess
import requests
import json
from pathlib import Path
import psutil

class AutoDeepSeekLauncher:
    """DeepSeek自动启动器"""
    
    def __init__(self):
        self.lmstudio_paths = [
            Path.home() / "AppData/Local/LM Studio/LM Studio.exe",
            Path.home() / "AppData/Local/Programs/LM Studio/LM Studio.exe",
            Path("C:/Program Files/LM Studio/LM Studio.exe"),
            Path("C:/Program Files (x86)/LM Studio/LM Studio.exe")
        ]
        
        self.model_paths = [
            "C:/Users/<USER>/.lmstudio/models/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf",
            "C:/Users/<USER>/.lmstudio/models/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
        ]
        
        self.api_base = "http://localhost:1234/v1"
        self.lmstudio_exe = None
        self.selected_model = None
    
    def find_lmstudio(self):
        """查找LM Studio"""
        print("🔍 搜索LM Studio...")
        
        for path in self.lmstudio_paths:
            if path.exists():
                print(f"✅ 找到LM Studio: {path}")
                self.lmstudio_exe = str(path)
                return True
        
        print("❌ 未找到LM Studio安装")
        print("💡 请确保已安装LM Studio")
        return False
    
    def find_model(self):
        """查找可用的DeepSeek模型"""
        print("🔍 搜索DeepSeek模型...")
        
        for model_path in self.model_paths:
            if os.path.exists(model_path):
                size = os.path.getsize(model_path) / (1024**3)
                print(f"✅ 找到模型: {Path(model_path).name} ({size:.2f}GB)")
                self.selected_model = model_path
                return True
        
        print("❌ 未找到DeepSeek模型文件")
        print("💡 请确保模型已下载到LM Studio")
        return False
    
    def is_lmstudio_running(self):
        """检查LM Studio是否运行"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'LM Studio' in proc.info['name']:
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False
    
    def start_lmstudio(self):
        """启动LM Studio"""
        if self.is_lmstudio_running():
            print("✅ LM Studio已在运行")
            return True
        
        print("🚀 启动LM Studio...")
        try:
            # 启动LM Studio
            subprocess.Popen([self.lmstudio_exe], shell=True)
            
            # 等待启动
            print("⏳ 等待LM Studio启动...")
            for i in range(30):  # 等待30秒
                time.sleep(1)
                if self.is_lmstudio_running():
                    print("✅ LM Studio启动成功")
                    time.sleep(5)  # 额外等待界面完全加载
                    return True
                if i % 5 == 0:
                    print(f"   等待中... ({i}/30秒)")
            
            print("❌ LM Studio启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动LM Studio失败: {e}")
            return False
    
    def wait_for_api(self, timeout=60):
        """等待API服务就绪"""
        print("⏳ 等待LM Studio API服务启动...")
        
        for i in range(timeout):
            try:
                response = requests.get(f"{self.api_base}/models", timeout=2)
                if response.status_code == 200:
                    print("✅ LM Studio API服务已就绪")
                    return True
            except:
                pass
            
            time.sleep(1)
            if i % 10 == 0 and i > 0:
                print(f"   等待API服务... ({i}/{timeout}秒)")
        
        print("⚠️ API服务未自动启动，尝试自动配置...")
        return False
    
    def auto_load_model(self):
        """自动加载模型（通过LM Studio CLI或配置）"""
        print("🔄 尝试自动加载模型...")
        
        # 方法1: 尝试通过配置文件
        try:
            config_dir = Path.home() / ".lmstudio"
            if config_dir.exists():
                print("📁 找到LM Studio配置目录")
                # 这里可以尝试修改配置文件来自动加载模型
                # 但LM Studio的配置格式可能会变化，所以我们采用更稳妥的方法
        except Exception as e:
            print(f"⚠️ 配置方法失败: {e}")
        
        # 方法2: 等待用户手动操作，但提供清晰指导
        print("💡 请在LM Studio中进行以下操作:")
        print("   1. 点击左侧的 '🏠 Home' 或 '💬 Chat'")
        print("   2. 在模型选择区域，找到 'DeepSeek-R1' 模型")
        print("   3. 点击模型旁边的 '加载' 或 'Load' 按钮")
        print("   4. 等待模型加载完成（进度条消失）")
        print("   5. 模型加载完成后，API服务会自动启动")
        
        # 等待模型加载和API启动
        print("⏳ 等待您在LM Studio中加载模型...")
        
        for i in range(180):  # 等待3分钟
            try:
                response = requests.get(f"{self.api_base}/models", timeout=2)
                if response.status_code == 200:
                    models_data = response.json()
                    models = [model['id'] for model in models_data.get('data', [])]
                    if models:
                        print(f"✅ 检测到已加载的模型: {', '.join(models)}")
                        return True
            except:
                pass
            
            time.sleep(1)
            if i % 30 == 0 and i > 0:
                print(f"   仍在等待模型加载... ({i//60}分{i%60}秒)")
        
        print("❌ 等待超时，请检查LM Studio中的模型加载状态")
        return False
    
    def test_api(self):
        """测试API功能"""
        print("🧪 测试DeepSeek API...")
        
        try:
            # 获取模型列表
            response = requests.get(f"{self.api_base}/models", timeout=5)
            if response.status_code != 200:
                return False
            
            models_data = response.json()
            models = [model['id'] for model in models_data.get('data', [])]
            
            if not models:
                print("❌ 没有可用的模型")
                return False
            
            model_id = models[0]
            print(f"🎯 使用模型: {model_id}")
            
            # 测试聊天
            test_response = requests.post(
                f"{self.api_base}/chat/completions",
                json={
                    "model": model_id,
                    "messages": [{"role": "user", "content": "你好"}],
                    "max_tokens": 20,
                    "temperature": 0.7
                },
                timeout=30
            )
            
            if test_response.status_code == 200:
                result = test_response.json()
                if result.get('choices'):
                    content = result['choices'][0]['message']['content']
                    print(f"✅ API测试成功: {content[:50]}...")
                    return True
            
            print("❌ API测试失败")
            return False
            
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False
    
    def create_desktop_shortcut(self):
        """创建桌面快捷方式"""
        try:
            desktop = Path.home() / "Desktop"
            shortcut_path = desktop / "启动DeepSeek中医助手.bat"
            
            shortcut_content = f'''@echo off
cd /d "{os.getcwd()}"
python auto_start_deepseek.py
pause
'''
            
            with open(shortcut_path, 'w', encoding='utf-8') as f:
                f.write(shortcut_content)
            
            print(f"✅ 已创建桌面快捷方式: {shortcut_path}")
            
        except Exception as e:
            print(f"⚠️ 创建快捷方式失败: {e}")
    
    def launch(self):
        """完整启动流程"""
        print("🤖 DeepSeek-R1 完全自动化启动器")
        print("=" * 50)
        
        # 1. 查找LM Studio
        if not self.find_lmstudio():
            return False
        
        # 2. 查找模型
        if not self.find_model():
            return False
        
        # 3. 启动LM Studio
        if not self.start_lmstudio():
            return False
        
        # 4. 自动加载模型
        if not self.auto_load_model():
            print("⚠️ 自动加载失败，请手动在LM Studio中加载模型")
            return False
        
        # 5. 测试API
        if not self.test_api():
            print("⚠️ API测试失败，但LM Studio可能仍然可用")
        
        print("\n🎉 DeepSeek-R1启动完成!")
        print("=" * 50)
        print("✅ LM Studio已启动")
        print("✅ DeepSeek模型已加载")
        print("✅ API服务已就绪")
        print("🌐 API地址: http://localhost:1234")
        print("\n💡 现在可以在RAG系统中使用DeepSeek了!")
        print("🚀 访问: http://localhost:8507")
        
        # 创建快捷方式
        self.create_desktop_shortcut()
        
        return True

def main():
    """主函数"""
    launcher = AutoDeepSeekLauncher()
    
    try:
        success = launcher.launch()
        
        if success:
            print("\n🎊 恭喜！DeepSeek-R1已完全准备就绪！")
            print("现在您可以在RAG系统中享受智能的中医问答了！")
        else:
            print("\n❌ 启动过程中遇到问题")
            print("💡 请检查:")
            print("   1. LM Studio是否正确安装")
            print("   2. DeepSeek模型是否已下载")
            print("   3. 是否有足够的系统资源")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
