"""
内存清理后的干净启动脚本
"""
import subprocess
import sys
import psutil
import gc
import os

def optimize_environment():
    """优化环境设置"""
    # 设置环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:64'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
    os.environ['PYTHONHASHSEED'] = '0'
    
    # 强制垃圾回收
    gc.collect()

def main():
    print("🚀 RAG系统干净启动")
    print("=" * 40)
    
    # 显示当前内存状态
    memory = psutil.virtual_memory()
    print(f"💾 当前内存状态:")
    print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"   使用率: {memory.percent:.1f}%")
    
    if memory.percent > 80:
        print("⚠️ 内存使用仍然较高")
        return
    
    # 优化环境
    optimize_environment()
    print("✅ 环境优化完成")
    
    print("\n🌐 启动优化版RAG系统...")
    print("📝 访问地址: http://localhost:8505")
    print("⚡ 系统已优化，启动速度更快")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app_optimized.py", 
            "--server.address", "localhost",
            "--server.port", "8505",
            "--server.headless", "true",
            "--server.maxUploadSize", "20",
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
