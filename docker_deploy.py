#!/usr/bin/env python3
"""
Docker容器化部署脚本
一键打包增强版中医RAG系统为Docker镜像，方便移植到其他硬件
"""

import subprocess
import sys
import os
import time
from pathlib import Path
import json

def print_docker_banner():
    """打印Docker横幅"""
    print("=" * 80)
    print("🐳 增强版中医RAG系统 - Docker容器化部署")
    print("=" * 80)
    print("📦 功能:")
    print("   🔧 构建Docker镜像")
    print("   🚀 启动容器服务")
    print("   💾 数据持久化")
    print("   📤 导出镜像文件")
    print("   📥 导入镜像到其他机器")
    print("   🌐 ngrok远程访问")
    print("=" * 80)

def check_docker():
    """检查Docker环境"""
    print("🔍 检查Docker环境...")
    
    try:
        # 检查Docker是否安装
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker已安装: {result.stdout.strip()}")
        else:
            print("❌ Docker未安装")
            return False
        
        # 检查Docker是否运行
        result = subprocess.run(["docker", "info"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker服务正在运行")
        else:
            print("❌ Docker服务未运行，请启动Docker")
            return False
        
        return True
        
    except FileNotFoundError:
        print("❌ Docker未找到，请先安装Docker")
        print("💡 下载地址: https://www.docker.com/products/docker-desktop")
        return False

def prepare_build_context():
    """准备构建上下文"""
    print("\n📁 准备构建上下文...")
    
    # 检查必要文件
    required_files = [
        "enhanced_ultimate_tcm_system.py",
        "enhanced_requirements.txt",
        "Dockerfile"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 创建.dockerignore文件
    dockerignore_content = """
# 忽略不需要的文件
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git/
.mypy_cache/
.pytest_cache/
.hypothesis/

# 忽略大文件
*.gguf
*.bin
*.safetensors

# 忽略临时文件
enhanced_vector_db/
documents/
uploads/
online_cache/
logs/
"""
    
    with open(".dockerignore", "w", encoding="utf-8") as f:
        f.write(dockerignore_content.strip())
    
    print("✅ .dockerignore文件已创建")
    return True

def build_docker_image():
    """构建Docker镜像"""
    print("\n🔨 构建Docker镜像...")
    
    image_name = "enhanced-tcm-rag"
    image_tag = "latest"
    full_image_name = f"{image_name}:{image_tag}"
    
    try:
        print(f"📦 构建镜像: {full_image_name}")
        
        # 构建命令
        build_cmd = [
            "docker", "build",
            "-t", full_image_name,
            "-f", "Dockerfile",
            "."
        ]
        
        # 执行构建
        process = subprocess.Popen(
            build_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时显示构建输出
        for line in process.stdout:
            print(f"🔧 {line.strip()}")
        
        process.wait()
        
        if process.returncode == 0:
            print(f"✅ Docker镜像构建成功: {full_image_name}")
            return full_image_name
        else:
            print("❌ Docker镜像构建失败")
            return None
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return None

def run_docker_container(image_name):
    """运行Docker容器"""
    print(f"\n🚀 启动Docker容器...")
    
    container_name = "enhanced-tcm-rag-container"
    
    try:
        # 停止并删除已存在的容器
        subprocess.run(["docker", "stop", container_name], capture_output=True)
        subprocess.run(["docker", "rm", container_name], capture_output=True)
        
        # 创建数据卷目录
        volumes = [
            "./documents:/app/documents",
            "./enhanced_vector_db:/app/enhanced_vector_db",
            "./uploads:/app/uploads",
            "./logs:/app/logs"
        ]
        
        # 运行容器命令
        run_cmd = [
            "docker", "run",
            "-d",  # 后台运行
            "--name", container_name,
            "-p", "8503:8503",  # 端口映射
        ]
        
        # 添加数据卷
        for volume in volumes:
            run_cmd.extend(["-v", volume])
        
        # 添加镜像名
        run_cmd.append(image_name)
        
        # 执行运行命令
        result = subprocess.run(run_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            container_id = result.stdout.strip()
            print(f"✅ 容器启动成功")
            print(f"📦 容器ID: {container_id[:12]}")
            print(f"🌐 访问地址: http://localhost:8503")
            return container_id
        else:
            print(f"❌ 容器启动失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动容器出错: {e}")
        return None

def export_docker_image(image_name):
    """导出Docker镜像"""
    print(f"\n📤 导出Docker镜像...")
    
    export_file = "enhanced-tcm-rag-image.tar"
    
    try:
        print(f"💾 导出到文件: {export_file}")
        
        export_cmd = [
            "docker", "save",
            "-o", export_file,
            image_name
        ]
        
        result = subprocess.run(export_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            file_size = os.path.getsize(export_file) / (1024 * 1024)  # MB
            print(f"✅ 镜像导出成功")
            print(f"📁 文件: {export_file}")
            print(f"📊 大小: {file_size:.1f} MB")
            
            # 生成导入说明
            import_instructions = f"""
# 在其他机器上导入和运行镜像

## 1. 导入镜像
docker load -i {export_file}

## 2. 运行容器
docker run -d \\
  --name enhanced-tcm-rag \\
  -p 8503:8503 \\
  -v ./documents:/app/documents \\
  -v ./enhanced_vector_db:/app/enhanced_vector_db \\
  -v ./uploads:/app/uploads \\
  -v ./logs:/app/logs \\
  {image_name}

## 3. 访问应用
浏览器打开: http://localhost:8503

## 4. 查看日志
docker logs enhanced-tcm-rag

## 5. 停止容器
docker stop enhanced-tcm-rag

## 6. 删除容器
docker rm enhanced-tcm-rag
"""
            
            with open("docker_import_instructions.md", "w", encoding="utf-8") as f:
                f.write(import_instructions.strip())
            
            print("📝 导入说明已保存到: docker_import_instructions.md")
            return export_file
        else:
            print(f"❌ 镜像导出失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 导出过程出错: {e}")
        return None

def setup_ngrok_tunnel():
    """设置ngrok隧道"""
    print(f"\n🌐 设置ngrok远程访问...")
    
    try:
        # 检查ngrok
        result = subprocess.run(["ngrok", "version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ ngrok未安装")
            print("💡 请先安装ngrok: https://ngrok.com/download")
            return False
        
        print("✅ ngrok已安装")
        
        # 启动ngrok隧道
        print("🚀 启动ngrok隧道...")
        
        ngrok_cmd = [
            "ngrok", "http", "8503",
            "--authtoken", "your_ngrok_token_here"  # 需要替换
        ]
        
        # 这里应该在后台启动ngrok
        print("💡 请手动运行以下命令启动ngrok:")
        print(f"   {' '.join(ngrok_cmd)}")
        print("🔐 建议设置访问密码: MVP168918")
        
        return True
        
    except FileNotFoundError:
        print("❌ ngrok未找到")
        return False

def monitor_container(container_id):
    """监控容器状态"""
    print(f"\n📊 监控容器状态...")
    
    try:
        while True:
            # 检查容器状态
            result = subprocess.run([
                "docker", "ps", "--filter", f"id={container_id}", "--format", "table {{.Status}}"
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and "Up" in result.stdout:
                print(f"✅ 容器运行正常 - {time.strftime('%H:%M:%S')}")
            else:
                print(f"⚠️ 容器状态异常")
                break
            
            time.sleep(30)  # 每30秒检查一次
            
    except KeyboardInterrupt:
        print("\n🛑 停止监控")

def main():
    """主函数"""
    print_docker_banner()
    
    # 1. 检查Docker环境
    if not check_docker():
        input("按回车键退出...")
        return
    
    # 2. 准备构建上下文
    if not prepare_build_context():
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 80)
    print("🐳 Docker部署选项:")
    print("   1. 构建并运行容器")
    print("   2. 仅构建镜像")
    print("   3. 导出镜像文件")
    print("   4. 设置ngrok远程访问")
    print("   5. 监控容器状态")
    print("=" * 80)
    
    while True:
        choice = input("\n请选择操作 (1-5, q=退出): ").strip()
        
        if choice == "1":
            # 构建并运行
            image_name = build_docker_image()
            if image_name:
                container_id = run_docker_container(image_name)
                if container_id:
                    print("\n🎉 部署完成！")
                    print("🌐 访问地址: http://localhost:8503")
                    
                    monitor_choice = input("是否监控容器状态？(y/N): ")
                    if monitor_choice.lower() == 'y':
                        monitor_container(container_id)
            break
            
        elif choice == "2":
            # 仅构建
            image_name = build_docker_image()
            if image_name:
                print(f"\n✅ 镜像构建完成: {image_name}")
            break
            
        elif choice == "3":
            # 导出镜像
            image_name = "enhanced-tcm-rag:latest"
            export_file = export_docker_image(image_name)
            if export_file:
                print(f"\n✅ 镜像导出完成: {export_file}")
            break
            
        elif choice == "4":
            # 设置ngrok
            setup_ngrok_tunnel()
            break
            
        elif choice == "5":
            # 监控状态
            container_name = "enhanced-tcm-rag-container"
            result = subprocess.run([
                "docker", "ps", "--filter", f"name={container_name}", "--format", "{{.ID}}"
            ], capture_output=True, text=True)
            
            if result.stdout.strip():
                container_id = result.stdout.strip()
                monitor_container(container_id)
            else:
                print("❌ 未找到运行中的容器")
            break
            
        elif choice.lower() == "q":
            print("👋 退出")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
