{"timestamp": "2025-06-22 21:48:22.530141", "deleted_files_count": 251, "deleted_directories_count": 4, "deleted_files": ["app.py", "app_fast.py", "app_optimized.py", "app_simple.py", "app_ultra_fast.py", "auto_download_deepseek.py", "auto_install_deepseek.py", "auto_lmstudio_api.py", "auto_startup_mcp_api_system.py", "auto_start_deepseek.py", "batch_document_processor.py", "chatglm3_6b_api_server.py", "chatglm3_api.py", "chatglm3_mcp_server.py", "chatglm3_simple_api.py", "check_api_ready.py", "check_lmstudio_status.py", "check_vector_content.py", "cleanup_old_versions.py", "cleanup_system.py"], "deleted_directories": ["backend", "temp", "vector_db", "__pycache__"], "core_files_preserved": ["perfect_launcher.py", "test_vector_db.py", "deepseek_ollama_api.py", "perfect_unified_tcm_system.py", "SYSTEM_OPTIMIZATION_REPORT.md", "test_mcp_service.py", "test_deepseek_integration.py", "config.py", "test_system_integration.py", "README_PERFECT.md", "intelligent_mcp_service.py", "intelligent_rag_retriever.py", "check_vector_db.py", "ultimate_cleanup.py", "test_deepseek_simple.py", "requirements_perfect.txt"], "core_directories_preserved": ["uploads", "documents", "conversations", "models", "cache", "logs", "perfect_vector_db"], "cleanup_summary": {"total_deleted_files": 251, "total_deleted_dirs": 4, "core_files_count": 16, "core_dirs_count": 7}}