#!/usr/bin/env python3
"""
智能LM Studio管理器
自动检测模型状态并启动API服务
"""

import requests
import time
import json
import subprocess
import sys
from pathlib import Path
import psutil

class SmartLMStudioManager:
    """智能LM Studio管理器"""
    
    def __init__(self):
        self.api_base = "http://localhost:1234/v1"
        self.lmstudio_config_dir = Path.home() / ".lmstudio"
        
    def check_lmstudio_running(self):
        """检查LM Studio是否运行"""
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'LM Studio' in proc.info['name'] or 'lmstudio' in proc.info['name'].lower():
                    return True, proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False, None
    
    def check_api_status(self):
        """检查API状态"""
        try:
            response = requests.get(f"{self.api_base}/models", timeout=3)
            if response.status_code == 200:
                models_data = response.json()
                models = [model['id'] for model in models_data.get('data', [])]
                return True, models
            return False, []
        except:
            return False, []
    
    def check_model_loaded_in_lmstudio(self):
        """检查LM Studio中是否有模型加载（通过进程内存使用判断）"""
        running, pid = self.check_lmstudio_running()
        if not running:
            return False, "LM Studio未运行"
        
        try:
            proc = psutil.Process(pid)
            memory_mb = proc.memory_info().rss / 1024 / 1024
            
            # 如果LM Studio使用超过2GB内存，很可能已加载模型
            if memory_mb > 2048:
                return True, f"检测到模型已加载 (内存使用: {memory_mb:.0f}MB)"
            else:
                return False, f"可能未加载模型 (内存使用: {memory_mb:.0f}MB)"
        except:
            return False, "无法检测模型状态"
    
    def try_start_api_via_http(self):
        """尝试通过HTTP请求启动API服务"""
        try:
            # 尝试访问LM Studio的本地管理接口（如果存在）
            management_urls = [
                "http://localhost:1234/v1/models",
                "http://localhost:1234/health",
                "http://localhost:1234/status"
            ]
            
            for url in management_urls:
                try:
                    response = requests.get(url, timeout=2)
                    if response.status_code == 200:
                        return True, "API服务已激活"
                except:
                    continue
            
            return False, "无法通过HTTP启动API"
        except:
            return False, "HTTP启动失败"
    
    def simulate_lmstudio_actions(self):
        """模拟LM Studio界面操作来启动API"""
        try:
            # 方法1: 尝试发送键盘快捷键（如果LM Studio支持）
            import pyautogui
            pyautogui.FAILSAFE = False
            
            # 尝试切换到LM Studio窗口
            windows = []
            for proc in psutil.process_iter(['pid', 'name']):
                if 'LM Studio' in proc.info['name']:
                    windows.append(proc.info['pid'])
            
            if windows:
                # 这里可以尝试窗口操作，但比较复杂
                return False, "需要手动操作"
            
        except ImportError:
            pass
        except:
            pass
        
        return False, "无法自动操作界面"
    
    def wait_for_api_with_retry(self, max_wait=60):
        """等待API启动，带重试机制"""
        print(f"等待API服务启动 (最多{max_wait}秒)...")
        
        for i in range(max_wait):
            api_ok, models = self.check_api_status()
            if api_ok and models:
                return True, f"API就绪，模型: {', '.join(models)}"
            
            # 每10秒尝试一次激活
            if i % 10 == 0 and i > 0:
                print(f"尝试激活API服务... ({i}/{max_wait}秒)")
                self.try_start_api_via_http()
            
            time.sleep(1)
        
        return False, "API启动超时"
    
    def get_status_report(self):
        """获取完整状态报告"""
        report = {
            "lmstudio_running": False,
            "model_loaded": False,
            "api_available": False,
            "models": [],
            "memory_usage": 0,
            "recommendations": []
        }
        
        # 检查LM Studio
        running, pid = self.check_lmstudio_running()
        report["lmstudio_running"] = running
        
        if not running:
            report["recommendations"].append("请启动LM Studio")
            return report
        
        # 检查模型加载
        model_loaded, model_msg = self.check_model_loaded_in_lmstudio()
        report["model_loaded"] = model_loaded
        
        if pid:
            try:
                proc = psutil.Process(pid)
                report["memory_usage"] = proc.memory_info().rss / 1024 / 1024
            except:
                pass
        
        # 检查API
        api_ok, models = self.check_api_status()
        report["api_available"] = api_ok
        report["models"] = models
        
        # 生成建议
        if not model_loaded:
            report["recommendations"].append("请在LM Studio中加载DeepSeek模型")
        elif not api_ok:
            report["recommendations"].append("模型已加载但API未启动，请检查LM Studio设置")
        elif not models:
            report["recommendations"].append("API已启动但没有可用模型")
        
        return report
    
    def auto_fix(self):
        """自动修复常见问题"""
        print("🔧 开始自动诊断和修复...")
        
        report = self.get_status_report()
        
        print(f"LM Studio运行: {'✅' if report['lmstudio_running'] else '❌'}")
        print(f"模型已加载: {'✅' if report['model_loaded'] else '❌'}")
        print(f"API可用: {'✅' if report['api_available'] else '❌'}")
        print(f"内存使用: {report['memory_usage']:.0f}MB")
        
        if report["models"]:
            print(f"可用模型: {', '.join(report['models'])}")
        
        # 如果一切正常
        if report["api_available"] and report["models"]:
            print("✅ 系统状态正常，可以使用！")
            return True, "系统就绪"
        
        # 如果LM Studio运行且模型已加载，但API不可用
        if report["lmstudio_running"] and report["model_loaded"] and not report["api_available"]:
            print("🔄 检测到模型已加载但API未启动，尝试激活...")
            
            # 等待API启动
            success, message = self.wait_for_api_with_retry(30)
            if success:
                print("✅ API服务已激活！")
                return True, message
            else:
                print("⚠️ API未能自动启动")
                return False, "需要手动在LM Studio中启动服务器"
        
        # 其他情况的建议
        if report["recommendations"]:
            print("💡 建议操作:")
            for i, rec in enumerate(report["recommendations"], 1):
                print(f"   {i}. {rec}")
        
        return False, "需要手动操作"

def main():
    """主函数"""
    manager = SmartLMStudioManager()
    
    print("🤖 智能LM Studio管理器")
    print("=" * 40)
    
    try:
        success, message = manager.auto_fix()
        
        if success:
            print(f"\n🎉 成功: {message}")
            return True
        else:
            print(f"\n⚠️ 需要手动操作: {message}")
            return False
            
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
