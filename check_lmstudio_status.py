#!/usr/bin/env python3
"""
检查LM Studio状态
专门处理已启动LM Studio的情况
"""

import sys
import requests
import time

def check_api_status():
    """检查API状态"""
    try:
        print("检查LM Studio API状态...")
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        
        if response.status_code == 200:
            models_data = response.json()
            models = [model['id'] for model in models_data.get('data', [])]
            
            if models:
                print(f"API正常，找到模型: {', '.join(models)}")
                return True, f"API就绪，模型: {models[0]}"
            else:
                print("API连接成功但没有加载模型")
                return False, "请在LM Studio中加载DeepSeek模型"
        else:
            print(f"API响应异常: {response.status_code}")
            return False, "API服务异常"
            
    except requests.exceptions.ConnectionError:
        print("无法连接到API服务")
        return False, "API服务未启动，请在LM Studio中启动服务器"
    except Exception as e:
        print(f"检查API时出错: {e}")
        return False, f"API检查失败: {str(e)}"

def wait_for_model_load():
    """等待模型加载"""
    print("等待模型加载...")
    
    for i in range(60):  # 等待1分钟
        success, message = check_api_status()
        if success:
            return True, message
        
        time.sleep(1)
        if i % 10 == 0 and i > 0:
            print(f"仍在等待... ({i}/60秒)")
    
    return False, "等待超时"

def main():
    """主函数"""
    print("=== LM Studio状态检查 ===")
    
    # 立即检查状态
    success, message = check_api_status()
    
    if success:
        print("状态: 就绪")
        print(f"结果: {message}")
        return True
    else:
        print("状态: 未就绪")
        print(f"问题: {message}")
        
        # 如果是模型未加载，等待一下
        if "加载" in message:
            print("尝试等待模型加载...")
            success, message = wait_for_model_load()
            
            if success:
                print("状态: 就绪")
                print(f"结果: {message}")
                return True
        
        print("建议: 请在LM Studio中手动加载DeepSeek模型")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
