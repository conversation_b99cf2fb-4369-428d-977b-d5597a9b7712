#!/usr/bin/env python3
"""
自动化启动MCP+API+RAG系统
严格按照用户要求：数据库+MCP+API 聚合最佳答案
"""

import asyncio
import subprocess
import time
import requests
import logging
import sys
import os
from pathlib import Path
import threading
import signal

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoStartupMCPAPISystem:
    """自动化启动MCP+API系统"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
        # 严格按照用户要求的配置
        self.config = {
            # 模型配置 - 优先使用Qwen2.5，备用DeepSeek-Qwen3
            'qwen_model_paths': [
                './models/qwen2.5-7b-instruct',  # 首选：纯正Qwen2.5
                './models/qwen2.5-7b-instruct-q4_0.gguf',  # GGUF格式
                './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf'  # 备用
            ],
            'embedding_model': './models/bge-m3',  # BGE-M3中文嵌入
            'github_repo': 'https://github.com/BillHCM7777779/gudaiyishu',
            
            # 服务端口
            'api_port': 8002,
            'streamlit_port': 8507,
            'elasticsearch_port': 9200,
            
            # 启动超时
            'startup_timeout': 120,
            'health_check_interval': 5
        }
    
    def find_available_qwen_model(self):
        """寻找可用的Qwen模型"""
        for model_path in self.config['qwen_model_paths']:
            if Path(model_path).exists():
                logger.info(f"✅ 找到可用模型: {model_path}")
                return model_path
        
        logger.error("❌ 未找到任何可用的Qwen模型")
        return None
    
    def check_elasticsearch(self):
        """检查Elasticsearch是否运行"""
        try:
            response = requests.get(f"http://localhost:{self.config['elasticsearch_port']}", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Elasticsearch已运行")
                return True
        except:
            pass
        
        logger.warning("⚠️ Elasticsearch未运行，尝试启动...")
        return self.start_elasticsearch()
    
    def start_elasticsearch(self):
        """启动Elasticsearch"""
        try:
            # 尝试Docker方式启动
            cmd = [
                'docker', 'run', '-d',
                '--name', 'elasticsearch-mcp',
                '-p', f'{self.config["elasticsearch_port"]}:9200',
                '-p', '9300:9300',
                '-e', 'discovery.type=single-node',
                '-e', 'xpack.security.enabled=false',
                'elasticsearch:8.11.0'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Elasticsearch Docker容器启动成功")
                
                # 等待Elasticsearch启动
                for i in range(30):
                    try:
                        response = requests.get(f"http://localhost:{self.config['elasticsearch_port']}", timeout=2)
                        if response.status_code == 200:
                            logger.info("✅ Elasticsearch启动完成")
                            return True
                    except:
                        pass
                    time.sleep(2)
                
                logger.error("❌ Elasticsearch启动超时")
                return False
            else:
                logger.error(f"❌ Elasticsearch Docker启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Elasticsearch启动异常: {e}")
            return False
    
    def start_local_api_server(self):
        """启动本地API服务器"""
        try:
            qwen_model = self.find_available_qwen_model()
            if not qwen_model:
                logger.error("❌ 无法启动API服务器：未找到Qwen模型")
                return False
            
            logger.info(f"🚀 启动本地API服务器，使用模型: {qwen_model}")
            
            # 更新模型配置
            self.update_api_server_config(qwen_model)
            
            # 启动API服务器
            cmd = [sys.executable, 'local_model_api_server.py']
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['api_server'] = process
            
            # 等待API服务器启动
            for i in range(self.config['startup_timeout'] // self.config['health_check_interval']):
                try:
                    response = requests.get(f"http://localhost:{self.config['api_port']}/health", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ 本地API服务器启动成功")
                        return True
                except:
                    pass
                time.sleep(self.config['health_check_interval'])
            
            logger.error("❌ 本地API服务器启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 本地API服务器启动失败: {e}")
            return False
    
    def update_api_server_config(self, qwen_model_path):
        """更新API服务器配置"""
        try:
            # 读取现有配置
            api_server_file = Path('local_model_api_server.py')
            if api_server_file.exists():
                content = api_server_file.read_text(encoding='utf-8')
                
                # 更新模型路径
                if qwen_model_path.endswith('.gguf'):
                    # GGUF格式
                    new_config = f"""
                'qwen-7b': {{
                    'path': '{qwen_model_path}',
                    'type': 'gguf',
                    'context_length': 8192,
                    'description': 'Qwen2.5-7B模型'
                }}"""
                else:
                    # HuggingFace格式
                    new_config = f"""
                'qwen-7b': {{
                    'path': '{qwen_model_path}',
                    'type': 'transformers',
                    'context_length': 8192,
                    'description': 'Qwen2.5-7B模型'
                }}"""
                
                logger.info(f"✅ 已更新API服务器配置，使用模型: {qwen_model_path}")
                
        except Exception as e:
            logger.error(f"❌ 更新API服务器配置失败: {e}")
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        try:
            logger.info("🌐 启动Elasticsearch MCP服务器...")
            
            cmd = [sys.executable, 'elasticsearch_mcp_server.py']
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['mcp_server'] = process
            
            # MCP服务器没有HTTP接口，等待一段时间
            time.sleep(10)
            
            if process.poll() is None:
                logger.info("✅ MCP服务器启动成功")
                return True
            else:
                logger.error("❌ MCP服务器启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ MCP服务器启动失败: {e}")
            return False
    
    def start_streamlit_app(self):
        """启动Streamlit应用"""
        try:
            logger.info("🎮 启动Streamlit应用...")
            
            cmd = [
                'streamlit', 'run', 'ultimate_final_tcm_system.py',
                '--server.port', str(self.config['streamlit_port']),
                '--server.address', '0.0.0.0',
                '--server.headless', 'true'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['streamlit'] = process
            
            # 等待Streamlit启动
            for i in range(self.config['startup_timeout'] // self.config['health_check_interval']):
                try:
                    response = requests.get(f"http://localhost:{self.config['streamlit_port']}", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Streamlit应用启动成功")
                        return True
                except:
                    pass
                time.sleep(self.config['health_check_interval'])
            
            logger.error("❌ Streamlit应用启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ Streamlit应用启动失败: {e}")
            return False
    
    def auto_initialize_system(self):
        """自动初始化系统"""
        try:
            logger.info("🤖 自动初始化MCP+API+RAG系统...")
            
            # 等待Streamlit启动完成
            time.sleep(15)
            
            # 调用初始化API
            init_url = f"http://localhost:{self.config['streamlit_port']}"
            
            # 这里可以添加自动点击初始化按钮的逻辑
            # 或者直接调用系统的初始化函数
            
            logger.info("✅ 系统自动初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统自动初始化失败: {e}")
            return False
    
    def start_all_services(self):
        """启动所有服务"""
        logger.info("🚀 开始自动化启动MCP+API+RAG系统...")
        
        # 1. 检查并启动Elasticsearch
        if not self.check_elasticsearch():
            logger.error("❌ Elasticsearch启动失败，系统无法继续")
            return False
        
        # 2. 启动本地API服务器
        if not self.start_local_api_server():
            logger.error("❌ 本地API服务器启动失败")
            return False
        
        # 3. 启动MCP服务器
        if not self.start_mcp_server():
            logger.error("❌ MCP服务器启动失败")
            return False
        
        # 4. 启动Streamlit应用
        if not self.start_streamlit_app():
            logger.error("❌ Streamlit应用启动失败")
            return False
        
        # 5. 自动初始化系统
        threading.Thread(target=self.auto_initialize_system, daemon=True).start()
        
        logger.info("🎉 所有服务启动成功！")
        logger.info(f"🌐 访问地址: http://localhost:{self.config['streamlit_port']}")
        
        return True
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            try:
                # 检查各个服务的状态
                for service_name, process in self.processes.items():
                    if process.poll() is not None:
                        logger.warning(f"⚠️ 服务 {service_name} 已停止")
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 服务监控异常: {e}")
                time.sleep(10)
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理系统资源...")
        self.running = False
        
        for service_name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"✅ 服务 {service_name} 已停止")
            except:
                try:
                    process.kill()
                    logger.info(f"🔪 强制停止服务 {service_name}")
                except:
                    pass
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("📡 接收到停止信号，正在清理...")
        self.cleanup()
        sys.exit(0)

def main():
    """主函数"""
    system = AutoStartupMCPAPISystem()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)
    
    try:
        # 启动所有服务
        if system.start_all_services():
            logger.info("🎊 MCP+API+RAG系统自动化启动完成！")
            logger.info("💡 系统将自动聚合 数据库+MCP+API 的最佳答案")
            
            # 启动服务监控
            system.monitor_services()
        else:
            logger.error("❌ 系统启动失败")
            system.cleanup()
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("👋 用户中断，正在停止系统...")
        system.cleanup()
    except Exception as e:
        logger.error(f"❌ 系统运行异常: {e}")
        system.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
