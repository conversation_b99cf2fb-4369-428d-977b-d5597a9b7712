#!/usr/bin/env python3
"""
MCP Elasticsearch服务器
集成Elasticsearch工具到MCP系统，提供智能全文检索功能
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

# MCP相关导入
try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource, Tool, TextContent, ImageContent, EmbeddedResource,
        CallToolRequest, CallToolResult, ListResourcesRequest, ListResourcesResult,
        ListToolsRequest, ListToolsResult, ReadResourceRequest, ReadResourceResult
    )
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    print("❌ MCP未安装，请运行: pip install mcp")

# Elasticsearch相关导入
try:
    from elasticsearch import Elasticsearch
    ES_AVAILABLE = True
except ImportError:
    ES_AVAILABLE = False
    print("❌ Elasticsearch未安装，请运行: pip install elasticsearch")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatGLM3MCPServer:
    """ChatGLM3-6B MCP服务器"""
    
    def __init__(self):
        self.server = Server("chatglm3-tcm-assistant")
        self.chatglm_api_url = "http://127.0.0.1:8004"
        
        # 中医知识库
        self.tcm_resources = {
            "basic_theory": {
                "name": "中医基础理论",
                "description": "中医学基本理论体系",
                "content": {
                    "阴阳学说": "阴阳是中医学的基本理论，认为宇宙万物都存在阴阳两个对立统一的方面",
                    "五行学说": "五行（木火土金水）用来说明事物的属性和相互关系",
                    "气血津液": "气血津液是构成人体和维持生命活动的基本物质",
                    "脏腑学说": "脏腑是人体内脏器官的总称，包括五脏六腑"
                }
            },
            "diagnosis": {
                "name": "中医诊断",
                "description": "中医诊断方法和技术",
                "content": {
                    "望诊": "通过观察患者的神色、形态、舌象等进行诊断",
                    "闻诊": "通过听声音、嗅气味进行诊断",
                    "问诊": "通过询问症状、病史等获取信息",
                    "切诊": "通过触摸脉搏、按压穴位等进行诊断"
                }
            },
            "treatment": {
                "name": "中医治疗",
                "description": "中医治疗方法和原则",
                "content": {
                    "中药治疗": "使用中草药配方治疗疾病",
                    "针灸治疗": "通过针刺和艾灸调节经络气血",
                    "推拿按摩": "通过手法按摩调理身体",
                    "食疗养生": "通过饮食调养身体健康"
                }
            }
        }
        
        # 对话历史
        self.conversation_history = []
        
        self.setup_handlers()
    
    def setup_handlers(self):
        """设置MCP处理器"""
        
        @self.server.list_resources()
        async def handle_list_resources() -> ListResourcesResult:
            """列出可用资源"""
            resources = []
            for resource_id, resource_data in self.tcm_resources.items():
                resources.append(Resource(
                    uri=f"tcm://{resource_id}",
                    name=resource_data["name"],
                    description=resource_data["description"],
                    mimeType="application/json"
                ))
            
            return ListResourcesResult(resources=resources)
        
        @self.server.read_resource()
        async def handle_read_resource(request: ReadResourceRequest) -> ReadResourceResult:
            """读取资源内容"""
            uri = request.uri
            if uri.startswith("tcm://"):
                resource_id = uri[6:]  # 移除 "tcm://" 前缀
                
                if resource_id in self.tcm_resources:
                    resource_data = self.tcm_resources[resource_id]
                    content = json.dumps(resource_data["content"], ensure_ascii=False, indent=2)
                    
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=content
                        )]
                    )
            
            raise ValueError(f"未知资源: {uri}")
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """列出可用工具"""
            tools = [
                Tool(
                    name="chat_with_chatglm3",
                    description="与ChatGLM3-6B进行中医相关对话",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "description": "要发送给ChatGLM3-6B的消息"
                            },
                            "temperature": {
                                "type": "number",
                                "description": "生成温度 (0.0-1.0)",
                                "default": 0.7
                            },
                            "max_tokens": {
                                "type": "integer",
                                "description": "最大生成token数",
                                "default": 500
                            }
                        },
                        "required": ["message"]
                    }
                ),
                Tool(
                    name="search_tcm_knowledge",
                    description="搜索中医知识库",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索关键词"
                            },
                            "category": {
                                "type": "string",
                                "description": "知识类别 (basic_theory, diagnosis, treatment)",
                                "enum": ["basic_theory", "diagnosis", "treatment"]
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="get_conversation_history",
                    description="获取对话历史",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {
                                "type": "integer",
                                "description": "返回的历史记录数量",
                                "default": 10
                            }
                        }
                    }
                ),
                Tool(
                    name="clear_conversation_history",
                    description="清除对话历史",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                )
            ]
            
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
            """处理工具调用"""
            try:
                if request.name == "chat_with_chatglm3":
                    return await self.chat_with_chatglm3(request.arguments)
                elif request.name == "search_tcm_knowledge":
                    return await self.search_tcm_knowledge(request.arguments)
                elif request.name == "get_conversation_history":
                    return await self.get_conversation_history(request.arguments)
                elif request.name == "clear_conversation_history":
                    return await self.clear_conversation_history(request.arguments)
                else:
                    raise ValueError(f"未知工具: {request.name}")
                    
            except Exception as e:
                logger.error(f"工具调用失败: {e}")
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"工具调用失败: {str(e)}"
                    )],
                    isError=True
                )
    
    async def chat_with_chatglm3(self, arguments: Dict[str, Any]) -> CallToolResult:
        """与ChatGLM3-6B对话"""
        message = arguments.get("message", "")
        temperature = arguments.get("temperature", 0.7)
        max_tokens = arguments.get("max_tokens", 500)
        
        if not message:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text="错误：消息不能为空"
                )],
                isError=True
            )
        
        try:
            # 构建对话请求
            chat_data = {
                "model": "chatglm3-6b",
                "messages": [{"role": "user", "content": message}],
                "temperature": temperature,
                "max_tokens": max_tokens
            }
            
            # 调用ChatGLM3-6B API
            response = requests.post(
                f"{self.chatglm_api_url}/v1/chat/completions",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                assistant_message = result["choices"][0]["message"]["content"]
                
                # 保存对话历史
                self.conversation_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "user": message,
                    "assistant": assistant_message,
                    "model": "ChatGLM3-6B"
                })
                
                # 限制历史记录数量
                if len(self.conversation_history) > 50:
                    self.conversation_history = self.conversation_history[-50:]
                
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"ChatGLM3-6B回答：\n\n{assistant_message}"
                    )]
                )
            else:
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"API调用失败: {response.status_code} - {response.text}"
                    )],
                    isError=True
                )
                
        except Exception as e:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"ChatGLM3-6B调用失败: {str(e)}"
                )],
                isError=True
            )
    
    async def search_tcm_knowledge(self, arguments: Dict[str, Any]) -> CallToolResult:
        """搜索中医知识库"""
        query = arguments.get("query", "").lower()
        category = arguments.get("category")
        
        if not query:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text="错误：搜索关键词不能为空"
                )],
                isError=True
            )
        
        results = []
        search_categories = [category] if category else self.tcm_resources.keys()
        
        for cat in search_categories:
            if cat in self.tcm_resources:
                resource = self.tcm_resources[cat]
                for key, value in resource["content"].items():
                    if query in key.lower() or query in value.lower():
                        results.append({
                            "category": resource["name"],
                            "topic": key,
                            "content": value
                        })
        
        if results:
            result_text = "搜索结果：\n\n"
            for i, result in enumerate(results, 1):
                result_text += f"{i}. 【{result['category']} - {result['topic']}】\n"
                result_text += f"   {result['content']}\n\n"
        else:
            result_text = f"未找到与 '{query}' 相关的中医知识。"
        
        return CallToolResult(
            content=[TextContent(
                type="text",
                text=result_text
            )]
        )
    
    async def get_conversation_history(self, arguments: Dict[str, Any]) -> CallToolResult:
        """获取对话历史"""
        limit = arguments.get("limit", 10)
        
        recent_history = self.conversation_history[-limit:] if self.conversation_history else []
        
        if not recent_history:
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text="暂无对话历史"
                )]
            )
        
        history_text = f"最近 {len(recent_history)} 条对话记录：\n\n"
        for i, conv in enumerate(recent_history, 1):
            timestamp = conv["timestamp"][:19]  # 只显示到秒
            history_text += f"{i}. [{timestamp}]\n"
            history_text += f"   用户: {conv['user']}\n"
            history_text += f"   助手: {conv['assistant'][:100]}...\n\n"
        
        return CallToolResult(
            content=[TextContent(
                type="text",
                text=history_text
            )]
        )
    
    async def clear_conversation_history(self, arguments: Dict[str, Any]) -> CallToolResult:
        """清除对话历史"""
        cleared_count = len(self.conversation_history)
        self.conversation_history.clear()
        
        return CallToolResult(
            content=[TextContent(
                type="text",
                text=f"已清除 {cleared_count} 条对话记录"
            )]
        )
    
    async def run(self):
        """运行MCP服务器"""
        if not MCP_AVAILABLE:
            logger.error("MCP未安装，无法启动服务器")
            return
        
        logger.info("启动ChatGLM3-6B MCP服务器...")
        logger.info("功能:")
        logger.info("  - 与ChatGLM3-6B对话")
        logger.info("  - 搜索中医知识库")
        logger.info("  - 管理对话历史")
        logger.info("  - 提供中医资源")
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="chatglm3-tcm-assistant",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None
                    )
                )
            )

async def main():
    """主函数"""
    server = ChatGLM3MCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
