#!/usr/bin/env python3
"""
重启TCM系统 - 清理所有服务并重新启动
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def kill_all_services():
    """停止所有相关服务"""
    print("🛑 停止所有相关服务...")
    
    try:
        # 停止所有Python进程（谨慎使用）
        subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                      capture_output=True, text=True)
        print("✅ 已停止所有Python服务")
        time.sleep(2)
    except Exception as e:
        print(f"⚠️ 停止服务时出现问题: {e}")

def check_port_status():
    """检查端口状态"""
    print("\n🔍 检查端口状态...")
    
    ports_to_check = [8003, 8004, 8005, 8006, 8501]
    
    for port in ports_to_check:
        try:
            response = requests.get(f'http://localhost:{port}/health', timeout=2)
            print(f"  端口 {port}: ✅ 服务运行中")
        except:
            print(f"  端口 {port}: ❌ 无服务")

def start_intelligent_mcp():
    """启动智能MCP服务"""
    print("\n🚀 启动智能MCP服务...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        
        print(f"✅ 智能MCP服务已启动 (PID: {process.pid})")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查服务状态
        try:
            response = requests.get('http://localhost:8006/health', timeout=5)
            if response.status_code == 200:
                print("✅ 智能MCP服务健康检查通过")
                return True
            else:
                print(f"⚠️ 服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 启动智能MCP服务失败: {e}")
        return False

def test_intelligent_responses():
    """快速测试智能回答"""
    print("\n🧪 快速测试智能回答...")
    
    test_query = "失眠多梦怎么办"
    
    try:
        mcp_request = {
            "method": "search_knowledge",
            "params": {
                "query": test_query,
                "domain": "medical",
                "max_results": 1
            },
            "id": "test_1"
        }
        
        response = requests.post(
            'http://localhost:8006/mcp',
            json=mcp_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result and result['result'].get('results'):
                first_result = result['result']['results'][0]
                title = first_result.get('title', '')
                content = first_result.get('content', '')
                
                print(f"✅ 测试成功")
                print(f"   问题: {test_query}")
                print(f"   回答: {title}")
                print(f"   内容: {content[:100]}...")
                
                # 检查是否针对性回答
                if '失眠' in title or '失眠' in content:
                    print("🎉 回答具有针对性，智能系统工作正常")
                    return True
                else:
                    print("⚠️ 回答可能不够针对性")
                    return False
            else:
                print("❌ 无返回结果")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 TCM系统完整重启程序")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    if not (current_dir / "ultimate_final_tcm_system.py").exists():
        print("❌ 未找到主程序文件，请在正确目录下运行")
        input("按回车键退出...")
        return False
    
    # 1. 停止所有服务
    kill_all_services()
    
    # 2. 检查端口状态
    check_port_status()
    
    # 3. 启动智能MCP服务
    if not start_intelligent_mcp():
        print("❌ 智能MCP服务启动失败")
        input("按回车键退出...")
        return False
    
    # 4. 测试智能回答
    if not test_intelligent_responses():
        print("⚠️ 智能回答测试未完全通过，但系统可能仍可使用")
    
    # 5. 启动主系统
    print("\n🌐 启动主系统...")
    print("💡 提示：系统将在新窗口中启动")
    print("🔗 访问地址：http://localhost:8501")
    print("🎯 测试问题：失眠多梦怎么办")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_final_tcm_system.py",
            "--server.headless", "false",
            "--server.port", "8501"
        ])
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 启动主系统失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 目标：确保智能回答系统正常工作")
    print("🔧 方法：完全重启所有服务并测试")
    print()
    
    success = main()
    
    if success:
        print("\n✅ 系统重启完成")
    else:
        print("\n❌ 系统重启失败")
        print("💡 建议：检查端口占用和服务状态")
    
    input("按回车键退出...")
