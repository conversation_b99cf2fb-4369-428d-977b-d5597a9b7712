#!/usr/bin/env python3
"""
获取本机IP地址，方便手机访问
"""
import socket
import subprocess
import platform

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 方法1: 连接到外部地址获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        try:
            # 方法2: 获取主机名对应的IP
            hostname = socket.gethostname()
            ip = socket.gethostbyname(hostname)
            if ip.startswith("127."):
                return None
            return ip
        except:
            return None

def get_all_ips():
    """获取所有网络接口的IP地址"""
    ips = []
    
    try:
        if platform.system() == "Windows":
            # Windows系统
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            
            for line in lines:
                if 'IPv4' in line and '地址' in line:
                    ip = line.split(':')[-1].strip()
                    if ip and not ip.startswith('127.') and '.' in ip:
                        ips.append(ip)
        else:
            # Linux/Mac系统
            result = subprocess.run(['ifconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for line in lines:
                if 'inet ' in line and 'netmask' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'inet' and i + 1 < len(parts):
                            ip = parts[i + 1]
                            if not ip.startswith('127.') and '.' in ip:
                                ips.append(ip)
    except:
        pass
    
    return ips

def main():
    print("🌐 获取网络访问地址")
    print("=" * 50)
    
    # 获取主要IP
    main_ip = get_local_ip()
    if main_ip:
        print(f"📱 主要IP地址: {main_ip}")
        print(f"🔗 手机访问地址: http://{main_ip}:8006")
        print()
    
    # 获取所有IP
    all_ips = get_all_ips()
    if all_ips:
        print("📋 所有可用IP地址:")
        for i, ip in enumerate(all_ips, 1):
            print(f"   {i}. http://{ip}:8006")
        print()
    
    print("💡 使用说明:")
    print("   1. 确保手机和电脑在同一WiFi网络")
    print("   2. 在手机浏览器中输入上述任一地址")
    print("   3. 支持语音输入和播放功能")
    print("   4. 界面已优化移动端体验")
    print()
    
    # 生成二维码（如果有qrcode库）
    try:
        import qrcode
        if main_ip:
            url = f"http://{main_ip}:8006"
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(url)
            qr.make(fit=True)
            
            print("📱 二维码:")
            qr.print_ascii(invert=True)
            print(f"扫描二维码直接访问: {url}")
    except ImportError:
        print("💡 安装qrcode库可生成二维码: pip install qrcode[pil]")

if __name__ == "__main__":
    main()
