#!/usr/bin/env python3
"""
最终测试所有修复 - 验证向量检索和MCP检索
"""

import time
import requests

def test_vector_search():
    """测试向量检索修复"""
    print("🧪 测试向量检索修复...")
    
    try:
        from ultimate_final_tcm_system import UltimateVectorDatabase, CONFIG
        
        print(f"当前阈值: {CONFIG['MIN_RELEVANCE_SCORE']}")
        
        # 创建向量数据库
        vector_db = UltimateVectorDatabase()
        
        if vector_db.initialize():
            print("✅ 向量数据库初始化成功")
            
            # 测试查询
            query = "肾虚脾虚怎么治疗"
            results = vector_db.search(query, top_k=5)
            
            print(f"查询: {query}")
            print(f"结果数量: {len(results)}")
            
            if results:
                print("✅ 向量检索修复成功！")
                for i, result in enumerate(results[:3], 1):
                    similarity = result.get('similarity', 0)
                    source = result.get('source', 'unknown')
                    content = result.get('content', '')
                    print(f"  {i}. 相似度: {similarity:.4f}")
                    print(f"     来源: {source}")
                    print(f"     内容: {content[:80]}...")
                    print()
                return True
            else:
                print("❌ 向量检索仍然无结果")
                return False
        else:
            print("❌ 向量数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 向量检索测试失败: {e}")
        return False

def test_mcp_search():
    """测试MCP检索修复"""
    print("\n🧪 测试MCP检索修复...")
    
    try:
        # 等待MCP服务启动
        time.sleep(2)
        
        # 检查服务健康状态
        try:
            response = requests.get('http://localhost:8004/health', timeout=5)
            if response.status_code != 200:
                print("❌ MCP服务未运行")
                return False
            print("✅ MCP服务运行正常")
        except:
            print("❌ 无法连接MCP服务")
            return False
        
        # 测试肾虚脾虚查询
        mcp_request = {
            "method": "search_knowledge",
            "params": {
                "query": "肾虚脾虚怎么治疗",
                "domain": "medical",
                "max_results": 5,
                "search_type": "comprehensive"
            },
            "id": "test_kidney_spleen_fix"
        }
        
        response = requests.post(
            'http://localhost:8004/mcp',
            json=mcp_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result:
                results = result['result'].get('results', [])
                print(f"✅ MCP检索成功: {len(results)} 条结果")
                
                # 检查是否包含肾虚脾虚相关内容
                kidney_spleen_found = False
                for i, res in enumerate(results, 1):
                    title = res.get('title', 'unknown')
                    score = res.get('score', 0)
                    content = res.get('content', '')
                    
                    print(f"  {i}. {title} (评分: {score:.3f})")
                    print(f"     内容: {content[:100]}...")
                    
                    # 检查是否包含肾虚脾虚相关内容
                    if ('肾虚' in content and '脾虚' in content) or '肾脾双补' in content:
                        print(f"     ✅ 包含肾虚脾虚相关内容")
                        kidney_spleen_found = True
                    elif '肾虚' in content or '脾虚' in content:
                        print(f"     ⚠️ 部分相关")
                    else:
                        print(f"     ❌ 不相关")
                    print()
                
                if kidney_spleen_found:
                    print("✅ MCP检索修复成功！找到肾虚脾虚相关内容")
                    return True
                else:
                    print("⚠️ MCP检索有结果但内容不够相关")
                    return False
            else:
                print(f"❌ MCP返回错误: {result.get('error', 'unknown')}")
                return False
        else:
            print(f"❌ MCP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MCP检索测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🧪 测试系统集成...")
    
    try:
        from ultimate_final_tcm_system import initialize_ultimate_components
        
        # 初始化组件
        components = initialize_ultimate_components()
        
        # 检查各组件状态
        vector_ok = components['vector_db'].initialized if components['vector_db'] else False
        mcp_ok = components['mcp_retriever'].initialized if components['mcp_retriever'] else False
        
        print(f"向量数据库: {'✅' if vector_ok else '❌'}")
        print(f"MCP检索器: {'✅' if mcp_ok else '❌'}")
        
        if vector_ok and mcp_ok:
            print("✅ 系统集成测试成功")
            return True
        else:
            print("⚠️ 部分组件未正常初始化")
            return False
            
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 最终修复验证测试")
    print("=" * 50)
    
    # 测试项目
    tests = [
        ("向量检索修复", test_vector_search),
        ("MCP检索修复", test_mcp_search),
        ("系统集成", test_system_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{'='*20} {name} {'='*20}")
        if test_func():
            passed += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有修复验证成功！")
        print("\n💡 现在可以运行:")
        print("   streamlit run ultimate_final_tcm_system.py")
        print("   然后测试查询: '肾虚脾虚怎么治疗'")
    else:
        print("⚠️ 部分修复需要进一步调整")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
