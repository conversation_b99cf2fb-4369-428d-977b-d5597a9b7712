#!/usr/bin/env python3
"""
下载兼容的DeepSeek-R1-0528-Qwen3-8B模型
使用barto<PERSON>的高质量GGUF版本
"""

import os
import sys
import requests
from pathlib import Path
import time

def download_deepseek_r1():
    """下载兼容的DeepSeek-R1模型"""
    
    print("🤖 DeepSeek-R1-0528-Qwen3-8B 兼容版本下载器")
    print("=" * 60)
    
    # 推荐的模型版本（按兼容性排序）
    models = {
        "Q4_0": {
            "url": "https://huggingface.co/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf",
            "size": "4.79GB",
            "description": "最佳兼容性，推荐首选",
            "compatibility": "⭐⭐⭐⭐⭐"
        },
        "IQ4_XS": {
            "url": "https://huggingface.co/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-IQ4_XS.gguf",
            "size": "4.56GB",
            "description": "更小文件，相似性能",
            "compatibility": "⭐⭐⭐⭐⭐"
        },
        "Q4_K_S": {
            "url": "https://huggingface.co/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_K_S.gguf",
            "size": "4.80GB", 
            "description": "稍低质量但节省空间",
            "compatibility": "⭐⭐⭐⭐"
        },
        "Q3_K_L": {
            "url": "https://huggingface.co/bartowski/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf",
            "size": "4.43GB",
            "description": "更小文件，适合低内存",
            "compatibility": "⭐⭐⭐⭐⭐"
        }
    }
    
    print("📋 可用的DeepSeek-R1兼容版本:")
    print()
    for i, (name, info) in enumerate(models.items(), 1):
        print(f"{i}. DeepSeek-R1-8B-{name}")
        print(f"   大小: {info['size']}")
        print(f"   兼容性: {info['compatibility']}")
        print(f"   描述: {info['description']}")
        print()
    
    # 推荐选择
    print("💡 推荐: Q4_0 版本（最佳兼容性）")
    print("🎯 您的32GB内存完全足够运行任何版本")
    print()
    
    # 用户选择
    try:
        choice = input("请选择要下载的版本 (1-4) 或按回车使用推荐版本: ").strip()
        
        if not choice:
            selected = "Q4_0"
        else:
            model_names = list(models.keys())
            selected = model_names[int(choice) - 1]
        
        print(f"📥 准备下载: DeepSeek-R1-8B-{selected}")
        
        # 确认下载
        confirm = input("确认下载? (Y/n): ").strip().lower()
        if confirm in ['', 'y', 'yes']:
            return download_model(selected, models[selected])
        else:
            print("❌ 下载已取消")
            return False
            
    except (ValueError, IndexError):
        print("❌ 无效选择")
        return False
    except KeyboardInterrupt:
        print("\n❌ 用户取消")
        return False

def download_model(model_name: str, model_info: dict):
    """下载模型文件"""
    
    # 设置保存路径
    base_dir = Path.home() / ".lmstudio" / "models" / "bartowski" / "deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-GGUF"
    base_dir.mkdir(parents=True, exist_ok=True)
    
    filename = f"deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-{model_name}.gguf"
    model_path = base_dir / filename
    
    print(f"📍 下载URL: {model_info['url']}")
    print(f"💾 保存路径: {model_path}")
    print(f"📊 文件大小: {model_info['size']}")
    print()
    
    try:
        print("🚀 开始下载...")
        start_time = time.time()
        
        response = requests.get(model_info['url'], stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(model_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # 显示进度
                    if total_size > 0:
                        progress = downloaded / total_size * 100
                        speed = downloaded / (time.time() - start_time) / (1024 * 1024)  # MB/s
                        print(f"\r📥 下载进度: {progress:.1f}% ({downloaded/(1024**3):.2f}GB/{total_size/(1024**3):.2f}GB) 速度: {speed:.1f}MB/s", end="")
        
        print(f"\n✅ 下载完成!")
        
        # 验证文件
        if model_path.exists():
            actual_size = model_path.stat().st_size / (1024**3)
            print(f"📊 文件大小验证: {actual_size:.2f}GB")
            
            # 测试模型加载
            if test_model_loading(str(model_path)):
                print("🎉 模型下载并测试成功!")
                create_system_config(str(model_path), model_name)
                return True
            else:
                print("⚠️ 模型下载成功但测试失败")
                return False
        else:
            print("❌ 文件下载失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        if model_path.exists():
            model_path.unlink()
        return False

def test_model_loading(model_path: str):
    """测试模型加载"""
    print(f"\n🧪 测试模型加载...")
    
    try:
        from llama_cpp import Llama
        
        # 使用最保守的参数测试
        print("   🔄 使用最小参数测试...")
        model = Llama(
            model_path=model_path,
            n_ctx=256,
            n_threads=1,
            n_gpu_layers=0,
            verbose=False,
            use_mmap=False,
            use_mlock=False
        )
        
        # 简单推理测试
        response = model("Hello", max_tokens=5, echo=False)
        print(f"   ✅ 测试成功: {response}")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def create_system_config(model_path: str, model_name: str):
    """创建系统配置"""
    print(f"\n🔧 创建系统配置...")
    
    config_content = f'''# DeepSeek-R1 兼容配置
# 自动生成，适配您的笔记本电脑

# 模型路径
DEEPSEEK_R1_MODEL_PATH = r"{model_path}"

# 模型信息
MODEL_NAME = "DeepSeek-R1-8B-{model_name}"
MODEL_TYPE = "DeepSeek-R1-0528-Qwen3-8B"

# 推荐的加载参数（已测试兼容）
DEEPSEEK_R1_CONFIG = {{
    "n_ctx": 2048,
    "n_threads": 4,
    "n_gpu_layers": 0,  # 可以根据GPU情况调整
    "use_mmap": False,
    "use_mlock": False,
    "verbose": True
}}

# 使用说明:
# 1. 将此配置导入到您的系统中
# 2. 使用上述参数加载模型
# 3. 模型已通过兼容性测试
'''
    
    with open("deepseek_r1_compatible_config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ 配置文件已创建: deepseek_r1_compatible_config.py")
    
    # 更新主系统配置
    update_main_system_config(model_path)

def update_main_system_config(model_path: str):
    """更新主系统配置"""
    print("🔄 更新主系统配置...")
    
    try:
        # 读取当前系统文件
        with open("ultimate_final_tcm_system.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 更新模型路径
        old_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
        new_content = content.replace(old_path, model_path)
        
        # 写回文件
        with open("ultimate_final_tcm_system.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("✅ 主系统配置已更新")
        
    except Exception as e:
        print(f"⚠️ 更新主系统配置失败: {e}")

def main():
    """主函数"""
    success = download_deepseek_r1()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 DeepSeek-R1兼容版本安装完成!")
        print("=" * 60)
        print("💡 下一步:")
        print("1. 重启您的系统:")
        print("   streamlit run ultimate_final_tcm_system.py --server.port=8507")
        print("2. 点击'🚀 初始化系统'")
        print("3. 享受智能的DeepSeek-R1模型!")
        print()
        print("🎯 您现在拥有:")
        print("   ✅ 兼容的DeepSeek-R1-0528-Qwen3-8B模型")
        print("   ✅ 优化的加载配置")
        print("   ✅ 通过测试的系统集成")
    else:
        print("\n❌ 安装失败，请重试或选择其他版本")

if __name__ == "__main__":
    main()
