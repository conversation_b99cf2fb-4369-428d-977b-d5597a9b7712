#!/usr/bin/env python3
"""
商用级部署脚本
一键部署中医智能助手到云端，支持全球访问
"""
import subprocess
import sys
import json
import socket
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查部署依赖"""
    missing = []
    
    # 检查Python包
    required_packages = ['streamlit', 'requests', 'beautifulsoup4']
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    return missing

def install_dependencies():
    """安装依赖"""
    print("📦 正在安装依赖...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def get_network_info():
    """获取网络信息"""
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        return hostname, local_ip
    except:
        return "未知", "localhost"

def start_local_service():
    """启动本地服务"""
    print("🚀 启动本地服务...")
    
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "commercial_tcm_system.py",
        "--server.port", "8519",
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--server.maxUploadSize", "200"
    ]
    
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    time.sleep(3)  # 等待启动
    
    return process

def create_deployment_package():
    """创建部署包"""
    print("📦 创建部署包...")
    
    # 确保所有必要文件存在
    required_files = [
        'commercial_tcm_system.py',
        'requirements.txt',
        'manifest.json',
        'sw.js',
        'mobile_app.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("💡 请先运行 python mobile_pwa_generator.py")
        return False
    
    print("✅ 部署包检查完成")
    return True

def show_deployment_options():
    """显示部署选项"""
    print("\n🌐 部署选项：")
    print("=" * 50)
    
    options = {
        "1": {
            "name": "Vercel (推荐)",
            "desc": "免费，自动HTTPS，全球CDN",
            "steps": [
                "1. 安装Vercel CLI: npm i -g vercel",
                "2. 运行: vercel",
                "3. 按提示完成部署"
            ]
        },
        "2": {
            "name": "Netlify",
            "desc": "免费，简单易用，自动部署",
            "steps": [
                "1. 访问 netlify.com",
                "2. 拖拽项目文件夹到页面",
                "3. 自动部署完成"
            ]
        },
        "3": {
            "name": "Heroku",
            "desc": "免费额度，支持Python",
            "steps": [
                "1. 安装Heroku CLI",
                "2. heroku create your-app-name",
                "3. git push heroku main"
            ]
        },
        "4": {
            "name": "阿里云/腾讯云",
            "desc": "国内访问快，企业级",
            "steps": [
                "1. 购买云服务器",
                "2. 上传代码到服务器",
                "3. 配置域名和SSL"
            ]
        }
    }
    
    for key, option in options.items():
        print(f"\n{key}. **{option['name']}**")
        print(f"   {option['desc']}")
        for step in option['steps']:
            print(f"   {step}")

def create_quick_deploy_scripts():
    """创建快速部署脚本"""
    
    # Vercel部署脚本
    vercel_script = """#!/bin/bash
# Vercel快速部署脚本

echo "🚀 开始Vercel部署..."

# 检查Vercel CLI
if ! command -v vercel &> /dev/null; then
    echo "📦 安装Vercel CLI..."
    npm install -g vercel
fi

# 部署
echo "🌐 部署到Vercel..."
vercel --prod

echo "✅ 部署完成！"
echo "💡 您的应用现在可以全球访问了"
"""
    
    with open("deploy_vercel.sh", "w") as f:
        f.write(vercel_script)
    
    # Docker部署脚本
    docker_script = """#!/bin/bash
# Docker部署脚本

echo "🐳 开始Docker部署..."

# 构建镜像
echo "📦 构建Docker镜像..."
docker build -t tcm-assistant .

# 运行容器
echo "🚀 启动容器..."
docker run -d -p 8080:8080 --name tcm-assistant tcm-assistant

echo "✅ Docker部署完成！"
echo "🌐 访问地址: http://localhost:8080"
"""
    
    with open("deploy_docker.sh", "w") as f:
        f.write(docker_script)
    
    print("📝 快速部署脚本已生成：")
    print("   - deploy_vercel.sh (Vercel部署)")
    print("   - deploy_docker.sh (Docker部署)")

def generate_qr_code_html(url):
    """生成二维码HTML页面"""
    qr_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>中医智能助手 - 手机访问</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {{
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }}
        .container {{
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }}
        .title {{
            color: #333;
            margin-bottom: 20px;
        }}
        .qr-container {{
            margin: 20px 0;
        }}
        .url {{
            background: #f5f5f5;
            padding: 10px;
            border-radius: 8px;
            word-break: break-all;
            margin: 20px 0;
        }}
        .instructions {{
            text-align: left;
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🏥 中医智能助手</h1>
        <p>扫描二维码或复制链接在手机上访问</p>
        
        <div class="qr-container">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={url}" 
                 alt="二维码" style="max-width: 200px;">
        </div>
        
        <div class="url">
            <strong>访问地址：</strong><br>
            {url}
        </div>
        
        <div class="instructions">
            <h3>📱 手机使用步骤：</h3>
            <ol>
                <li>用手机扫描上方二维码</li>
                <li>或复制链接在浏览器中打开</li>
                <li>点击"添加到主屏幕"</li>
                <li>像使用APP一样访问</li>
            </ol>
        </div>
        
        <p style="color: #666; font-size: 0.9rem;">
            ⚠️ 仅供中医文化学习参考<br>
            如有健康问题请咨询专业医师
        </p>
    </div>
</body>
</html>
"""
    
    with open("手机访问二维码.html", "w", encoding="utf-8") as f:
        f.write(qr_html)
    
    return "手机访问二维码.html"

def main():
    """主函数"""
    print("🏥 中医智能助手 - 商用级部署工具")
    print("=" * 60)
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        if input("是否自动安装？(y/n): ").lower() == 'y':
            if not install_dependencies():
                return
        else:
            print("💡 请手动安装依赖后重新运行")
            return
    
    # 检查部署包
    if not create_deployment_package():
        return
    
    # 获取网络信息
    hostname, local_ip = get_network_info()
    
    print("✅ 系统检查通过")
    print(f"🖥️ 主机名: {hostname}")
    print(f"🌐 本机IP: {local_ip}")
    print()
    
    # 询问部署方式
    print("🚀 选择部署方式：")
    print("1. 本地测试 (局域网访问)")
    print("2. 云端部署 (全球访问)")
    print("3. 查看部署选项")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        # 本地部署
        print("\n🏠 启动本地服务...")
        process = start_local_service()
        
        local_url = f"http://{local_ip}:8519"
        print(f"✅ 服务启动成功！")
        print(f"🌐 访问地址: {local_url}")
        
        # 生成二维码页面
        qr_file = generate_qr_code_html(local_url)
        print(f"📱 手机访问页面: {qr_file}")
        
        # 询问是否打开浏览器
        if input("\n是否打开浏览器？(y/n): ").lower() != 'n':
            webbrowser.open(f"http://localhost:8519")
            webbrowser.open(qr_file)
        
        print("\n🔧 控制说明:")
        print("   - 按 Ctrl+C 停止服务")
        print("   - 服务运行期间请保持此窗口开启")
        print("   - 手机扫描二维码即可访问")
        
        try:
            print("\n🔄 服务运行中... (按 Ctrl+C 停止)")
            process.wait()
        except KeyboardInterrupt:
            print("\n\n👋 正在停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")
    
    elif choice == "2":
        # 云端部署
        print("\n☁️ 准备云端部署...")
        create_quick_deploy_scripts()
        show_deployment_options()
        
        print("\n💡 推荐步骤：")
        print("1. 选择Vercel (最简单)")
        print("2. 运行: ./deploy_vercel.sh")
        print("3. 获得全球访问地址")
        print("4. 配置自定义域名 (可选)")
        
    elif choice == "3":
        # 查看选项
        show_deployment_options()
        create_quick_deploy_scripts()
        
        print("\n📋 部署文件已准备就绪：")
        print("   - vercel.json (Vercel配置)")
        print("   - Dockerfile (Docker配置)")
        print("   - requirements.txt (依赖列表)")
        print("   - deploy_vercel.sh (Vercel脚本)")
        print("   - deploy_docker.sh (Docker脚本)")
    
    else:
        print("❌ 无效选择")
        return
    
    print("\n🎉 部署工具运行完成！")
    print("💰 商用化建议：")
    print("   - 申请域名和SSL证书")
    print("   - 配置CDN加速")
    print("   - 添加用户分析")
    print("   - 设置监控告警")

if __name__ == "__main__":
    main()
