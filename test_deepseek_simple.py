#!/usr/bin/env python3
"""
简化的DeepSeek测试
"""

import sys
sys.path.append('.')

def test_deepseek_simple():
    print('🧪 简化DeepSeek测试...')
    
    try:
        from deepseek_ollama_api import DeepSeekOllamaAPI
        
        print('🔄 初始化DeepSeek API...')
        api = DeepSeekOllamaAPI()
        
        if not api.available:
            print('❌ DeepSeek模型不可用')
            return False
        
        print('✅ DeepSeek模型可用')
        
        # 简单测试
        test_query = "中医是什么"
        print(f'🔍 测试查询: {test_query}')
        
        try:
            response = api.generate_response(test_query, max_tokens=100, temperature=0.5)
            if response and len(response) > 20:
                print('✅ DeepSeek生成成功')
                print(f'回答长度: {len(response)} 字符')
                print(f'回答内容: {response[:200]}...')
                return True
            else:
                print('❌ DeepSeek回答质量不佳')
                return False
        except Exception as e:
            print(f'❌ DeepSeek生成失败: {e}')
            return False
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return False

if __name__ == "__main__":
    test_deepseek_simple()
