#!/usr/bin/env python3
"""
中医模型管理器
专门管理适合中医领域的轻量级模型
"""

import requests
import subprocess
import time
import streamlit as st

class TCMModelManager:
    """中医模型管理器"""
    
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.ollama_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe"
        
        # 推荐的中医模型列表（按性能优化排序）
        self.recommended_models = [
            {
                "name": "qwen2.5:7b-instruct",
                "display_name": "Qwen2.5-7B-Instruct",
                "size": "4.4GB",
                "description": "阿里开发，中文医学优秀，推荐首选",
                "tcm_score": 9.5
            },
            {
                "name": "llama3.1:8b",
                "display_name": "Llama3.1-8B",
                "size": "4.7GB", 
                "description": "Meta开发，通用性强，医学知识丰富",
                "tcm_score": 8.5
            },
            {
                "name": "gemma2:9b",
                "display_name": "Gemma2-9B",
                "size": "5.4GB",
                "description": "Google开发，推理能力强",
                "tcm_score": 8.0
            },
            {
                "name": "mistral:7b-instruct",
                "display_name": "Mistral-7B-Instruct", 
                "size": "4.1GB",
                "description": "欧洲开发，轻量高效",
                "tcm_score": 7.5
            }
        ]
        
        self.current_model = None
        self.initialized = False
        
    def check_available_models(self):
        """检查已下载的模型"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
            return []
        except:
            return []
    
    def find_best_available_model(self):
        """找到最佳可用模型"""
        available = self.check_available_models()
        
        # 按推荐顺序查找
        for model_info in self.recommended_models:
            if model_info["name"] in available:
                return model_info["name"], model_info["display_name"]
        
        # 如果没有推荐模型，查找任何可用的
        for model in available:
            if any(keyword in model.lower() for keyword in ['qwen', 'llama', 'gemma', 'mistral', 'baichuan']):
                return model, model
        
        return None, None
    
    def download_recommended_model(self, model_name=None):
        """下载推荐模型"""
        if not model_name:
            model_name = self.recommended_models[0]["name"]  # 默认下载Qwen2.5
        
        model_info = next((m for m in self.recommended_models if m["name"] == model_name), None)
        if not model_info:
            st.error(f"未知模型: {model_name}")
            return False
        
        st.info(f"📥 正在下载 {model_info['display_name']} ({model_info['size']})")
        st.info(f"💡 {model_info['description']}")
        
        try:
            # 启动下载
            process = subprocess.Popen(
                [self.ollama_exe, "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 显示进度
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            progress = 0
            while process.poll() is None:
                progress = min(progress + 0.02, 0.9)
                progress_bar.progress(progress)
                status_text.text(f"下载中... {progress*100:.1f}%")
                time.sleep(1)
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                progress_bar.progress(1.0)
                status_text.text("下载完成!")
                st.success(f"✅ {model_info['display_name']} 下载完成!")
                return True
            else:
                st.error(f"❌ 下载失败: {stderr}")
                return False
                
        except Exception as e:
            st.error(f"下载异常: {e}")
            return False
    
    def test_model(self, model_name):
        """测试模型中医问答能力"""
        try:
            st.info("🧪 测试中医问答能力...")
            
            # 中医专业测试问题
            test_prompt = "请简单介绍一下中医的基本理论，包括阴阳学说和五行学说。"
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": model_name,
                    "prompt": test_prompt,
                    "stream": False,
                    "options": {
                        "num_predict": 200,
                        "temperature": 0.7
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '').strip()
                
                if content and len(content) > 50:
                    st.success("✅ 中医问答测试通过")
                    st.info(f"🎯 测试回答预览: {content[:100]}...")
                    return True, content
                else:
                    st.warning("⚠️ 回答过短")
                    return False, content
            else:
                st.error(f"❌ 测试失败: {response.status_code}")
                return False, ""
                
        except Exception as e:
            st.error(f"测试异常: {e}")
            return False, ""
    
    def initialize(self):
        """初始化最佳中医模型"""
        st.info("🤖 初始化中医智能模型...")
        
        # 1. 检查可用模型
        model_name, display_name = self.find_best_available_model()
        
        if model_name:
            st.success(f"✅ 找到可用模型: {display_name}")
            self.current_model = model_name
        else:
            st.info("📥 未找到合适模型，下载推荐模型...")
            if self.download_recommended_model():
                model_name, display_name = self.find_best_available_model()
                if model_name:
                    self.current_model = model_name
                else:
                    st.error("❌ 模型下载后仍未找到")
                    return False
            else:
                st.error("❌ 模型下载失败")
                return False
        
        # 2. 测试模型
        success, response = self.test_model(self.current_model)
        if success:
            st.success(f"✅ {display_name} 初始化成功!")
            st.info("🎯 专门优化用于中医问答")
            self.initialized = True
            return True
        else:
            st.error("❌ 模型测试失败")
            return False
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成中医专业回答"""
        if not self.initialized or not self.current_model:
            return "中医模型未初始化"
        
        try:
            st.info("🧠 中医智能助手正在思考...")
            
            # 构建中医专业提示词
            system_prompt = """你是一位经验丰富的中医医生，具有深厚的中医理论基础和丰富的临床经验。
请用专业、温和、易懂的语气回答用户的中医相关问题。
回答时请：
1. 基于中医理论（如阴阳五行、脏腑经络等）
2. 提供实用的建议
3. 必要时引用经典医籍
4. 提醒用户咨询专业医生"""
            
            full_prompt = f"{system_prompt}\n\n用户问题: {prompt}\n\n中医回答:"
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.current_model,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens,
                        "top_p": 0.9,
                        "repeat_penalty": 1.1
                    }
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                
                if content:
                    content = self._clean_response(content)
                    st.success("✅ 中医智能助手回答完成")
                    return content
                else:
                    return "回答生成为空，请重试"
            else:
                return f"生成失败: {response.status_code}"
                
        except Exception as e:
            return f"生成异常: {str(e)}"
    
    def _clean_response(self, text: str) -> str:
        """清理回答"""
        import re
        
        # 移除多余的换行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 移除提示词残留
        text = re.sub(r'^(用户|助手|中医|回答|问题)[:：]\s*', '', text, flags=re.MULTILINE)
        
        return text.strip()
    
    def get_model_info(self):
        """获取当前模型信息"""
        if not self.current_model:
            return "未加载模型"
        
        model_info = next((m for m in self.recommended_models if m["name"] == self.current_model), None)
        if model_info:
            return f"{model_info['display_name']} - {model_info['description']}"
        else:
            return self.current_model

def main():
    """测试函数"""
    manager = TCMModelManager()
    
    print("🤖 中医模型管理器测试")
    print("=" * 40)
    
    if manager.initialize():
        print("✅ 初始化成功!")
        print(f"当前模型: {manager.get_model_info()}")
        
        # 测试生成
        response = manager.generate_response("什么是气血？", max_tokens=200)
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
