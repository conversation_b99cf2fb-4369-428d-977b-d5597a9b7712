#!/usr/bin/env python3
"""
Ollama自动安装脚本
"""

import os
import sys
import subprocess
import requests
import time
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    print(f"📥 下载 {filename}...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r进度: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ {filename} 下载完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def install_ollama_windows():
    """Windows安装Ollama"""
    print("🪟 检测到Windows系统")
    
    # 下载Ollama安装程序
    ollama_url = "https://ollama.ai/download/OllamaSetup.exe"
    installer_path = "OllamaSetup.exe"
    
    if download_file(ollama_url, installer_path):
        print("🚀 启动安装程序...")
        print("💡 请按照安装向导完成安装")
        
        try:
            subprocess.run([installer_path], check=True)
            print("✅ Ollama安装完成")
            
            # 清理安装文件
            os.remove(installer_path)
            return True
            
        except subprocess.CalledProcessError:
            print("❌ 安装失败")
            return False
    else:
        return False

def install_ollama_unix():
    """Unix系统安装Ollama"""
    print("🐧 检测到Unix系统")
    
    try:
        # 下载并执行安装脚本
        result = subprocess.run([
            "curl", "-fsSL", "https://ollama.ai/install.sh"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # 执行安装脚本
            subprocess.run(["sh"], input=result.stdout, text=True, check=True)
            print("✅ Ollama安装完成")
            return True
        else:
            print("❌ 下载安装脚本失败")
            return False
            
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def check_ollama_installed():
    """检查Ollama是否已安装"""
    try:
        result = subprocess.run(
            ["ollama", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except:
        return False

def start_ollama_service():
    """启动Ollama服务"""
    print("🚀 启动Ollama服务...")
    
    try:
        if os.name == 'nt':
            # Windows
            subprocess.Popen(
                ["ollama", "serve"],
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:
            # Unix
            subprocess.Popen(
                ["ollama", "serve"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        
        # 等待服务启动
        for i in range(30):
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    print("✅ Ollama服务启动成功")
                    return True
            except:
                pass
            
            time.sleep(1)
            print(f"\r等待服务启动... {i+1}/30", end='', flush=True)
        
        print("\n⚠️ 服务启动超时，但可能已在后台运行")
        return True
        
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False

def pull_deepseek_model():
    """拉取DeepSeek模型"""
    print("📥 下载DeepSeek-R1模型...")
    print("💡 这可能需要几分钟时间")
    
    try:
        result = subprocess.run([
            "ollama", "pull", "deepseek-r1:8b"
        ], timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print("✅ DeepSeek-R1模型下载完成")
            return True
        else:
            print("❌ 模型下载失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 下载超时，请稍后手动执行: ollama pull deepseek-r1:8b")
        return False
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return False

def main():
    """主函数"""
    print("🤖 Ollama自动安装脚本")
    print("=" * 40)
    
    # 检查是否已安装
    if check_ollama_installed():
        print("✅ Ollama已安装")
    else:
        print("📦 开始安装Ollama...")
        
        # 根据系统类型安装
        if os.name == 'nt':
            success = install_ollama_windows()
        else:
            success = install_ollama_unix()
        
        if not success:
            print("❌ Ollama安装失败")
            return False
        
        # 重新检查
        if not check_ollama_installed():
            print("❌ 安装验证失败")
            return False
    
    # 启动服务
    if not start_ollama_service():
        print("❌ 服务启动失败")
        return False
    
    # 下载模型
    if not pull_deepseek_model():
        print("⚠️ 模型下载失败，但Ollama已安装")
        print("💡 您可以稍后手动执行: ollama pull deepseek-r1:8b")
    
    print("\n🎉 Ollama设置完成！")
    print("现在可以在RAG系统中使用DeepSeek-R1了！")
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消安装")
        sys.exit(1)
