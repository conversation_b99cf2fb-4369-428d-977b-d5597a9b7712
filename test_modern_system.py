#!/usr/bin/env python3
"""
测试现代化中医RAG系统
"""
import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 创建必要的目录
    directories = [
        "backend",
        "static", 
        "uploads",
        "vector_db",
        "sessions"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录创建完成")

def install_basic_dependencies():
    """安装基础依赖"""
    print("📦 安装基础依赖...")
    
    basic_deps = [
        "fastapi",
        "uvicorn",
        "python-multipart",
        "websockets",
        "pydantic",
        "requests",
        "beautifulsoup4",
        "PyPDF2",
        "python-docx",
        "pandas",
        "numpy",
        "scikit-learn"
    ]
    
    try:
        for dep in basic_deps:
            try:
                __import__(dep.replace('-', '_'))
                print(f"✅ {dep} 已安装")
            except ImportError:
                print(f"🔄 安装 {dep}...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", dep
                ])
        
        # 尝试安装sentence-transformers
        try:
            import sentence_transformers
            print("✅ sentence-transformers 已安装")
        except ImportError:
            print("🔄 安装 sentence-transformers...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "sentence-transformers"
                ])
            except:
                print("⚠️ sentence-transformers 安装失败，将使用备用方案")
        
        # 尝试安装faiss
        try:
            import faiss
            print("✅ faiss 已安装")
        except ImportError:
            print("🔄 安装 faiss-cpu...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "faiss-cpu"
                ])
            except:
                print("⚠️ faiss-cpu 安装失败，将使用备用方案")
        
        return True
        
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def create_simple_frontend():
    """创建简单的前端页面"""
    frontend_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智能助手 - 测试版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2E8B57;
        }
        .chat-area {
            border: 1px solid #ddd;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }
        .user-message {
            background: #2E8B57;
            color: white;
            margin-left: 20%;
        }
        .assistant-message {
            background: #e3f2fd;
            margin-right: 20%;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        .input-area input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        .input-area button {
            padding: 12px 20px;
            background: #2E8B57;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        .input-area button:hover {
            background: #228B22;
        }
        .quick-buttons {
            margin-bottom: 20px;
            text-align: center;
        }
        .quick-btn {
            margin: 5px;
            padding: 8px 15px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            display: inline-block;
        }
        .quick-btn:hover {
            background: #e0e0e0;
        }
        .upload-area {
            margin-bottom: 20px;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 中医智能助手</h1>
            <p>现代化聊天界面 - 测试版</p>
        </div>
        
        <div class="upload-area">
            <h3>📁 文档上传</h3>
            <input type="file" id="fileInput" accept=".pdf,.txt,.doc,.docx" multiple>
            <button onclick="uploadFiles()">上传文档</button>
        </div>
        
        <div class="quick-buttons">
            <div class="quick-btn" onclick="sendQuickMessage('湿气重有什么表现？')">🌿 湿气问题</div>
            <div class="quick-btn" onclick="sendQuickMessage('气血不足如何调理？')">🫖 气血调理</div>
            <div class="quick-btn" onclick="sendQuickMessage('什么是阴阳平衡？')">☯️ 阴阳平衡</div>
            <div class="quick-btn" onclick="sendQuickMessage('四季养生要点')">🌸 四季养生</div>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message assistant-message">
                <strong>🤖 助手:</strong> 您好！我是中医智能助手。您可以：<br>
                • 点击上方快捷按钮查询常见问题<br>
                • 上传PDF文档扩充知识库<br>
                • 直接输入您的问题<br><br>
                请问有什么可以帮助您的吗？
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 显示加载状态
            addMessage('assistant', '正在思考中...', 'loading');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: 'test_session'
                    })
                });
                
                const data = await response.json();
                
                // 移除加载消息
                removeLoadingMessage();
                
                // 显示助手回复
                addMessage('assistant', data.response);
                
                // 显示来源信息
                if (data.sources && data.sources.length > 0) {
                    let sourcesText = '<br><small><strong>📚 参考来源:</strong><br>';
                    data.sources.forEach((source, index) => {
                        sourcesText += `${index + 1}. ${source.source}<br>`;
                    });
                    sourcesText += '</small>';
                    addMessage('assistant', sourcesText);
                }
                
            } catch (error) {
                removeLoadingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
            }
        }
        
        function addMessage(type, content, className = '') {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message ${className}`;
            
            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            messageDiv.innerHTML = `<strong>${prefix}</strong> ${content}`;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        function removeLoadingMessage() {
            const loadingMessage = document.querySelector('.loading');
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }
        
        async function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }
            
            try {
                addMessage('assistant', '正在上传和处理文档...', 'loading');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                removeLoadingMessage();
                
                let resultText = `📁 文档上传完成！<br>`;
                data.results.forEach(result => {
                    if (result.status === 'success') {
                        resultText += `✅ ${result.filename} (${result.chunks} 个文本块)<br>`;
                    } else {
                        resultText += `❌ ${result.filename}: ${result.error}<br>`;
                    }
                });
                
                addMessage('assistant', resultText);
                fileInput.value = '';
                
            } catch (error) {
                removeLoadingMessage();
                addMessage('assistant', '文档上传失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
"""
    
    static_dir = Path("static")
    static_dir.mkdir(exist_ok=True)
    
    with open(static_dir / "index.html", "w", encoding="utf-8") as f:
        f.write(frontend_html)
    
    print("✅ 简单前端页面创建完成")

def start_test_server():
    """启动测试服务器"""
    print("🚀 启动测试服务器...")
    
    # 设置环境
    setup_environment()
    
    # 安装依赖
    if not install_basic_dependencies():
        print("❌ 依赖安装失败")
        return False
    
    # 创建前端
    create_simple_frontend()
    
    # 切换到backend目录
    os.chdir("backend")
    
    try:
        print("🔄 启动FastAPI服务器...")
        
        # 启动命令
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "simple_main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        process = subprocess.Popen(cmd)
        
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        print("\n" + "="*60)
        print("🎉 测试服务器启动成功！")
        print("="*60)
        print("🌐 访问地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🔍 健康检查: http://localhost:8000/api/health")
        print("="*60)
        print("\n💡 功能测试:")
        print("  1. 打开浏览器访问 http://localhost:8000")
        print("  2. 尝试发送消息测试聊天功能")
        print("  3. 上传PDF文档测试文档处理")
        print("  4. 点击快捷按钮测试预设问题")
        print("\n⚠️  注意:")
        print("  - 这是测试版本，功能可能有限")
        print("  - 如果遇到错误，请查看控制台输出")
        print("  - 按 Ctrl+C 停止服务")
        print("="*60)
        
        # 询问是否打开浏览器
        try:
            open_browser = input("\n是否打开浏览器？(y/n): ").lower()
            if open_browser != 'n':
                webbrowser.open("http://localhost:8000")
        except KeyboardInterrupt:
            pass
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n\n👋 停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 中医RAG系统 - 测试启动器")
    print("=" * 50)
    
    try:
        start_test_server()
    except KeyboardInterrupt:
        print("\n👋 用户取消")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
