<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        #logs { background: #f8f9fa; padding: 10px; height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🏥 系统状态独立测试</h1>
    
    <div class="status">
        <h3>📊 系统状态:</h3>
        <div id="systemStatus">加载中...</div>
        <button class="btn-primary" onclick="loadSystemStatus()">🔄 刷新状态</button>
    </div>
    
    <div class="status">
        <h3>🔧 测试工具:</h3>
        <button class="btn-success" onclick="testBasicFetch()">测试基础Fetch</button>
        <button class="btn-success" onclick="testHealthAPI()">测试健康API</button>
        <button class="btn-danger" onclick="clearLogs()">清除日志</button>
    </div>
    
    <div class="status">
        <h3>📝 实时日志:</h3>
        <div id="logs"></div>
    </div>

    <script>
        // 日志系统
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `[${time}] <span class="${type}">${type.toUpperCase()}</span>: ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${time}] ${type.toUpperCase()}: ${message}`);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // 加载系统状态
        async function loadSystemStatus() {
            addLog('开始加载系统状态...', 'info');
            const statusElement = document.getElementById('systemStatus');
            
            try {
                statusElement.innerHTML = '<div class="info">🔄 正在加载...</div>';
                
                // 添加时间戳防止缓存
                const timestamp = new Date().getTime();
                const url = `/api/health?t=${timestamp}`;
                addLog(`请求URL: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                
                addLog(`响应状态: ${response.status} ${response.statusText}`, 'info');
                addLog(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addLog(`响应数据: ${JSON.stringify(data, null, 2)}`, 'success');

                statusElement.innerHTML = `
                    <div class="success">状态: ✅ ${data.status}</div>
                    <div>版本: ${data.version}</div>
                    <div>文档: ${data.documents} 个</div>
                    <div>功能: ${data.features ? data.features.length : 0} 项</div>
                    <div style="font-size: 11px; color: #888;">更新时间: ${new Date().toLocaleTimeString()}</div>
                `;
                
                addLog('系统状态加载成功！', 'success');

            } catch (error) {
                addLog(`系统状态加载失败: ${error.message}`, 'error');
                addLog(`错误堆栈: ${error.stack}`, 'error');
                
                statusElement.innerHTML = `
                    <div class="error">❌ 加载失败</div>
                    <div style="font-size: 12px; color: #666;">${error.message}</div>
                    <div style="font-size: 11px; color: #888;">时间: ${new Date().toLocaleTimeString()}</div>
                `;
            }
        }
        
        // 测试基础Fetch功能
        async function testBasicFetch() {
            addLog('测试基础Fetch功能...', 'info');
            
            try {
                addLog(`fetch函数存在: ${typeof fetch !== 'undefined'}`, 'info');
                addLog(`Promise支持: ${typeof Promise !== 'undefined'}`, 'info');
                addLog(`async/await支持: ${typeof (async function(){}) === 'function'}`, 'info');
                
                // 测试简单请求
                const response = await fetch('/api/health');
                addLog(`基础请求状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('基础Fetch测试成功', 'success');
                } else {
                    addLog(`基础Fetch测试失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                addLog(`基础Fetch测试错误: ${error.message}`, 'error');
            }
        }
        
        // 测试健康API
        async function testHealthAPI() {
            addLog('测试健康检查API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8006/api/health');
                addLog(`跨域请求状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addLog(`API数据: ${JSON.stringify(data)}`, 'success');
                } else {
                    addLog(`API请求失败: ${response.status}`, 'error');
                }
                
            } catch (error) {
                addLog(`API测试错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面DOM加载完成', 'info');
            addLog(`当前URL: ${window.location.href}`, 'info');
            addLog(`User Agent: ${navigator.userAgent}`, 'info');
            
            // 延迟自动加载状态
            setTimeout(() => {
                addLog('开始自动加载系统状态...', 'info');
                loadSystemStatus();
            }, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            addLog(`JavaScript错误: ${e.message} 在 ${e.filename}:${e.lineno}:${e.colno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            addLog(`未处理的Promise拒绝: ${e.reason}`, 'error');
        });
        
        addLog('脚本初始化完成', 'info');
    </script>
</body>
</html>
