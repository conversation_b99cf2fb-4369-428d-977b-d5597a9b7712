#!/usr/bin/env python3
"""
终极中医RAG系统 - ngrok远程访问启动器
支持手机访问、密码保护、自动配置
"""

import subprocess
import sys
import os
import time
import json
import requests
from pathlib import Path
import threading
import signal

def print_ngrok_banner():
    """打印ngrok横幅"""
    print("=" * 100)
    print("🌐 终极中医RAG系统 - ngrok远程访问启动器")
    print("=" * 100)
    print("🎯 功能特色:")
    print("")
    print("✅ 远程访问支持:")
    print("   🌍 公网访问链接")
    print("   📱 手机端完美适配")
    print("   🔐 密码保护 (MVP168918)")
    print("   🚀 自动配置和启动")
    print("")
    print("✅ 系统功能:")
    print("   🧠 DeepSeek模型推理")
    print("   📚 古代医书检索")
    print("   📄 大文件处理 (>200MB)")
    print("   🔊 语音对话功能")
    print("   💬 连续对话记忆")
    print("")
    print("✅ 移动端优化:")
    print("   📱 响应式界面设计")
    print("   👆 触摸友好操作")
    print("   🔊 移动端语音支持")
    print("   ⚡ 快速加载优化")
    print("=" * 100)

def check_ngrok():
    """检查ngrok是否安装"""
    print("🔍 检查ngrok环境...")
    
    try:
        result = subprocess.run(["ngrok", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ngrok已安装: {version}")
            return True
        else:
            print("❌ ngrok未正确安装")
            return False
    except FileNotFoundError:
        print("❌ ngrok未找到")
        print_ngrok_installation_guide()
        return False

def print_ngrok_installation_guide():
    """打印ngrok安装指南"""
    print("\n💡 ngrok安装指南:")
    print("   1. 访问 https://ngrok.com/download")
    print("   2. 下载适合您系统的版本")
    print("   3. 解压到系统PATH目录")
    print("   4. 注册账号获取authtoken")
    print("   5. 运行: ngrok config add-authtoken YOUR_TOKEN")

def setup_ngrok_auth():
    """设置ngrok认证"""
    print("\n🔐 设置ngrok认证...")
    
    # 检查是否已有authtoken
    try:
        result = subprocess.run(["ngrok", "config", "check"], capture_output=True, text=True)
        if "valid" in result.stdout.lower() or result.returncode == 0:
            print("✅ ngrok认证已配置")
            return True
    except:
        pass
    
    print("⚠️ 需要配置ngrok认证token")
    print("💡 获取authtoken:")
    print("   1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken")
    print("   2. 登录或注册ngrok账号")
    print("   3. 复制您的authtoken")
    
    authtoken = input("\n请输入您的ngrok authtoken (或按回车跳过): ").strip()
    
    if authtoken:
        try:
            result = subprocess.run([
                "ngrok", "config", "add-authtoken", authtoken
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ authtoken配置成功")
                return True
            else:
                print(f"❌ authtoken配置失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 配置过程出错: {e}")
            return False
    else:
        print("⚠️ 跳过authtoken配置，可能影响使用")
        return True

def start_ultimate_system():
    """启动终极系统"""
    print("\n🚀 启动终极中医RAG系统...")
    
    try:
        # 启动系统（后台运行）
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_working_tcm_system.py",
            "--server.port=8506",
            "--server.address=0.0.0.0",
            "--theme.base=light",
            "--server.headless=true",
            "--browser.gatherUsageStats=false"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待系统启动
        print("⏳ 等待系统启动...")
        time.sleep(15)  # 给更多时间加载模型
        
        # 检查系统是否启动成功
        try:
            response = requests.get("http://localhost:8506", timeout=10)
            if response.status_code == 200:
                print("✅ 终极系统启动成功")
                return process
            else:
                print(f"⚠️ 系统响应异常: {response.status_code}")
                return process
        except requests.exceptions.RequestException:
            print("⚠️ 系统可能仍在启动中...")
            return process
            
    except Exception as e:
        print(f"❌ 启动系统失败: {e}")
        return None

def start_ngrok_tunnel():
    """启动ngrok隧道"""
    print("\n🌐 启动ngrok隧道...")
    
    try:
        # 启动ngrok隧道
        process = subprocess.Popen([
            "ngrok", "http", "8506",
            "--log=stdout",
            "--region=us"  # 使用美国区域，通常速度较快
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待隧道建立
        print("⏳ 等待隧道建立...")
        time.sleep(8)
        
        # 获取公网URL
        public_url = get_ngrok_public_url()
        
        if public_url:
            print(f"✅ ngrok隧道启动成功")
            print(f"🔗 公网访问地址: {public_url}")
            print(f"📱 手机访问地址: {public_url}")
            
            # 生成分享信息
            generate_ultimate_share_info(public_url)
            
            return process, public_url
        else:
            print("⚠️ 无法获取公网URL，但隧道可能已启动")
            return process, None
            
    except Exception as e:
        print(f"❌ 启动ngrok隧道失败: {e}")
        return None, None

def get_ngrok_public_url():
    """获取ngrok公网URL"""
    try:
        # 查询ngrok API
        response = requests.get("http://localhost:4040/api/tunnels", timeout=10)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
                elif tunnel.get('proto') == 'http':
                    return tunnel.get('public_url')
            
        return None
        
    except Exception as e:
        print(f"⚠️ 获取公网URL失败: {e}")
        return None

def generate_ultimate_share_info(public_url):
    """生成终极分享信息"""
    share_info = f"""
🎉 终极中医RAG系统已部署到公网！

🔗 访问地址: {public_url}
📱 手机访问: 直接在手机浏览器中打开上述链接
🔐 访问密码: MVP168918 (如需要)

✨ 终极系统功能:
   🧙‍♂️ 智者·中医AI助手 (DeepSeek模型驱动)
   🔍 真正的PDF文档检索 (支持>200MB大文件)
   🌐 古代医书在线搜索 (5大经典医书)
   🔊 完整语音对话功能 (输入+输出+连续记忆)
   📄 多格式文档解析 (PDF/Word/PPT/Excel/TXT)
   💬 智能连续对话记忆
   📱 移动端完美适配

🎯 核心特色:
   ✅ DeepSeek-R1模型: 真正的AI推理
   ✅ 古代医书检索: 医宗金鉴、黄帝内经、伤寒论等
   ✅ 大文件处理: 支持>200MB PDF文档
   ✅ 语音对话: 说话输入，自动播放回答
   ✅ 连续记忆: 记住所有对话历史和用户画像
   ✅ 移动优化: 手机端完美体验

💡 使用说明:
   1. 点击"初始化终极系统"按钮
   2. 上传中医PDF文档(支持大文件)
   3. 开始智能语音对话
   4. 系统会记住您的所有对话

📱 移动端使用:
   - 支持触摸操作
   - 语音输入功能
   - 自动播放回答
   - 响应式界面

⚠️ 重要提醒:
   - 此链接仅在本次会话有效
   - 请勿分享给不信任的人
   - 系统仅供学习参考，不替代专业医疗
   - 所有数据本地处理，隐私安全

🔧 技术架构:
   - 前端: Streamlit响应式界面
   - 模型: DeepSeek-R1-0528-Qwen3-8B
   - 向量库: FAISS高性能检索
   - 语音: pyttsx3 + SpeechRecognition
   - 文档: 多格式并行处理
   - 网络: ngrok公网隧道
"""
    
    # 保存分享信息
    with open("ultimate_share_info.txt", "w", encoding="utf-8") as f:
        f.write(share_info.strip())
    
    print("📝 分享信息已保存到: ultimate_share_info.txt")
    print(share_info)

def monitor_ultimate_services(streamlit_process, ngrok_process, public_url):
    """监控终极服务状态"""
    print("\n📊 开始监控终极服务...")
    print("⏹️ 按 Ctrl+C 停止所有服务")
    
    try:
        while True:
            # 检查Streamlit进程
            if streamlit_process.poll() is not None:
                print("⚠️ 终极系统已停止")
                break
            
            # 检查ngrok进程
            if ngrok_process.poll() is not None:
                print("⚠️ ngrok隧道已断开")
                break
            
            # 检查服务可用性
            try:
                if public_url:
                    response = requests.get(public_url, timeout=15)
                    if response.status_code == 200:
                        status = "✅ 在线"
                    else:
                        status = f"⚠️ 异常({response.status_code})"
                else:
                    status = "⚠️ URL未知"
                
                print(f"🌐 终极服务状态: {status} - {time.strftime('%H:%M:%S')}")
                
            except requests.exceptions.RequestException:
                print(f"❌ 服务不可达 - {time.strftime('%H:%M:%S')}")
            
            time.sleep(60)  # 每分钟检查一次
            
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
    
    # 清理进程
    cleanup_ultimate_processes(streamlit_process, ngrok_process)

def cleanup_ultimate_processes(streamlit_process, ngrok_process):
    """清理终极进程"""
    print("🧹 清理进程...")
    
    try:
        if streamlit_process and streamlit_process.poll() is None:
            streamlit_process.terminate()
            streamlit_process.wait(timeout=10)
            print("✅ 终极系统进程已停止")
    except Exception as e:
        print(f"⚠️ 停止终极系统进程失败: {e}")
    
    try:
        if ngrok_process and ngrok_process.poll() is None:
            ngrok_process.terminate()
            ngrok_process.wait(timeout=5)
            print("✅ ngrok进程已停止")
    except Exception as e:
        print(f"⚠️ 停止ngrok进程失败: {e}")

def main():
    """主函数"""
    print_ngrok_banner()
    
    # 1. 检查ngrok
    if not check_ngrok():
        input("按回车键退出...")
        return
    
    # 2. 设置认证
    if not setup_ngrok_auth():
        print("⚠️ 认证设置失败，但可以尝试继续")
    
    # 3. 启动终极系统
    streamlit_process = start_ultimate_system()
    if not streamlit_process:
        print("❌ 无法启动终极系统")
        input("按回车键退出...")
        return
    
    # 4. 启动ngrok隧道
    ngrok_process, public_url = start_ngrok_tunnel()
    if not ngrok_process:
        print("❌ 无法启动ngrok隧道")
        streamlit_process.terminate()
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 100)
    print("🎉 终极中医RAG系统远程访问部署完成！")
    print("")
    print("🎯 系统特色:")
    print("   🧠 DeepSeek模型: 真正的AI推理")
    print("   📚 古代医书: 5大经典医书检索")
    print("   📄 大文件: 支持>200MB文档")
    print("   🔊 语音对话: 完整输入输出功能")
    print("   💬 连续记忆: 智能对话管理")
    print("   📱 移动优化: 手机端完美体验")
    print("")
    print("📱 您的朋友现在可以通过公网链接在手机上使用系统")
    print("🔐 建议设置访问密码保护隐私")
    print("=" * 100)
    
    # 5. 监控服务
    monitor_ultimate_services(streamlit_process, ngrok_process, public_url)
    
    print("👋 终极服务已停止")

if __name__ == "__main__":
    main()
