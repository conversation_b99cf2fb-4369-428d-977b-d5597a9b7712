#!/usr/bin/env python3
"""
完整的中医AI助手
集成ChatGLM3-6B API + MCP + RAG系统的完整解决方案
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import requests
from datetime import datetime
import threading
import time

# FastAPI相关
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# 导入我们的组件
from tcm_rag_system import TCMRAGSystem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API模型定义
class ChatRequest(BaseModel):
    message: str
    use_rag: bool = True
    temperature: float = 0.7
    max_tokens: int = 800

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]] = []
    model: str = "ChatGLM3-6B"
    timestamp: str
    processing_time: float

class HealthResponse(BaseModel):
    status: str
    components: Dict[str, bool]
    uptime: float
    version: str

class TCMAIAssistant:
    """完整的中医AI助手"""
    
    def __init__(self):
        self.app = FastAPI(
            title="中医AI助手",
            description="基于ChatGLM3-6B的智能中医助手，集成RAG系统",
            version="1.0.0"
        )
        
        # 组件状态
        self.start_time = time.time()
        self.chatglm_api_url = "http://127.0.0.1:8004"
        
        # 初始化RAG系统
        self.rag_system = TCMRAGSystem()
        
        # 对话历史
        self.conversation_history = []
        
        # 设置应用
        self.setup_middleware()
        self.setup_routes()
        
        # 组件状态检查
        self.component_status = {
            "chatglm3_api": False,
            "rag_system": True,
            "elasticsearch": False,
            "vector_db": False
        }
        
        # 启动后台任务
        self.start_background_tasks()
    
    def setup_middleware(self):
        """设置中间件"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def home():
            """主页"""
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <title>中医AI助手</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    h1 { color: #2c3e50; text-align: center; }
                    .chat-box { border: 1px solid #ddd; height: 400px; overflow-y: auto; padding: 15px; margin: 20px 0; background: #fafafa; }
                    .input-group { display: flex; gap: 10px; }
                    input[type="text"] { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                    button { padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
                    button:hover { background: #2980b9; }
                    .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
                    .user { background: #e3f2fd; text-align: right; }
                    .assistant { background: #f1f8e9; }
                    .status { text-align: center; color: #666; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🏥 中医AI助手</h1>
                    <p class="status">基于ChatGLM3-6B的智能中医助手，集成RAG检索增强生成</p>
                    
                    <div class="chat-box" id="chatBox">
                        <div class="message assistant">
                            <strong>中医AI助手：</strong>您好！我是专业的中医AI助手，可以为您解答中医相关问题。请问有什么可以帮助您的吗？
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <input type="text" id="messageInput" placeholder="请输入您的中医问题..." onkeypress="if(event.key==='Enter') sendMessage()">
                        <button onclick="sendMessage()">发送</button>
                        <button onclick="clearChat()">清空</button>
                    </div>
                    
                    <div class="status">
                        <a href="/health" target="_blank">系统状态</a> | 
                        <a href="/docs" target="_blank">API文档</a>
                    </div>
                </div>
                
                <script>
                    async function sendMessage() {
                        const input = document.getElementById('messageInput');
                        const message = input.value.trim();
                        if (!message) return;
                        
                        const chatBox = document.getElementById('chatBox');
                        
                        // 显示用户消息
                        chatBox.innerHTML += `<div class="message user"><strong>您：</strong>${message}</div>`;
                        input.value = '';
                        chatBox.scrollTop = chatBox.scrollHeight;
                        
                        // 显示加载状态
                        chatBox.innerHTML += `<div class="message assistant" id="loading"><strong>中医AI助手：</strong>正在思考中...</div>`;
                        chatBox.scrollTop = chatBox.scrollHeight;
                        
                        try {
                            const response = await fetch('/chat', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ message: message, use_rag: true })
                            });
                            
                            const data = await response.json();
                            
                            // 移除加载状态
                            document.getElementById('loading').remove();
                            
                            // 显示AI回答
                            let sourceInfo = '';
                            if (data.sources && data.sources.length > 0) {
                                sourceInfo = `<br><small>📚 参考了 ${data.sources.length} 条相关资料</small>`;
                            }
                            
                            chatBox.innerHTML += `<div class="message assistant"><strong>中医AI助手：</strong>${data.response}${sourceInfo}</div>`;
                            chatBox.scrollTop = chatBox.scrollHeight;
                            
                        } catch (error) {
                            document.getElementById('loading').remove();
                            chatBox.innerHTML += `<div class="message assistant"><strong>系统：</strong>抱歉，发生了错误：${error.message}</div>`;
                            chatBox.scrollTop = chatBox.scrollHeight;
                        }
                    }
                    
                    function clearChat() {
                        const chatBox = document.getElementById('chatBox');
                        chatBox.innerHTML = `<div class="message assistant"><strong>中医AI助手：</strong>您好！我是专业的中医AI助手，可以为您解答中医相关问题。请问有什么可以帮助您的吗？</div>`;
                    }
                </script>
            </body>
            </html>
            """
        
        @self.app.get("/health", response_model=HealthResponse)
        async def health_check():
            """健康检查"""
            # 检查ChatGLM3 API状态
            try:
                response = requests.get(f"{self.chatglm_api_url}/health", timeout=5)
                self.component_status["chatglm3_api"] = response.status_code == 200
            except:
                self.component_status["chatglm3_api"] = False
            
            # 检查其他组件
            self.component_status["rag_system"] = self.rag_system is not None
            self.component_status["elasticsearch"] = self.rag_system.es_client is not None
            self.component_status["vector_db"] = self.rag_system.faiss_index is not None
            
            uptime = time.time() - self.start_time
            status = "healthy" if self.component_status["chatglm3_api"] else "degraded"
            
            return HealthResponse(
                status=status,
                components=self.component_status,
                uptime=uptime,
                version="1.0.0"
            )
        
        @self.app.post("/chat", response_model=ChatResponse)
        async def chat(request: ChatRequest):
            """智能对话接口"""
            start_time = time.time()
            
            try:
                if request.use_rag:
                    # 使用RAG系统生成回答
                    rag_response = await self.rag_system.generate_rag_response(
                        request.message, 
                        request.max_tokens
                    )
                    
                    response_text = rag_response.get("answer", "抱歉，无法生成回答")
                    sources = rag_response.get("relevant_documents", [])
                    
                else:
                    # 直接调用ChatGLM3 API
                    chat_data = {
                        "model": "chatglm3-6b",
                        "messages": [{"role": "user", "content": request.message}],
                        "temperature": request.temperature,
                        "max_tokens": request.max_tokens
                    }
                    
                    response = requests.post(
                        f"{self.chatglm_api_url}/v1/chat/completions",
                        json=chat_data,
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        response_text = result["choices"][0]["message"]["content"]
                        sources = []
                    else:
                        raise HTTPException(status_code=500, detail="ChatGLM3 API调用失败")
                
                # 保存对话历史
                conversation = {
                    "timestamp": datetime.now().isoformat(),
                    "user": request.message,
                    "assistant": response_text,
                    "use_rag": request.use_rag,
                    "sources_count": len(sources)
                }
                self.conversation_history.append(conversation)
                
                # 限制历史记录数量
                if len(self.conversation_history) > 100:
                    self.conversation_history = self.conversation_history[-100:]
                
                processing_time = time.time() - start_time
                
                return ChatResponse(
                    response=response_text,
                    sources=sources,
                    model="ChatGLM3-6B",
                    timestamp=datetime.now().isoformat(),
                    processing_time=processing_time
                )
                
            except Exception as e:
                logger.error(f"对话处理失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/history")
        async def get_history(limit: int = 20):
            """获取对话历史"""
            return {
                "history": self.conversation_history[-limit:],
                "total": len(self.conversation_history)
            }
        
        @self.app.delete("/history")
        async def clear_history():
            """清空对话历史"""
            cleared_count = len(self.conversation_history)
            self.conversation_history.clear()
            return {"message": f"已清空 {cleared_count} 条对话记录"}
        
        @self.app.get("/search")
        async def search_knowledge(query: str, top_k: int = 5):
            """搜索知识库"""
            try:
                results = await self.rag_system.search_knowledge(query, top_k)
                return {
                    "query": query,
                    "results": results,
                    "count": len(results)
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
    
    def start_background_tasks(self):
        """启动后台任务"""
        def background_monitor():
            """后台监控任务"""
            while True:
                try:
                    # 定期检查组件状态
                    time.sleep(60)  # 每分钟检查一次
                    
                    # 这里可以添加更多后台任务
                    # 比如：清理过期数据、更新知识库等
                    
                except Exception as e:
                    logger.error(f"后台任务错误: {e}")
        
        # 启动后台线程
        monitor_thread = threading.Thread(target=background_monitor, daemon=True)
        monitor_thread.start()
        logger.info("后台监控任务已启动")
    
    async def start_server(self, host: str = "127.0.0.1", port: int = 8005):
        """启动服务器"""
        logger.info("🚀 启动完整的中医AI助手...")
        logger.info("📋 功能特性:")
        logger.info("  ✅ ChatGLM3-6B (6B模型) 智能对话")
        logger.info("  ✅ RAG检索增强生成")
        logger.info("  ✅ 中医知识库搜索")
        logger.info("  ✅ Web界面交互")
        logger.info("  ✅ API接口服务")
        logger.info("  ✅ 对话历史管理")
        logger.info(f"🌐 访问地址: http://{host}:{port}")
        logger.info("📚 API文档: http://{host}:{port}/docs")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

# 全局助手实例
tcm_assistant = TCMAIAssistant()

async def main():
    """主函数"""
    await tcm_assistant.start_server()

if __name__ == "__main__":
    asyncio.run(main())
