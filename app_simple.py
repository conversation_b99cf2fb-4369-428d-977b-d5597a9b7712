"""
简化版RAG系统界面 - 用于调试
"""
import streamlit as st
import sys
import traceback

# 页面配置
st.set_page_config(
    page_title="中医经典RAG问答系统",
    page_icon="📚",
    layout="wide"
)

def main():
    st.title("📚 中医经典RAG问答系统")
    st.write("系统正在启动中...")
    
    try:
        # 测试导入
        st.write("正在测试模块导入...")
        
        import config
        st.success("✅ 配置模块导入成功")
        
        from models.model_manager import model_manager
        st.success("✅ 模型管理器导入成功")
        
        from document_processor import doc_processor
        st.success("✅ 文档处理器导入成功")
        
        from session_manager import session_manager
        st.success("✅ 会话管理器导入成功")
        
        from rag_system import rag_system
        st.success("✅ RAG系统导入成功")
        
        st.write("---")
        st.success("🎉 所有模块导入成功！系统准备就绪。")
        
        # 显示系统信息
        st.subheader("系统信息")
        st.write(f"Python版本: {sys.version}")
        st.write(f"设备: {model_manager.device}")
        
        # 简单的问答界面
        st.subheader("测试问答")
        question = st.text_input("输入测试问题:")
        if st.button("测试") and question:
            st.write(f"您的问题: {question}")
            st.info("这是一个测试回答。完整功能正在开发中...")
        
    except Exception as e:
        st.error(f"❌ 系统启动失败: {str(e)}")
        st.code(traceback.format_exc())

if __name__ == "__main__":
    main()
