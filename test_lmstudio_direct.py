#!/usr/bin/env python3
"""
直接测试LM Studio API连接
"""

import requests
import json

def test_lmstudio_api():
    """测试LM Studio API"""
    api_base = "http://localhost:1234/v1"
    
    print("🔍 测试LM Studio API连接...")
    print(f"API地址: {api_base}")
    
    try:
        # 测试模型列表
        print("\n1. 测试模型列表...")
        response = requests.get(f"{api_base}/models", timeout=5)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API连接成功!")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            models = [model['id'] for model in data.get('data', [])]
            if models:
                print(f"✅ 找到模型: {models}")
                
                # 测试聊天
                print("\n2. 测试聊天API...")
                chat_response = requests.post(
                    f"{api_base}/chat/completions",
                    json={
                        "model": models[0],
                        "messages": [{"role": "user", "content": "你好"}],
                        "max_tokens": 10
                    },
                    timeout=30
                )
                
                print(f"聊天状态码: {chat_response.status_code}")
                if chat_response.status_code == 200:
                    chat_data = chat_response.json()
                    print("✅ 聊天API测试成功!")
                    print(f"回答: {chat_data}")
                else:
                    print(f"❌ 聊天API失败: {chat_response.text}")
            else:
                print("⚠️ API可用但没有模型")
        else:
            print(f"❌ API连接失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("💡 可能原因:")
        print("   1. LM Studio未启动")
        print("   2. API服务器未启动")
        print("   3. 端口1234被占用")
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    test_lmstudio_api()
