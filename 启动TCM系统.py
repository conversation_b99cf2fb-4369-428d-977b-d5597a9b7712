#!/usr/bin/env python3
"""
TCM系统一键启动脚本 - 自动启动所有服务并打开浏览器
"""

import subprocess
import sys
import time
import webbrowser
import os
from pathlib import Path

def main():
    """主函数 - 一键启动TCM系统"""
    print("🏥 家庭私人医生小帮手 - 一键启动")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    main_file = current_dir / "ultimate_final_tcm_system.py"
    
    if not main_file.exists():
        print("❌ 未找到主程序文件")
        print(f"当前目录: {current_dir}")
        print("请确保在正确目录下运行此脚本")
        input("按回车键退出...")
        return False
    
    print("✅ 找到主程序文件")
    
    # 启动智能MCP服务
    print("\n🚀 启动智能MCP服务...")
    try:
        mcp_process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        print(f"✅ 智能MCP服务已启动 (PID: {mcp_process.pid})")
        time.sleep(2)  # 等待MCP服务启动
    except Exception as e:
        print(f"⚠️ MCP服务启动失败: {e}")
        print("系统仍可运行，但MCP功能可能不可用")
    
    # 启动主系统
    print("\n🌐 启动TCM系统主界面...")
    try:
        # 自动打开浏览器
        print("🌐 准备打开浏览器...")
        time.sleep(1)
        webbrowser.open('http://localhost:8501')
        
        print("🚀 启动Streamlit应用...")
        
        # 启动Streamlit - 使用简化配置
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_final_tcm_system.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在停止系统...")
        try:
            mcp_process.terminate()
        except:
            pass
        print("✅ 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
        return False
    
    return True

if __name__ == "__main__":
    print("🎯 使用方法:")
    print("1. 确保在项目根目录下运行")
    print("2. 系统会自动启动MCP服务和主界面")
    print("3. 浏览器会自动打开系统界面")
    print("4. 按 Ctrl+C 停止系统")
    print("\n" + "=" * 50)
    
    success = main()
    
    if not success:
        print("\n❌ 系统启动失败")
        print("💡 故障排除:")
        print("1. 检查是否在正确目录")
        print("2. 检查Python环境和依赖")
        print("3. 检查端口8501和8006是否被占用")
        input("按回车键退出...")
    
    sys.exit(0 if success else 1)
