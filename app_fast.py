"""
快速PDF处理版RAG系统界面
"""
import streamlit as st
import psutil
import gc
import time
import threading
from pathlib import Path

# 页面配置
st.set_page_config(
    page_title="RAG系统 (快速处理版)",
    page_icon="⚡",
    layout="wide"
)

def show_memory_usage():
    """显示内存使用情况"""
    memory = psutil.virtual_memory()
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总内存", f"{memory.total / (1024**3):.1f} GB")
    with col2:
        st.metric("可用内存", f"{memory.available / (1024**3):.1f} GB")
    with col3:
        color = "red" if memory.percent > 90 else "orange" if memory.percent > 80 else "green"
        st.metric("使用率", f"{memory.percent:.1f}%")

def initialize_system():
    """初始化系统"""
    if 'system_initialized' not in st.session_state:
        with st.spinner("正在初始化快速处理系统..."):
            try:
                from rag_system import rag_system
                success = rag_system.initialize()
                st.session_state.system_initialized = success
                
                if success:
                    st.success("✅ 快速处理系统初始化成功！")
                else:
                    st.error("❌ 系统初始化失败")
                    
            except Exception as e:
                st.error(f"初始化错误: {str(e)}")
                st.session_state.system_initialized = False
                
    return st.session_state.get('system_initialized', False)

def process_pdf_fast(uploaded_file):
    """快速处理PDF"""
    if not st.session_state.get('system_initialized', False):
        st.error("请先初始化系统")
        return False
    
    # 检查文件大小
    if uploaded_file.size > 50 * 1024 * 1024:  # 50MB限制
        st.error("文件过大，请选择小于50MB的文件")
        return False
    
    # 检查内存
    memory = psutil.virtual_memory()
    if memory.percent > 85:
        st.error("内存不足，请先清理内存")
        return False
    
    # 保存文件
    import config
    file_path = config.DOCUMENTS_DIR / uploaded_file.name
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    
    # 创建进度条
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # 导入快速处理器
        from fast_document_processor import fast_processor
        
        # 开始处理
        status_text.text("🔄 开始处理PDF...")
        progress_bar.progress(10)
        
        # 使用线程处理以避免阻塞UI
        result_container = {'success': False, 'error': None}
        
        def process_thread():
            try:
                result_container['success'] = fast_processor.process_pdf_fast(str(file_path))
            except Exception as e:
                result_container['error'] = str(e)
        
        # 启动处理线程
        thread = threading.Thread(target=process_thread)
        thread.start()
        
        # 模拟进度更新
        progress_steps = [20, 40, 60, 80, 95]
        step_messages = [
            "📄 提取PDF文本...",
            "🔪 分割文本块...", 
            "🔄 生成向量嵌入...",
            "🏗️ 创建搜索索引...",
            "💾 保存处理结果..."
        ]
        
        for i, (progress, message) in enumerate(zip(progress_steps, step_messages)):
            if thread.is_alive():
                status_text.text(message)
                progress_bar.progress(progress)
                time.sleep(2)  # 等待2秒
            else:
                break
        
        # 等待线程完成
        thread.join(timeout=300)  # 最多等待5分钟
        
        if thread.is_alive():
            st.error("⏰ 处理超时，请尝试更小的文件")
            return False
        
        if result_container['error']:
            st.error(f"❌ 处理失败: {result_container['error']}")
            return False
        
        if result_container['success']:
            progress_bar.progress(100)
            status_text.text("✅ 处理完成！")
            st.success(f"🎉 文档 {uploaded_file.name} 处理成功！")
            
            # 清理内存
            gc.collect()
            return True
        else:
            st.error("❌ 处理失败，请检查文件格式")
            return False
            
    except Exception as e:
        st.error(f"处理错误: {str(e)}")
        return False

def main():
    """主界面"""
    st.title("⚡ RAG系统 - 快速PDF处理版")
    
    # 显示优化说明
    with st.expander("🚀 快速处理版特点", expanded=True):
        st.write("""
        **性能优化:**
        - ⚡ 批量处理：32个文本块一批
        - 🔪 小块分割：200字符/块，减少处理时间
        - 📄 分页处理：10页一批，避免内存溢出
        - 🎯 智能限制：最多500个块，确保速度
        - 🧹 自动清理：处理过程中自动释放内存
        
        **预期速度:**
        - 小文件(<1MB): 30-60秒
        - 中文件(1-5MB): 1-3分钟  
        - 大文件(5-20MB): 3-8分钟
        """)
    
    # 初始化系统
    if not initialize_system():
        st.stop()
    
    # 主要内容
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📁 快速文档处理")
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择PDF文档",
            type=['pdf'],
            help="建议文件大小 < 20MB，页数 < 200页"
        )
        
        if uploaded_file:
            # 显示文件信息
            file_size_mb = uploaded_file.size / (1024 * 1024)
            st.info(f"📄 文件: {uploaded_file.name}")
            st.info(f"📊 大小: {file_size_mb:.2f} MB")
            
            # 处理按钮
            if st.button("⚡ 快速处理", type="primary"):
                with st.spinner("处理中，请稍候..."):
                    success = process_pdf_fast(uploaded_file)
                    
                if success:
                    st.balloons()
                    st.success("🎉 处理完成！现在可以开始问答了。")
        
        # 问答界面
        st.subheader("💬 智能问答")
        
        question = st.text_input(
            "请输入您的问题:",
            placeholder="例如：这个文档的主要内容是什么？"
        )
        
        if st.button("🔍 提问") and question:
            handle_question(question)
    
    with col2:
        st.subheader("📊 系统监控")
        show_memory_usage()
        
        # 清理内存按钮
        if st.button("🧹 清理内存"):
            gc.collect()
            st.success("内存清理完成")
            time.sleep(1)
            st.rerun()
        
        # 系统状态
        st.subheader("⚙️ 系统状态")
        try:
            from rag_system import rag_system
            status = rag_system.get_system_status()
            
            st.metric("已索引块数", status['documents_indexed'])
            st.write(f"🖥️ 设备: {status['device']}")
            
            if status['documents_indexed'] > 0:
                st.success("✅ 已有文档可供问答")
            else:
                st.info("📝 请先上传并处理文档")
                
        except:
            st.warning("⚠️ 无法获取系统状态")

def handle_question(question):
    """处理问题"""
    try:
        from rag_system import rag_system
        from session_manager import session_manager
        
        # 创建会话
        if 'current_session_id' not in st.session_state:
            st.session_state.current_session_id = session_manager.create_session()
        
        with st.spinner("正在思考..."):
            result = rag_system.retrieve_and_generate(
                question, 
                st.session_state.current_session_id
            )
        
        if 'error' in result:
            st.error(f"错误: {result['error']}")
            return
        
        # 显示结果
        st.write("**问题:**", question)
        st.write("**回答:**", result['answer'])
        
        # 显示来源
        if result.get('sources'):
            with st.expander("📖 参考来源"):
                for i, source in enumerate(result['sources'][:2], 1):
                    st.write(f"**来源 {i}:** {Path(source['source']).name}")
                    st.write(f"**内容:** {source['content'][:150]}...")
        
    except Exception as e:
        st.error(f"处理错误: {str(e)}")

if __name__ == "__main__":
    main()
