#!/usr/bin/env python3
"""
终极启动脚本 - 支持本地和异地访问
一键启动家庭中医智能助手，支持全球访问
"""
import subprocess
import sys
import socket
import time
import webbrowser
import threading
import json
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    missing = []
    try:
        import streamlit
    except ImportError:
        missing.append("streamlit")
    
    try:
        import requests
    except ImportError:
        missing.append("requests")
    
    return missing

def get_network_info():
    """获取网络信息"""
    try:
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        return hostname, local_ip
    except:
        return "未知", "localhost"

def start_streamlit_service():
    """启动Streamlit服务"""
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "enhanced_ultra_fast_tcm.py",
        "--server.port", "8517",
        "--server.address", "0.0.0.0",
        "--server.headless", "true",
        "--server.maxUploadSize", "200"
    ]
    
    return subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

def check_ngrok():
    """检查ngrok是否可用"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def start_ngrok_tunnel():
    """启动ngrok隧道"""
    try:
        import requests
        
        # 启动ngrok
        process = subprocess.Popen(
            ['ngrok', 'http', '8517', '--log=stdout'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(4)
        
        # 获取公网地址
        try:
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            tunnels = response.json()
            
            if tunnels['tunnels']:
                public_url = tunnels['tunnels'][0]['public_url']
                return public_url, process
            else:
                return None, process
                
        except Exception:
            return None, process
            
    except Exception:
        return None, None

def create_access_summary(local_ip, public_url=None):
    """创建访问摘要"""
    summary = f"""
🏥 家庭中医智能助手 - 访问地址

📱 本机访问:
   http://localhost:8517

🏠 局域网访问 (同一WiFi):
   http://{local_ip}:8517
"""
    
    if public_url:
        summary += f"""
🌐 异地访问 (全球可访问):
   {public_url}
   
📋 分享给家人朋友:
   将异地访问地址发送给他们即可使用
"""
    else:
        summary += """
❌ 异地访问未启用
   如需异地访问，请运行: python remote_access_setup.py
"""
    
    return summary

def save_quick_access_file(local_ip, public_url=None):
    """保存快速访问文件"""
    content = f"""家庭中医智能助手 - 快速访问

本机访问: http://localhost:8517
局域网访问: http://{local_ip}:8517"""
    
    if public_url:
        content += f"""
异地访问: {public_url}

分享说明:
- 本机访问: 在您的电脑上使用
- 局域网访问: 家人在同一WiFi下使用  
- 异地访问: 任何地方都可以使用，分享给远方的家人朋友

使用方法:
1. 在浏览器中打开任一地址
2. 输入中医相关问题
3. 获得专业的中医知识回答

示例问题:
- 栀子甘草豉汤方的组成
- 防己黄芪汤的功效
- 甘草的作用和功效

注意事项:
- 仅供中医学习参考
- 不能替代医生诊断
- 如有疾病请及时就医
"""
    
    with open("快速访问地址.txt", "w", encoding="utf-8") as f:
        f.write(content)

def main():
    """主函数"""
    print("🏥 家庭中医智能助手 - 终极启动器")
    print("=" * 60)
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        print("💡 请运行: pip install streamlit requests")
        input("按回车键退出...")
        return
    
    # 检查知识库
    if not Path("vector_db/chunks.pkl").exists():
        print("❌ 知识库文件不存在")
        print("💡 请先运行 emergency_fix.py 生成知识库")
        input("按回车键退出...")
        return
    
    # 获取网络信息
    hostname, local_ip = get_network_info()
    
    print("✅ 系统检查通过")
    print(f"🖥️ 主机名: {hostname}")
    print(f"🌐 本机IP: {local_ip}")
    print()
    
    # 询问是否启用异地访问
    enable_remote = False
    if check_ngrok():
        print("🔍 检测到ngrok，支持异地访问")
        choice = input("是否启用异地访问？(y/n，默认n): ").lower().strip()
        enable_remote = choice == 'y'
    else:
        print("💡 如需异地访问，请先安装ngrok")
        print("   运行: python remote_access_setup.py")
    
    print()
    print("🚀 正在启动服务...")
    
    # 启动Streamlit
    streamlit_process = start_streamlit_service()
    if not streamlit_process:
        print("❌ Streamlit启动失败")
        return
    
    print("⏳ 等待服务启动...")
    time.sleep(4)
    
    # 启动ngrok（如果需要）
    public_url = None
    ngrok_process = None
    
    if enable_remote:
        print("🌐 正在创建异地访问隧道...")
        public_url, ngrok_process = start_ngrok_tunnel()
        
        if public_url:
            print(f"✅ 异地访问已启用: {public_url}")
        else:
            print("❌ 异地访问启动失败，仅启用本地访问")
    
    print()
    print("🎉 启动完成！")
    print("=" * 60)
    
    # 显示访问信息
    access_summary = create_access_summary(local_ip, public_url)
    print(access_summary)
    
    # 保存访问信息
    save_quick_access_file(local_ip, public_url)
    print("💾 访问地址已保存到: 快速访问地址.txt")
    print()
    
    # 询问是否打开浏览器
    try:
        open_browser = input("是否自动打开浏览器？(y/n，默认y): ").lower().strip()
        if open_browser != 'n':
            webbrowser.open("http://localhost:8517")
            print("🌐 浏览器已打开")
    except KeyboardInterrupt:
        pass
    
    print()
    print("🔧 控制说明:")
    print("   - 按 Ctrl+C 停止所有服务")
    print("   - 服务运行期间请保持此窗口开启")
    if public_url:
        print("   - 异地访问地址可分享给任何人")
    print("   - 所有访问地址已保存到文件中")
    print()
    
    # 保持运行
    try:
        print("🔄 服务运行中... (按 Ctrl+C 停止)")
        
        # 监控进程
        while True:
            if streamlit_process.poll() is not None:
                print("❌ Streamlit服务意外停止")
                break
            
            if ngrok_process and ngrok_process.poll() is not None:
                print("⚠️ ngrok隧道意外停止")
                ngrok_process = None
                public_url = None
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 正在停止所有服务...")
        
        # 停止Streamlit
        if streamlit_process:
            streamlit_process.terminate()
            streamlit_process.wait()
            print("✅ Streamlit服务已停止")
        
        # 停止ngrok
        if ngrok_process:
            ngrok_process.terminate()
            ngrok_process.wait()
            print("✅ ngrok隧道已停止")
        
        print("💾 所有数据已保存")
        print("🏥 感谢使用家庭中医智能助手！")

if __name__ == "__main__":
    main()
