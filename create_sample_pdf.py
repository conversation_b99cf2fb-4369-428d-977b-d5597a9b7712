"""
创建示例中医PDF文档
"""
import os
from pathlib import Path

def create_sample_text_file():
    """创建示例文本文件（如果无法创建PDF）"""
    
    sample_content = """
中医基础理论示例文档

第一章 阴阳学说

阴阳学说是中医理论体系的重要组成部分，是古代中国哲学思想在医学领域的具体应用。

一、阴阳的基本概念
阴阳是对自然界相互关联的某些事物或现象对立双方属性的概括。阳代表光明、温热、上升、外向、功能等；阴代表黑暗、寒冷、下降、内向、物质等。

二、阴阳学说在中医学中的应用

1. 说明人体的组织结构
人体是一个有机整体，各个部分都可以用阴阳来概括：
- 上为阳，下为阴
- 外为阳，内为阴  
- 背为阳，腹为阴
- 左为阳，右为阴

2. 说明人体的生理功能
人体的生理活动是阴阳对立统一的结果：
- 气属阳，血属阴
- 气为血之帅，血为气之母
- 阳化气，阴成形

3. 说明人体的病理变化
疾病的发生发展过程，实质上就是阴阳失调的过程：
- 阴阳偏盛：阳盛则热，阴盛则寒
- 阴阳偏衰：阳虚则寒，阴虚则热
- 阴阳互损：阳损及阴，阴损及阳

第二章 五行学说

五行学说是中医理论体系的另一重要组成部分，用木、火、土、金、水五种物质的特性来类比和说明事物的属性及其相互关系。

一、五行的特性
1. 木：具有生长、升发、条达舒畅的特性
2. 火：具有温热、上炎、光明的特性
3. 土：具有生化、承载、受纳的特性
4. 金：具有清洁、肃降、收敛的特性
5. 水：具有寒凉、滋润、向下的特性

二、五行与脏腑的关系
1. 肝属木：肝主疏泄，性喜条达
2. 心属火：心主血脉，心阳温煦全身
3. 脾属土：脾主运化，为气血生化之源
4. 肺属金：肺主气，司呼吸，主肃降
5. 肾属水：肾主水，藏精，主生长发育

三、五行的相互关系
1. 相生关系：
- 木生火：肝藏血以济心
- 火生土：心阳温脾土
- 土生金：脾气散精以充肺
- 金生水：肺气肃降以助肾
- 水生木：肾精滋养肝血

2. 相克关系：
- 木克土：肝气疏泄以助脾运
- 土克水：脾运化水湿
- 水克火：肾水上济心火
- 火克金：心火温煦肺金
- 金克木：肺气肃降抑制肝阳

第三章 脏腑学说

脏腑学说是研究人体脏腑的生理功能、病理变化及其相互关系的理论。

一、五脏的生理功能

1. 心的生理功能
- 心主血脉：心气推动血液在脉管内运行
- 心主神明：心是精神意识思维活动的主宰

2. 肝的生理功能  
- 肝主疏泄：调节气机，促进脾胃运化
- 肝主藏血：储藏血液，调节血量

3. 脾的生理功能
- 脾主运化：运化水谷精微和水液
- 脾主升清：将水谷精微上输于心肺

4. 肺的生理功能
- 肺主气：主呼吸之气和一身之气
- 肺主宣发肃降：宣发卫气，肃降浊气

5. 肾的生理功能
- 肾主藏精：藏先天之精和后天之精
- 肾主水：调节水液代谢

二、六腑的生理功能

1. 胆：储存和排泄胆汁，主决断
2. 胃：受纳腐熟水谷
3. 小肠：受盛化物，泌别清浊  
4. 大肠：传化糟粕
5. 膀胱：储尿和排尿
6. 三焦：通行元气，运行水液

第四章 气血津液学说

气血津液是构成人体和维持人体生命活动的基本物质。

一、气的生理功能
1. 推动作用：推动人体生长发育和脏腑功能活动
2. 温煦作用：维持体温和脏腑温度
3. 防御作用：护卫肌表，防御外邪
4. 固摄作用：固摄血液、津液等
5. 气化作用：促进物质转化

二、血的生理功能
1. 濡养作用：营养全身脏腑组织
2. 载神作用：血为神志活动的物质基础

三、津液的生理功能
1. 滋润作用：滋润脏腑组织
2. 充养作用：充养血脉，化生血液

四、气血津液的关系
1. 气与血：气为血帅，血为气母
2. 气与津液：气能生津，气能行津，津能载气
3. 血与津液：津血同源，相互转化

第五章 经络学说

经络是运行气血、联系脏腑和体表及全身各部的通道，是人体功能的调控系统。

一、经络系统的组成
1. 十二经脉：手三阴、手三阳、足三阴、足三阳
2. 奇经八脉：督脉、任脉、冲脉、带脉、阴跷脉、阳跷脉、阴维脉、阳维脉
3. 十五络脉：十二经络脉、任督二络、脾之大络
4. 十二经别、十二经筋、十二皮部

二、经络的生理功能
1. 运行气血：经络是气血运行的通道
2. 联系脏腑：沟通内外，联系上下
3. 抗御病邪：经络具有传注病邪的作用
4. 调节虚实：双向调节脏腑功能

三、经络学说的临床应用
1. 指导诊断：根据经络循行诊断疾病
2. 指导治疗：循经取穴，经络辨证
3. 指导针灸：经络是针灸治疗的理论基础

结语

中医基础理论是中医学的理论基础，包括阴阳学说、五行学说、脏腑学说、气血津液学说、经络学说等。这些理论相互联系，构成了完整的中医理论体系，指导着中医的临床实践。

学习中医基础理论，要注重理论联系实际，在临床实践中不断加深理解，才能真正掌握中医学的精髓。
"""
    
    # 创建documents目录
    docs_dir = Path("documents")
    docs_dir.mkdir(exist_ok=True)
    
    # 保存为文本文件
    txt_file = docs_dir / "中医基础理论_示例.txt"
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print(f"✅ 创建示例文档: {txt_file}")
    
    # 尝试创建PDF版本
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        
        pdf_file = docs_dir / "中医基础理论_示例.pdf"
        
        # 创建PDF文档
        doc = SimpleDocTemplate(str(pdf_file), pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # 分段处理文本
        paragraphs = sample_content.split('\n\n')
        for para in paragraphs:
            if para.strip():
                if para.startswith('第') and '章' in para:
                    # 章节标题
                    p = Paragraph(para.strip(), styles['Heading1'])
                elif para.strip().endswith('：') or para.strip().endswith(':'):
                    # 小标题
                    p = Paragraph(para.strip(), styles['Heading2'])
                else:
                    # 正文
                    p = Paragraph(para.strip(), styles['Normal'])
                story.append(p)
                story.append(Spacer(1, 12))
        
        doc.build(story)
        print(f"✅ 创建示例PDF: {pdf_file}")
        
    except ImportError:
        print("📝 未安装reportlab，只创建了文本文件")
    except Exception as e:
        print(f"⚠️ 创建PDF失败: {e}")
    
    return True

if __name__ == "__main__":
    create_sample_text_file()
