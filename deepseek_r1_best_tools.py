#!/usr/bin/env python3
"""
DeepSeek-R1-0528-Qwen3-8B 最佳推理工具选择器
找到并安装最适合您系统的推理引擎
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class DeepSeekR1ToolSelector:
    """DeepSeek-R1最佳工具选择器"""
    
    def __init__(self):
        self.system_info = self.get_system_info()
        self.tools = {
            "Ollama": {
                "description": "最简单易用，自动管理模型",
                "performance": "⭐⭐⭐⭐",
                "ease_of_use": "⭐⭐⭐⭐⭐",
                "gpu_support": "✅ 自动检测",
                "memory_efficiency": "⭐⭐⭐⭐",
                "qwen3_support": "✅ 原生支持",
                "install_cmd": "curl -fsSL https://ollama.com/install.sh | sh",
                "model_cmd": "ollama run deepseek-r1:8b",
                "pros": ["一键安装", "自动模型管理", "API兼容", "跨平台"],
                "cons": ["较新工具", "定制性较低"]
            },
            "vLLM": {
                "description": "高性能推理引擎，生产级",
                "performance": "⭐⭐⭐⭐⭐",
                "ease_of_use": "⭐⭐⭐",
                "gpu_support": "✅ CUDA/ROCm",
                "memory_efficiency": "⭐⭐⭐⭐⭐",
                "qwen3_support": "✅ 完全支持",
                "install_cmd": "pip install vllm",
                "model_cmd": "python -m vllm.entrypoints.openai.api_server --model deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                "pros": ["最高性能", "生产级稳定", "OpenAI API兼容", "批处理优化"],
                "cons": ["需要GPU", "配置复杂"]
            },
            "SGLang": {
                "description": "最新高性能框架，专为推理优化",
                "performance": "⭐⭐⭐⭐⭐",
                "ease_of_use": "⭐⭐⭐⭐",
                "gpu_support": "✅ CUDA/ROCm",
                "memory_efficiency": "⭐⭐⭐⭐⭐",
                "qwen3_support": "✅ 原生支持",
                "install_cmd": "pip install sglang[all]",
                "model_cmd": "python -m sglang.launch_server --model-path deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                "pros": ["最新技术", "极高性能", "内存优化", "易于集成"],
                "cons": ["较新项目", "文档较少"]
            },
            "Transformers": {
                "description": "HuggingFace官方库，最兼容",
                "performance": "⭐⭐⭐",
                "ease_of_use": "⭐⭐⭐⭐⭐",
                "gpu_support": "✅ 自动检测",
                "memory_efficiency": "⭐⭐⭐",
                "qwen3_support": "✅ 官方支持",
                "install_cmd": "pip install transformers torch",
                "model_cmd": "直接Python调用",
                "pros": ["最兼容", "文档完善", "社区支持", "易于使用"],
                "cons": ["性能一般", "内存占用大"]
            }
        }
    
    def get_system_info(self):
        """获取系统信息"""
        info = {
            "os": platform.system(),
            "arch": platform.machine(),
            "python": sys.version,
            "has_gpu": self.check_gpu(),
            "memory_gb": self.get_memory_gb()
        }
        return info
    
    def check_gpu(self):
        """检查GPU"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def get_memory_gb(self):
        """获取内存大小"""
        try:
            import psutil
            return psutil.virtual_memory().total / (1024**3)
        except ImportError:
            return 32  # 默认值
    
    def recommend_tool(self):
        """推荐最佳工具"""
        print("🔍 分析您的系统配置...")
        print(f"操作系统: {self.system_info['os']}")
        print(f"架构: {self.system_info['arch']}")
        print(f"GPU: {'✅ 可用' if self.system_info['has_gpu'] else '❌ 不可用'}")
        print(f"内存: {self.system_info['memory_gb']:.1f}GB")
        print()
        
        # 基于系统配置推荐
        if self.system_info['has_gpu'] and self.system_info['memory_gb'] >= 16:
            recommendations = ["SGLang", "vLLM", "Ollama", "Transformers"]
            reason = "您有GPU和充足内存，推荐高性能工具"
        elif self.system_info['memory_gb'] >= 16:
            recommendations = ["Ollama", "Transformers", "SGLang", "vLLM"]
            reason = "CPU推理，推荐易用性优先"
        else:
            recommendations = ["Ollama", "Transformers"]
            reason = "内存有限，推荐轻量级工具"
        
        print(f"💡 推荐理由: {reason}")
        print()
        
        return recommendations
    
    def display_comparison(self):
        """显示工具对比"""
        print("📊 DeepSeek-R1推理工具对比")
        print("=" * 80)
        
        for name, info in self.tools.items():
            print(f"\n🛠️  {name}")
            print(f"   描述: {info['description']}")
            print(f"   性能: {info['performance']}")
            print(f"   易用性: {info['ease_of_use']}")
            print(f"   GPU支持: {info['gpu_support']}")
            print(f"   内存效率: {info['memory_efficiency']}")
            print(f"   Qwen3支持: {info['qwen3_support']}")
            print(f"   优点: {', '.join(info['pros'])}")
            print(f"   缺点: {', '.join(info['cons'])}")
    
    def install_tool(self, tool_name):
        """安装选定的工具"""
        if tool_name not in self.tools:
            print(f"❌ 未知工具: {tool_name}")
            return False
        
        tool = self.tools[tool_name]
        print(f"📥 安装 {tool_name}...")
        print(f"命令: {tool['install_cmd']}")
        
        try:
            if tool_name == "Ollama":
                return self.install_ollama()
            else:
                result = subprocess.run(
                    tool['install_cmd'].split(),
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                if result.returncode == 0:
                    print(f"✅ {tool_name} 安装成功!")
                    return True
                else:
                    print(f"❌ {tool_name} 安装失败: {result.stderr}")
                    return False
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            return False
    
    def install_ollama(self):
        """安装Ollama"""
        if self.system_info['os'] == "Windows":
            print("💡 Windows用户请访问: https://ollama.com/download")
            print("下载并安装Ollama for Windows")
            return True
        else:
            try:
                result = subprocess.run(
                    ["curl", "-fsSL", "https://ollama.com/install.sh"],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    subprocess.run(["sh"], input=result.stdout, text=True)
                    print("✅ Ollama 安装成功!")
                    return True
                else:
                    print("❌ Ollama 安装失败")
                    return False
            except Exception as e:
                print(f"❌ 安装失败: {e}")
                return False
    
    def setup_model(self, tool_name):
        """设置模型"""
        if tool_name not in self.tools:
            return False
        
        tool = self.tools[tool_name]
        print(f"\n🚀 设置 {tool_name} 模型...")
        
        if tool_name == "Ollama":
            return self.setup_ollama_model()
        elif tool_name == "vLLM":
            return self.setup_vllm_model()
        elif tool_name == "SGLang":
            return self.setup_sglang_model()
        elif tool_name == "Transformers":
            return self.setup_transformers_model()
        
        return False
    
    def setup_ollama_model(self):
        """设置Ollama模型"""
        print("📥 下载DeepSeek-R1模型...")
        try:
            result = subprocess.run(
                ["ollama", "pull", "deepseek-r1:8b"],
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            if result.returncode == 0:
                print("✅ 模型下载成功!")
                print("🚀 启动命令: ollama run deepseek-r1:8b")
                return True
            else:
                print(f"❌ 模型下载失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 设置失败: {e}")
            return False
    
    def setup_vllm_model(self):
        """设置vLLM模型"""
        script_content = '''
import subprocess
import sys

# 启动vLLM服务器
cmd = [
    sys.executable, "-m", "vllm.entrypoints.openai.api_server",
    "--model", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
    "--host", "0.0.0.0",
    "--port", "8000",
    "--trust-remote-code"
]

print("🚀 启动vLLM服务器...")
print("命令:", " ".join(cmd))
subprocess.run(cmd)
'''
        
        with open("start_vllm.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ vLLM启动脚本已创建: start_vllm.py")
        print("🚀 启动命令: python start_vllm.py")
        return True
    
    def setup_sglang_model(self):
        """设置SGLang模型"""
        script_content = '''
import subprocess
import sys

# 启动SGLang服务器
cmd = [
    sys.executable, "-m", "sglang.launch_server",
    "--model-path", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
    "--host", "0.0.0.0",
    "--port", "30000",
    "--trust-remote-code"
]

print("🚀 启动SGLang服务器...")
print("命令:", " ".join(cmd))
subprocess.run(cmd)
'''
        
        with open("start_sglang.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ SGLang启动脚本已创建: start_sglang.py")
        print("🚀 启动命令: python start_sglang.py")
        return True
    
    def setup_transformers_model(self):
        """设置Transformers模型"""
        script_content = '''
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

print("📥 加载DeepSeek-R1模型...")

# 加载模型和分词器
model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True
)

print("✅ 模型加载成功!")

# 测试生成
def generate_response(prompt, max_length=512):
    inputs = tokenizer(prompt, return_tensors="pt")
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_length=max_length,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response[len(prompt):]

# 示例使用
if __name__ == "__main__":
    prompt = "中医是什么？"
    response = generate_response(prompt)
    print(f"问题: {prompt}")
    print(f"回答: {response}")
'''
        
        with open("deepseek_transformers.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ Transformers脚本已创建: deepseek_transformers.py")
        print("🚀 启动命令: python deepseek_transformers.py")
        return True

def main():
    """主函数"""
    print("🤖 DeepSeek-R1-0528-Qwen3-8B 最佳推理工具选择器")
    print("=" * 60)
    
    selector = DeepSeekR1ToolSelector()
    
    # 显示工具对比
    selector.display_comparison()
    
    # 获取推荐
    recommendations = selector.recommend_tool()
    
    print("🎯 推荐工具排序:")
    for i, tool in enumerate(recommendations, 1):
        print(f"{i}. {tool} - {selector.tools[tool]['description']}")
    
    print()
    choice = input("请选择要安装的工具 (1-4) 或按回车使用推荐: ").strip()
    
    if not choice:
        selected_tool = recommendations[0]
    else:
        try:
            selected_tool = recommendations[int(choice) - 1]
        except (ValueError, IndexError):
            print("❌ 无效选择")
            return
    
    print(f"\n📦 准备安装: {selected_tool}")
    
    # 安装工具
    if selector.install_tool(selected_tool):
        # 设置模型
        if selector.setup_model(selected_tool):
            print(f"\n🎉 {selected_tool} 安装和配置完成!")
            print("\n💡 下一步:")
            print("1. 启动推理服务")
            print("2. 集成到您的RAG系统")
            print("3. 享受DeepSeek-R1的强大能力!")
        else:
            print(f"\n⚠️ {selected_tool} 安装成功但模型配置失败")
    else:
        print(f"\n❌ {selected_tool} 安装失败")

if __name__ == "__main__":
    main()
