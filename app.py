"""
RAG系统 Streamlit Web界面
"""
import streamlit as st
import os
import time
from pathlib import Path
from rag_system import rag_system
from session_manager import session_manager
import config

# 页面配置
st.set_page_config(
    page_title="中医经典RAG问答系统",
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    text-align: center;
    color: #2E8B57;
    font-size: 2.5rem;
    margin-bottom: 2rem;
}
.chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.user-message {
    background-color: #E3F2FD;
    border-left: 4px solid #2196F3;
}
.assistant-message {
    background-color: #F1F8E9;
    border-left: 4px solid #4CAF50;
}
.source-box {
    background-color: #FFF3E0;
    border: 1px solid #FF9800;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

def initialize_system():
    """初始化系统"""
    if 'system_initialized' not in st.session_state:
        with st.spinner("正在初始化RAG系统，请稍候..."):
            success = rag_system.initialize()
            st.session_state.system_initialized = success
            if success:
                st.success("系统初始化成功！")
            else:
                st.error("系统初始化失败，请检查配置")
    return st.session_state.system_initialized

def sidebar():
    """侧边栏"""
    st.sidebar.title("📚 中医经典RAG系统")
    
    # 系统状态
    st.sidebar.subheader("系统状态")
    if st.session_state.get('system_initialized', False):
        status = rag_system.get_system_status()
        st.sidebar.success("✅ 系统已就绪")
        st.sidebar.write(f"📄 已索引文档: {status['documents_indexed']} 块")
        st.sidebar.write(f"🖥️ 运行设备: {status['device']}")
    else:
        st.sidebar.warning("⚠️ 系统未初始化")
    
    st.sidebar.divider()
    
    # 文档管理
    st.sidebar.subheader("📁 文档管理")
    
    uploaded_files = st.sidebar.file_uploader(
        "上传PDF文档",
        type=['pdf'],
        accept_multiple_files=True,
        help="支持上传《黄帝内经》、《伤寒论》等中医经典PDF文档"
    )
    
    if uploaded_files and st.sidebar.button("处理文档"):
        process_documents(uploaded_files)
    
    st.sidebar.divider()
    
    # 会话管理
    st.sidebar.subheader("💬 会话管理")
    
    if st.sidebar.button("新建会话"):
        new_session()
    
    # 显示会话列表
    sessions = session_manager.get_all_sessions()
    if sessions:
        session_options = {f"{s['title']} ({s['message_count']}条)": s['id'] for s in sessions}
        selected_session = st.sidebar.selectbox(
            "选择会话",
            options=list(session_options.keys()),
            index=0 if 'current_session_id' not in st.session_state else 
                  list(session_options.values()).index(st.session_state.current_session_id) 
                  if st.session_state.current_session_id in session_options.values() else 0
        )
        
        if selected_session:
            st.session_state.current_session_id = session_options[selected_session]
    
    # 清理按钮
    if st.sidebar.button("清理旧会话"):
        session_manager.cleanup_old_sessions()
        st.sidebar.success("清理完成")
        st.rerun()

def process_documents(uploaded_files):
    """处理上传的文档"""
    if not st.session_state.get('system_initialized', False):
        st.error("请先初始化系统")
        return
    
    # 保存上传的文件
    saved_files = []
    for uploaded_file in uploaded_files:
        file_path = config.DOCUMENTS_DIR / uploaded_file.name
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        saved_files.append(str(file_path))
    
    # 处理文档
    with st.spinner("正在处理文档，请稍候..."):
        success = rag_system.process_documents(saved_files)
        
    if success:
        st.success(f"成功处理 {len(saved_files)} 个文档！")
        st.rerun()
    else:
        st.error("文档处理失败")

def new_session():
    """创建新会话"""
    session_id = session_manager.create_session()
    if session_id:
        st.session_state.current_session_id = session_id
        st.session_state.messages = []
        st.success("新会话已创建")
        st.rerun()

def display_chat_history():
    """显示聊天历史"""
    if 'current_session_id' in st.session_state:
        history = session_manager.get_session_history(st.session_state.current_session_id)
        
        for item in history:
            # 用户消息
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>🙋 用户:</strong><br>
                {item['question']}
            </div>
            """, unsafe_allow_html=True)
            
            # 助手回复
            st.markdown(f"""
            <div class="chat-message assistant-message">
                <strong>🤖 助手:</strong><br>
                {item['answer']}
            </div>
            """, unsafe_allow_html=True)

def main():
    """主界面"""
    # 页面标题
    st.markdown('<h1 class="main-header">📚 中医经典RAG问答系统</h1>', unsafe_allow_html=True)
    
    # 初始化系统
    if not initialize_system():
        st.stop()
    
    # 侧边栏
    sidebar()
    
    # 主要内容区域
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("💬 智能问答")
        
        # 显示聊天历史
        if 'current_session_id' in st.session_state:
            display_chat_history()
        
        # 问题输入
        question = st.text_input(
            "请输入您的问题:",
            placeholder="例如：黄帝内经中关于五脏六腑的理论是什么？",
            key="question_input"
        )
        
        col_ask, col_stream = st.columns([1, 1])
        
        with col_ask:
            ask_button = st.button("🔍 提问", type="primary")
        
        with col_stream:
            stream_button = st.button("⚡ 流式回答")
        
        # 处理问题
        if (ask_button or stream_button) and question:
            if 'current_session_id' not in st.session_state:
                st.session_state.current_session_id = session_manager.create_session()
            
            if stream_button:
                handle_stream_question(question)
            else:
                handle_question(question)
    
    with col2:
        st.subheader("📊 系统信息")
        
        # 显示系统状态
        status = rag_system.get_system_status()
        
        st.metric("已索引文档块", status['documents_indexed'])
        st.metric("运行设备", status['device'])
        
        # 模型状态
        st.write("**模型状态:**")
        st.write(f"✅ 嵌入模型: {'已加载' if status['models_loaded']['embedding'] else '未加载'}")
        st.write(f"✅ 语言模型: {'已加载' if status['models_loaded']['llm'] else '未加载'}")

def handle_question(question):
    """处理普通问题"""
    with st.spinner("正在思考中..."):
        result = rag_system.retrieve_and_generate(
            question, 
            st.session_state.current_session_id
        )
    
    if 'error' in result:
        st.error(f"错误: {result['error']}")
        return
    
    # 显示回答
    st.markdown(f"""
    <div class="chat-message user-message">
        <strong>🙋 用户:</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)
    
    st.markdown(f"""
    <div class="chat-message assistant-message">
        <strong>🤖 助手:</strong><br>
        {result['answer']}
    </div>
    """, unsafe_allow_html=True)
    
    # 显示参考来源
    if result.get('sources'):
        st.subheader("📖 参考来源")
        for i, source in enumerate(result['sources'], 1):
            st.markdown(f"""
            <div class="source-box">
                <strong>来源 {i}:</strong> {Path(source['source']).name}<br>
                <strong>相似度:</strong> {source['similarity']:.3f}<br>
                <strong>内容:</strong> {source['content']}
            </div>
            """, unsafe_allow_html=True)

def handle_stream_question(question):
    """处理流式问题"""
    # 显示用户问题
    st.markdown(f"""
    <div class="chat-message user-message">
        <strong>🙋 用户:</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)
    
    # 流式显示回答
    response_placeholder = st.empty()
    
    full_response = ""
    for chunk in rag_system.stream_generate(question, st.session_state.current_session_id):
        full_response = chunk
        response_placeholder.markdown(f"""
        <div class="chat-message assistant-message">
            <strong>🤖 助手:</strong><br>
            {full_response}
        </div>
        """, unsafe_allow_html=True)
        time.sleep(0.05)  # 控制流式速度

if __name__ == "__main__":
    main()
