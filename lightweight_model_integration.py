#!/usr/bin/env python3
"""
轻量级模型集成
根据用户电脑配置推荐最适合的小模型
"""
import torch
import psutil
from transformers import AutoTokenizer, AutoModelForCausalLM
import streamlit as st
from pathlib import Path

def analyze_system_specs():
    """分析系统配置"""
    specs = {
        'total_memory_gb': psutil.virtual_memory().total / (1024**3),
        'available_memory_gb': psutil.virtual_memory().available / (1024**3),
        'cpu_cores': psutil.cpu_count(),
        'cuda_available': torch.cuda.is_available(),
        'gpu_memory_gb': 0
    }
    
    if specs['cuda_available']:
        try:
            specs['gpu_memory_gb'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            specs['gpu_name'] = torch.cuda.get_device_properties(0).name
        except:
            specs['cuda_available'] = False
    
    return specs

def recommend_model(specs):
    """根据配置推荐模型"""
    recommendations = []
    
    # 您的配置：30.7GB内存，CPU处理
    if specs['total_memory_gb'] >= 16:
        recommendations.extend([
            {
                'name': 'Qwen2-1.5B-Instruct',
                'size': '1.5B参数',
                'memory_req': '3-4GB',
                'speed': '快',
                'quality': '优秀',
                'chinese_support': '优秀',
                'model_id': 'Qwen/Qwen2-1.5B-Instruct',
                'recommended': True,
                'reason': '专为中文优化，参数量适中，在您的配置下运行流畅'
            },
            {
                'name': 'ChatGLM3-6B',
                'size': '6B参数', 
                'memory_req': '12-16GB',
                'speed': '中等',
                'quality': '优秀',
                'chinese_support': '优秀',
                'model_id': 'THUDM/chatglm3-6b',
                'recommended': True,
                'reason': '清华大学开发，中文理解能力强，适合中医问答'
            },
            {
                'name': 'Baichuan2-7B-Chat',
                'size': '7B参数',
                'memory_req': '14-18GB', 
                'speed': '中等',
                'quality': '优秀',
                'chinese_support': '优秀',
                'model_id': 'baichuan-inc/Baichuan2-7B-Chat',
                'recommended': False,
                'reason': '百川智能开发，中文能力强但内存需求较高'
            }
        ])
    
    return recommendations

def setup_lightweight_model(model_id):
    """设置轻量级模型"""
    try:
        st.info(f"正在加载模型: {model_id}")
        
        # 使用CPU和优化设置
        tokenizer = AutoTokenizer.from_pretrained(
            model_id,
            trust_remote_code=True,
            cache_dir="./model_cache"
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            model_id,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else "cpu",
            trust_remote_code=True,
            cache_dir="./model_cache",
            low_cpu_mem_usage=True
        )
        
        return tokenizer, model, True
        
    except Exception as e:
        st.error(f"模型加载失败: {e}")
        return None, None, False

def generate_with_lightweight_model(tokenizer, model, prompt, max_length=200):
    """使用轻量级模型生成回答"""
    try:
        # 构建中医专用提示词
        system_prompt = """你是一个专业的中医智能助手。请根据提供的中医文献内容，给出准确、专业的回答。
回答要求：
1. 基于中医理论
2. 语言简洁明了
3. 包含方剂组成（如适用）
4. 提醒仅供参考，建议咨询专业医师

用户问题："""
        
        full_prompt = system_prompt + prompt
        
        inputs = tokenizer.encode(full_prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_length=len(inputs[0]) + max_length,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        # 移除输入部分
        response = response[len(full_prompt):].strip()
        
        return response
        
    except Exception as e:
        return f"生成回答时出错: {e}"

def create_model_setup_interface():
    """创建模型设置界面"""
    st.header("🤖 智能模型设置")
    
    # 分析系统配置
    specs = analyze_system_specs()
    
    # 显示系统信息
    st.subheader("💻 您的系统配置")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("总内存", f"{specs['total_memory_gb']:.1f} GB")
        st.metric("可用内存", f"{specs['available_memory_gb']:.1f} GB")
    
    with col2:
        st.metric("CPU核心", specs['cpu_cores'])
        st.metric("CUDA支持", "✅" if specs['cuda_available'] else "❌")
    
    with col3:
        if specs['cuda_available']:
            st.metric("GPU显存", f"{specs['gpu_memory_gb']:.1f} GB")
            st.metric("GPU型号", specs.get('gpu_name', '未知'))
        else:
            st.metric("处理器", "CPU")
            st.metric("推荐模式", "轻量级")
    
    st.divider()
    
    # 模型推荐
    st.subheader("🎯 推荐模型")
    recommendations = recommend_model(specs)
    
    for model in recommendations:
        with st.expander(f"{'🌟 ' if model['recommended'] else ''}**{model['name']}** - {model['size']}", 
                        expanded=model['recommended']):
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**参数规模：** {model['size']}")
                st.write(f"**内存需求：** {model['memory_req']}")
                st.write(f"**运行速度：** {model['speed']}")
                st.write(f"**回答质量：** {model['quality']}")
                st.write(f"**中文支持：** {model['chinese_support']}")
                st.write(f"**推荐理由：** {model['reason']}")
            
            with col2:
                if model['recommended']:
                    st.success("🌟 推荐")
                
                if st.button(f"安装 {model['name']}", key=f"install_{model['name']}"):
                    install_model(model['model_id'], model['name'])
    
    st.divider()
    
    # 当前模型状态
    st.subheader("📊 当前模型状态")
    
    model_cache_dir = Path("./model_cache")
    if model_cache_dir.exists():
        cached_models = list(model_cache_dir.glob("*"))
        if cached_models:
            st.success(f"✅ 已缓存 {len(cached_models)} 个模型")
            for model_dir in cached_models:
                st.write(f"- {model_dir.name}")
        else:
            st.info("📦 暂无已安装的模型")
    else:
        st.info("📦 暂无已安装的模型")

def install_model(model_id, model_name):
    """安装模型"""
    try:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        status_text.text(f"正在下载 {model_name}...")
        progress_bar.progress(25)
        
        # 下载tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            model_id,
            trust_remote_code=True,
            cache_dir="./model_cache"
        )
        
        progress_bar.progress(50)
        status_text.text(f"正在下载模型权重...")
        
        # 下载模型
        model = AutoModelForCausalLM.from_pretrained(
            model_id,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            trust_remote_code=True,
            cache_dir="./model_cache",
            low_cpu_mem_usage=True
        )
        
        progress_bar.progress(100)
        status_text.text("安装完成！")
        
        st.success(f"🎉 {model_name} 安装成功！")
        st.info("💡 现在可以在智能问答中使用AI增强回答功能")
        
        # 保存模型配置
        config = {
            'model_id': model_id,
            'model_name': model_name,
            'installed_at': str(datetime.now())
        }
        
        with open('./model_cache/current_model.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        progress_bar.empty()
        status_text.empty()
        
    except Exception as e:
        st.error(f"❌ 安装失败: {e}")
        st.info("💡 建议选择更小的模型或检查网络连接")

def main():
    """主函数"""
    st.title("🤖 轻量级AI模型集成")
    
    st.markdown("""
    **根据您的配置推荐：**
    - 💻 **内存：** 30.7GB（优秀）
    - 🔧 **处理器：** CPU模式
    - 🎯 **推荐：** Qwen2-1.5B 或 ChatGLM3-6B
    - ⚡ **优势：** 快速响应，中文优化
    """)
    
    create_model_setup_interface()

if __name__ == "__main__":
    main()
