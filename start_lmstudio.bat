@echo off
echo 🚀 启动LM Studio服务...

REM 检查LM Studio是否已运行
tasklist /FI "IMAGENAME eq LM Studio.exe" 2>NUL | find /I /N "LM Studio.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ LM Studio已在运行
) else (
    echo 📥 启动LM Studio...
    start "" "C:\Users\<USER>\AppData\Local\LM Studio\LM Studio.exe"
    echo ⏳ 等待LM Studio启动...
    timeout /t 10 /nobreak >nul
)

echo 🔄 检查API服务状态...
curl -s http://localhost:1234/v1/models >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LM Studio API服务已就绪
) else (
    echo ⚠️ 请在LM Studio中手动启动服务器
    echo 💡 操作步骤:
    echo    1. 打开LM Studio
    echo    2. 点击"Local Server"
    echo    3. 选择DeepSeek模型
    echo    4. 点击"Start Server"
)

pause
