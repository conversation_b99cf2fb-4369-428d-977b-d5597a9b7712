#!/usr/bin/env python3
"""
移动端和语音功能最终测试
"""
import requests
import json
import time

def test_mobile_ui():
    """测试移动端UI适配"""
    print("📱 测试移动端UI适配")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8006", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查移动端适配元素
            mobile_elements = [
                "@media (max-width: 768px)",  # 移动端媒体查询
                "flex-direction: column",      # 移动端布局
                "width: 100%",                # 全宽布局
                "min-height: 400px"           # 最小高度
            ]
            
            found_elements = []
            for element in mobile_elements:
                if element in content:
                    found_elements.append(element)
            
            print(f"✅ 移动端适配检查:")
            print(f"  发现适配: {len(found_elements)}/{len(mobile_elements)}")
            for element in found_elements:
                print(f"  ✓ {element}")
            
            return len(found_elements) >= 3  # 至少3个适配元素
            
        else:
            print(f"❌ 无法访问页面: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 移动端UI测试失败: {e}")
        return False

def test_voice_features():
    """测试语音功能"""
    print("\n🎤 测试语音功能")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:8006", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查语音相关功能
            voice_features = [
                "toggleVoiceInput",      # 语音输入
                "toggleVoiceOutput",     # 语音播放开关
                "speakText",             # 语音播放
                "speechSynthesis",       # 语音合成
                "voiceOutputEnabled",    # 语音开关状态
                "🎤",                    # 语音输入按钮
                "🔊"                     # 语音播放按钮
            ]
            
            found_features = []
            for feature in voice_features:
                if feature in content:
                    found_features.append(feature)
            
            print(f"✅ 语音功能检查:")
            print(f"  发现功能: {len(found_features)}/{len(voice_features)}")
            for feature in found_features:
                print(f"  ✓ {feature}")
            
            return len(found_features) >= 5  # 至少5个语音功能
            
        else:
            print(f"❌ 无法访问页面: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 语音功能测试失败: {e}")
        return False

def test_remote_access():
    """测试远程访问配置"""
    print("\n🌐 测试远程访问配置")
    print("-" * 30)
    
    try:
        # 测试健康检查接口
        response = requests.get("http://localhost:8006/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器状态: {data.get('status')}")
            print(f"  版本: {data.get('version')}")
            print(f"  功能数: {len(data.get('features', []))}")
            
            # 检查是否支持CORS（跨域访问）
            headers = response.headers
            cors_headers = [
                'access-control-allow-origin',
                'access-control-allow-methods',
                'access-control-allow-headers'
            ]
            
            cors_support = any(header in headers for header in cors_headers)
            if cors_support:
                print(f"✅ CORS支持: 已启用（支持跨域访问）")
            else:
                print(f"⚠️ CORS支持: 未检测到")
            
            return True
            
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 远程访问测试失败: {e}")
        return False

def test_chat_with_voice():
    """测试聊天和语音播放"""
    print("\n💬 测试聊天和语音播放")
    print("-" * 30)
    
    try:
        # 发送测试消息
        response = requests.post(
            "http://localhost:8006/api/chat",
            json={"message": "你好，请简单介绍一下栀子甘草豉汤"},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 聊天功能: 正常")
            print(f"  会话ID: {data.get('session_id')}")
            print(f"  响应长度: {len(data.get('response', ''))}")
            print(f"  处理时间: {data.get('processing_time', 0):.2f}s")
            print(f"  来源数量: {len(data.get('sources', []))}")
            
            # 检查响应内容是否适合语音播放
            response_text = data.get('response', '')
            
            # 检查是否包含过多的格式化字符
            format_chars = ['#', '*', '`', '[', ']', '(', ')']
            format_count = sum(response_text.count(char) for char in format_chars)
            
            if format_count < len(response_text) * 0.1:  # 格式化字符少于10%
                print(f"✅ 语音播放适配: 内容适合语音播放")
            else:
                print(f"⚠️ 语音播放适配: 内容包含较多格式化字符")
            
            return True
            
        else:
            print(f"❌ 聊天测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天测试失败: {e}")
        return False

def test_network_info():
    """显示网络访问信息"""
    print("\n🌍 网络访问信息")
    print("-" * 30)
    
    import socket
    
    try:
        # 获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        print(f"📱 本机IP: {local_ip}")
        print(f"🔗 手机访问地址: http://{local_ip}:8006")
        print(f"💻 电脑访问地址: http://localhost:8006")
        print()
        print("📋 使用指南:")
        print("  1. 确保手机和电脑在同一WiFi")
        print("  2. 手机浏览器输入上述地址")
        print("  3. 点击🎤进行语音输入")
        print("  4. 点击🔊开关语音播放")
        print("  5. 支持连续对话和上下文记忆")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络信息获取失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 移动端和语音功能最终测试")
    print("=" * 60)
    
    tests = [
        ("移动端UI适配", test_mobile_ui),
        ("语音功能", test_voice_features),
        ("远程访问配置", test_remote_access),
        ("聊天和语音播放", test_chat_with_voice),
        ("网络访问信息", test_network_info)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n🎉 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed >= 4:  # 至少4项通过
        print("🎉 系统已准备好供手机用户使用！")
        print("\n🌟 新增功能:")
        print("   ✅ 语音播放开关（🔊按钮）")
        print("   ✅ 移动端界面优化")
        print("   ✅ 远程访问支持")
        print("   ✅ 自动语音播报回答")
        print("   ✅ 智能文本清理（适合语音）")
    else:
        print("⚠️ 部分功能需要进一步优化")
    
    return passed >= 4

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
