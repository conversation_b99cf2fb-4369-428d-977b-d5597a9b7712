"""
测试增强版中医RAG系统
"""
from enhanced_rag_system import enhanced_rag_system
from session_manager import session_manager

def test_enhanced_rag():
    """测试增强RAG系统"""
    print("🧪 测试增强版中医RAG系统")
    print("=" * 50)
    
    # 初始化系统
    print("1. 初始化增强RAG系统...")
    success = enhanced_rag_system.initialize()
    if not success:
        print("❌ 系统初始化失败")
        return
    
    print("✅ 系统初始化成功")
    
    # 获取系统状态
    print("\n2. 检查系统状态...")
    status = enhanced_rag_system.get_system_status()
    print(f"   文档索引数量: {status['documents_indexed']}")
    print(f"   模型加载状态: {status['models_loaded']}")
    print(f"   最大上下文长度: {status['max_context_length']}")
    print(f"   最大检索文档数: {status['max_retrieved_docs']}")
    
    # 创建测试会话
    print("\n3. 创建测试会话...")
    session_id = session_manager.create_session()
    print(f"   会话ID: {session_id}")
    
    # 测试问题列表
    test_questions = [
        "身体湿气严重的表现是什么？该如何治疗？",
        "中医如何看待阴阳学说？",
        "五行学说在中医中的应用有哪些？",
        "脾胃虚弱有什么症状？"
    ]
    
    print("\n4. 测试问答功能...")
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- 测试问题 {i} ---")
        print(f"问题: {question}")
        
        try:
            result = enhanced_rag_system.retrieve_and_generate(question, session_id)
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
                continue
            
            print(f"✅ 回答: {result['answer'][:200]}...")
            print(f"📊 检索文档数: {result.get('retrieved_count', 0)}")
            print(f"📋 相关文档数: {result.get('relevant_count', 0)}")
            print(f"📖 来源数量: {len(result.get('sources', []))}")
            
            # 显示来源
            if result.get('sources'):
                print("📚 参考来源:")
                for j, source in enumerate(result['sources'][:2], 1):
                    print(f"   {j}. 相似度: {source.get('similarity', 0):.3f}")
                    print(f"      内容: {source['content'][:100]}...")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n5. 测试流式生成...")
    test_question = "什么是气血津液？"
    print(f"问题: {test_question}")
    
    try:
        print("回答: ", end="")
        for chunk in enhanced_rag_system.stream_generate(test_question, session_id):
            print(".", end="", flush=True)
        print(" ✅ 流式生成完成")
    except Exception as e:
        print(f"❌ 流式生成失败: {e}")
    
    print("\n🎉 增强RAG系统测试完成！")

if __name__ == "__main__":
    test_enhanced_rag()
