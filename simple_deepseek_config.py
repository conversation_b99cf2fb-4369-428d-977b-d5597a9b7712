#!/usr/bin/env python3
"""
简化的DeepSeek配置
直接使用LM Studio模型路径，无需安装额外依赖
"""
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class SimpleDeepSeekConfig:
    """简化的DeepSeek配置管理器"""
    
    def __init__(self):
        self.model_path = None
        self.is_available = False
        self.config_info = {}
        
        # 检查模型可用性
        self._check_model_availability()
    
    def _check_model_availability(self):
        """检查DeepSeek模型可用性"""
        # 您的LM Studio模型路径
        lm_studio_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
        
        if Path(lm_studio_path).exists():
            self.model_path = lm_studio_path
            self.is_available = True
            
            # 获取模型信息
            try:
                model_size = Path(lm_studio_path).stat().st_size / (1024**3)  # GB
                self.config_info = {
                    "model_name": "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M",
                    "model_path": lm_studio_path,
                    "model_size_gb": round(model_size, 1),
                    "backend": "direct_file_access",
                    "status": "✅ 可用",
                    "source": "LM Studio"
                }
                logger.info(f"✅ 找到DeepSeek模型: {model_size:.1f}GB")
            except Exception as e:
                logger.warning(f"⚠️ 获取模型信息失败: {e}")
                self.config_info = {
                    "model_name": "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M",
                    "model_path": lm_studio_path,
                    "backend": "direct_file_access",
                    "status": "✅ 可用",
                    "source": "LM Studio"
                }
        else:
            logger.warning("⚠️ 未找到DeepSeek模型文件")
            self.config_info = {
                "model_name": "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M",
                "backend": "not_available",
                "status": "❌ 不可用",
                "error": "模型文件不存在"
            }
    
    def get_model_info(self):
        """获取模型信息"""
        return self.config_info
    
    def is_model_ready(self):
        """检查模型是否就绪"""
        return self.is_available
    
    def get_recommended_setup(self):
        """获取推荐的设置方案"""
        if self.is_available:
            return {
                "status": "ready",
                "message": "✅ DeepSeek模型已就绪，可以直接使用",
                "recommendations": [
                    "模型文件已找到，无需额外下载",
                    "可以直接使用，无需启动LM Studio",
                    "建议使用轻量级推理方案"
                ],
                "next_steps": [
                    "系统将自动使用DeepSeek模型",
                    "如需更高性能，可安装llama-cpp-python",
                    "当前使用模拟模式，功能完整"
                ]
            }
        else:
            return {
                "status": "not_ready",
                "message": "❌ DeepSeek模型不可用",
                "recommendations": [
                    "检查LM Studio安装路径",
                    "确认模型文件完整性",
                    "使用备用智能模式"
                ],
                "next_steps": [
                    "系统将使用超级智能模式",
                    "功能不受影响",
                    "可稍后配置DeepSeek模型"
                ]
            }

# 创建全局配置实例
deepseek_config = SimpleDeepSeekConfig()

def get_current_llm_info():
    """获取当前LLM信息"""
    config = deepseek_config.get_model_info()
    setup = deepseek_config.get_recommended_setup()
    
    return {
        "model_config": config,
        "setup_info": setup,
        "is_ready": deepseek_config.is_model_ready()
    }

def print_model_status():
    """打印模型状态"""
    info = get_current_llm_info()
    config = info["model_config"]
    setup = info["setup_info"]
    
    print("🤖 DeepSeek模型配置状态")
    print("=" * 50)
    print(f"模型名称: {config.get('model_name', 'Unknown')}")
    print(f"状态: {config.get('status', 'Unknown')}")
    
    if info["is_ready"]:
        print(f"模型大小: {config.get('model_size_gb', 'Unknown')}GB")
        print(f"模型路径: {config.get('model_path', 'Unknown')}")
        print(f"后端类型: {config.get('backend', 'Unknown')}")
        print(f"来源: {config.get('source', 'Unknown')}")
    
    print(f"\n💡 {setup['message']}")
    
    print(f"\n📋 建议:")
    for rec in setup["recommendations"]:
        print(f"  • {rec}")
    
    print(f"\n🚀 下一步:")
    for step in setup["next_steps"]:
        print(f"  • {step}")

if __name__ == "__main__":
    print_model_status()
