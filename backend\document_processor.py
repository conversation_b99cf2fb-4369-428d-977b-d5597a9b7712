"""
文档处理器 - 支持多种文档格式
"""
import os
import re
from pathlib import Path
from typing import List, Dict, Any
import PyPDF2
import docx
from io import StringIO

class DocumentProcessor:
    def __init__(self):
        """初始化文档处理器"""
        self.chunk_size = 500
        self.chunk_overlap = 50
        
    def process_document(self, file_path: str) -> List[str]:
        """处理文档并返回文本块"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 根据文件类型选择处理方法
        suffix = file_path.suffix.lower()
        
        if suffix == '.pdf':
            text = self._extract_pdf_text(file_path)
        elif suffix == '.txt':
            text = self._extract_txt_text(file_path)
        elif suffix in ['.doc', '.docx']:
            text = self._extract_docx_text(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {suffix}")
        
        # 清理和分块
        cleaned_text = self._clean_text(text)
        chunks = self._split_text_into_chunks(cleaned_text)
        
        return chunks
    
    def _extract_pdf_text(self, file_path: Path) -> str:
        """提取PDF文本"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text += f"\n--- 第{page_num + 1}页 ---\n"
                            text += page_text + "\n"
                    except Exception as e:
                        print(f"⚠️ 提取第{page_num + 1}页失败: {e}")
                        continue
            
            if not text.strip():
                raise ValueError("PDF文件中没有可提取的文本")
            
            return text
            
        except Exception as e:
            raise ValueError(f"PDF处理失败: {e}")
    
    def _extract_txt_text(self, file_path: Path) -> str:
        """提取TXT文本"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read()
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("无法识别文件编码")
            
        except Exception as e:
            raise ValueError(f"TXT处理失败: {e}")
    
    def _extract_docx_text(self, file_path: Path) -> str:
        """提取DOCX文本"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"
            
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text += " | ".join(row_text) + "\n"
            
            if not text.strip():
                raise ValueError("DOCX文件中没有可提取的文本")
            
            return text
            
        except Exception as e:
            raise ValueError(f"DOCX处理失败: {e}")
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,;:!?()[\]{}""''—–-]', '', text)
        
        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.strip()
            if len(line) > 10:  # 保留长度大于10的行
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _split_text_into_chunks(self, text: str) -> List[str]:
        """将文本分割成块"""
        if not text.strip():
            return []
        
        # 按段落分割
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            # 如果当前块加上新段落超过限制，保存当前块
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                
                # 保留重叠部分
                if self.chunk_overlap > 0:
                    words = current_chunk.split()
                    overlap_words = words[-self.chunk_overlap:] if len(words) > self.chunk_overlap else words
                    current_chunk = ' '.join(overlap_words) + ' '
                else:
                    current_chunk = ""
            
            current_chunk += paragraph + ' '
            
            # 如果单个段落就超过限制，强制分割
            if len(current_chunk) > self.chunk_size * 2:
                chunks.append(current_chunk.strip())
                current_chunk = ""
        
        # 添加最后一个块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        # 过滤太短的块
        chunks = [chunk for chunk in chunks if len(chunk) > 50]
        
        return chunks
    
    def get_document_info(self, file_path: str) -> Dict[str, Any]:
        """获取文档信息"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        info = {
            'filename': file_path.name,
            'size': file_path.stat().st_size,
            'type': file_path.suffix.lower(),
            'created': file_path.stat().st_ctime,
            'modified': file_path.stat().st_mtime
        }
        
        # 尝试获取页数或字数
        try:
            if info['type'] == '.pdf':
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    info['pages'] = len(pdf_reader.pages)
            elif info['type'] in ['.doc', '.docx']:
                doc = docx.Document(file_path)
                info['paragraphs'] = len(doc.paragraphs)
                info['tables'] = len(doc.tables)
            elif info['type'] == '.txt':
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    info['characters'] = len(content)
                    info['lines'] = len(content.split('\n'))
        except Exception as e:
            info['error'] = str(e)
        
        return info
    
    def validate_document(self, file_path: str) -> Dict[str, Any]:
        """验证文档是否可以处理"""
        try:
            file_path = Path(file_path)
            
            # 检查文件是否存在
            if not file_path.exists():
                return {'valid': False, 'error': '文件不存在'}
            
            # 检查文件大小
            size_mb = file_path.stat().st_size / (1024 * 1024)
            if size_mb > 50:  # 限制50MB
                return {'valid': False, 'error': f'文件过大: {size_mb:.1f}MB (限制50MB)'}
            
            # 检查文件类型
            suffix = file_path.suffix.lower()
            supported_types = ['.pdf', '.txt', '.doc', '.docx']
            if suffix not in supported_types:
                return {'valid': False, 'error': f'不支持的文件类型: {suffix}'}
            
            # 尝试提取少量文本验证
            try:
                if suffix == '.pdf':
                    with open(file_path, 'rb') as file:
                        pdf_reader = PyPDF2.PdfReader(file)
                        if len(pdf_reader.pages) == 0:
                            return {'valid': False, 'error': 'PDF文件没有页面'}
                        
                        # 尝试提取第一页
                        first_page_text = pdf_reader.pages[0].extract_text()
                        if not first_page_text.strip():
                            return {'valid': False, 'error': 'PDF文件无法提取文本'}
                
                elif suffix in ['.doc', '.docx']:
                    doc = docx.Document(file_path)
                    if len(doc.paragraphs) == 0:
                        return {'valid': False, 'error': 'Word文档没有内容'}
                
                elif suffix == '.txt':
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read(1000)  # 读取前1000字符
                        if not content.strip():
                            return {'valid': False, 'error': '文本文件为空'}
            
            except Exception as e:
                return {'valid': False, 'error': f'文件验证失败: {str(e)}'}
            
            return {'valid': True, 'message': '文档验证通过'}
            
        except Exception as e:
            return {'valid': False, 'error': f'验证过程出错: {str(e)}'}

# 测试函数
def test_document_processor():
    """测试文档处理器"""
    processor = DocumentProcessor()
    
    # 创建测试文本文件
    test_file = Path("test_document.txt")
    test_content = """
    中医基础理论

    中医学是中华民族的传统医学，有着悠久的历史和深厚的文化底蕴。

    阴阳学说
    阴阳学说是中医理论的哲学基础。阴阳是宇宙间相互关联的两个方面，
    在人体中，阴阳的相对平衡是健康的标志。

    五脏六腑
    五脏包括心、肝、脾、肺、肾，主要功能是化生和储藏精气。
    六腑包括胆、胃、小肠、大肠、膀胱、三焦，主要功能是受纳和传化水谷。

    气血理论
    气血是中医学的核心概念，气为血之帅，血为气之母。
    气具有推动、温煦、防御、固摄、气化等功能。
    """
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 测试处理
        chunks = processor.process_document(str(test_file))
        print(f"✅ 测试成功，生成 {len(chunks)} 个文本块")
        
        for i, chunk in enumerate(chunks, 1):
            print(f"\n--- 块 {i} ---")
            print(chunk[:100] + "..." if len(chunk) > 100 else chunk)
        
        # 清理测试文件
        test_file.unlink()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        if test_file.exists():
            test_file.unlink()

if __name__ == "__main__":
    test_document_processor()
