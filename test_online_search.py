#!/usr/bin/env python3
"""
测试在线搜索功能
"""
import asyncio
import requests
from bs4 import BeautifulSoup
import re
import time

async def test_chinesebooks_access():
    """测试访问chinesebooks网站"""
    print("🔍 测试访问 https://chinesebooks.github.io/gudaiyishu/")
    
    try:
        # 测试主页
        main_url = "https://chinesebooks.github.io/gudaiyishu/"
        response = requests.get(main_url, timeout=10)
        print(f"📡 主页响应状态: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            print(f"📄 页面标题: {soup.title.string if soup.title else '无标题'}")
            
            # 查找所有链接
            links = soup.find_all('a', href=True)
            print(f"🔗 找到 {len(links)} 个链接")
            
            # 显示前10个链接
            for i, link in enumerate(links[:10]):
                text = link.get_text(strip=True)
                href = link.get('href', '')
                print(f"  {i+1}. {text} -> {href}")
        
        # 测试医宗金鉴
        yizong_url = "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
        print(f"\n🔍 测试访问医宗金鉴: {yizong_url}")
        response = requests.get(yizong_url, timeout=10)
        print(f"📡 医宗金鉴响应状态: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            print(f"📄 页面标题: {soup.title.string if soup.title else '无标题'}")
            
            # 查找包含"栀子"的内容
            page_text = soup.get_text()
            if "栀子" in page_text:
                print("✅ 找到栀子相关内容")
                # 提取相关句子
                sentences = re.split(r'[。！？\n]', page_text)
                for sentence in sentences:
                    if "栀子" in sentence and len(sentence.strip()) > 5:
                        print(f"  📝 {sentence.strip()}")
                        break
            else:
                print("❌ 未找到栀子相关内容")
        
        # 测试搜索功能
        print(f"\n🔍 测试搜索'栀子甘草豉汤'")
        await test_search_function("栀子甘草豉汤")
        
    except Exception as e:
        print(f"❌ 访问失败: {e}")

async def test_search_function(query):
    """测试搜索功能"""
    try:
        # 模拟系统的搜索逻辑
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        results = []
        
        # 1. 搜索医宗金鉴
        base_url = "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
        response = session.get(base_url, timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
            
            # 查找相关链接
            for link in soup.find_all('a', href=True)[:15]:
                text = link.get_text(strip=True)
                if text and len(text) > 2:
                    text_keywords = set(re.findall(r'[\u4e00-\u9fff]+', text))
                    if query_keywords.intersection(text_keywords):
                        print(f"  🎯 找到相关链接: {text}")
                        
                        # 获取完整URL
                        href = link.get('href', '')
                        if href.startswith('/'):
                            full_url = "https://chinesebooks.github.io" + href
                        elif href.startswith('./'):
                            full_url = base_url + href[2:]
                        elif not href.startswith('http'):
                            full_url = base_url + href
                        else:
                            full_url = href
                        
                        print(f"    🔗 URL: {full_url}")
                        
                        # 获取页面内容
                        try:
                            page_response = session.get(full_url, timeout=8)
                            if page_response.status_code == 200:
                                page_soup = BeautifulSoup(page_response.content, 'html.parser')
                                page_content = page_soup.get_text(strip=True)
                                
                                # 查找相关内容
                                if any(keyword in page_content for keyword in query_keywords):
                                    print(f"    ✅ 页面包含相关内容")
                                    
                                    # 提取相关句子
                                    sentences = re.split(r'[。！？\n]', page_content)
                                    for sentence in sentences:
                                        sentence = sentence.strip()
                                        if any(keyword in sentence for keyword in query_keywords) and len(sentence) > 10:
                                            print(f"    📝 {sentence[:100]}...")
                                            results.append({
                                                'source': f"医宗金鉴 - {text}",
                                                'content': sentence,
                                                'url': full_url
                                            })
                                            break
                                else:
                                    print(f"    ❌ 页面不包含相关内容")
                            
                            time.sleep(0.5)  # 避免请求过快
                            
                        except Exception as e:
                            print(f"    ❌ 获取页面失败: {e}")
                        
                        if len(results) >= 2:
                            break
        
        # 2. 搜索其他古代医书
        other_books = ["shanghan", "jinkuiyaolue"]
        for book in other_books:
            if len(results) >= 3:
                break
                
            book_url = f"https://chinesebooks.github.io/gudaiyishu/{book}/"
            print(f"  🔍 搜索 {book}: {book_url}")
            
            try:
                response = session.get(book_url, timeout=8)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_text = soup.get_text()
                    
                    if any(keyword in page_text for keyword in query_keywords):
                        print(f"    ✅ {book} 包含相关内容")
                        
                        # 提取相关句子
                        sentences = re.split(r'[。！？\n]', page_text)
                        for sentence in sentences:
                            sentence = sentence.strip()
                            if any(keyword in sentence for keyword in query_keywords) and len(sentence) > 10:
                                print(f"    📝 {sentence[:100]}...")
                                results.append({
                                    'source': f"古代医书 - {book}",
                                    'content': sentence,
                                    'url': book_url
                                })
                                break
                    else:
                        print(f"    ❌ {book} 不包含相关内容")
                
                time.sleep(0.3)
                
            except Exception as e:
                print(f"    ❌ 搜索{book}失败: {e}")
        
        print(f"\n📊 搜索结果总结:")
        print(f"  找到 {len(results)} 条相关结果")
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['source']}")
            print(f"     {result['content'][:100]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {e}")
        return []

if __name__ == "__main__":
    asyncio.run(test_chinesebooks_access())
