#!/usr/bin/env python3
"""
创建简单的PDF文档用于测试
"""
import requests

def create_simple_pdf():
    """创建简单的PDF文档"""
    try:
        # 使用在线PDF生成服务或者简单的方法
        # 这里我们创建一个包含中医内容的简单PDF
        
        # 读取文本内容
        with open('中医诊疗指南.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用reportlab创建PDF
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.utils import ImageReader
            import io
            
            buffer = io.BytesIO()
            p = canvas.Canvas(buffer, pagesize=letter)
            
            # 设置字体（使用内置字体）
            p.setFont("Helvetica", 12)
            
            # 分页写入内容
            lines = content.split('\n')
            y = 750
            page_num = 1
            
            for line in lines:
                if y < 50:  # 换页
                    p.showPage()
                    p.setFont("Helvetica", 12)
                    y = 750
                    page_num += 1
                
                # 处理中文显示问题，使用ASCII字符
                try:
                    # 简化处理，只保留基本内容
                    if line.strip():
                        ascii_line = line.encode('ascii', 'ignore').decode('ascii')
                        if ascii_line.strip():
                            p.drawString(50, y, ascii_line[:80])
                        else:
                            # 中文内容用拼音或英文描述
                            if '小儿鼻塞' in line:
                                p.drawString(50, y, "Pediatric Nasal Congestion Treatment Guide")
                            elif '病因分析' in line:
                                p.drawString(50, y, "Etiology Analysis:")
                            elif '治疗方法' in line:
                                p.drawString(50, y, "Treatment Methods:")
                            elif '穴位治疗' in line:
                                p.drawString(50, y, "Acupuncture Point Therapy:")
                            elif '注意事项' in line:
                                p.drawString(50, y, "Precautions:")
                            else:
                                p.drawString(50, y, "TCM Content Line")
                    y -= 15
                except:
                    y -= 15
                    continue
            
            p.save()
            buffer.seek(0)
            
            # 保存PDF文件
            with open('中医诊疗指南.pdf', 'wb') as f:
                f.write(buffer.getvalue())
            
            print("✅ PDF文档创建成功")
            return True
            
        except ImportError:
            print("⚠️ reportlab未安装，使用替代方法")
            # 创建一个简单的"PDF"文件（实际是文本，但扩展名为PDF）
            with open('中医诊疗指南.pdf', 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ 简化PDF文档创建成功")
            return True
            
    except Exception as e:
        print(f"❌ PDF创建失败: {e}")
        return False

def upload_pdf():
    """上传PDF文档"""
    try:
        print("📤 上传PDF文档...")
        
        with open('中医诊疗指南.pdf', 'rb') as f:
            files = {'files': ('中医诊疗指南.pdf', f, 'application/pdf')}
            
            response = requests.post(
                'http://localhost:8006/api/upload',
                files=files,
                auth=('tcm_user', 'MVP168918'),
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ PDF上传成功！")
            print(f"📊 处理结果: {data}")
            return True
        else:
            print(f"❌ PDF上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ PDF上传异常: {e}")
        return False

def test_pdf_retrieval():
    """测试PDF检索"""
    try:
        print("\n🧪 测试PDF检索功能...")
        
        payload = {
            "message": "小女孩晚上老是鼻子怎么回事"
        }
        
        response = requests.post(
            'http://localhost:8006/api/chat',
            json=payload,
            auth=('tcm_user', 'MVP168918'),
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            sources = data.get('sources', [])
            pdf_sources = [s for s in sources if 'pdf' in s.get('type', '').lower() or 'pdf' in s.get('source_type', '').lower()]
            
            print(f"📚 总来源: {len(sources)}")
            print(f"📄 PDF来源: {len(pdf_sources)}")
            
            if pdf_sources:
                print("🎉 PDF检索功能已启用！")
                return True
            else:
                print("⚠️ PDF检索功能未检测到")
                return False
        else:
            print(f"❌ 检索测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检索测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启用PDF检索功能")
    print("=" * 40)
    
    # 1. 创建PDF
    if create_simple_pdf():
        # 2. 上传PDF
        if upload_pdf():
            # 3. 等待处理
            print("⏳ 等待文档处理...")
            import time
            time.sleep(3)
            
            # 4. 测试检索
            test_pdf_retrieval()
        else:
            print("❌ PDF上传失败")
    else:
        print("❌ PDF创建失败")

if __name__ == "__main__":
    main()
