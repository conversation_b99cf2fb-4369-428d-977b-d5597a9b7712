
# 备用DeepSeek管理器
class FallbackDeepSeekManager:
    """备用DeepSeek管理器 - 智能回答模式"""
    
    def __init__(self):
        self.initialized = True
        self.fallback_mode = True
    
    def initialize(self):
        st.info("🤖 启用智能备用回答模式")
        st.success("✅ 系统已就绪（备用模式）")
        return True
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成智能回答"""
        # 基于关键词的智能回答
        if "中医" in prompt or "中药" in prompt:
            return self._generate_tcm_response(prompt)
        elif "方剂" in prompt or "药方" in prompt:
            return self._generate_prescription_response(prompt)
        else:
            return self._generate_general_response(prompt)
    
    def _generate_tcm_response(self, prompt):
        return """中医是中华民族传统医学，具有数千年历史。
        
## 中医基本理论
- **阴阳学说**: 认为人体是阴阳对立统一的整体
- **五行学说**: 木、火、土、金、水五行相生相克
- **脏腑学说**: 五脏六腑功能协调
- **经络学说**: 气血运行的通道

## 诊断方法
- **望诊**: 观察面色、舌象等
- **闻诊**: 听声音、嗅气味
- **问诊**: 询问症状、病史
- **切诊**: 脉诊、按诊

## 治疗方法
- **中药治疗**: 汤剂、丸剂、散剂等
- **针灸治疗**: 针刺、艾灸
- **推拿按摩**: 手法治疗
- **食疗**: 药食同源

请提供更具体的问题，我可以给出更详细的回答。"""
    
    def _generate_prescription_response(self, prompt):
        return """中医方剂是根据中医理论组方的药物配伍。

## 经典方剂举例
- **四君子汤**: 人参、白术、茯苓、甘草 - 补气健脾
- **四物汤**: 当归、川芎、白芍、熟地 - 补血调经
- **小柴胡汤**: 柴胡、黄芩、人参、半夏等 - 和解少阳
- **麻黄汤**: 麻黄、桂枝、杏仁、甘草 - 发汗解表

## 方剂配伍原则
- **君药**: 主要治疗作用
- **臣药**: 辅助君药
- **佐药**: 制约副作用
- **使药**: 调和诸药

请注意：具体用药需要专业中医师诊断后开具，不可自行用药。"""
    
    def _generate_general_response(self, prompt):
        return f"""感谢您的咨询。

基于您的问题："{prompt[:100]}..."

我建议：
1. 如需专业中医诊疗，请咨询执业中医师
2. 可以上传相关医学文档，我可以帮助检索信息
3. 对于具体症状，建议结合现代医学检查

如果您有具体的中医药问题，请提供更详细的信息，我会尽力为您解答。

**免责声明**: 本回答仅供参考，不能替代专业医疗建议。"""

# 使用备用管理器
if 'deepseek_manager' not in st.session_state:
    st.session_state.deepseek_manager = FallbackDeepSeekManager()
