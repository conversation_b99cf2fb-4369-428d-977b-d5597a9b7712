#!/usr/bin/env python3
"""
立即启动系统 - 使用现有资源
不等待模型下载，立即启动可用的功能
"""

import asyncio
import subprocess
import time
import requests
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImmediateStartup:
    """立即启动系统"""
    
    def __init__(self):
        self.processes = {}
        self.config = {
            'streamlit_port': 8510,
            'elasticsearch_port': 9200
        }
    
    def check_available_resources(self):
        """检查可用资源"""
        logger.info("🔍 检查可用资源...")
        
        resources = {
            'deepseek_model': Path('./models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf').exists(),
            'qwen2_model': Path('./models/qwen2-1.5b-instruct/config.json').exists(),
            'bge_m3_model': Path('./models/bge-m3').exists(),
            'intelligent_retriever': Path('./intelligent_rag_retriever.py').exists(),
            'mcp_service': Path('./elasticsearch_mcp_server.py').exists(),
            'github_retriever': Path('./github_knowledge_retriever.py').exists()
        }
        
        logger.info("📊 资源状态:")
        for resource, available in resources.items():
            status = "✅ 可用" if available else "❌ 不可用"
            logger.info(f"  {resource}: {status}")
        
        return resources
    
    def start_elasticsearch_if_needed(self):
        """如果需要，启动Elasticsearch"""
        try:
            response = requests.get(f"http://localhost:{self.config['elasticsearch_port']}", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Elasticsearch已运行")
                return True
        except:
            pass
        
        logger.info("🔄 尝试启动Elasticsearch...")
        try:
            # 尝试Docker启动
            cmd = [
                'docker', 'run', '-d',
                '--name', 'elasticsearch-immediate',
                '-p', f'{self.config["elasticsearch_port"]}:9200',
                '-e', 'discovery.type=single-node',
                '-e', 'xpack.security.enabled=false',
                'elasticsearch:8.11.0'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Elasticsearch启动成功")
                return True
            else:
                logger.warning("⚠️ Elasticsearch启动失败，将跳过")
                return False
        except:
            logger.warning("⚠️ 无法启动Elasticsearch，将跳过")
            return False
    
    def start_streamlit_with_available_features(self):
        """启动Streamlit，使用可用功能"""
        try:
            logger.info("🎮 启动Streamlit应用 (使用可用功能)...")
            
            cmd = [
                'streamlit', 'run', 'ultimate_final_tcm_system.py',
                '--server.port', str(self.config['streamlit_port']),
                '--server.address', '0.0.0.0',
                '--server.headless', 'true'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['streamlit'] = process
            
            # 等待启动
            for i in range(30):
                try:
                    response = requests.get(f"http://localhost:{self.config['streamlit_port']}", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Streamlit应用启动成功")
                        return True
                except:
                    pass
                time.sleep(2)
            
            logger.error("❌ Streamlit启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ Streamlit启动失败: {e}")
            return False
    
    def start_system(self):
        """启动系统"""
        logger.info("🚀 立即启动MCP+API+RAG系统...")
        
        # 1. 检查可用资源
        resources = self.check_available_resources()
        
        # 2. 启动Elasticsearch (可选)
        self.start_elasticsearch_if_needed()
        
        # 3. 启动Streamlit应用
        if self.start_streamlit_with_available_features():
            logger.info("🎉 系统启动成功!")
            logger.info(f"🌐 访问地址: http://localhost:{self.config['streamlit_port']}")
            logger.info("💡 功能说明:")
            
            if resources['intelligent_retriever']:
                logger.info("  ✅ 智能检索器 (96分准确性)")
            if resources['deepseek_model']:
                logger.info("  ✅ DeepSeek模型 (4.5GB)")
            if resources['mcp_service']:
                logger.info("  ✅ MCP服务")
            if resources['github_retriever']:
                logger.info("  ✅ GitHub古代医术检索")
            
            logger.info("🎯 系统将输出基于可用资源的最佳答案")
            
            return True
        else:
            logger.error("❌ 系统启动失败")
            return False
    
    def monitor_and_upgrade(self):
        """监控并升级系统"""
        logger.info("🔄 监控系统状态，等待模型下载完成...")
        
        while True:
            try:
                # 检查Qwen2模型是否下载完成
                qwen2_ready = Path('./models/qwen2-1.5b-instruct/config.json').exists()
                bge_ready = Path('./models/bge-m3').exists()
                
                if qwen2_ready and bge_ready:
                    logger.info("🎉 新模型下载完成，可以升级系统!")
                    logger.info("💡 重启系统以使用新模型:")
                    logger.info("  python fully_auto_startup.py")
                    break
                
                time.sleep(60)  # 每分钟检查一次
                
            except KeyboardInterrupt:
                logger.info("👋 监控停止")
                break
            except Exception as e:
                logger.error(f"❌ 监控异常: {e}")
                time.sleep(30)
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理资源...")
        for service_name, process in self.processes.items():
            try:
                process.terminate()
                logger.info(f"✅ 停止 {service_name}")
            except:
                pass

def main():
    """主函数"""
    startup = ImmediateStartup()
    
    try:
        if startup.start_system():
            logger.info("🎊 系统立即启动完成!")
            logger.info("📋 当前功能:")
            logger.info("  • 智能检索器 (96分准确性)")
            logger.info("  • PDF文档检索")
            logger.info("  • 古籍记载检索")
            logger.info("  • GitHub古代医术检索")
            logger.info("  • MCP协议通信")
            logger.info("  • 语音对话功能")
            
            logger.info("⏳ 后台监控模型下载进度...")
            startup.monitor_and_upgrade()
        else:
            logger.error("❌ 系统启动失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("👋 用户中断")
        startup.cleanup()
    except Exception as e:
        logger.error(f"❌ 系统异常: {e}")
        startup.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
