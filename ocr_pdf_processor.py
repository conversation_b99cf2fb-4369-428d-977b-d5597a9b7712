#!/usr/bin/env python3
"""
使用OCR处理PDF文档
专门处理扫描版《伤寒论》
"""
import os
from pathlib import Path
import PyPDF2
from pdf2image import convert_from_path
import pytesseract

def extract_text_with_ocr(pdf_path, max_pages=20):
    """使用OCR从PDF提取文本"""
    try:
        print(f"🔍 开始OCR处理: {pdf_path}")
        print(f"📄 处理前{max_pages}页...")
        
        # 转换PDF为图像
        pages = convert_from_path(pdf_path, first_page=1, last_page=max_pages, dpi=200)
        
        extracted_text = ""
        for i, page in enumerate(pages):
            print(f"   处理第{i+1}页...")
            
            # OCR识别
            page_text = pytesseract.image_to_string(page, lang='chi_sim+eng')
            
            if page_text.strip():
                extracted_text += f"\n=== 第{i+1}页 ===\n"
                extracted_text += page_text + "\n"
                
                # 显示部分内容
                preview = page_text.strip()[:100].replace('\n', ' ')
                print(f"     提取内容: {preview}...")
            else:
                print(f"     第{i+1}页无文本内容")
        
        return extracted_text
        
    except Exception as e:
        print(f"❌ OCR处理失败: {e}")
        return ""

def process_shanghan_lun():
    """专门处理《伤寒论》"""
    pdf_path = Path("documents/伤寒论.pdf")
    
    if not pdf_path.exists():
        print(f"❌ 文件不存在: {pdf_path}")
        return False
    
    print(f"📚 开始处理《伤寒论》...")
    print(f"📁 文件路径: {pdf_path}")
    print(f"📊 文件大小: {pdf_path.stat().st_size / 1024:.1f} KB")
    
    # 首先尝试直接文本提取
    print("\n🔍 尝试直接文本提取...")
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            print(f"📄 总页数: {len(pdf_reader.pages)}")
            
            direct_text = ""
            for i, page in enumerate(pdf_reader.pages[:5]):
                page_text = page.extract_text()
                if page_text.strip():
                    direct_text += page_text + "\n"
            
            if len(direct_text.strip()) > 100:
                print(f"✅ 直接提取成功: {len(direct_text)} 字符")
                return direct_text
            else:
                print(f"⚠️ 直接提取内容较少: {len(direct_text)} 字符")
    
    except Exception as e:
        print(f"❌ 直接提取失败: {e}")
    
    # 使用OCR处理
    print("\n🤖 使用OCR处理...")
    ocr_text = extract_text_with_ocr(pdf_path, max_pages=20)
    
    if ocr_text:
        print(f"✅ OCR提取成功: {len(ocr_text)} 字符")
        
        # 保存OCR结果
        output_path = Path("documents/伤寒论_OCR.txt")
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(ocr_text)
        print(f"💾 OCR结果已保存: {output_path}")
        
        return ocr_text
    else:
        print("❌ OCR处理失败")
        return False

def update_vector_db_with_ocr():
    """使用OCR结果更新向量数据库"""
    print("\n🔄 使用OCR结果更新向量数据库...")
    
    # 检查OCR文本文件
    ocr_file = Path("documents/伤寒论_OCR.txt")
    if not ocr_file.exists():
        print("❌ OCR文本文件不存在")
        return False
    
    # 读取OCR文本
    with open(ocr_file, 'r', encoding='utf-8') as f:
        ocr_text = f.read()
    
    print(f"📖 OCR文本长度: {len(ocr_text)} 字符")
    
    # 更新emergency_fix.py中的伤寒论处理
    try:
        import sys
        sys.path.append('.')
        
        # 重新运行emergency_fix，但这次包含OCR文本
        print("🔧 重新生成向量数据库...")
        
        # 这里可以调用emergency_fix的函数，或者创建新的处理逻辑
        from emergency_fix import main as emergency_main
        emergency_main()
        
        print("✅ 向量数据库更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新向量数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 OCR PDF处理器")
    print("=" * 50)
    
    # 检查OCR依赖
    try:
        import pdf2image
        import pytesseract
        print("✅ OCR依赖检查通过")
    except ImportError as e:
        print(f"❌ OCR依赖缺失: {e}")
        print("💡 请运行: pip install pdf2image pytesseract")
        return
    
    # 处理《伤寒论》
    result = process_shanghan_lun()
    
    if result:
        print("\n🎉 《伤寒论》OCR处理完成！")
        
        # 询问是否更新向量数据库
        response = input("\n是否使用OCR结果更新向量数据库？(y/n): ").lower().strip()
        if response == 'y':
            update_vector_db_with_ocr()
    else:
        print("\n❌ 《伤寒论》处理失败")
    
    print("\n👋 OCR处理完成")

if __name__ == "__main__":
    main()
