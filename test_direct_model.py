#!/usr/bin/env python3
"""
直接测试DeepSeek模型文件加载
"""

import os
import sys
from pathlib import Path

def test_model_file():
    """测试模型文件"""
    model_path = r"C:/Users/<USER>/.lmstudio/models/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    
    print("🔍 检查DeepSeek模型文件...")
    print(f"路径: {model_path}")
    
    # 检查文件是否存在
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ 文件存在，大小: {file_size:.2f} GB")
        
        # 检查llama-cpp-python
        try:
            from llama_cpp import Llama
            print("✅ llama-cpp-python已安装")
            
            # 尝试加载模型
            print("🚀 尝试加载模型...")
            try:
                model = Llama(
                    model_path=model_path,
                    n_ctx=512,  # 小的上下文用于测试
                    n_threads=2,
                    n_gpu_layers=0,
                    verbose=False
                )
                
                print("✅ 模型加载成功!")
                
                # 测试生成
                print("🧪 测试生成...")
                response = model(
                    "你好",
                    max_tokens=5,
                    temperature=0.7,
                    echo=False
                )
                
                if response and response.get('choices'):
                    text = response['choices'][0]['text']
                    print(f"✅ 生成测试成功: {text}")
                    return True
                else:
                    print("⚠️ 生成测试失败")
                    return False
                    
            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
                return False
                
        except ImportError:
            print("❌ llama-cpp-python未安装")
            print("💡 安装命令: pip install llama-cpp-python")
            return False
    else:
        print("❌ 模型文件不存在")
        
        # 检查目录是否存在
        model_dir = Path(model_path).parent
        if model_dir.exists():
            print(f"📁 目录存在: {model_dir}")
            print("📄 目录内容:")
            for file in model_dir.iterdir():
                print(f"   - {file.name}")
        else:
            print(f"❌ 目录不存在: {model_dir}")
        
        return False

def main():
    """主函数"""
    print("🤖 DeepSeek模型直接加载测试")
    print("=" * 40)
    
    success = test_model_file()
    
    if success:
        print("\n🎉 模型测试成功!")
        print("现在可以在RAG系统中使用直接加载功能了!")
    else:
        print("\n❌ 模型测试失败")
        print("请检查模型文件路径和llama-cpp-python安装")

if __name__ == "__main__":
    main()
