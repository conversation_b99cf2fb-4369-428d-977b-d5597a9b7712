#!/usr/bin/env python3
"""
本地模型API服务器
使用本地下载的模型提供API接口
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import logging
import torch
from pathlib import Path
import json
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI应用
app = FastAPI(
    title="Local Model API Server",
    description="本地模型API服务，支持多种模型推理",
    version="1.0.0"
)

# 请求模型
class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Dict[str, str]]
    temperature: float = 0.7
    max_tokens: int = 2048
    stream: bool = False

class EmbeddingRequest(BaseModel):
    model: str
    input: List[str]

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class EmbeddingResponse(BaseModel):
    object: str = "list"
    data: List[Dict[str, Any]]
    model: str
    usage: Dict[str, int]

class LocalModelManager:
    """本地模型管理器"""
    
    def __init__(self):
        self.models = {}
        self.embedding_models = {}
        self.initialized = False
        
        # 模型配置 - 严格按照用户要求使用Qwen2.5模型
        self.model_configs = {
            'chat_models': {
                'qwen-7b': self._get_best_qwen_config()
            },
            'embedding_models': {
                'bge-m3': {
                    'path': './models/bge-m3',
                    'type': 'sentence_transformers',
                    'dimension': 1024,
                    'description': 'BGE-M3中文嵌入模型'
                },
                'm3e-base': {
                    'path': './models/m3e-base',
                    'type': 'sentence_transformers',
                    'dimension': 768,
                    'description': 'M3E中文嵌入模型(备用)'
                }
            }
        }

    def _get_best_qwen_config(self):
        """获取最佳的轻量级模型配置 - 适合6B以下笔记本"""
        from pathlib import Path

        # 按优先级检查轻量级模型 (6GB以下)
        lightweight_candidates = [
            {
                'path': './models/chatglm3-6b-q4_0.gguf',
                'type': 'gguf',
                'context_length': 8192,
                'description': 'ChatGLM3-6B量化版 (3.5GB) - 首选'
            },
            {
                'path': './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf',
                'type': 'gguf',
                'context_length': 8192,
                'description': 'DeepSeek-R1-Qwen3-8B量化版 (4.5GB) - 可用'
            },
            {
                'path': './models/chatglm3-6b',
                'type': 'transformers',
                'context_length': 8192,
                'description': 'ChatGLM3-6B原版 (如果已量化)'
            }
        ]

        for config in lightweight_candidates:
            if Path(config['path']).exists():
                # 检查文件大小
                if config['type'] == 'gguf':
                    size_gb = Path(config['path']).stat().st_size / (1024**3)
                    if size_gb <= 6.0:
                        logger.info(f"✅ 选择轻量级模型: {config['description']} ({size_gb:.1f}GB)")
                        return config
                    else:
                        logger.warning(f"⚠️ 模型太大: {config['description']} ({size_gb:.1f}GB)")
                else:
                    logger.info(f"✅ 选择模型: {config['description']}")
                    return config

        # 如果都没找到，返回默认配置
        logger.warning("⚠️ 未找到合适的轻量级模型")
        return {
            'path': './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf',
            'type': 'gguf',
            'context_length': 4096,
            'description': 'DeepSeek-R1-Qwen3-8B (默认)'
        }
    
    def initialize(self) -> bool:
        """初始化所有模型"""
        try:
            logger.info("🚀 初始化本地模型管理器...")
            
            # 初始化聊天模型
            self._initialize_chat_models()
            
            # 初始化嵌入模型
            self._initialize_embedding_models()
            
            self.initialized = True
            logger.info("✅ 本地模型管理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            return False
    
    def _initialize_chat_models(self):
        """初始化聊天模型"""
        for model_name, config in self.model_configs['chat_models'].items():
            try:
                model_path = Path(config['path'])
                if not model_path.exists():
                    logger.warning(f"⚠️ 模型文件不存在: {model_path}")
                    continue
                
                if config['type'] == 'gguf':
                    # 使用llama-cpp-python加载GGUF模型
                    try:
                        from llama_cpp import Llama
                        model = Llama(
                            model_path=str(model_path),
                            n_ctx=config['context_length'],
                            n_threads=4,
                            verbose=False
                        )
                        self.models[model_name] = {
                            'model': model,
                            'type': 'gguf',
                            'config': config
                        }
                        logger.info(f"✅ GGUF模型加载成功: {model_name}")
                    except ImportError:
                        logger.warning(f"⚠️ llama-cpp-python未安装，跳过GGUF模型: {model_name}")
                
                elif config['type'] == 'transformers':
                    # 使用transformers加载模型
                    try:
                        from transformers import AutoTokenizer, AutoModelForCausalLM
                        tokenizer = AutoTokenizer.from_pretrained(model_path)
                        model = AutoModelForCausalLM.from_pretrained(
                            model_path,
                            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                            device_map="auto" if torch.cuda.is_available() else None
                        )
                        self.models[model_name] = {
                            'model': model,
                            'tokenizer': tokenizer,
                            'type': 'transformers',
                            'config': config
                        }
                        logger.info(f"✅ Transformers模型加载成功: {model_name}")
                    except ImportError:
                        logger.warning(f"⚠️ transformers未安装，跳过模型: {model_name}")
                        
            except Exception as e:
                logger.error(f"❌ 加载聊天模型失败 {model_name}: {e}")
    
    def _initialize_embedding_models(self):
        """初始化嵌入模型"""
        for model_name, config in self.model_configs['embedding_models'].items():
            try:
                model_path = Path(config['path'])
                if not model_path.exists():
                    logger.warning(f"⚠️ 嵌入模型文件不存在: {model_path}")
                    continue
                
                if config['type'] == 'sentence_transformers':
                    try:
                        from sentence_transformers import SentenceTransformer
                        model = SentenceTransformer(str(model_path))
                        self.embedding_models[model_name] = {
                            'model': model,
                            'config': config
                        }
                        logger.info(f"✅ 嵌入模型加载成功: {model_name}")
                    except ImportError:
                        logger.warning(f"⚠️ sentence-transformers未安装，跳过嵌入模型: {model_name}")
                        
            except Exception as e:
                logger.error(f"❌ 加载嵌入模型失败 {model_name}: {e}")
    
    def chat_completion(self, request: ChatCompletionRequest) -> ChatCompletionResponse:
        """聊天补全"""
        if request.model not in self.models:
            raise HTTPException(status_code=404, detail=f"模型未找到: {request.model}")
        
        model_info = self.models[request.model]
        
        try:
            if model_info['type'] == 'gguf':
                # GGUF模型推理
                messages_text = self._format_messages_for_gguf(request.messages)
                response = model_info['model'](
                    messages_text,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    stop=["</s>", "<|im_end|>"]
                )
                
                content = response['choices'][0]['text'].strip()
                
            elif model_info['type'] == 'transformers':
                # Transformers模型推理
                messages_text = self._format_messages_for_transformers(request.messages)
                inputs = model_info['tokenizer'].encode(messages_text, return_tensors="pt")
                
                with torch.no_grad():
                    outputs = model_info['model'].generate(
                        inputs,
                        max_new_tokens=request.max_tokens,
                        temperature=request.temperature,
                        do_sample=True,
                        pad_token_id=model_info['tokenizer'].eos_token_id
                    )
                
                content = model_info['tokenizer'].decode(outputs[0][len(inputs[0]):], skip_special_tokens=True)
            
            else:
                raise HTTPException(status_code=500, detail="不支持的模型类型")
            
            return ChatCompletionResponse(
                id=f"chatcmpl-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                created=int(datetime.now().timestamp()),
                model=request.model,
                choices=[{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": "stop"
                }],
                usage={
                    "prompt_tokens": len(request.messages),
                    "completion_tokens": len(content.split()),
                    "total_tokens": len(request.messages) + len(content.split())
                }
            )
            
        except Exception as e:
            logger.error(f"聊天补全失败: {e}")
            raise HTTPException(status_code=500, detail=f"推理失败: {str(e)}")
    
    def create_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """创建嵌入"""
        if request.model not in self.embedding_models:
            raise HTTPException(status_code=404, detail=f"嵌入模型未找到: {request.model}")
        
        model_info = self.embedding_models[request.model]
        
        try:
            embeddings = model_info['model'].encode(request.input)
            
            data = []
            for i, embedding in enumerate(embeddings):
                data.append({
                    "object": "embedding",
                    "index": i,
                    "embedding": embedding.tolist()
                })
            
            return EmbeddingResponse(
                data=data,
                model=request.model,
                usage={
                    "prompt_tokens": sum(len(text.split()) for text in request.input),
                    "total_tokens": sum(len(text.split()) for text in request.input)
                }
            )
            
        except Exception as e:
            logger.error(f"嵌入生成失败: {e}")
            raise HTTPException(status_code=500, detail=f"嵌入生成失败: {str(e)}")
    
    def _format_messages_for_gguf(self, messages: List[Dict[str, str]]) -> str:
        """为GGUF模型格式化消息"""
        formatted = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role == "system":
                formatted += f"<|im_start|>system\n{content}<|im_end|>\n"
            elif role == "user":
                formatted += f"<|im_start|>user\n{content}<|im_end|>\n"
            elif role == "assistant":
                formatted += f"<|im_start|>assistant\n{content}<|im_end|>\n"
        
        formatted += "<|im_start|>assistant\n"
        return formatted
    
    def _format_messages_for_transformers(self, messages: List[Dict[str, str]]) -> str:
        """为Transformers模型格式化消息"""
        formatted = ""
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            formatted += f"{role}: {content}\n"
        
        formatted += "assistant: "
        return formatted
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """获取可用模型列表"""
        return {
            "chat_models": list(self.models.keys()),
            "embedding_models": list(self.embedding_models.keys())
        }

# 全局模型管理器
model_manager = LocalModelManager()

@app.on_event("startup")
async def startup_event():
    """启动事件"""
    model_manager.initialize()

@app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
async def chat_completions(request: ChatCompletionRequest):
    """聊天补全API - 兼容OpenAI格式"""
    return model_manager.chat_completion(request)

@app.post("/v1/embeddings", response_model=EmbeddingResponse)
async def create_embeddings(request: EmbeddingRequest):
    """嵌入生成API - 兼容OpenAI格式"""
    return model_manager.create_embeddings(request)

@app.get("/v1/models")
async def list_models():
    """列出可用模型"""
    models = model_manager.get_available_models()
    return {
        "object": "list",
        "data": [
            {"id": model, "object": "model", "owned_by": "local"}
            for model_list in models.values()
            for model in model_list
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "Local Model API Server",
        "initialized": model_manager.initialized,
        "available_models": model_manager.get_available_models()
    }

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8002)
