#!/usr/bin/env python3
"""
测试新的检索系统
"""

import sys
import time

def test_simple_retriever():
    """测试简化检索器"""
    print("🔍 测试简化检索器")
    print("=" * 40)
    
    try:
        from simple_tcm_retriever import SimpleTCMRetriever
        
        retriever = SimpleTCMRetriever()
        
        print("初始化检索器...")
        if retriever.initialize():
            print("✅ 初始化成功")
            print(retriever.get_stats())
            
            # 测试搜索
            test_queries = [
                "气血不足",
                "失眠多梦", 
                "脾胃虚弱",
                "肾阳虚",
                "四君子汤"
            ]
            
            for query in test_queries:
                print(f"\n🔍 搜索: {query}")
                results = retriever.search_knowledge(query, top_k=3)
                print(f"找到 {len(results)} 个结果")
                
                for i, result in enumerate(results[:2]):
                    print(f"  {i+1}. {result['title']}")
                    print(f"     来源: {result['source']}")
                    print(f"     评分: {result['score']:.2f}")
                    print(f"     内容: {result['content'][:100]}...")
                    
        else:
            print("❌ 初始化失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_qwen_model():
    """测试Qwen模型"""
    print("\n🤖 测试Qwen2.5模型")
    print("=" * 40)
    
    try:
        from qwen_ollama_manager import QwenOllamaManager
        
        manager = QwenOllamaManager()
        
        print("初始化Qwen模型...")
        if manager.initialize():
            print("✅ 初始化成功")
            print(f"模型信息: {manager.get_model_info()}")
            
            # 测试生成
            test_prompt = "请简单介绍一下中医的气血理论，包括气血不足的症状和调理方法。"
            print(f"\n🧪 测试生成: {test_prompt}")
            
            response = manager.generate_response(test_prompt, max_tokens=200)
            print(f"回答: {response}")
            
        else:
            print("❌ 初始化失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_integration():
    """测试集成效果"""
    print("\n🔗 测试检索+生成集成")
    print("=" * 40)
    
    try:
        from simple_tcm_retriever import SimpleTCMRetriever
        from qwen_ollama_manager import QwenOllamaManager
        
        # 初始化组件
        retriever = SimpleTCMRetriever()
        qwen = QwenOllamaManager()
        
        if retriever.initialize() and qwen.initialize():
            print("✅ 组件初始化成功")
            
            # 测试查询
            query = "气血不足怎么调理"
            print(f"\n🔍 用户查询: {query}")
            
            # 1. 检索相关知识
            print("1. 检索相关知识...")
            search_results = retriever.search_knowledge(query, top_k=3)
            print(f"   找到 {len(search_results)} 个相关结果")
            
            # 2. 构建上下文
            context_parts = []
            for result in search_results:
                context_parts.append(f"参考资料: {result['title']}")
                context_parts.append(f"内容: {result['content'][:200]}...")
            
            context = "\n".join(context_parts)
            
            # 3. 生成回答
            print("2. 生成智能回答...")
            full_prompt = f"""基于以下中医知识，回答用户问题：

{context}

用户问题: {query}

请结合上述参考资料，给出专业的中医建议："""
            
            response = qwen.generate_response(full_prompt, max_tokens=300)
            print(f"\n🎯 最终回答:\n{response}")
            
        else:
            print("❌ 组件初始化失败")
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 新检索系统测试套件")
    print("=" * 50)
    
    # 测试简化检索器
    test_simple_retriever()
    
    time.sleep(2)
    
    # 测试Qwen模型
    test_qwen_model()
    
    time.sleep(2)
    
    # 测试集成效果
    test_integration()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
