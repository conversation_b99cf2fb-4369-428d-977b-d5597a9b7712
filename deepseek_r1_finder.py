#!/usr/bin/env python3
"""
DeepSeek-R1-0528-Qwen3-8B 兼容版本查找和下载工具
专门为您的笔记本电脑找到最佳兼容版本
"""

import os
import sys
import requests
from pathlib import Path
import json

class DeepSeekR1Finder:
    """DeepSeek-R1模型查找器"""
    
    def __init__(self):
        self.base_dir = Path.home() / ".lmstudio" / "models"
        
        # DeepSeek-R1-0528-Qwen3-8B的各种兼容版本
        self.deepseek_r1_variants = {
            "DeepSeek-R1-8B-Q4_K_M": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
                "size": "4.68GB",
                "compatibility": "高",
                "description": "标准Q4量化版本，平衡性能和大小",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF"
            },
            "DeepSeek-R1-8B-Q4_0": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf",
                "size": "4.34GB",
                "compatibility": "很高",
                "description": "Q4_0量化，兼容性最佳",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF"
            },
            "DeepSeek-R1-8B-Q5_K_M": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q5_K_M.gguf",
                "size": "5.73GB",
                "compatibility": "中",
                "description": "Q5量化，质量更高但文件更大",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF"
            },
            "DeepSeek-R1-8B-Q3_K_M": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q3_K_M.gguf",
                "size": "3.16GB",
                "compatibility": "很高",
                "description": "Q3量化，文件最小，兼容性极佳",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF"
            },
            "DeepSeek-R1-8B-Q8_0": {
                "url": "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q8_0.gguf",
                "size": "8.54GB",
                "compatibility": "低",
                "description": "Q8量化，质量最高但需要更多内存",
                "path": "lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF"
            }
        }
    
    def check_huggingface_availability(self):
        """检查HuggingFace可访问性"""
        print("🔍 检查HuggingFace连接...")
        
        try:
            response = requests.get("https://huggingface.co", timeout=10)
            if response.status_code == 200:
                print("✅ HuggingFace可访问")
                return True
            else:
                print("⚠️ HuggingFace访问异常")
                return False
        except Exception as e:
            print(f"❌ HuggingFace连接失败: {e}")
            return False
    
    def check_existing_models(self):
        """检查现有的DeepSeek-R1模型"""
        print("🔍 检查现有DeepSeek-R1模型...")
        
        found_models = []
        
        # 检查各种可能的路径
        search_paths = [
            self.base_dir / "lmstudio-community" / "DeepSeek-R1-0528-Qwen3-8B-GGUF",
            self.base_dir / "DeepSeek-R1-0528-Qwen3-8B-GGUF",
            Path("./models"),
            Path(".")
        ]
        
        for search_path in search_paths:
            if search_path.exists():
                for file in search_path.glob("*DeepSeek*R1*.gguf"):
                    size = file.stat().st_size / (1024**3)
                    found_models.append({
                        "name": file.name,
                        "path": str(file),
                        "size": f"{size:.2f}GB"
                    })
                    print(f"✅ 找到: {file.name} ({size:.2f}GB)")
        
        if not found_models:
            print("❌ 未找到现有DeepSeek-R1模型")
        
        return found_models
    
    def test_model_compatibility(self, model_path: str):
        """测试模型兼容性"""
        print(f"\n🧪 测试模型兼容性: {Path(model_path).name}")
        
        try:
            from llama_cpp import Llama
            
            # 渐进式测试不同配置
            test_configs = [
                {
                    "name": "最小配置",
                    "params": {
                        "n_ctx": 256,
                        "n_threads": 1,
                        "n_gpu_layers": 0,
                        "verbose": False,
                        "use_mmap": False,
                        "use_mlock": False
                    }
                },
                {
                    "name": "保守配置",
                    "params": {
                        "n_ctx": 1024,
                        "n_threads": 2,
                        "n_gpu_layers": 0,
                        "verbose": False,
                        "use_mmap": True,
                        "use_mlock": False
                    }
                },
                {
                    "name": "优化配置",
                    "params": {
                        "n_ctx": 2048,
                        "n_threads": 4,
                        "n_gpu_layers": 10,
                        "verbose": False,
                        "use_mmap": True,
                        "use_mlock": False
                    }
                }
            ]
            
            working_config = None
            
            for config in test_configs:
                try:
                    print(f"   🔄 测试{config['name']}...")
                    
                    model = Llama(
                        model_path=model_path,
                        **config['params']
                    )
                    
                    # 简单推理测试
                    response = model("Hello", max_tokens=5, echo=False)
                    
                    print(f"   ✅ {config['name']}成功: {response}")
                    working_config = config
                    break
                    
                except Exception as e:
                    print(f"   ❌ {config['name']}失败: {str(e)[:50]}...")
                    continue
            
            return working_config
            
        except ImportError:
            print("❌ llama-cpp-python未安装")
            return None
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return None
    
    def recommend_best_variant(self):
        """推荐最佳变体"""
        print("\n💡 DeepSeek-R1-0528-Qwen3-8B 版本推荐:")
        print("=" * 60)
        
        # 基于您的32GB内存，推荐优先级
        recommendations = [
            {
                "variant": "DeepSeek-R1-8B-Q4_0",
                "reason": "兼容性最佳，推荐首选",
                "priority": 1
            },
            {
                "variant": "DeepSeek-R1-8B-Q3_K_M", 
                "reason": "文件最小，加载最快",
                "priority": 2
            },
            {
                "variant": "DeepSeek-R1-8B-Q4_K_M",
                "reason": "标准版本，平衡性能",
                "priority": 3
            },
            {
                "variant": "DeepSeek-R1-8B-Q5_K_M",
                "reason": "质量更高，内存充足可选",
                "priority": 4
            }
        ]
        
        for rec in recommendations:
            variant = rec["variant"]
            info = self.deepseek_r1_variants[variant]
            print(f"{rec['priority']}. {variant}")
            print(f"   大小: {info['size']}")
            print(f"   兼容性: {info['compatibility']}")
            print(f"   推荐理由: {rec['reason']}")
            print(f"   描述: {info['description']}")
            print()
        
        return recommendations[0]["variant"]  # 返回最推荐的
    
    def download_model(self, variant_name: str):
        """下载指定变体"""
        if variant_name not in self.deepseek_r1_variants:
            print(f"❌ 未知变体: {variant_name}")
            return False
        
        info = self.deepseek_r1_variants[variant_name]
        model_dir = self.base_dir / info["path"]
        model_path = model_dir / f"{variant_name}.gguf"
        
        # 创建目录
        model_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📥 开始下载 {variant_name}")
        print(f"📍 URL: {info['url']}")
        print(f"💾 保存到: {model_path}")
        print(f"📊 大小: {info['size']}")
        
        try:
            response = requests.get(info['url'], stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = downloaded / total_size * 100
                            print(f"\r📥 下载进度: {progress:.1f}% ({downloaded/(1024**3):.2f}GB/{total_size/(1024**3):.2f}GB)", end="")
            
            print(f"\n✅ {variant_name} 下载完成!")
            
            # 立即测试兼容性
            working_config = self.test_model_compatibility(str(model_path))
            if working_config:
                print(f"🎉 模型兼容性测试通过!")
                self.create_config_file(str(model_path), working_config)
                return True
            else:
                print("⚠️ 模型下载成功但兼容性测试失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 下载失败: {e}")
            if model_path.exists():
                model_path.unlink()
            return False
    
    def create_config_file(self, model_path: str, config: dict):
        """创建配置文件"""
        config_content = f'''# DeepSeek-R1 优化配置
# 自动生成，适配您的笔记本电脑

DEEPSEEK_R1_MODEL_PATH = r"{model_path}"

DEEPSEEK_R1_CONFIG = {config['params']}

# 使用说明:
# 1. 将此配置导入到您的系统中
# 2. 使用 {config['name']} 参数加载模型
# 3. 模型已通过兼容性测试
'''
        
        with open("deepseek_r1_config.py", "w", encoding="utf-8") as f:
            f.write(config_content)
        
        print(f"✅ 配置文件已创建: deepseek_r1_config.py")

def main():
    """主函数"""
    print("🤖 DeepSeek-R1-0528-Qwen3-8B 专用查找器")
    print("为您的笔记本电脑找到最佳兼容版本")
    print("=" * 60)
    
    finder = DeepSeekR1Finder()
    
    # 检查网络连接
    if not finder.check_huggingface_availability():
        print("❌ 无法连接到HuggingFace，请检查网络")
        return
    
    # 检查现有模型
    existing = finder.check_existing_models()
    
    # 测试现有模型
    working_model = None
    if existing:
        print(f"\n🧪 测试现有模型兼容性...")
        for model in existing:
            config = finder.test_model_compatibility(model['path'])
            if config:
                working_model = model
                finder.create_config_file(model['path'], config)
                break
    
    if working_model:
        print(f"\n🎉 找到可用的DeepSeek-R1模型!")
        print(f"📁 路径: {working_model['path']}")
        print(f"📊 大小: {working_model['size']}")
    else:
        print(f"\n📥 需要下载新的兼容版本")
        
        # 推荐最佳版本
        recommended = finder.recommend_best_variant()
        
        # 用户选择
        choice = input(f"\n下载推荐版本 {recommended}? (Y/n): ").strip().lower()
        
        if choice in ['', 'y', 'yes']:
            success = finder.download_model(recommended)
            if success:
                print("\n🎉 DeepSeek-R1模型安装完成!")
            else:
                print("\n❌ 安装失败，请尝试其他版本")
        else:
            print("❌ 用户取消下载")
    
    print("\n💡 下一步:")
    print("1. 重启您的系统: streamlit run ultimate_final_tcm_system.py --server.port=8507")
    print("2. 点击'🚀 初始化系统'测试DeepSeek直接调用")
    print("3. 享受智能的DeepSeek-R1模型!")

if __name__ == "__main__":
    main()
