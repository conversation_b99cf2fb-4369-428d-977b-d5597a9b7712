#!/usr/bin/env python3
"""
启动优化版中医RAG系统
主启动文件 - 解决卡顿问题 + 语音对话 + 连续记忆
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def print_optimized_banner():
    """打印优化版横幅"""
    print("=" * 90)
    print("🧙‍♂️ 智者·中医AI助手 - 优化版")
    print("=" * 90)
    print("🎯 主要优化:")
    print("")
    print("✅ 解决PDF解析卡顿问题")
    print("   📄 文件大小限制: ≤10MB")
    print("   📑 页数限制: ≤50页")
    print("   🔄 分批处理，防止内存溢出")
    print("   🗑️ 自动垃圾回收")
    print("")
    print("✅ 语音对话功能")
    print("   🎤 语音输入 (speech_recognition)")
    print("   🔊 语音输出 (pyttsx3)")
    print("   💬 连续对话记忆")
    print("")
    print("✅ 连续对话记忆")
    print("   📝 保存对话历史")
    print("   🧠 用户画像分析")
    print("   🔗 上下文关联")
    print("=" * 90)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    required_modules = [
        ("streamlit", "界面框架"),
        ("sentence_transformers", "文本嵌入"),
        ("faiss", "向量搜索"),
        ("PyPDF2", "PDF处理"),
        ("numpy", "数值计算"),
        ("requests", "HTTP请求"),
        ("bs4", "HTML解析"),
    ]
    
    missing_modules = []
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError:
            print(f"❌ {description} - 缺失")
            missing_modules.append(module)
    
    # 检查语音功能
    print("\n🔍 检查语音功能...")
    
    voice_modules = [
        ("pyttsx3", "语音输出"),
        ("speech_recognition", "语音识别"),
    ]
    
    voice_available = True
    for module, description in voice_modules:
        try:
            __import__(module)
            print(f"✅ {description}")
        except ImportError:
            print(f"⚠️ {description} - 不可用")
            voice_available = False
    
    if not voice_available:
        print("💡 安装语音功能: python install_voice_dependencies.py")
    
    if missing_modules:
        print(f"\n❌ 缺少必要依赖: {', '.join(missing_modules)}")
        print("💡 请运行: python install_enhanced_dependencies.py")
        return False
    
    print("✅ 所有必要依赖检查完成")
    return True

def check_system_files():
    """检查系统文件"""
    print("\n📁 检查系统文件...")
    
    required_files = [
        "optimized_tcm_system.py"
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            return False
    
    return True

def create_directories():
    """创建必要目录"""
    print("\n📁 创建系统目录...")
    
    directories = [
        "./optimized_vector_db",
        "./documents",
        "./uploads",
        "./logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}")

def check_data_status():
    """检查数据状态"""
    print("\n📊 检查数据状态...")
    
    # 检查文档
    documents_dir = Path("./documents")
    if documents_dir.exists():
        all_files = list(documents_dir.glob("*"))
        pdf_files = list(documents_dir.glob("*.pdf"))
        
        if all_files:
            print(f"📚 发现文档: {len(all_files)} 个")
            print(f"   PDF文件: {len(pdf_files)} 个")
            
            # 检查文件大小
            large_files = []
            for file in pdf_files:
                size_mb = os.path.getsize(file) / (1024 * 1024)
                if size_mb > 10:
                    large_files.append((file.name, size_mb))
            
            if large_files:
                print("⚠️ 发现大文件 (>10MB):")
                for name, size in large_files:
                    print(f"   - {name}: {size:.1f}MB")
                print("💡 建议压缩或分割大文件以避免卡顿")
        else:
            print("📄 documents目录为空")
    
    # 检查向量数据库
    vector_db_dir = Path("./optimized_vector_db")
    if vector_db_dir.exists():
        required_files = ["index.faiss", "chunks.pkl", "metadata.pkl"]
        existing_files = [f for f in required_files if (vector_db_dir / f).exists()]
        
        if len(existing_files) == len(required_files):
            print("🗄️ 向量数据库完整")
        elif existing_files:
            print(f"⚠️ 向量数据库不完整")
        else:
            print("🆕 向量数据库为空")

def show_usage_tips():
    """显示使用技巧"""
    print("\n💡 使用技巧:")
    print("   1. 文件限制: 单个PDF ≤10MB, ≤50页")
    print("   2. 语音输入: 点击🎤按钮后说话")
    print("   3. 连续对话: 系统会记住之前的对话")
    print("   4. 防卡顿: 系统会自动优化内存使用")
    print("   5. 最佳体验: 上传高质量中医PDF文档")

def launch_optimized_system():
    """启动优化系统"""
    print("\n🚀 启动优化版系统...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "optimized_tcm_system.py",
            "--server.port=8505",
            "--server.address=0.0.0.0",
            "--theme.base=light"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")

def main():
    """主函数"""
    print_optimized_banner()
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        input("按回车键退出...")
        return
    
    # 2. 检查系统文件
    if not check_system_files():
        print("\n❌ 系统文件检查失败")
        input("按回车键退出...")
        return
    
    # 3. 创建目录
    create_directories()
    
    # 4. 检查数据状态
    check_data_status()
    
    # 5. 显示使用技巧
    show_usage_tips()
    
    print("\n" + "=" * 90)
    print("🎉 优化版系统准备完成！")
    print("")
    print("🎯 核心优化:")
    print("   ✅ 防卡顿: 文件大小和页数限制")
    print("   ✅ 语音对话: 输入+输出+连续记忆")
    print("   ✅ 内存优化: 分批处理+垃圾回收")
    print("   ✅ 用户体验: 响应式界面+智能分析")
    print("")
    print("🌐 访问地址: http://localhost:8505")
    print("=" * 90)
    
    # 6. 启动系统
    input("按回车键启动优化版系统...")
    launch_optimized_system()

if __name__ == "__main__":
    main()
