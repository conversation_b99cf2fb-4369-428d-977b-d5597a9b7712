"""
超快速RAG系统界面 - AMD GPU优化版
"""
import streamlit as st
import psutil
import gc
import time
import threading
from pathlib import Path

# 页面配置
st.set_page_config(
    page_title="超快速RAG系统 (AMD GPU优化)",
    page_icon="🚀",
    layout="wide"
)

def show_system_info():
    """显示系统信息"""
    col1, col2, col3, col4 = st.columns(4)
    
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent()
    
    with col1:
        st.metric("内存使用", f"{memory.percent:.1f}%")
    with col2:
        st.metric("可用内存", f"{memory.available / (1024**3):.1f} GB")
    with col3:
        st.metric("CPU使用", f"{cpu_percent:.1f}%")
    with col4:
        # 显示GPU信息
        try:
            from gpu_optimizer import setup_gpu_optimization
            gpu_config = setup_gpu_optimization()
            st.metric("处理设备", gpu_config['device_name'][:15] + "...")
        except:
            st.metric("处理设备", "CPU")

def initialize_ultra_system():
    """初始化超快速系统"""
    if 'ultra_system_initialized' not in st.session_state:
        with st.spinner("正在初始化超快速系统..."):
            try:
                # 初始化GPU优化
                from gpu_optimizer import setup_gpu_optimization
                gpu_config = setup_gpu_optimization()
                st.session_state.gpu_config = gpu_config
                
                # 初始化增强RAG系统
                from enhanced_rag_system import enhanced_rag_system
                success = enhanced_rag_system.initialize()
                st.session_state.ultra_system_initialized = success
                
                if success:
                    st.success(f"✅ 超快速系统初始化成功！")
                    st.info(f"🚀 使用设备: {gpu_config['device_name']}")
                else:
                    st.error("❌ 系统初始化失败")
                    
            except Exception as e:
                st.error(f"初始化错误: {str(e)}")
                st.session_state.ultra_system_initialized = False
                
    return st.session_state.get('ultra_system_initialized', False)

def process_tcm_pdf_fast(uploaded_file):
    """快速处理中医PDF - 修复线程问题"""
    if not st.session_state.get('ultra_system_initialized', False):
        st.error("请先初始化系统")
        return False

    # 检查文件大小
    max_size = 100 * 1024 * 1024  # 100MB
    if uploaded_file.size > max_size:
        st.error(f"文件过大，请选择小于{max_size//1024//1024}MB的文件")
        return False

    # 检查内存
    memory = psutil.virtual_memory()
    if memory.percent > 90:
        st.error("内存不足，请先清理内存")
        return False

    # 保存文件
    import config
    file_path = config.DOCUMENTS_DIR / uploaded_file.name
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())

    # 创建进度显示容器
    progress_container = st.container()

    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()
        time_text = st.empty()

        start_time = time.time()

        try:
            # 根据文件类型选择处理器
            file_extension = uploaded_file.name.lower().split('.')[-1]

            if file_extension == 'txt':
                from tcm_text_processor import process_tcm_text
                process_func = lambda path: process_tcm_text(path)
                processor_name = "中医文本处理器"
            else:  # pdf
                from robust_tcm_processor import process_tcm_pdf_robust
                process_func = lambda path: process_tcm_pdf_robust(path)
                processor_name = "强化PDF处理器"

            # 使用线程处理，但不传递UI回调
            result_container = {'success': False, 'error': None, 'progress': 0}

            def process_thread():
                try:
                    result_container['success'] = process_func(str(file_path))
                except Exception as e:
                    result_container['error'] = str(e)

            # 启动处理线程
            thread = threading.Thread(target=process_thread)
            thread.start()

            # 根据文件类型显示不同的进度步骤
            if file_extension == 'txt':
                progress_steps = [
                    (15, "📄 读取中医文本文件..."),
                    (25, "🧹 清理中医文本..."),
                    (35, "🔪 智能分割中医文本..."),
                    (50, "📋 创建中医文档元数据..."),
                    (75, "🔄 生成中医文本向量..."),
                    (90, "🏗️ 创建中医知识索引..."),
                    (98, "💾 保存中医知识库...")
                ]
            else:  # pdf
                progress_steps = [
                    (10, "📄 多方法提取PDF文本..."),
                    (25, "🔪 智能分割中医文本..."),
                    (35, "📋 创建中医文档元数据..."),
                    (60, "🔄 生成中医文本向量..."),
                    (85, "🏗️ 创建中医知识索引..."),
                    (95, "💾 保存中医知识库...")
                ]

            step_index = 0
            while thread.is_alive() and step_index < len(progress_steps):
                if step_index < len(progress_steps):
                    progress, message = progress_steps[step_index]
                    progress_bar.progress(progress)
                    status_text.text(message)
                    elapsed = time.time() - start_time
                    time_text.text(f"⏱️ 已用时: {elapsed:.1f}秒")
                    step_index += 1

                time.sleep(3)  # 每3秒更新一次

            # 等待线程完成
            thread.join(timeout=600)  # 10分钟超时

            if thread.is_alive():
                st.error("⏰ 处理超时，请尝试更小的文件")
                return False

            if result_container['error']:
                st.error(f"❌ 中医文档处理失败: {result_container['error']}")
                return False

            if result_container['success']:
                total_time = time.time() - start_time
                progress_bar.progress(100)
                status_text.text("🎉 中医文档处理完成！")
                time_text.text(f"⏱️ 总耗时: {total_time:.1f}秒")

                st.success(f"🏥 中医文档 {uploaded_file.name} 处理成功！")
                st.info(f"⚡ 处理速度: {uploaded_file.size / 1024 / total_time:.1f} KB/秒")

                # 清理内存
                gc.collect()
                return True
            else:
                st.error("❌ 中医文档处理失败")
                return False

        except Exception as e:
            st.error(f"处理错误: {str(e)}")
            return False

def main():
    """主界面"""
    st.title("🏥 中医RAG系统 - AMD GPU优化版")
    
    # 显示中医RAG特点
    with st.expander("🏥 中医RAG系统特点", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.write("""
            **🔥 中医专用优化:**
            - 🧵 并行PDF提取 (多线程)
            - 🔄 中医文本智能分割
            - 🏗️ 中医知识索引创建
            - 💾 中医知识库保存
            - 🧠 AMD GPU智能优化
            """)

        with col2:
            st.write("""
            **⚡ 中医文档处理速度:**
            - 《伤寒论》: 2-4分钟
            - 《金匮要略》: 1-3分钟
            - 《黄帝内经》: 3-6分钟
            - 其他中医典籍: 视大小而定
            """)
    
    # 初始化系统
    if not initialize_ultra_system():
        st.stop()
    
    # 显示系统信息
    st.subheader("📊 系统状态")
    show_system_info()
    
    # 主要功能区域
    col1, col2 = st.columns([3, 2])
    
    with col1:
        st.subheader("📁 中医文档处理")

        # 文件上传
        uploaded_file = st.file_uploader(
            "选择中医文档",
            type=['pdf', 'txt'],
            help="支持PDF和TXT格式，如《伤寒论》《金匮要略》《黄帝内经》等中医经典"
        )
        
        if uploaded_file:
            # 显示文件信息
            file_size_mb = uploaded_file.size / (1024 * 1024)
            
            col_info1, col_info2 = st.columns(2)
            with col_info1:
                st.info(f"📄 文件: {uploaded_file.name}")
            with col_info2:
                st.info(f"📊 大小: {file_size_mb:.2f} MB")
            
            # 预估处理时间
            if file_size_mb <= 5:
                estimated_time = "15-30秒"
                color = "green"
            elif file_size_mb <= 20:
                estimated_time = "1-2分钟"
                color = "blue"
            elif file_size_mb <= 50:
                estimated_time = "2-4分钟"
                color = "orange"
            else:
                estimated_time = "4-8分钟"
                color = "red"
            
            st.markdown(f"⏱️ 预估处理时间: <span style='color:{color}'>{estimated_time}</span>", 
                       unsafe_allow_html=True)
            
            # 处理按钮
            if st.button("🏥 处理中医文档", type="primary", use_container_width=True):
                success = process_tcm_pdf_fast(uploaded_file)
                
                if success:
                    st.balloons()
                    st.success("🎉 处理完成！现在可以开始问答了。")
        
        # 问答界面
        st.subheader("💬 智能问答")
        
        question = st.text_input(
            "请输入您的问题:",
            placeholder="例如：这个文档的主要内容是什么？",
            key="ultra_question"
        )
        
        col_q1, col_q2 = st.columns(2)
        with col_q1:
            if st.button("🔍 提问", use_container_width=True) and question:
                handle_question(question)
        
        with col_q2:
            if st.button("⚡ 流式回答", use_container_width=True) and question:
                handle_stream_question(question)
    
    with col2:
        st.subheader("⚙️ 系统控制")
        
        # 内存管理
        if st.button("🧹 清理内存", use_container_width=True):
            gc.collect()
            st.success("内存清理完成")
            time.sleep(1)
            st.rerun()
        
        # 系统状态详情
        st.subheader("📈 处理状态")
        try:
            from enhanced_rag_system import enhanced_rag_system
            status = enhanced_rag_system.get_system_status()
            
            st.metric("已索引块数", status['documents_indexed'])
            
            if status['documents_indexed'] > 0:
                st.success("✅ 已有文档可供问答")
                
                # 显示处理统计
                chunks_per_mb = status['documents_indexed'] / max(1, file_size_mb) if 'file_size_mb' in locals() else 0
                if chunks_per_mb > 0:
                    st.metric("处理密度", f"{chunks_per_mb:.0f} 块/MB")
            else:
                st.info("📝 请先上传并处理文档")
                
        except Exception as e:
            st.warning(f"⚠️ 状态获取失败: {e}")
        
        # GPU配置信息
        if 'gpu_config' in st.session_state:
            st.subheader("🔧 优化配置")
            config = st.session_state.gpu_config
            st.write(f"**设备**: {config['device_name']}")
            st.write(f"**批处理**: {config['batch_size']}")
            st.write(f"**块大小**: {config['chunk_size']}")
            st.write(f"**优化级别**: {config['optimization_level']}")

def handle_question(question):
    """处理问题 - 使用增强RAG系统"""
    try:
        from enhanced_rag_system import enhanced_rag_system
        from session_manager import session_manager

        if 'ultra_session_id' not in st.session_state:
            st.session_state.ultra_session_id = session_manager.create_session()

        with st.spinner("正在分析中医文献..."):
            result = enhanced_rag_system.retrieve_and_generate(
                question,
                st.session_state.ultra_session_id
            )
        
        if 'error' in result:
            st.error(f"错误: {result['error']}")
            return
        
        st.write("**问题:**", question)
        st.write("**回答:**", result['answer'])
        
        if result.get('sources'):
            with st.expander("📖 参考来源"):
                for i, source in enumerate(result['sources'][:3], 1):
                    st.write(f"**来源 {i}:** {Path(source['source']).name}")
                    st.write(f"**相似度:** {source['similarity']:.3f}")
                    st.write(f"**内容:** {source['content'][:200]}...")
        
    except Exception as e:
        st.error(f"处理错误: {str(e)}")

def handle_stream_question(question):
    """处理流式问题 - 使用增强RAG系统"""
    try:
        from enhanced_rag_system import enhanced_rag_system

        if 'ultra_session_id' not in st.session_state:
            from session_manager import session_manager
            st.session_state.ultra_session_id = session_manager.create_session()

        st.write("**问题:**", question)

        response_placeholder = st.empty()

        for chunk in enhanced_rag_system.stream_generate(question, st.session_state.ultra_session_id):
            response_placeholder.write(f"**回答:** {chunk}")
            time.sleep(0.05)
        
    except Exception as e:
        st.error(f"流式处理错误: {str(e)}")

if __name__ == "__main__":
    main()
