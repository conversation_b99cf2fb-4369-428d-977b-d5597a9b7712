#!/usr/bin/env python3
"""
在线中医资源爬取模块
专门爬取 https://chinesebooks.github.io/gudaiyishu/ 的内容
确保能够真正获取到有价值的中医知识
"""

import requests
import time
import json
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from pathlib import Path
from typing import List, Dict, Optional
import hashlib
from datetime import datetime

class OnlineMedicalCrawler:
    """在线中医资源爬取器"""
    
    def __init__(self):
        self.base_url = "https://chinesebooks.github.io/gudaiyishu/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.cache_dir = Path("./online_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
    def get_cache_key(self, url):
        """生成缓存键"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get_cached_content(self, url):
        """获取缓存内容"""
        cache_key = self.get_cache_key(url)
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # 检查缓存是否过期（24小时）
                cache_time = datetime.fromisoformat(data['timestamp'])
                if (datetime.now() - cache_time).total_seconds() < 86400:
                    return data['content']
            except:
                pass
        return None
    
    def save_to_cache(self, url, content):
        """保存到缓存"""
        cache_key = self.get_cache_key(url)
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        data = {
            'url': url,
            'content': content,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def fetch_page_content(self, url, timeout=15):
        """获取页面内容"""
        # 先检查缓存
        cached_content = self.get_cached_content(url)
        if cached_content:
            return cached_content
        
        try:
            response = self.session.get(url, timeout=timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取主要内容
            content = self.extract_main_content(soup)
            
            # 保存到缓存
            self.save_to_cache(url, content)
            
            return content
            
        except Exception as e:
            print(f"获取页面失败 {url}: {e}")
            return None
    
    def extract_main_content(self, soup):
        """提取页面主要内容"""
        content_selectors = [
            'main',
            'article',
            '.content',
            '.main-content',
            '.article-content',
            '#content',
            '#main',
            'div[class*="content"]',
            'div[class*="article"]'
        ]
        
        main_content = None
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                main_content = elements[0]
                break
        
        if not main_content:
            # 如果没有找到特定的内容区域，使用body
            main_content = soup.find('body') or soup
        
        # 提取文本内容
        text_content = []
        
        # 提取标题
        for heading in main_content.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            text = heading.get_text(strip=True)
            if text and len(text) > 2:
                text_content.append(f"## {text}")
        
        # 提取段落
        for paragraph in main_content.find_all(['p', 'div']):
            text = paragraph.get_text(strip=True)
            if text and len(text) > 10:  # 过滤太短的文本
                # 清理文本
                text = re.sub(r'\s+', ' ', text)
                text_content.append(text)
        
        # 提取列表
        for ul in main_content.find_all(['ul', 'ol']):
            for li in ul.find_all('li'):
                text = li.get_text(strip=True)
                if text and len(text) > 5:
                    text_content.append(f"• {text}")
        
        return '\n\n'.join(text_content)
    
    def discover_medical_pages(self):
        """发现中医相关页面"""
        discovered_urls = set()
        
        # 主要的中医典籍页面
        main_pages = [
            "yizongjinjian/",
            "huangdineijing/",
            "shanghan/",
            "jinkuiyaolue/",
            "bencaogangmu/",
            "zhenjiu/",
            "wenbing/"
        ]
        
        for page in main_pages:
            url = urljoin(self.base_url, page)
            discovered_urls.add(url)
            
            # 尝试获取子页面
            try:
                content = self.fetch_page_content(url)
                if content:
                    # 查找更多链接
                    response = self.session.get(url, timeout=10)
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    for link in soup.find_all('a', href=True):
                        href = link['href']
                        if href.startswith('/') or href.startswith('./'):
                            full_url = urljoin(url, href)
                            if self.base_url in full_url:
                                discovered_urls.add(full_url)
                
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"发现页面失败 {url}: {e}")
                continue
        
        return list(discovered_urls)
    
    def search_relevant_content(self, query, max_results=5):
        """搜索相关内容"""
        results = []
        
        # 发现相关页面
        pages = self.discover_medical_pages()
        
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query.lower()))
        
        for url in pages[:20]:  # 限制搜索页面数量
            try:
                content = self.fetch_page_content(url)
                if not content:
                    continue
                
                # 计算相关性
                content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content.lower()))
                relevance = self.calculate_relevance(query_keywords, content_keywords, content, query)
                
                if relevance > 0.1:  # 相关性阈值
                    # 提取最相关的段落
                    relevant_paragraphs = self.extract_relevant_paragraphs(content, query)
                    
                    if relevant_paragraphs:
                        results.append({
                            'url': url,
                            'title': self.extract_page_title(url),
                            'content': relevant_paragraphs,
                            'relevance': relevance,
                            'source': '古代医书在线'
                        })
                
                time.sleep(0.5)  # 控制请求频率
                
            except Exception as e:
                print(f"搜索内容失败 {url}: {e}")
                continue
        
        # 按相关性排序
        results.sort(key=lambda x: x['relevance'], reverse=True)
        return results[:max_results]
    
    def calculate_relevance(self, query_keywords, content_keywords, content, query):
        """计算内容相关性"""
        if not query_keywords:
            return 0
        
        # 关键词匹配得分
        keyword_score = len(query_keywords.intersection(content_keywords)) / len(query_keywords)
        
        # 直接包含查询的得分
        direct_match_score = 1.0 if query.strip() in content else 0
        
        # 综合得分
        total_score = keyword_score * 0.7 + direct_match_score * 0.3
        
        return total_score
    
    def extract_relevant_paragraphs(self, content, query, max_length=800):
        """提取最相关的段落"""
        paragraphs = content.split('\n\n')
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query.lower()))
        
        scored_paragraphs = []
        for paragraph in paragraphs:
            if len(paragraph.strip()) < 20:
                continue
            
            paragraph_keywords = set(re.findall(r'[\u4e00-\u9fff]+', paragraph.lower()))
            score = len(query_keywords.intersection(paragraph_keywords))
            
            if query.strip() in paragraph:
                score += 5
            
            if score > 0:
                scored_paragraphs.append((score, paragraph))
        
        # 按得分排序，取前几个段落
        scored_paragraphs.sort(key=lambda x: x[0], reverse=True)
        
        result = ""
        for score, paragraph in scored_paragraphs:
            if len(result) + len(paragraph) > max_length:
                break
            result += paragraph + "\n\n"
        
        return result.strip()
    
    def extract_page_title(self, url):
        """提取页面标题"""
        try:
            response = self.session.get(url, timeout=5)
            soup = BeautifulSoup(response.content, 'html.parser')
            title = soup.find('title')
            if title:
                return title.get_text(strip=True)
        except:
            pass
        
        # 从URL推断标题
        path = urlparse(url).path
        if 'yizongjinjian' in path:
            return '医宗金鉴'
        elif 'huangdineijing' in path:
            return '黄帝内经'
        elif 'shanghan' in path:
            return '伤寒论'
        elif 'jinkuiyaolue' in path:
            return '金匮要略'
        elif 'bencaogangmu' in path:
            return '本草纲目'
        else:
            return '古代医书'

# 全局爬虫实例
medical_crawler = OnlineMedicalCrawler()

def search_online_medical_resources(query, max_results=3):
    """搜索在线中医资源的便捷函数"""
    return medical_crawler.search_relevant_content(query, max_results)

if __name__ == "__main__":
    # 测试爬虫
    crawler = OnlineMedicalCrawler()
    results = crawler.search_relevant_content("湿气", max_results=3)
    
    for result in results:
        print(f"标题: {result['title']}")
        print(f"URL: {result['url']}")
        print(f"相关性: {result['relevance']:.3f}")
        print(f"内容: {result['content'][:200]}...")
        print("-" * 50)
