#!/usr/bin/env python3
"""
TCM RAG系统依赖安装脚本
自动安装所需的Python包
"""

import subprocess
import sys
import os

def install_package(package_name: str) -> bool:
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主安装函数"""
    print("🔧 TCM RAG系统 - 依赖安装器")
    print("=" * 50)
    
    # 核心依赖包
    core_packages = [
        "streamlit>=1.28.0",
        "fastapi>=0.104.0", 
        "uvicorn>=0.24.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "sentence-transformers>=2.2.0",
        "numpy>=1.24.0",
        "scikit-learn>=1.3.0",
        "pandas>=2.0.0",
        "pydantic>=2.0.0"
    ]
    
    # 可选依赖包
    optional_packages = [
        "python-docx",  # Word文档支持
        "openpyxl",     # Excel支持
        "python-pptx",  # PowerPoint支持
        "PyPDF2",       # PDF支持
        "pyttsx3",      # 语音合成
        "SpeechRecognition",  # 语音识别
        "pyaudio"       # 音频处理
    ]
    
    print("🚀 开始安装核心依赖...")
    
    failed_packages = []
    
    # 安装核心包
    for package in core_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n🔧 安装可选依赖...")
    
    # 安装可选包
    for package in optional_packages:
        if not install_package(package):
            print(f"⚠️ {package} 安装失败（可选功能可能受限）")
    
    # 特殊处理：中文嵌入模型
    print("\n🤖 安装中文嵌入模型...")
    try:
        # 安装更稳定的中文嵌入模型
        install_package("text2vec")
        print("✅ 中文嵌入模型支持已安装")
    except:
        print("⚠️ 中文嵌入模型安装失败，将使用默认模型")
    
    # 结果汇总
    print("\n" + "=" * 50)
    if failed_packages:
        print("❌ 以下核心包安装失败:")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n💡 请手动安装失败的包:")
        print(f"pip install {' '.join(failed_packages)}")
        print("\n⚠️ 系统可能无法正常运行")
    else:
        print("🎉 所有核心依赖安装成功！")
        print("✅ 系统已准备就绪")
        print("\n🚀 现在可以运行以下命令启动系统:")
        print("python start_tcm_system.py")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
