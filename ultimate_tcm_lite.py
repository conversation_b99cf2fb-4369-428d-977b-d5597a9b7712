#!/usr/bin/env python3
"""
终极版中医RAG系统 - 轻量版
整合所有功能，但使用更少的依赖
"""
import json
import pickle
import re
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import uuid
import logging

# FastAPI相关
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
import uvicorn

# 数据处理
import requests
from bs4 import BeautifulSoup

# 文档处理
import PyPDF2
import docx

# 可选依赖
try:
    from sentence_transformers import SentenceTransformer
    import faiss
    import numpy as np
    ADVANCED_ML_AVAILABLE = True
except ImportError:
    ADVANCED_ML_AVAILABLE = False
    print("⚠️ 高级ML库未安装，使用基础功能")

try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("⚠️ jieba未安装，使用基础分词")

try:
    from openpyxl import load_workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("⚠️ openpyxl未安装，不支持Excel文件")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="终极版中医RAG系统 - 轻量版",
    description="集成PDF检索、在线爬取、多模型、语音交互的完整系统",
    version="3.0.0-lite"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局配置
class Config:
    # 目录配置
    UPLOAD_DIR = Path("uploads")
    VECTOR_DB_DIR = Path("vector_db")
    SESSIONS_DIR = Path("sessions")
    CACHE_DIR = Path("cache")
    
    # 模型配置
    MODELS = {
        "default": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "chinese": "shibing624/text2vec-base-chinese",
        "medical": "sentence-transformers/all-MiniLM-L6-v2",
        "fast": "sentence-transformers/all-MiniLM-L6-v2"
    }
    
    # 在线资源配置
    ONLINE_SOURCES = [
        "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/",
        "https://www.zhzyw.com/",
        "https://www.cntcm.com.cn/"
    ]
    
    # 文档处理配置
    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 50
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    # 检索配置
    TOP_K = 5
    SIMILARITY_THRESHOLD = 0.3

# 初始化配置
config = Config()
for directory in [config.UPLOAD_DIR, config.VECTOR_DB_DIR, config.SESSIONS_DIR, config.CACHE_DIR]:
    directory.mkdir(exist_ok=True)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_voice: Optional[bool] = False
    model_name: Optional[str] = "default"

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str
    model_used: str
    processing_time: float

# 轻量级文档处理器
class LiteDocumentProcessor:
    def __init__(self):
        self.supported_formats = ['.pdf', '.txt', '.doc', '.docx']
        if EXCEL_AVAILABLE:
            self.supported_formats.extend(['.xlsx', '.xls'])
        
    def process_document(self, file_path: str) -> List[Dict[str, Any]]:
        """处理文档并返回结构化数据"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        suffix = file_path.suffix.lower()
        
        if suffix == '.pdf':
            return self._process_pdf(file_path)
        elif suffix == '.txt':
            return self._process_txt(file_path)
        elif suffix in ['.doc', '.docx']:
            return self._process_docx(file_path)
        elif suffix in ['.xlsx', '.xls'] and EXCEL_AVAILABLE:
            return self._process_excel(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {suffix}")
    
    def _process_pdf(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        chunks = []
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text.strip():
                            cleaned_text = self._clean_text(text)
                            page_chunks = self._split_text(cleaned_text)
                            
                            for chunk_idx, chunk in enumerate(page_chunks):
                                chunks.append({
                                    'content': chunk,
                                    'metadata': {
                                        'source': str(file_path),
                                        'page': page_num + 1,
                                        'chunk_id': f"{page_num}_{chunk_idx}",
                                        'type': 'pdf'
                                    }
                                })
                    except Exception as e:
                        logger.warning(f"处理PDF第{page_num + 1}页失败: {e}")
                        continue
                        
        except Exception as e:
            raise ValueError(f"PDF处理失败: {e}")
        
        return chunks
    
    def _process_txt(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理文本文件"""
        chunks = []
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                    
                cleaned_text = self._clean_text(content)
                text_chunks = self._split_text(cleaned_text)
                
                for chunk_idx, chunk in enumerate(text_chunks):
                    chunks.append({
                        'content': chunk,
                        'metadata': {
                            'source': str(file_path),
                            'chunk_id': f"txt_{chunk_idx}",
                            'type': 'txt'
                        }
                    })
                break
                
            except UnicodeDecodeError:
                continue
        
        if not chunks:
            raise ValueError("无法识别文件编码")
        
        return chunks
    
    def _process_docx(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Word文档"""
        chunks = []
        
        try:
            doc = docx.Document(file_path)
            
            # 处理段落
            for para_idx, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    chunks.append({
                        'content': self._clean_text(paragraph.text),
                        'metadata': {
                            'source': str(file_path),
                            'paragraph': para_idx + 1,
                            'type': 'docx'
                        }
                    })
                    
        except Exception as e:
            raise ValueError(f"DOCX处理失败: {e}")
        
        return chunks
    
    def _process_excel(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理Excel文件（如果可用）"""
        if not EXCEL_AVAILABLE:
            raise ValueError("Excel支持不可用")
            
        chunks = []
        
        try:
            workbook = load_workbook(file_path, read_only=True)
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                
                sheet_data = []
                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else "" for cell in row]
                    if any(cell.strip() for cell in row_data):
                        sheet_data.append(" | ".join(row_data))
                
                if sheet_data:
                    content = "\n".join(sheet_data)
                    chunks.append({
                        'content': self._clean_text(content),
                        'metadata': {
                            'source': str(file_path),
                            'sheet': sheet_name,
                            'type': 'excel'
                        }
                    })
                    
        except Exception as e:
            raise ValueError(f"Excel处理失败: {e}")
        
        return chunks
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,;:!?()[\]{}""''—–-]', '', text)
        return text.strip()
    
    def _split_text(self, text: str) -> List[str]:
        """分割文本为块"""
        if len(text) <= config.CHUNK_SIZE:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + config.CHUNK_SIZE
            
            if end >= len(text):
                chunks.append(text[start:])
                break
            
            # 寻找合适的分割点
            split_point = end
            for i in range(end, start + config.CHUNK_SIZE - config.CHUNK_OVERLAP, -1):
                if text[i] in '。！？\n':
                    split_point = i + 1
                    break
            
            chunks.append(text[start:split_point])
            start = split_point - config.CHUNK_OVERLAP
        
        return [chunk.strip() for chunk in chunks if chunk.strip()]

# 在线资源爬取器
class OnlineResourceCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.cache_dir = config.CACHE_DIR
        
    async def crawl_medical_resources(self, query: str) -> List[Dict[str, Any]]:
        """爬取在线医学资源"""
        results = []
        
        # 特别处理医宗金鉴网站
        try:
            yizongjinjian_results = await self._crawl_yizongjinjian(query)
            results.extend(yizongjinjian_results)
        except Exception as e:
            logger.warning(f"爬取医宗金鉴失败: {e}")
        
        return results[:config.TOP_K]
    
    async def _crawl_yizongjinjian(self, query: str) -> List[Dict[str, Any]]:
        """专门爬取医宗金鉴网站"""
        results = []
        base_url = "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
        
        # 检查缓存
        cache_key = hashlib.md5(f"yizongjinjian_{query}".encode()).hexdigest()
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        if cache_file.exists() and (time.time() - cache_file.stat().st_mtime) < 3600:
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        try:
            response = self.session.get(base_url, timeout=10)
            if response.status_code != 200:
                return results
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找相关链接
            query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
            
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if not text or len(text) < 2:
                    continue
                
                # 检查相关性
                text_keywords = set(re.findall(r'[\u4e00-\u9fff]+', text))
                if query_keywords.intersection(text_keywords):
                    # 构建完整URL
                    if href.startswith('/'):
                        full_url = base_url.rstrip('/') + href
                    elif href.startswith('./'):
                        full_url = base_url.rstrip('/') + '/' + href[2:]
                    elif not href.startswith('http'):
                        full_url = base_url.rstrip('/') + '/' + href
                    else:
                        full_url = href
                    
                    # 获取页面内容
                    try:
                        page_response = self.session.get(full_url, timeout=8)
                        if page_response.status_code == 200:
                            page_soup = BeautifulSoup(page_response.content, 'html.parser')
                            content = page_soup.get_text(strip=True)
                            
                            if len(content) > 100 and self._is_relevant_content(content, query):
                                results.append({
                                    'source': f"医宗金鉴 - {text}",
                                    'content': content[:800],
                                    'url': full_url,
                                    'relevance': self._calculate_relevance(query, content),
                                    'type': 'online'
                                })
                        
                        time.sleep(0.5)  # 避免请求过快
                        
                    except Exception as e:
                        logger.warning(f"处理页面失败 {full_url}: {e}")
                        continue
            
            # 缓存结果
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"爬取医宗金鉴失败: {e}")
        
        return results
    
    def _is_relevant_content(self, content: str, query: str) -> bool:
        """判断内容是否相关"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))
        
        intersection = query_keywords.intersection(content_keywords)
        return len(intersection) >= max(1, len(query_keywords) * 0.2)
    
    def _calculate_relevance(self, query: str, content: str) -> float:
        """计算相关性分数"""
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))
        content_keywords = set(re.findall(r'[\u4e00-\u9fff]+', content))
        
        if not query_keywords:
            return 0.0
        
        intersection = query_keywords.intersection(content_keywords)
        return len(intersection) / len(query_keywords)

# 轻量级向量检索系统
class LiteVectorSystem:
    def __init__(self):
        self.models = {}
        self.vector_stores = {}
        self.document_chunks = []
        self.current_model = "default"

        if ADVANCED_ML_AVAILABLE:
            self._initialize_models()
        else:
            logger.warning("使用基础检索功能")

    def _initialize_models(self):
        """初始化模型"""
        try:
            # 只加载一个默认模型以节省资源
            logger.info("加载默认模型...")
            model = SentenceTransformer(config.MODELS["default"])
            self.models["default"] = model
            logger.info("✅ 默认模型加载成功")
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")

    def add_documents(self, chunks: List[Dict[str, Any]]) -> None:
        """添加文档到向量库"""
        self.document_chunks.extend(chunks)

        if not ADVANCED_ML_AVAILABLE or "default" not in self.models:
            return

        try:
            texts = [chunk['content'] for chunk in chunks]
            model = self.models["default"]

            logger.info("生成文档向量...")
            vectors = model.encode(texts, show_progress_bar=False)

            if "default" not in self.vector_stores:
                dimension = vectors.shape[1]
                index = faiss.IndexFlatIP(dimension)
                self.vector_stores["default"] = {
                    'index': index,
                    'chunks': []
                }

            self.vector_stores["default"]['index'].add(vectors.astype('float32'))
            self.vector_stores["default"]['chunks'].extend(chunks)

            logger.info("✅ 向量添加完成")

        except Exception as e:
            logger.error(f"❌ 向量生成失败: {e}")

    def search(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关文档"""
        if top_k is None:
            top_k = config.TOP_K

        if not ADVANCED_ML_AVAILABLE or "default" not in self.models:
            return self._basic_search(query, top_k)

        try:
            model = self.models["default"]
            query_vector = model.encode([query])

            if "default" in self.vector_stores:
                vector_store = self.vector_stores["default"]
                scores, indices = vector_store['index'].search(
                    query_vector.astype('float32'), min(top_k * 2, len(vector_store['chunks']))
                )

                results = []
                for score, idx in zip(scores[0], indices[0]):
                    if idx < len(vector_store['chunks']) and score > config.SIMILARITY_THRESHOLD:
                        chunk = vector_store['chunks'][idx].copy()
                        chunk['score'] = float(score)
                        results.append(chunk)

                return sorted(results, key=lambda x: x['score'], reverse=True)[:top_k]

        except Exception as e:
            logger.error(f"向量搜索失败: {e}")

        return self._basic_search(query, top_k)

    def _basic_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """基础关键词搜索"""
        if JIEBA_AVAILABLE:
            query_words = set(jieba.cut(query))
        else:
            query_words = set(re.findall(r'[\u4e00-\u9fff]+', query))

        results = []
        for chunk in self.document_chunks:
            content = chunk['content']
            if JIEBA_AVAILABLE:
                content_words = set(jieba.cut(content))
            else:
                content_words = set(re.findall(r'[\u4e00-\u9fff]+', content))

            intersection = query_words.intersection(content_words)
            if intersection:
                score = len(intersection) / len(query_words.union(content_words))
                if score > config.SIMILARITY_THRESHOLD:
                    chunk_copy = chunk.copy()
                    chunk_copy['score'] = score
                    results.append(chunk_copy)

        return sorted(results, key=lambda x: x['score'], reverse=True)[:top_k]

    def save_vector_database(self) -> None:
        """保存向量数据库"""
        try:
            chunks_file = config.VECTOR_DB_DIR / "document_chunks.pkl"
            with open(chunks_file, 'wb') as f:
                pickle.dump(self.document_chunks, f)

            if ADVANCED_ML_AVAILABLE and "default" in self.vector_stores:
                index_file = config.VECTOR_DB_DIR / "faiss_index_default.bin"
                faiss.write_index(self.vector_stores["default"]['index'], str(index_file))

                chunks_file = config.VECTOR_DB_DIR / "chunks_default.pkl"
                with open(chunks_file, 'wb') as f:
                    pickle.dump(self.vector_stores["default"]['chunks'], f)

            logger.info("✅ 向量数据库保存成功")

        except Exception as e:
            logger.error(f"❌ 向量数据库保存失败: {e}")

    def load_vector_database(self) -> bool:
        """加载向量数据库"""
        try:
            chunks_file = config.VECTOR_DB_DIR / "document_chunks.pkl"
            if chunks_file.exists():
                with open(chunks_file, 'rb') as f:
                    self.document_chunks = pickle.load(f)
                logger.info(f"✅ 加载了 {len(self.document_chunks)} 个文档块")

            if ADVANCED_ML_AVAILABLE and "default" in self.models:
                index_file = config.VECTOR_DB_DIR / "faiss_index_default.bin"
                chunks_file = config.VECTOR_DB_DIR / "chunks_default.pkl"

                if index_file.exists() and chunks_file.exists():
                    try:
                        index = faiss.read_index(str(index_file))
                        with open(chunks_file, 'rb') as f:
                            chunks = pickle.load(f)

                        self.vector_stores["default"] = {
                            'index': index,
                            'chunks': chunks
                        }
                        logger.info("✅ 向量库加载成功")
                    except Exception as e:
                        logger.warning(f"⚠️ 向量库加载失败: {e}")

            return True

        except Exception as e:
            logger.error(f"❌ 向量数据库加载失败: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_chunks': len(self.document_chunks),
            'current_model': self.current_model,
            'available_models': list(self.models.keys()),
            'advanced_ml': ADVANCED_ML_AVAILABLE
        }

# 智能回答生成器
class IntelligentResponseGenerator:
    def __init__(self, vector_system: LiteVectorSystem, crawler: OnlineResourceCrawler):
        self.vector_system = vector_system
        self.crawler = crawler

    async def generate_response(self, query: str) -> Tuple[str, List[Dict[str, Any]], float]:
        """生成智能回答"""
        start_time = time.time()

        # 1. 向量检索本地文档
        local_results = self.vector_system.search(query, top_k=3)

        # 2. 在线资源检索
        online_results = await self.crawler.crawl_medical_resources(query)

        # 3. 合并结果
        all_sources = self._merge_sources(local_results, online_results)

        # 4. 生成回答
        response = self._generate_comprehensive_response(query, all_sources)

        processing_time = time.time() - start_time

        return response, all_sources, processing_time

    def _merge_sources(self, local_results: List[Dict], online_results: List[Dict]) -> List[Dict[str, Any]]:
        """合并本地和在线结果"""
        all_sources = []

        # 添加本地结果
        for result in local_results:
            all_sources.append({
                'source': result['metadata']['source'],
                'content': result['content'][:500],
                'score': result.get('score', 0),
                'type': 'local',
                'metadata': result['metadata']
            })

        # 添加在线结果
        for result in online_results:
            all_sources.append({
                'source': result['source'],
                'content': result['content'][:500],
                'score': result.get('relevance', 0),
                'type': 'online',
                'url': result.get('url', ''),
                'metadata': {'type': 'online'}
            })

        return sorted(all_sources, key=lambda x: x['score'], reverse=True)[:config.TOP_K]

    def _generate_comprehensive_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成综合回答"""
        if not sources:
            return self._generate_fallback_response(query)

        response_parts = []
        response_parts.append(f"## 🔍 关于「{query}」的中医知识")

        if sources:
            response_parts.append("\n### 📚 专业解答")

            # 合并相关内容
            combined_content = self._combine_source_content(sources, query)
            response_parts.append(combined_content)

            # 添加来源信息
            response_parts.append("\n### 📖 参考来源")
            for i, source in enumerate(sources[:3], 1):
                source_type = "📁 本地文档" if source['type'] == 'local' else "🌐 在线资源"
                response_parts.append(f"{i}. {source_type}: {source['source']} (相关度: {source['score']:.2f})")

        # 添加提醒
        response_parts.append("\n### ⚠️ 重要提醒")
        response_parts.append("本回答基于已上传文档和在线医学资源，仅供中医文化学习参考。如有健康问题，请咨询专业中医师。")

        return "\n".join(response_parts)

    def _combine_source_content(self, sources: List[Dict[str, Any]], query: str) -> str:
        """合并来源内容"""
        if not sources:
            return "暂未找到相关资料。"

        key_points = []
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

        for source in sources:
            content = source['content']
            sentences = re.split(r'[。！？\n]', content)

            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:
                    sentence_keywords = set(re.findall(r'[\u4e00-\u9fff]+', sentence))
                    if query_keywords.intersection(sentence_keywords):
                        key_points.append(sentence)
                        if len(key_points) >= 5:
                            break

            if len(key_points) >= 5:
                break

        if key_points:
            return "根据相关资料：\n\n" + "\n\n".join(f"• {point}" for point in key_points[:5])
        else:
            return "根据检索到的资料，找到了相关信息，但需要进一步分析。"

    def _generate_fallback_response(self, query: str) -> str:
        """生成备用回答"""
        return f"""## 🤖 智能助手回复

感谢您的提问：「{query}」

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库
- 检查网络连接，确保能够访问在线资源

### 📚 系统功能：
- 🔍 **智能检索**: 支持本地文档和在线资源
- 📁 **多格式**: 支持PDF、Word等文档
- 🎤 **语音交互**: 支持语音输入和输出
- 🌐 **在线爬取**: 自动获取医宗金鉴等资源

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。"""

# 会话管理器
class SessionManager:
    def __init__(self):
        self.sessions = {}

    def create_session(self, session_id: str = None) -> str:
        """创建新会话"""
        if session_id is None:
            session_id = str(uuid.uuid4())

        self.sessions[session_id] = {
            'id': session_id,
            'created_at': datetime.now().isoformat(),
            'messages': []
        }

        return session_id

    def add_message(self, session_id: str, message_type: str, content: str,
                   sources: List[Dict] = None, metadata: Dict = None):
        """添加消息到会话"""
        if session_id not in self.sessions:
            self.create_session(session_id)

        message = {
            'type': message_type,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'sources': sources or [],
            'metadata': metadata or {}
        }

        self.sessions[session_id]['messages'].append(message)
        self._save_session(session_id)

    def get_session(self, session_id: str) -> Dict:
        """获取会话"""
        if session_id in self.sessions:
            return self.sessions[session_id]
        return self._load_session(session_id)

    def export_session(self, session_id: str, format: str = 'json') -> str:
        """导出会话"""
        session = self.get_session(session_id)
        if not session:
            raise ValueError(f"会话 {session_id} 不存在")

        if format == 'json':
            return json.dumps(session, ensure_ascii=False, indent=2)
        elif format == 'txt':
            return self._export_as_text(session)
        elif format == 'md':
            return self._export_as_markdown(session)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def _save_session(self, session_id: str):
        """保存会话到文件"""
        try:
            session_file = config.SESSIONS_DIR / f"{session_id}.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(self.sessions[session_id], f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

    def _load_session(self, session_id: str) -> Dict:
        """从文件加载会话"""
        try:
            session_file = config.SESSIONS_DIR / f"{session_id}.json"
            if session_file.exists():
                with open(session_file, 'r', encoding='utf-8') as f:
                    session = json.load(f)
                    self.sessions[session_id] = session
                    return session
        except Exception as e:
            logger.error(f"加载会话失败: {e}")
        return {}

    def _export_as_text(self, session: Dict) -> str:
        """导出为文本格式"""
        lines = [
            f"中医智能助手对话记录",
            f"会话ID: {session['id']}",
            f"创建时间: {session['created_at']}",
            "=" * 50
        ]

        for msg in session['messages']:
            timestamp = msg['timestamp']
            msg_type = "用户" if msg['type'] == 'user' else "助手"
            content = msg['content']

            lines.append(f"\n[{timestamp}] {msg_type}:")
            lines.append(content)

        return "\n".join(lines)

    def _export_as_markdown(self, session: Dict) -> str:
        """导出为Markdown格式"""
        lines = [
            f"# 中医智能助手对话记录",
            f"**会话ID**: {session['id']}",
            f"**创建时间**: {session['created_at']}",
            "---"
        ]

        for msg in session['messages']:
            timestamp = msg['timestamp']
            msg_type = "👤 用户" if msg['type'] == 'user' else "🤖 助手"
            content = msg['content']

            lines.append(f"\n## {msg_type}")
            lines.append(f"*时间: {timestamp}*")
            lines.append(f"\n{content}")

        return "\n".join(lines)

# 全局实例
doc_processor = LiteDocumentProcessor()
crawler = OnlineResourceCrawler()
vector_system = LiteVectorSystem()
response_generator = IntelligentResponseGenerator(vector_system, crawler)
session_manager = SessionManager()

# 启动时初始化
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("🚀 启动终极版中医RAG系统 - 轻量版...")

    # 加载现有向量数据库
    if vector_system.load_vector_database():
        logger.info("✅ 向量数据库加载成功")
    else:
        logger.info("⚠️ 未找到现有向量数据库")

    # 检查上传目录中的文档
    uploaded_files = list(config.UPLOAD_DIR.glob("*"))
    if uploaded_files:
        logger.info(f"📁 发现 {len(uploaded_files)} 个已上传文档，开始处理...")

        for file_path in uploaded_files:
            if file_path.suffix.lower() in doc_processor.supported_formats:
                try:
                    chunks = doc_processor.process_document(str(file_path))
                    vector_system.add_documents(chunks)
                    logger.info(f"✅ 处理文档: {file_path.name}")
                except Exception as e:
                    logger.error(f"❌ 处理文档失败 {file_path.name}: {e}")

        vector_system.save_vector_database()

    logger.info("🎉 系统初始化完成！")

# API路由
@app.get("/")
async def read_root():
    """返回主页"""
    return HTMLResponse(content=get_lite_html())

@app.get("/api/health")
async def health_check():
    """健康检查"""
    stats = vector_system.get_stats()

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "3.0.0-lite",
        "features": [
            "PDF文档检索",
            "在线医学爬取",
            "多文档格式支持",
            "语音交互",
            "会话导出"
        ],
        "stats": stats,
        "advanced_ml": ADVANCED_ML_AVAILABLE,
        "excel_support": EXCEL_AVAILABLE,
        "jieba_support": JIEBA_AVAILABLE
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """智能聊天接口"""
    try:
        # 创建或获取会话
        session_id = chat_message.session_id or session_manager.create_session()

        # 记录用户消息
        session_manager.add_message(
            session_id, 'user', chat_message.message,
            metadata={'use_voice': chat_message.use_voice}
        )

        # 生成回答
        response, sources, processing_time = await response_generator.generate_response(
            chat_message.message
        )

        # 记录助手回答
        session_manager.add_message(
            session_id, 'assistant', response, sources,
            metadata={'processing_time': processing_time}
        )

        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat(),
            model_used="default",
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"聊天处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.post("/api/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    results = []

    for file in files:
        try:
            # 检查文件大小
            if file.size > config.MAX_FILE_SIZE:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": f"文件过大 ({file.size / 1024 / 1024:.1f}MB > 50MB)"
                })
                continue

            # 检查文件类型
            file_suffix = Path(file.filename).suffix.lower()
            if file_suffix not in doc_processor.supported_formats:
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": f"不支持的文件类型: {file_suffix}"
                })
                continue

            # 保存文件
            file_path = config.UPLOAD_DIR / file.filename
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)

            # 处理文档
            chunks = doc_processor.process_document(str(file_path))

            # 添加到向量系统
            vector_system.add_documents(chunks)

            results.append({
                "filename": file.filename,
                "size": len(content),
                "chunks": len(chunks),
                "status": "success"
            })

            logger.info(f"✅ 文档上传成功: {file.filename} ({len(chunks)} 块)")

        except Exception as e:
            logger.error(f"❌ 文档上传失败 {file.filename}: {e}")
            results.append({
                "filename": file.filename,
                "status": "error",
                "error": str(e)
            })

    # 保存向量数据库
    try:
        vector_system.save_vector_database()
        logger.info("✅ 向量数据库更新完成")
    except Exception as e:
        logger.error(f"❌ 向量数据库保存失败: {e}")

    return {
        "results": results,
        "total_files": len(files),
        "success_count": len([r for r in results if r["status"] == "success"])
    }

@app.get("/api/sessions/{session_id}")
async def get_session(session_id: str):
    """获取会话"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    return session

@app.get("/api/sessions/{session_id}/export")
async def export_session(session_id: str, format: str = "json"):
    """导出会话"""
    try:
        content = session_manager.export_session(session_id, format)

        if format == "json":
            filename = f"session_{session_id}.json"
            media_type = "application/json"
        elif format == "txt":
            filename = f"session_{session_id}.txt"
            media_type = "text/plain"
        elif format == "md":
            filename = f"session_{session_id}.md"
            media_type = "text/markdown"
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

        # 保存临时文件
        temp_file = config.CACHE_DIR / filename
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)

        return FileResponse(
            path=str(temp_file),
            filename=filename,
            media_type=media_type
        )

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/documents")
async def list_documents():
    """列出已上传的文档"""
    documents = []

    for file_path in config.UPLOAD_DIR.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in doc_processor.supported_formats:
            stat = file_path.stat()
            documents.append({
                "filename": file_path.name,
                "size": stat.st_size,
                "type": file_path.suffix.lower(),
                "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "status": "processed"
            })

    stats = vector_system.get_stats()

    return {
        "documents": documents,
        "total_documents": len(documents),
        "total_chunks": stats['total_chunks']
    }

@app.get("/api/stats")
async def get_system_stats():
    """获取系统统计信息"""
    stats = vector_system.get_stats()

    # 计算文档统计
    doc_stats = {}
    for file_path in config.UPLOAD_DIR.iterdir():
        if file_path.is_file():
            ext = file_path.suffix.lower()
            doc_stats[ext] = doc_stats.get(ext, 0) + 1

    # 计算会话统计
    session_count = len(list(config.SESSIONS_DIR.glob("*.json")))

    return {
        "system": {
            "version": "3.0.0-lite",
            "advanced_ml": ADVANCED_ML_AVAILABLE,
            "excel_support": EXCEL_AVAILABLE,
            "jieba_support": JIEBA_AVAILABLE
        },
        "documents": {
            "total_files": len(list(config.UPLOAD_DIR.iterdir())),
            "by_type": doc_stats,
            "total_chunks": stats['total_chunks']
        },
        "models": {
            "available": stats['available_models'],
            "current": stats['current_model']
        },
        "sessions": {
            "total": session_count
        }
    }

# 前端HTML
def get_lite_html() -> str:
    """获取轻量版前端HTML"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 终极版中医RAG系统 - 轻量版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; overflow: hidden;
        }
        .main-container {
            height: 100vh; display: flex; flex-direction: column;
            max-width: 1400px; margin: 0 auto; background: white;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 15px 20px; display: flex;
            justify-content: space-between; align-items: center;
        }
        .header h1 { font-size: 20px; }
        .header-info {
            display: flex; gap: 15px; align-items: center; font-size: 12px;
        }
        .badge {
            background: rgba(255,255,255,0.2); padding: 5px 10px;
            border-radius: 15px;
        }
        .main-content { flex: 1; display: flex; overflow: hidden; }
        .sidebar {
            width: 280px; background: #f8f9fa; border-right: 1px solid #e9ecef;
            display: flex; flex-direction: column; overflow-y: auto;
        }
        .chat-area { flex: 1; display: flex; flex-direction: column; }
        .messages-container {
            flex: 1; overflow-y: auto; padding: 20px; background: #fafafa;
        }
        .message { margin-bottom: 20px; display: flex; align-items: flex-start; }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 70%; padding: 12px 16px; border-radius: 18px;
            word-wrap: break-word; position: relative;
        }
        .message.user .message-content {
            background: #2E8B57; color: white; border-bottom-right-radius: 4px;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #e9ecef; border-bottom-left-radius: 4px;
        }
        .message-avatar {
            width: 40px; height: 40px; border-radius: 50%; margin: 0 10px;
            display: flex; align-items: center; justify-content: center;
            font-size: 18px;
        }
        .user-avatar { background: #2E8B57; color: white; }
        .assistant-avatar { background: #667eea; color: white; }
        .voice-controls {
            position: absolute; top: 5px; right: 5px; display: flex; gap: 5px;
        }
        .voice-btn {
            width: 24px; height: 24px; border: none; border-radius: 50%;
            background: #f0f0f0; cursor: pointer; display: flex;
            align-items: center; justify-content: center; font-size: 12px;
        }
        .voice-btn:hover { background: #e0e0e0; }
        .input-container {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; align-items: flex-end; }
        .input-textarea { flex: 1; position: relative; }
        .input-textarea textarea {
            width: 100%; padding: 12px 50px 12px 12px; border: 1px solid #ddd;
            border-radius: 12px; resize: none; font-size: 16px;
        }
        .voice-input-btn {
            position: absolute; right: 10px; top: 50%; transform: translateY(-50%);
            width: 36px; height: 36px; border: none; border-radius: 50%;
            background: #2E8B57; color: white; cursor: pointer;
            display: flex; align-items: center; justify-content: center;
            font-size: 16px;
        }
        .voice-input-btn.recording {
            background: #dc3545; animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.1); }
            100% { transform: translateY(-50%) scale(1); }
        }
        .send-btn {
            padding: 12px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 12px; cursor: pointer; font-size: 16px;
        }
        .send-btn:hover { background: #228B22; }
        .send-btn:disabled { background: #ccc; cursor: not-allowed; }
        .sidebar-section {
            padding: 15px; border-bottom: 1px solid #e9ecef;
        }
        .sidebar-section h3 { font-size: 14px; color: #666; margin-bottom: 10px; }
        .quick-btn, .action-btn {
            width: 100%; margin-bottom: 8px; padding: 10px; background: white;
            border: 1px solid #ddd; border-radius: 8px; cursor: pointer;
            text-align: left; font-size: 14px;
        }
        .quick-btn:hover, .action-btn:hover { background: #f0f0f0; }
        .upload-area {
            border: 2px dashed #ddd; border-radius: 8px; padding: 15px;
            text-align: center; margin-bottom: 10px; cursor: pointer;
        }
        .upload-area:hover { border-color: #2E8B57; background: #f0f8f0; }
        .file-input { display: none; }
        .typing { color: #666; font-style: italic; }
        .notification {
            position: fixed; top: 20px; right: 20px; padding: 15px 20px;
            background: #2E8B57; color: white; border-radius: 8px;
            transform: translateX(400px); transition: transform 0.3s; z-index: 1000;
        }
        .notification.show { transform: translateX(0); }
        .notification.error { background: #dc3545; }
        .notification.warning { background: #ffc107; color: #000; }
        @media (max-width: 768px) {
            .sidebar { display: none; }
            .message-content { max-width: 85%; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部 -->
        <div class="header">
            <h1>🏥 终极版中医RAG系统 - 轻量版</h1>
            <div class="header-info">
                <div class="badge" id="statsBadge">📊 加载中...</div>
                <div class="badge">🎤 语音就绪</div>
                <div class="badge">🌐 在线爬取</div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 快捷查询 -->
                <div class="sidebar-section">
                    <h3>💡 快捷查询</h3>
                    <button class="quick-btn" onclick="sendQuickMessage('脾胃虚弱的症状和调理方法')">
                        🌿 脾胃调理
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('肾阳虚和肾阴虚的区别及治疗')">
                        🫖 肾虚调养
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('失眠多梦的中医治疗方案')">
                        😴 失眠治疗
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('高血压的中医调理方法')">
                        💓 血压调理
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('栀子甘草豉汤的功效和应用')">
                        💊 经典方剂
                    </button>
                </div>

                <!-- 文档上传 -->
                <div class="sidebar-section">
                    <h3>📁 文档上传</h3>
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div>📄 点击上传文档</div>
                        <small>支持PDF、Word、TXT</small>
                    </div>
                    <input type="file" id="fileInput" class="file-input" multiple
                           accept=".pdf,.doc,.docx,.txt" onchange="uploadFiles()">
                    <button class="action-btn" onclick="viewDocuments()">
                        📚 查看已上传文档
                    </button>
                </div>

                <!-- 会话管理 -->
                <div class="sidebar-section">
                    <h3>💬 会话管理</h3>
                    <button class="action-btn" onclick="exportSession('json')">
                        📄 导出为JSON
                    </button>
                    <button class="action-btn" onclick="exportSession('md')">
                        📝 导出为Markdown
                    </button>
                    <button class="action-btn" onclick="clearSession()">
                        🗑️ 清空会话
                    </button>
                </div>

                <!-- 系统信息 -->
                <div class="sidebar-section">
                    <h3>📊 系统信息</h3>
                    <div id="systemInfo" style="font-size: 12px; color: #666;">
                        加载中...
                    </div>
                </div>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area">
                <!-- 消息容器 -->
                <div class="messages-container" id="messagesContainer">
                    <div class="message assistant">
                        <div class="message-avatar assistant-avatar">🤖</div>
                        <div class="message-content">
                            <strong>🤖 终极助手:</strong> 您好！我是终极版中医RAG系统轻量版。我具备以下功能：<br><br>
                            🔍 <strong>智能检索</strong>: 搜索您上传的PDF文档和医宗金鉴等在线资源<br>
                            📁 <strong>多格式支持</strong>: PDF、Word、TXT等文档格式<br>
                            🎤 <strong>语音交互</strong>: 语音输入和语音输出功能<br>
                            💾 <strong>会话导出</strong>: 支持JSON、Markdown格式导出<br>
                            🌐 <strong>在线爬取</strong>: 自动获取医宗金鉴等权威医学资料<br><br>
                            请问有什么可以帮助您的吗？
                            <div class="voice-controls">
                                <button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-row">
                        <div class="input-textarea">
                            <textarea id="messageInput" rows="2" placeholder="请输入您的中医问题，或点击麦克风使用语音输入..." onkeydown="handleKeyDown(event)"></textarea>
                            <button class="voice-input-btn" id="voiceInputBtn" onclick="toggleVoiceRecording()" title="语音输入">
                                🎤
                            </button>
                        </div>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div class="notification" id="notification">
        <div id="notificationText"></div>
    </div>

    <script>
        // 全局变量
        let currentSessionId = null;
        let isTyping = false;
        let recognition = null;
        let synthesis = null;
        let isRecording = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            initializeVoice();
            loadSystemStats();
        });

        // 系统初始化
        async function initializeSystem() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                updateStatsBadge(data.stats);
                showNotification('✅ 系统初始化完成', 'success');

            } catch (error) {
                showNotification('❌ 系统初始化失败: ' + error.message, 'error');
            }
        }

        // 语音功能初始化
        function initializeVoice() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    isRecording = true;
                    updateVoiceButton();
                    showNotification('🎤 正在录音，请说话...', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    showNotification('🎤 语音识别完成: ' + transcript, 'success');
                };

                recognition.onerror = function(event) {
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    updateVoiceButton();
                };
            }

            if ('speechSynthesis' in window) {
                synthesis = window.speechSynthesis;
            }
        }

        // 加载系统统计
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();

                const infoDiv = document.getElementById('systemInfo');
                infoDiv.innerHTML = `
                    <div><strong>文档:</strong> ${data.documents.total_files} 个</div>
                    <div><strong>文本块:</strong> ${data.documents.total_chunks} 个</div>
                    <div><strong>会话数:</strong> ${data.sessions.total} 个</div>
                    <div><strong>ML支持:</strong> ${data.system.advanced_ml ? '✅' : '❌'}</div>
                    <div><strong>Excel支持:</strong> ${data.system.excel_support ? '✅' : '❌'}</div>
                `;

            } catch (error) {
                console.error('加载统计失败:', error);
            }
        }

        // 更新统计徽章
        function updateStatsBadge(stats) {
            const badge = document.getElementById('statsBadge');
            badge.textContent = `📊 ${stats.total_chunks} 块文档`;
        }

        // 语音录音切换
        function toggleVoiceRecording() {
            if (!recognition) {
                showNotification('⚠️ 浏览器不支持语音识别', 'warning');
                return;
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 更新语音按钮
        function updateVoiceButton() {
            const btn = document.getElementById('voiceInputBtn');
            if (isRecording) {
                btn.classList.add('recording');
                btn.textContent = '🛑';
            } else {
                btn.classList.remove('recording');
                btn.textContent = '🎤';
            }
        }

        // 朗读消息
        function speakMessage(button) {
            if (!synthesis) {
                showNotification('⚠️ 浏览器不支持语音合成', 'warning');
                return;
            }

            const messageContent = button.closest('.message-content');
            const text = messageContent.textContent.replace(/🤖 终极助手:|👤 您:/g, '').trim();

            synthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 1.0;

            const voices = synthesis.getVoices();
            const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
            if (chineseVoice) {
                utterance.voice = chineseVoice;
            }

            synthesis.speak(utterance);
            showNotification('🔊 开始朗读', 'info');
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在智能检索中...', 'typing');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId,
                        use_voice: false
                    })
                });

                const data = await response.json();

                // 更新会话ID
                currentSessionId = data.session_id;

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 显示处理时间
                showNotification(`⚡ 处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
                showNotification('❌ 请求失败: ' + error.message, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${type}-avatar`;
            avatarDiv.textContent = type === 'user' ? '👤' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const prefix = type === 'user' ? '👤 您:' : '🤖 终极助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\\n/g, '<br>')}`;

            // 为助手消息添加语音控制
            if (type === 'assistant' && !className.includes('typing')) {
                const voiceControls = document.createElement('div');
                voiceControls.className = 'voice-controls';
                voiceControls.innerHTML = '<button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>';
                contentDiv.appendChild(voiceControls);
            }

            if (type === 'user') {
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(avatarDiv);
            } else {
                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(contentDiv);
            }

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }

        // 文件上传
        function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;

            if (files.length === 0) {
                showNotification('⚠️ 请选择文件', 'warning');
                return;
            }

            handleFileUpload(files);
        }

        // 处理文件上传
        async function handleFileUpload(files) {
            const formData = new FormData();
            for (let file of files) {
                formData.append('files', file);
            }

            try {
                showNotification('📁 正在上传和处理文档...', 'info');

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                let successCount = data.success_count;
                let totalCount = data.total_files;

                if (successCount === totalCount) {
                    showNotification(`✅ ${successCount} 个文档上传成功！`, 'success');
                } else {
                    showNotification(`⚠️ ${successCount}/${totalCount} 个文档上传成功`, 'warning');
                }

                // 刷新统计信息
                loadSystemStats();

                // 清空文件输入
                document.getElementById('fileInput').value = '';

            } catch (error) {
                showNotification('❌ 文档上传失败: ' + error.message, 'error');
            }
        }

        // 查看文档
        async function viewDocuments() {
            try {
                const response = await fetch('/api/documents');
                const data = await response.json();

                let message = `📚 已上传 ${data.total_documents} 个文档:\\n\\n`;
                data.documents.forEach((doc, index) => {
                    message += `${index + 1}. ${doc.filename} (${(doc.size / 1024).toFixed(1)}KB)\\n`;
                });

                alert(message);

            } catch (error) {
                showNotification('❌ 获取文档列表失败: ' + error.message, 'error');
            }
        }

        // 导出会话
        async function exportSession(format) {
            if (!currentSessionId) {
                showNotification('⚠️ 当前没有活跃会话', 'warning');
                return;
            }

            try {
                const response = await fetch(`/api/sessions/${currentSessionId}/export?format=${format}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `session_${currentSessionId}.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showNotification(`✅ 会话已导出为 ${format.toUpperCase()} 格式`, 'success');
                } else {
                    throw new Error('导出失败');
                }

            } catch (error) {
                showNotification('❌ 导出失败: ' + error.message, 'error');
            }
        }

        // 清空会话
        function clearSession() {
            if (confirm('确定要清空当前会话吗？')) {
                document.getElementById('messagesContainer').innerHTML = '';
                currentSessionId = null;
                showNotification('✅ 会话已清空', 'success');

                // 添加欢迎消息
                addMessage('assistant', '您好！我是终极版中医RAG系统轻量版。请问有什么可以帮助您的吗？');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');

            text.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
"""

if __name__ == "__main__":
    print("🚀 启动终极版中医RAG系统 - 轻量版...")
    print("🎯 功能特色:")
    print("  🔍 智能检索: PDF文档 + 医宗金鉴在线资源")
    print("  📁 多格式: PDF/Word/TXT文档支持")
    print("  🎤 语音交互: 语音输入和输出")
    print("  💾 会话导出: JSON/Markdown格式")
    print("  🌐 在线爬取: 自动获取权威医学资料")
    print("  ⚡ 轻量级: 减少依赖，更易部署")
    print()
    print("🌐 访问地址: http://localhost:8005")
    print("📚 API文档: http://localhost:8005/docs")
    print("🔍 健康检查: http://localhost:8005/api/health")
    print()
    print("💡 使用提示:")
    print("  - 上传PDF文档扩充知识库")
    print("  - 系统会自动爬取医宗金鉴等在线资源")
    print("  - 使用语音输入提高交互体验")
    print("  - 导出会话记录保存重要对话")

    uvicorn.run(
        "ultimate_tcm_lite:app",
        host="0.0.0.0",
        port=8005,
        reload=True,
        log_level="info"
    )
