"""
中医文本处理器 - 支持TXT和PDF文件
"""
import numpy as np
import faiss
import pickle
import json
import time
import gc
from pathlib import Path
from typing import List, Dict
import config
from gpu_optimizer import setup_gpu_optimization

class TCMTextProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
        # 获取GPU优化配置
        self.gpu_config = setup_gpu_optimization()
        self.batch_size = self.gpu_config['batch_size']
        self.chunk_size = self.gpu_config['chunk_size']
        
        print(f"🏥 中医文本处理器初始化")
        print(f"   设备: {self.gpu_config['device_name']}")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   块大小: {self.chunk_size}")
    
    def read_text_file(self, file_path: str) -> str:
        """读取文本文件"""
        print(f"📄 读取中医文本文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            print(f"✅ 文本读取成功，长度: {len(text)} 字符")
            return text
            
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    text = f.read()
                print(f"✅ 文本读取成功(GBK编码)，长度: {len(text)} 字符")
                return text
            except:
                print("❌ 文本读取失败")
                return ""
        except Exception as e:
            print(f"❌ 文本读取失败: {e}")
            return ""
    
    def clean_tcm_text(self, text: str) -> str:
        """清理中医文本"""
        if not text:
            return ""
        
        # 移除多余空白
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) > 2:  # 过滤太短的行
                # 中医文本特殊字符处理
                line = line.replace('　', ' ')  # 全角空格
                cleaned_lines.append(line)
        
        text = '\n'.join(cleaned_lines)
        
        # 移除重复的空行
        while '\n\n\n' in text:
            text = text.replace('\n\n\n', '\n\n')
        
        return text
    
    def split_tcm_text(self, text: str) -> List[str]:
        """智能分割中医文本"""
        print("🔪 智能分割中医文本...")
        start_time = time.time()
        
        if len(text) < 50:
            print(f"❌ 文本太短({len(text)}字符)，无法分割")
            return []
        
        chunks = []
        chunk_size = self.chunk_size
        overlap = chunk_size // 10
        
        # 中医文本分割点（按重要性排序）
        tcm_split_patterns = [
            '\n\n',     # 段落分隔 - 最重要
            '。\n',     # 句号+换行
            '。',       # 句号
            '；\n',     # 分号+换行
            '；',       # 分号
            '，',       # 逗号
            '：',       # 冒号
            '、',       # 顿号
        ]
        
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = start + chunk_size
            if end >= text_length:
                end = text_length
                chunk = text[start:end].strip()
                if len(chunk) > 20:  # 至少20字符
                    chunks.append(chunk)
                break
            
            # 寻找最佳分割点
            best_split = end
            for pattern in tcm_split_patterns:
                split_pos = text.rfind(pattern, start + chunk_size//2, end)
                if split_pos != -1:
                    best_split = split_pos + len(pattern)
                    break
            
            chunk = text[start:best_split].strip()
            if len(chunk) > 20:
                chunks.append(chunk)
            
            start = best_split - overlap
            if start < 0:
                start = best_split
        
        split_time = time.time() - start_time
        print(f"✅ 中医文本分割完成，耗时: {split_time:.2f}秒")
        print(f"📊 总块数: {len(chunks)}")
        
        # 显示前几个块的样本
        if chunks:
            print("📝 文本块样本:")
            for i, chunk in enumerate(chunks[:3]):
                print(f"   块{i+1}: {chunk[:50]}...")
        
        return chunks
    
    def create_tcm_embeddings(self, texts: List[str]) -> np.ndarray:
        """创建中医文本嵌入"""
        print("🔄 生成中医文本向量...")
        start_time = time.time()
        
        from models.model_manager import model_manager
        
        embeddings = []
        batch_size = self.batch_size
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            print(f"   处理批次 {batch_num}/{total_batches} ({len(batch_texts)} 个中医文本)")
            
            try:
                batch_embeddings = []
                for j, text in enumerate(batch_texts):
                    try:
                        embedding = model_manager.get_embedding(text)
                        batch_embeddings.append(embedding)
                    except Exception as e:
                        print(f"     文本 {j+1} 嵌入失败: {e}")
                        # 使用零向量作为备用
                        batch_embeddings.append(np.zeros(768))
                
                embeddings.extend(batch_embeddings)
                
                # 每5个批次清理内存
                if batch_num % 5 == 0:
                    gc.collect()
                    
            except Exception as e:
                print(f"   批次 {batch_num} 处理失败: {e}")
                # 整个批次失败，使用零向量
                for _ in batch_texts:
                    embeddings.append(np.zeros(768))
        
        embeddings_array = np.array(embeddings).astype('float32')
        
        embedding_time = time.time() - start_time
        print(f"✅ 中医向量化完成，耗时: {embedding_time:.2f}秒")
        print(f"📊 向量维度: {embeddings_array.shape}")
        
        return embeddings_array
    
    def create_tcm_index(self, embeddings: np.ndarray) -> bool:
        """创建中医知识索引"""
        print("🏗️ 创建中医知识索引...")
        start_time = time.time()
        
        try:
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)
            
            # 归一化向量
            faiss.normalize_L2(embeddings)
            
            # 添加向量
            self.vector_index.add(embeddings)
            
            index_time = time.time() - start_time
            print(f"✅ 中医知识索引完成，耗时: {index_time:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 中医索引创建失败: {e}")
            return False
    
    def process_tcm_text_file(self, file_path: str) -> bool:
        """处理中医文本文件"""
        print(f"🏥 开始处理中医文本文件: {file_path}")
        total_start_time = time.time()
        
        try:
            # 1. 读取文本
            print("📄 第1步: 读取中医文本...")
            text = self.read_text_file(file_path)
            if not text or len(text) < 50:
                print(f"❌ 文本读取失败或内容太少({len(text)}字符)")
                return False
            
            # 2. 清理文本
            print("🧹 第2步: 清理中医文本...")
            text = self.clean_tcm_text(text)
            
            # 3. 分割文本
            print("🔪 第3步: 智能分割中医文本...")
            chunks = self.split_tcm_text(text)
            if not chunks:
                print("❌ 文本分割失败")
                return False
            
            # 4. 创建元数据
            print("📋 第4步: 创建中医文档元数据...")
            metadata = []
            for i, chunk in enumerate(chunks):
                metadata.append({
                    "source": file_path,
                    "chunk_id": i,
                    "chunk_index": i,
                    "content": chunk,
                    "document_type": "中医文献",
                    "processed_time": time.time(),
                    "text_length": len(chunk)
                })
            
            # 5. 生成嵌入
            print("🔄 第5步: 生成中医文本向量...")
            embeddings = self.create_tcm_embeddings(chunks)
            
            # 6. 创建索引
            print("🏗️ 第6步: 创建中医知识索引...")
            if not self.create_tcm_index(embeddings):
                return False
            
            # 7. 保存数据
            print("💾 第7步: 保存中医知识库...")
            self.document_chunks = chunks
            self.chunk_metadata = metadata
            self.save_tcm_data()
            
            total_time = time.time() - total_start_time
            
            print(f"🎉 中医文本处理完成！")
            print(f"⏱️ 总耗时: {total_time:.2f}秒")
            print(f"📊 处理了 {len(chunks)} 个中医文本块")
            print(f"📝 平均块长度: {sum(len(c) for c in chunks) / len(chunks):.0f} 字符")
            print(f"⚡ 处理速度: {len(chunks)/total_time:.1f} 块/秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 中医文本处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def save_tcm_data(self):
        """保存中医数据"""
        try:
            index_path = Path(config.VECTOR_DB_PATH)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            # 保存文档块
            with open(index_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # 保存元数据
            with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            print("✅ 中医知识库保存完成")
            
        except Exception as e:
            print(f"❌ 中医数据保存失败: {e}")

# 全局中医文本处理器
tcm_text_processor = TCMTextProcessor()

def process_tcm_text(file_path: str) -> bool:
    """处理中医文本的便捷函数"""
    return tcm_text_processor.process_tcm_text_file(file_path)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        text_file = sys.argv[1]
        success = process_tcm_text(text_file)
        if success:
            print("🎉 中医文本处理成功！")
        else:
            print("❌ 中医文本处理失败")
    else:
        print("用法: python tcm_text_processor.py <中医文本文件路径>")
