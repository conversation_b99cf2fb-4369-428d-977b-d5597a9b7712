#!/usr/bin/env python3
"""
简化的中医知识检索器
无需Elasticsearch，直接使用本地缓存和在线爬取
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import streamlit as st
from typing import List, Dict, Any
import pickle
import os
from pathlib import Path
from difflib import SequenceMatcher

class SimpleTCMRetriever:
    """简化的中医知识检索器"""
    
    def __init__(self):
        # 本地缓存
        self.cache_dir = Path("tcm_simple_cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.knowledge_file = self.cache_dir / "tcm_knowledge.pkl"
        
        # 线上古代医书源
        self.online_sources = {
            "gudaiyishu": "https://chinesebooks.github.io/gudaiyishu/",
            "yizongjinjian": "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
        }
        
        self.knowledge_base = []
        self.initialized = False
        
    def load_cached_knowledge(self):
        """加载缓存的知识"""
        if self.knowledge_file.exists():
            try:
                with open(self.knowledge_file, 'rb') as f:
                    self.knowledge_base = pickle.load(f)
                st.success(f"✅ 加载缓存知识: {len(self.knowledge_base)} 条")
                return True
            except Exception as e:
                st.warning(f"⚠️ 缓存加载失败: {e}")
        return False
    
    def save_knowledge_cache(self):
        """保存知识到缓存"""
        try:
            with open(self.knowledge_file, 'wb') as f:
                pickle.dump(self.knowledge_base, f)
            st.success("✅ 知识缓存已保存")
            return True
        except Exception as e:
            st.error(f"❌ 缓存保存失败: {e}")
            return False
    
    def crawl_sample_knowledge(self):
        """爬取示例知识（快速版本）"""
        st.info("🕷️ 快速获取中医知识...")
        
        # 预定义的中医知识样本
        sample_knowledge = [
            {
                "title": "气血理论",
                "content": """气血是中医学的基本概念。气是人体生命活动的动力，血是营养物质的载体。
                气血充足则身体健康，气血不足则容易生病。常见的气血不足症状包括：
                面色苍白、头晕乏力、心悸失眠、月经不调等。
                调理方法：补气可用人参、黄芪；补血可用当归、熟地黄。""",
                "source": "中医基础理论",
                "category": "基础理论",
                "keywords": ["气血", "补气", "补血", "人参", "黄芪", "当归"]
            },
            {
                "title": "阴阳学说",
                "content": """阴阳学说是中医理论的核心。阴阳是对立统一的两个方面，
                阴主静、寒、下、内；阳主动、热、上、外。人体健康需要阴阳平衡。
                阴虚症状：潮热盗汗、口干咽燥、失眠多梦。
                阳虚症状：畏寒肢冷、精神萎靡、腰膝酸软。""",
                "source": "中医基础理论", 
                "category": "基础理论",
                "keywords": ["阴阳", "阴虚", "阳虚", "平衡"]
            },
            {
                "title": "五行学说",
                "content": """五行学说包括木、火、土、金、水五个要素。
                在人体中：木对应肝胆，火对应心小肠，土对应脾胃，金对应肺大肠，水对应肾膀胱。
                五行相生：木生火，火生土，土生金，金生水，水生木。
                五行相克：木克土，土克水，水克火，火克金，金克木。""",
                "source": "中医基础理论",
                "category": "基础理论", 
                "keywords": ["五行", "肝", "心", "脾", "肺", "肾"]
            },
            {
                "title": "四诊合参",
                "content": """中医诊断包括望、闻、问、切四诊。
                望诊：观察面色、舌象、形体等。
                闻诊：听声音、嗅气味。
                问诊：询问症状、病史、生活习惯。
                切诊：摸脉象、按压穴位。
                四诊合参，综合分析，才能准确诊断。""",
                "source": "中医诊断学",
                "category": "诊断方法",
                "keywords": ["四诊", "望诊", "闻诊", "问诊", "切诊", "脉象"]
            },
            {
                "title": "常用方剂",
                "content": """四君子汤：人参、白术、茯苓、甘草，功效补气健脾。
                四物汤：当归、川芎、白芍、熟地黄，功效补血调经。
                六味地黄丸：熟地黄、山茱萸、山药、泽泻、茯苓、丹皮，功效滋阴补肾。
                逍遥散：柴胡、当归、白芍、白术、茯苓、薄荷、生姜、甘草，功效疏肝解郁。""",
                "source": "方剂学",
                "category": "方剂",
                "keywords": ["四君子汤", "四物汤", "六味地黄丸", "逍遥散", "补气", "补血"]
            }
        ]
        
        # 尝试爬取一些在线内容
        try:
            st.info("🌐 尝试获取在线中医知识...")
            
            # 简单爬取主页内容
            for source_name, url in self.online_sources.items():
                try:
                    response = requests.get(url, timeout=10)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 提取页面文本
                    text = soup.get_text()
                    text = self._clean_text(text)
                    
                    if len(text) > 500:
                        online_item = {
                            "title": f"{source_name}主页内容",
                            "content": text[:2000],  # 限制长度
                            "source": source_name,
                            "category": "在线资源",
                            "keywords": self._extract_keywords(text)
                        }
                        sample_knowledge.append(online_item)
                        
                except Exception as e:
                    st.warning(f"获取 {source_name} 失败: {e}")
                    
        except Exception as e:
            st.warning(f"在线获取失败: {e}")
        
        self.knowledge_base.extend(sample_knowledge)
        st.success(f"✅ 获取知识: {len(sample_knowledge)} 条")
        
        return sample_knowledge
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符，保留中文和基本标点
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》]', '', text)
        return text.strip()
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        tcm_keywords = [
            "气血", "阴阳", "五行", "脏腑", "经络", "穴位", "方剂", "药材",
            "症状", "病证", "治疗", "诊断", "脉象", "舌象", "针灸", "推拿",
            "本草", "伤寒", "温病", "内科", "外科", "妇科", "儿科", "养生",
            "人参", "黄芪", "当归", "川芎", "白芍", "熟地", "茯苓", "甘草"
        ]
        
        found_keywords = []
        for keyword in tcm_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def similarity_score(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        return SequenceMatcher(None, text1, text2).ratio()
    
    def search_knowledge(self, query: str, top_k: int = 10) -> List[Dict]:
        """搜索知识"""
        if not self.knowledge_base:
            return []
        
        results = []
        
        for item in self.knowledge_base:
            score = 0
            
            # 标题匹配（权重最高）
            if query in item.get('title', ''):
                score += 3
            
            # 内容匹配
            if query in item.get('content', ''):
                score += 2
            
            # 关键词匹配
            keywords = item.get('keywords', [])
            for keyword in keywords:
                if keyword in query or query in keyword:
                    score += 1
            
            # 相似度匹配
            title_sim = self.similarity_score(query, item.get('title', ''))
            content_sim = self.similarity_score(query, item.get('content', '')[:200])
            
            score += title_sim * 2 + content_sim
            
            if score > 0:
                result_item = item.copy()
                result_item['score'] = score
                result_item['highlights'] = self._get_highlights(query, item.get('content', ''))
                results.append(result_item)
        
        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        return results[:top_k]
    
    def _get_highlights(self, query: str, content: str, max_length: int = 200) -> List[str]:
        """获取高亮片段"""
        highlights = []
        
        # 查找包含查询词的句子
        sentences = re.split(r'[。！？；]', content)
        
        for sentence in sentences:
            if query in sentence and len(sentence.strip()) > 10:
                # 截取合适长度
                if len(sentence) > max_length:
                    start = max(0, sentence.find(query) - 50)
                    end = min(len(sentence), start + max_length)
                    highlight = sentence[start:end]
                else:
                    highlight = sentence
                
                highlights.append(highlight.strip())
                
                if len(highlights) >= 3:  # 最多3个高亮
                    break
        
        return highlights
    
    def initialize(self):
        """初始化检索系统"""
        st.info("🔧 初始化简化中医知识检索系统...")
        
        # 1. 尝试加载缓存
        if self.load_cached_knowledge():
            self.initialized = True
            return True
        
        # 2. 获取知识
        knowledge_items = self.crawl_sample_knowledge()
        
        if knowledge_items:
            # 3. 保存缓存
            self.save_knowledge_cache()
            self.initialized = True
            st.success("✅ 简化检索系统初始化完成")
            return True
        else:
            st.error("❌ 知识获取失败")
            return False
    
    def get_stats(self):
        """获取统计信息"""
        if not self.knowledge_base:
            return "知识库为空"
        
        total = len(self.knowledge_base)
        categories = {}
        sources = {}
        
        for item in self.knowledge_base:
            cat = item.get('category', '未分类')
            src = item.get('source', '未知来源')
            
            categories[cat] = categories.get(cat, 0) + 1
            sources[src] = sources.get(src, 0) + 1
        
        stats = f"总计: {total} 条知识\n"
        stats += f"分类: {', '.join([f'{k}({v})' for k, v in categories.items()])}\n"
        stats += f"来源: {', '.join([f'{k}({v})' for k, v in sources.items()])}"
        
        return stats

def main():
    """测试函数"""
    retriever = SimpleTCMRetriever()
    
    print("🔍 简化中医知识检索器测试")
    print("=" * 40)
    
    if retriever.initialize():
        print("✅ 初始化成功")
        print(retriever.get_stats())
        
        # 测试搜索
        results = retriever.search_knowledge("气血不足")
        print(f"\n搜索'气血不足'结果: {len(results)} 条")
        
        for i, result in enumerate(results[:3]):
            print(f"\n{i+1}. {result['title']}")
            print(f"   来源: {result['source']}")
            print(f"   评分: {result['score']:.2f}")
            print(f"   内容: {result['content'][:100]}...")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
