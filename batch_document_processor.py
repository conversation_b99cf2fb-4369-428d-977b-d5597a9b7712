"""
批量文档处理器
专门用于处理大量中医PDF文档，构建完整知识库
"""
import os
import time
import json
from pathlib import Path
from typing import List, Dict
import logging
from datetime import datetime
from robust_tcm_processor import robust_tcm_processor
from document_processor import doc_processor

class BatchDocumentProcessor:
    def __init__(self):
        self.processed_docs = []
        self.failed_docs = []
        self.total_chunks = 0
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("processing_logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"batch_processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_documents(self, directory: str) -> List[str]:
        """扫描目录中的PDF文档"""
        doc_dir = Path(directory)
        if not doc_dir.exists():
            self.logger.error(f"目录不存在: {directory}")
            return []
        
        pdf_files = list(doc_dir.glob("**/*.pdf"))
        self.logger.info(f"发现 {len(pdf_files)} 个PDF文档")
        
        return [str(f) for f in pdf_files]
    
    def process_single_document(self, pdf_path: str) -> Dict:
        """处理单个文档"""
        start_time = time.time()
        result = {
            "file": pdf_path,
            "success": False,
            "chunks": 0,
            "processing_time": 0,
            "error": None
        }
        
        try:
            self.logger.info(f"📄 开始处理: {pdf_path}")
            
            # 使用强化处理器
            success = robust_tcm_processor.process_tcm_document_robust(pdf_path)
            
            if success:
                result["success"] = True
                result["chunks"] = len(robust_tcm_processor.document_chunks)
                self.processed_docs.append(pdf_path)
                self.logger.info(f"✅ 处理成功: {pdf_path}")
            else:
                result["error"] = "处理失败"
                self.failed_docs.append(pdf_path)
                self.logger.error(f"❌ 处理失败: {pdf_path}")
            
        except Exception as e:
            result["error"] = str(e)
            self.failed_docs.append(pdf_path)
            self.logger.error(f"❌ 处理异常 {pdf_path}: {e}")
        
        result["processing_time"] = time.time() - start_time
        return result
    
    def process_batch(self, pdf_files: List[str], max_files: int = None) -> Dict:
        """批量处理文档"""
        if max_files:
            pdf_files = pdf_files[:max_files]
        
        self.logger.info(f"🚀 开始批量处理 {len(pdf_files)} 个文档")
        start_time = time.time()
        
        results = []
        total_chunks = 0
        
        for i, pdf_file in enumerate(pdf_files, 1):
            self.logger.info(f"📊 进度: {i}/{len(pdf_files)}")
            
            result = self.process_single_document(pdf_file)
            results.append(result)
            
            if result["success"]:
                total_chunks += result["chunks"]
            
            # 每处理5个文档保存一次
            if i % 5 == 0:
                self.save_intermediate_results(results)
        
        # 合并所有处理结果到主索引
        if self.processed_docs:
            self.merge_to_main_index()
        
        total_time = time.time() - start_time
        
        summary = {
            "total_files": len(pdf_files),
            "successful": len(self.processed_docs),
            "failed": len(self.failed_docs),
            "total_chunks": total_chunks,
            "total_time": total_time,
            "results": results
        }
        
        self.save_final_report(summary)
        return summary
    
    def save_intermediate_results(self, results: List[Dict]):
        """保存中间结果"""
        try:
            results_dir = Path("processing_logs")
            results_file = results_dir / f"intermediate_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"💾 中间结果已保存: {results_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存中间结果失败: {e}")
    
    def merge_to_main_index(self):
        """合并到主索引"""
        try:
            self.logger.info("🔄 合并处理结果到主索引...")
            
            # 重新加载主索引
            doc_processor.load_index()
            
            # 合并文档块
            if hasattr(robust_tcm_processor, 'document_chunks') and robust_tcm_processor.document_chunks:
                if doc_processor.document_chunks:
                    doc_processor.document_chunks.extend(robust_tcm_processor.document_chunks)
                else:
                    doc_processor.document_chunks = robust_tcm_processor.document_chunks.copy()
            
            # 合并元数据
            if hasattr(robust_tcm_processor, 'chunk_metadata') and robust_tcm_processor.chunk_metadata:
                if doc_processor.chunk_metadata:
                    doc_processor.chunk_metadata.extend(robust_tcm_processor.chunk_metadata)
                else:
                    doc_processor.chunk_metadata = robust_tcm_processor.chunk_metadata.copy()
            
            # 重新创建向量索引
            if doc_processor.document_chunks:
                self.logger.info("🔄 重新创建向量索引...")
                doc_processor.create_vector_index(doc_processor.document_chunks)
                doc_processor.save_index()
                self.logger.info("✅ 主索引更新完成")
            
        except Exception as e:
            self.logger.error(f"❌ 合并索引失败: {e}")
    
    def save_final_report(self, summary: Dict):
        """保存最终报告"""
        try:
            reports_dir = Path("processing_reports")
            reports_dir.mkdir(exist_ok=True)
            
            report_file = reports_dir / f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # 添加详细信息
            detailed_summary = summary.copy()
            detailed_summary.update({
                "processed_files": self.processed_docs,
                "failed_files": self.failed_docs,
                "processing_date": datetime.now().isoformat(),
                "average_time_per_file": summary["total_time"] / summary["total_files"] if summary["total_files"] > 0 else 0
            })
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(detailed_summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📊 最终报告已保存: {report_file}")
            
            # 打印摘要
            self.print_summary(summary)
            
        except Exception as e:
            self.logger.error(f"❌ 保存报告失败: {e}")
    
    def print_summary(self, summary: Dict):
        """打印处理摘要"""
        print("\n" + "="*60)
        print("📊 批量处理完成报告")
        print("="*60)
        print(f"📄 总文档数: {summary['total_files']}")
        print(f"✅ 成功处理: {summary['successful']}")
        print(f"❌ 处理失败: {summary['failed']}")
        print(f"📚 总知识块: {summary['total_chunks']}")
        print(f"⏱️ 总耗时: {summary['total_time']:.2f}秒")
        print(f"⚡ 平均耗时: {summary['total_time']/summary['total_files']:.2f}秒/文档")
        print(f"📈 成功率: {summary['successful']/summary['total_files']*100:.1f}%")
        
        if self.failed_docs:
            print(f"\n❌ 失败文档列表:")
            for doc in self.failed_docs:
                print(f"   - {doc}")
        
        print("="*60)

def main():
    """主函数 - 批量处理示例"""
    print("🏥 中医文档批量处理器")
    print("="*50)
    
    # 获取文档目录
    doc_directory = input("请输入PDF文档目录路径 (默认: documents): ").strip()
    if not doc_directory:
        doc_directory = "documents"
    
    # 获取最大处理数量
    max_files_input = input("最大处理文档数 (默认: 全部): ").strip()
    max_files = int(max_files_input) if max_files_input.isdigit() else None
    
    # 创建处理器
    processor = BatchDocumentProcessor()
    
    # 扫描文档
    pdf_files = processor.scan_documents(doc_directory)
    if not pdf_files:
        print("❌ 未找到PDF文档")
        return
    
    print(f"📚 发现 {len(pdf_files)} 个PDF文档")
    if max_files:
        print(f"📊 将处理前 {max_files} 个文档")
    
    # 确认处理
    response = input("\n是否开始批量处理？(y/n): ").lower().strip()
    if response != 'y':
        print("👋 处理已取消")
        return
    
    # 开始处理
    summary = processor.process_batch(pdf_files, max_files)
    
    print(f"\n🎉 批量处理完成！")
    print(f"📊 详细报告已保存到 processing_reports 目录")

if __name__ == "__main__":
    main()
