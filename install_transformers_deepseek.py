#!/usr/bin/env python3
"""
安装和配置Transformers版本的DeepSeek-R1
最兼容、最稳定的解决方案
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """安装必要的依赖"""
    print("📦 安装Transformers和相关依赖...")
    
    requirements = [
        "transformers>=4.45.0",
        "torch>=2.0.0",
        "accelerate",
        "bitsandbytes",  # 用于量化
        "sentencepiece",  # 分词器
        "protobuf"
    ]
    
    for req in requirements:
        try:
            print(f"📥 安装 {req}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", req],
                capture_output=True,
                text=True,
                timeout=300
            )
            if result.returncode == 0:
                print(f"✅ {req} 安装成功")
            else:
                print(f"⚠️ {req} 安装可能有问题: {result.stderr[:100]}...")
        except Exception as e:
            print(f"❌ {req} 安装失败: {e}")
    
    print("✅ 依赖安装完成")

def create_deepseek_manager():
    """创建DeepSeek管理器"""
    print("🔧 创建DeepSeek-R1管理器...")
    
    manager_code = '''
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import logging
import gc
from typing import Optional

class DeepSeekR1Manager:
    """DeepSeek-R1-0528-Qwen3-8B 管理器 - Transformers版本"""
    
    def __init__(self):
        self.model_name = "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.initialized = False
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def initialize(self, load_in_8bit: bool = True) -> bool:
        """初始化模型"""
        if self.initialized:
            return True
        
        try:
            print(f"🚀 正在加载DeepSeek-R1模型到 {self.device}...")
            
            # 加载分词器
            print("📝 加载分词器...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载模型
            print("🧠 加载模型...")
            model_kwargs = {
                "trust_remote_code": True,
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "low_cpu_mem_usage": True,
            }
            
            # 如果是CPU或者内存有限，使用8bit量化
            if load_in_8bit and self.device == "cuda":
                model_kwargs["load_in_8bit"] = True
                model_kwargs["device_map"] = "auto"
            elif self.device == "cpu":
                model_kwargs["torch_dtype"] = torch.float32
            else:
                model_kwargs["device_map"] = "auto"
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                **model_kwargs
            )
            
            # 如果不是自动设备映射，手动移动到设备
            if "device_map" not in model_kwargs:
                self.model = self.model.to(self.device)
            
            self.model.eval()  # 设置为评估模式
            
            self.initialized = True
            print("✅ DeepSeek-R1模型加载成功!")
            
            # 测试生成
            test_response = self.generate("你好", max_length=50)
            if test_response:
                print(f"🧪 测试成功: {test_response[:50]}...")
                return True
            else:
                print("⚠️ 测试生成失败")
                return False
                
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.logger.error(f"模型初始化失败: {e}")
            return False
    
    def generate(self, prompt: str, max_length: int = 512, temperature: float = 0.7, 
                do_sample: bool = True, top_p: float = 0.9) -> Optional[str]:
        """生成回答"""
        if not self.initialized:
            return "模型未初始化"
        
        try:
            # 编码输入
            inputs = self.tokenizer(
                prompt, 
                return_tensors="pt", 
                padding=True, 
                truncation=True,
                max_length=2048
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 生成参数
            generation_kwargs = {
                "max_length": len(inputs["input_ids"][0]) + max_length,
                "temperature": temperature,
                "do_sample": do_sample,
                "top_p": top_p,
                "pad_token_id": self.tokenizer.pad_token_id,
                "eos_token_id": self.tokenizer.eos_token_id,
                "repetition_penalty": 1.1,
            }
            
            # 生成
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs["input_ids"],
                    attention_mask=inputs.get("attention_mask"),
                    **generation_kwargs
                )
            
            # 解码
            generated_text = self.tokenizer.decode(
                outputs[0], 
                skip_special_tokens=True
            )
            
            # 提取新生成的部分
            response = generated_text[len(prompt):].strip()
            
            # 清理GPU内存
            if self.device == "cuda":
                torch.cuda.empty_cache()
            
            return response
            
        except Exception as e:
            self.logger.error(f"生成失败: {e}")
            return f"生成失败: {str(e)}"
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.initialized = False
        print("🧹 资源清理完成")

# 测试函数
def test_deepseek():
    """测试DeepSeek-R1"""
    print("🧪 测试DeepSeek-R1...")
    
    manager = DeepSeekR1Manager()
    
    if manager.initialize():
        # 测试中医问题
        questions = [
            "中医是什么？",
            "什么是四君子汤？",
            "针灸的原理是什么？"
        ]
        
        for question in questions:
            print(f"\\n❓ 问题: {question}")
            response = manager.generate(question, max_length=200)
            print(f"🤖 回答: {response}")
            print("-" * 50)
        
        manager.cleanup()
        return True
    else:
        print("❌ 模型初始化失败")
        return False

if __name__ == "__main__":
    test_deepseek()
'''
    
    with open("deepseek_r1_transformers.py", "w", encoding="utf-8") as f:
        f.write(manager_code)
    
    print("✅ DeepSeek-R1管理器已创建: deepseek_r1_transformers.py")

def create_integration_script():
    """创建集成脚本"""
    print("🔗 创建RAG系统集成脚本...")
    
    integration_code = '''
# DeepSeek-R1 Transformers集成到RAG系统
# 将此代码添加到您的ultimate_final_tcm_system.py中

from deepseek_r1_transformers import DeepSeekR1Manager
import streamlit as st

class TransformersDeepSeekManager:
    """Transformers版本的DeepSeek管理器"""
    
    def __init__(self):
        self.deepseek = DeepSeekR1Manager()
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化"""
        if self.initialized:
            return True
        
        st.info("🚀 正在加载DeepSeek-R1模型（Transformers版本）...")
        
        # 根据系统配置选择量化选项
        load_in_8bit = st.checkbox("使用8bit量化（节省内存）", value=True)
        
        if self.deepseek.initialize(load_in_8bit=load_in_8bit):
            self.initialized = True
            st.success("✅ DeepSeek-R1模型加载成功！")
            st.info("🎯 使用Transformers引擎，完全兼容Qwen3架构")
            return True
        else:
            st.error("❌ DeepSeek-R1模型加载失败")
            return False
    
    def generate_response(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
        """生成回答"""
        if not self.initialized:
            return "DeepSeek模型未初始化"
        
        st.info("🧠 DeepSeek-R1正在思考...")
        
        try:
            response = self.deepseek.generate(
                prompt=prompt,
                max_length=max_tokens,
                temperature=temperature
            )
            
            if response:
                st.success("✅ DeepSeek-R1生成完成")
                return response
            else:
                return "DeepSeek生成失败，请重试"
                
        except Exception as e:
            st.error(f"❌ DeepSeek调用失败: {e}")
            return f"DeepSeek调用失败: {str(e)}"

# 使用方法：
# 1. 将TransformersDeepSeekManager替换原来的UltimateDeepSeekManager
# 2. 在系统初始化时调用initialize()
# 3. 使用generate_response()生成回答
'''
    
    with open("deepseek_integration.py", "w", encoding="utf-8") as f:
        f.write(integration_code)
    
    print("✅ 集成脚本已创建: deepseek_integration.py")

def main():
    """主函数"""
    print("🚀 DeepSeek-R1 Transformers版本安装器")
    print("=" * 50)
    
    # 安装依赖
    install_requirements()
    
    # 创建管理器
    create_deepseek_manager()
    
    # 创建集成脚本
    create_integration_script()
    
    print("\\n🎉 安装完成!")
    print("=" * 50)
    print("💡 下一步:")
    print("1. 测试模型: python deepseek_r1_transformers.py")
    print("2. 集成到RAG系统: 参考 deepseek_integration.py")
    print("3. 享受DeepSeek-R1的强大能力!")
    print()
    print("🎯 优势:")
    print("✅ 完全兼容Qwen3架构")
    print("✅ 支持CPU和GPU推理")
    print("✅ 内存优化（8bit量化）")
    print("✅ 易于集成和使用")

if __name__ == "__main__":
    main()
