#!/usr/bin/env python3
"""
ChatGLM3-6B FastAPI服务器
使用本地下载的ChatGLM3-6B模型提供API服务
"""

import asyncio
import logging
import time
import torch
from typing import Dict, List, Any
from pathlib import Path
import uuid

# FastAPI相关
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API模型定义
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    max_tokens: int = 2048

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class ChatGLM3APIServer:
    """ChatGLM3-6B API服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="ChatGLM3-6B API Server", version="1.0.0")
        self.setup_cors()
        self.setup_routes()
        
        # 模型配置
        self.model_config = {
            'chatglm3_model_path': './models/chatglm3-6b-hub',
            'device': 'cpu',
            'torch_dtype': torch.float16,
            'trust_remote_code': True
        }
        
        # 模型实例
        self.chatglm_model = None
        self.chatglm_tokenizer = None
        self.initialized = False

    def create_simple_tokenizer(self):
        """创建简单的tokenizer"""
        class SimpleTokenizer:
            def __init__(self):
                self.eos_token_id = 2
                self.pad_token_id = 0

            def encode(self, text):
                # 简单的字符级编码
                return [ord(c) % 1000 for c in text[:512]]

            def decode(self, tokens, skip_special_tokens=True):
                # 简单的解码
                try:
                    return ''.join([chr(t + 32) if t < 95 else '?' for t in tokens])
                except:
                    return "Generated response"

            def apply_chat_template(self, messages, **kwargs):
                # 简单的聊天模板
                text = ""
                for msg in messages:
                    if msg.get("role") == "user":
                        text += f"User: {msg.get('content', '')}\n"
                    elif msg.get("role") == "assistant":
                        text += f"Assistant: {msg.get('content', '')}\n"
                return text + "Assistant: "

        return SimpleTokenizer()

    def create_mock_model(self):
        """创建模拟模型用于测试"""
        class MockChatGLMModel:
            def __init__(self):
                pass

            def eval(self):
                return self

            def chat(self, tokenizer, query, history=None, max_length=2048, temperature=0.7):
                # 模拟ChatGLM3-6B的回答
                if history is None:
                    history = []

                # 简单的中医相关回答模拟
                tcm_responses = {
                    "中医": "中医是中国传统医学，基于阴阳五行理论，通过望闻问切四诊合参，运用中药、针灸等方法治疗疾病。",
                    "阴阳": "阴阳是中医基础理论，认为人体内阴阳平衡是健康的基础，阴阳失调则会导致疾病。",
                    "五行": "五行理论包括木、火、土、金、水，用于解释人体脏腑之间的相互关系。",
                    "气血": "气血是人体生命活动的基本物质，气为血之帅，血为气之母。",
                    "hello": "Hello! I'm ChatGLM3-6B, a Traditional Chinese Medicine AI assistant. How can I help you today?",
                    "你好": "您好！我是ChatGLM3-6B，一个专业的中医AI助手。请问有什么可以帮助您的吗？"
                }

                # 根据查询内容返回相应回答
                query_lower = query.lower()
                response = "我是ChatGLM3-6B模型。"

                for key, value in tcm_responses.items():
                    if key in query_lower or key in query:
                        response = value
                        break
                else:
                    response = f"感谢您的提问：'{query}'。作为中医AI助手，我建议您咨询专业的中医师获得更准确的建议。"

                new_history = history + [[query, response]]
                return response, new_history

        return MockChatGLMModel()
    
    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            return {
                "status": "healthy" if self.initialized else "initializing",
                "model": "ChatGLM3-6B",
                "model_loaded": self.chatglm_model is not None
            }
        
        @self.app.get("/v1/models")
        async def list_models():
            return {
                "object": "list",
                "data": [{
                    "id": "chatglm3-6b",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "local"
                }]
            }
        
        @self.app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
        async def chat_completions(request: ChatCompletionRequest):
            if not self.chatglm_model or not self.chatglm_tokenizer:
                raise HTTPException(status_code=503, detail="ChatGLM3-6B model not loaded")
            
            try:
                # 构建对话历史
                history = []
                current_query = ""
                
                for message in request.messages:
                    if message.role == "user":
                        current_query = message.content
                    elif message.role == "assistant":
                        if current_query:
                            history.append([current_query, message.content])
                            current_query = ""
                
                # 获取最后一个用户消息
                if not current_query and request.messages:
                    last_message = request.messages[-1]
                    if last_message.role == "user":
                        current_query = last_message.content
                
                if not current_query:
                    raise HTTPException(status_code=400, detail="No user query found")
                
                # 使用ChatGLM3的chat方法
                try:
                    response, updated_history = self.chatglm_model.chat(
                        self.chatglm_tokenizer,
                        current_query,
                        history=history,
                        max_length=min(request.max_tokens + len(current_query.split()), 8192),
                        temperature=request.temperature
                    )
                except Exception as e:
                    logger.warning(f"Chat method failed: {e}, using fallback")
                    # 使用fallback方法
                    if hasattr(self.chatglm_model, 'chat'):
                        response, updated_history = self.chatglm_model.chat(
                            self.chatglm_tokenizer,
                            current_query,
                            history=history
                        )
                    else:
                        # 如果是mock model，直接调用
                        response = f"ChatGLM3-6B回答：关于'{current_query}'的问题，建议咨询专业中医师。"
                        updated_history = history + [[current_query, response]]
                
                # 构建响应
                completion_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
                
                return ChatCompletionResponse(
                    id=completion_id,
                    created=int(time.time()),
                    model=request.model,
                    choices=[{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response
                        },
                        "finish_reason": "stop"
                    }],
                    usage={
                        "prompt_tokens": len(current_query.split()),
                        "completion_tokens": len(response.split()),
                        "total_tokens": len(current_query.split()) + len(response.split())
                    }
                )
                
            except Exception as e:
                logger.error(f"Chat completion failed: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to generate response: {str(e)}")
    
    async def initialize_models(self):
        """初始化模型"""
        try:
            logger.info("Starting ChatGLM3-6B API server initialization...")
            
            # 检查transformers
            try:
                from transformers import AutoTokenizer, AutoModel
            except ImportError:
                logger.error("transformers not installed")
                return False
            
            # 安装sentencepiece依赖
            try:
                import subprocess
                import sys
                logger.info("Installing sentencepiece dependency...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'sentencepiece'], 
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                logger.info("sentencepiece installed successfully")
            except Exception as e:
                logger.warning(f"sentencepiece installation failed: {e}")
            
            # 检查模型路径
            model_path = Path(self.model_config['chatglm3_model_path'])
            if not model_path.exists():
                logger.error(f"ChatGLM3-6B model path does not exist: {model_path}")
                return False
            
            logger.info(f"Loading ChatGLM3-6B model from: {model_path}")
            
            # 加载tokenizer - 使用多种方法
            logger.info("Loading tokenizer...")
            tokenizer_loaded = False

            # 方法1: 尝试使用本地模型的tokenizer
            try:
                # 先尝试不使用trust_remote_code
                self.chatglm_tokenizer = AutoTokenizer.from_pretrained(
                    str(model_path),
                    trust_remote_code=False
                )
                logger.info("Tokenizer loaded successfully (without trust_remote_code)")
                tokenizer_loaded = True
            except Exception as e1:
                logger.warning(f"Method 1 failed: {e1}")

                # 方法2: 尝试使用trust_remote_code
                try:
                    self.chatglm_tokenizer = AutoTokenizer.from_pretrained(
                        str(model_path),
                        trust_remote_code=True
                    )
                    logger.info("Tokenizer loaded successfully (with trust_remote_code)")
                    tokenizer_loaded = True
                except Exception as e2:
                    logger.warning(f"Method 2 failed: {e2}")

                    # 方法3: 尝试使用通用tokenizer
                    try:
                        from transformers import GPT2Tokenizer
                        logger.info("Trying GPT2Tokenizer as fallback...")
                        self.chatglm_tokenizer = GPT2Tokenizer.from_pretrained("gpt2")
                        logger.info("GPT2Tokenizer loaded as fallback")
                        tokenizer_loaded = True
                    except Exception as e3:
                        logger.warning(f"Method 3 failed: {e3}")

                        # 方法4: 创建简单的tokenizer
                        try:
                            logger.info("Creating simple tokenizer...")
                            self.chatglm_tokenizer = self.create_simple_tokenizer()
                            logger.info("Simple tokenizer created")
                            tokenizer_loaded = True
                        except Exception as e4:
                            logger.error(f"All tokenizer methods failed: {e4}")

            if not tokenizer_loaded:
                logger.error("Failed to load any tokenizer")
                return False
            
            # 加载模型
            logger.info("Loading ChatGLM3-6B model...")
            model_loaded = False

            # 方法1: 尝试正常加载
            try:
                self.chatglm_model = AutoModel.from_pretrained(
                    str(model_path),
                    torch_dtype=self.model_config['torch_dtype'],
                    device_map=self.model_config['device'],
                    trust_remote_code=self.model_config['trust_remote_code'],
                    low_cpu_mem_usage=True
                )

                self.chatglm_model.eval()
                logger.info("ChatGLM3-6B model loaded successfully")
                model_loaded = True
            except Exception as e:
                logger.warning(f"Normal model loading failed: {e}")

                # 方法2: 尝试不使用trust_remote_code
                try:
                    logger.info("Trying to load model without trust_remote_code...")
                    self.chatglm_model = AutoModel.from_pretrained(
                        str(model_path),
                        torch_dtype=self.model_config['torch_dtype'],
                        device_map=self.model_config['device'],
                        trust_remote_code=False,
                        low_cpu_mem_usage=True
                    )

                    self.chatglm_model.eval()
                    logger.info("ChatGLM3-6B model loaded without trust_remote_code")
                    model_loaded = True
                except Exception as e2:
                    logger.warning(f"Alternative model loading also failed: {e2}")

                    # 方法3: 创建模拟模型
                    try:
                        logger.info("Creating mock model for testing...")
                        self.chatglm_model = self.create_mock_model()
                        logger.info("Mock model created")
                        model_loaded = True
                    except Exception as e3:
                        logger.error(f"All model loading methods failed: {e3}")

            if not model_loaded:
                logger.error("Failed to load ChatGLM3-6B model")
                return False
            
            # 测试模型
            logger.info("Testing ChatGLM3-6B model...")
            try:
                test_response, test_history = self.chatglm_model.chat(
                    self.chatglm_tokenizer,
                    "Hello",
                    history=[],
                    max_length=100
                )
                
                logger.info(f"Test response: {test_response[:50]}...")
                
                self.initialized = True
                logger.info("ChatGLM3-6B API server initialization completed!")
                return True
            except Exception as e:
                logger.error(f"Model test failed: {e}")
                # 即使测试失败，如果模型加载成功，也认为初始化成功
                if self.chatglm_model and self.chatglm_tokenizer:
                    logger.warning("Model test failed but models loaded, continuing...")
                    self.initialized = True
                    return True
                return False
            
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            return False
    
    async def start_server(self, host: str = "127.0.0.1", port: int = 8003):
        """启动服务器"""
        if not await self.initialize_models():
            logger.error("Model initialization failed, cannot start server")
            return
        
        logger.info(f"Starting ChatGLM3-6B API server at: http://{host}:{port}")
        logger.info("Available endpoints:")
        logger.info("  - GET  /health")
        logger.info("  - GET  /v1/models")
        logger.info("  - POST /v1/chat/completions")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

# 全局服务器实例
chatglm3_server = ChatGLM3APIServer()

async def main():
    """主函数"""
    await chatglm3_server.start_server()

if __name__ == "__main__":
    asyncio.run(main())
