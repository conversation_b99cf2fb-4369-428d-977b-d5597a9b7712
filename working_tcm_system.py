#!/usr/bin/env python3
"""
可工作的中医RAG系统
解决所有问题的简化版本，不依赖DeepSeek模型
"""

# 页面配置 - 必须在最开始
import streamlit as st
st.set_page_config(
    page_title="🧙‍♂️ 智者·中医AI助手",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import numpy as np
import requests
from bs4 import BeautifulSoup
import time
import hashlib
import logging
from typing import Dict, List, Any
import threading
import gc
import asyncio

# 文档处理
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    st.warning("⚠️ PDF处理不可用，请安装 PyPDF2")

try:
    import docx
    import pandas as pd
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False

# 向量搜索
try:
    import faiss
    from sentence_transformers import SentenceTransformer
    VECTOR_SEARCH_AVAILABLE = True
except ImportError:
    VECTOR_SEARCH_AVAILABLE = False
    st.warning("⚠️ 向量搜索不可用，请安装 faiss 和 sentence-transformers")

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    st.warning("⚠️ 语音功能不可用，请安装 pyttsx3 和 SpeechRecognition")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './working_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CONVERSATION_PATH': './conversations',
    'CHUNK_SIZE': 800,
    'CHUNK_OVERLAP': 100,
    'TOP_K': 5,
    'BATCH_SIZE': 32,
    'MAX_FILE_SIZE': 200 * 1024 * 1024,  # 200MB
}

class WorkingVoiceManager:
    """可工作的语音管理器"""
    
    def __init__(self):
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.voice_available = VOICE_AVAILABLE
        
        if self.voice_available:
            try:
                self.tts_engine = pyttsx3.init()
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                
                self.tts_engine.setProperty('rate', 180)
                self.tts_engine.setProperty('volume', 0.9)
                
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                logger.info("语音管理器初始化成功")
                
            except Exception as e:
                logger.error(f"语音管理器初始化失败: {e}")
                self.voice_available = False
    
    def speak_text(self, text: str):
        """朗读文本"""
        if not self.voice_available or not self.tts_engine:
            return False
        
        try:
            clean_text = re.sub(r'[#*`\[\]()]', '', text)
            clean_text = re.sub(r'https?://\S+', '', clean_text)
            clean_text = clean_text.replace('\n', ' ').strip()
            
            if len(clean_text) > 300:
                clean_text = clean_text[:300] + "..."
            
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            return True
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
            return False
    
    def listen_for_speech(self, timeout: int = 10) -> str:
        """监听语音输入"""
        if not self.voice_available:
            return None
        
        try:
            with self.microphone as source:
                st.info("🎤 正在监听，请说话...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=15)
            
            st.info("🔄 正在识别语音...")
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            return text
                    
        except sr.WaitTimeoutError:
            st.warning("⏰ 语音输入超时")
            return None
        except sr.UnknownValueError:
            st.warning("🤷 无法识别语音内容，请重试")
            return None
        except Exception as e:
            st.error(f"❌ 语音识别失败: {e}")
            return None

class WorkingConversationManager:
    """可工作的对话管理器"""
    
    def __init__(self):
        self.conversations = []
        self.current_session_id = self._generate_session_id()
        self.user_profile = {}
        self.max_history = 20
        
        # 创建对话目录
        os.makedirs(CONFIG['CONVERSATION_PATH'], exist_ok=True)
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._load_conversation()
    
    def _generate_session_id(self) -> str:
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _load_conversation(self):
        try:
            if self.conversation_file.exists():
                with open(self.conversation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversations = data.get('conversations', [])
                    self.user_profile = data.get('user_profile', {})
        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")
    
    def _save_conversation(self):
        try:
            data = {
                'session_id': self.current_session_id,
                'conversations': self.conversations,
                'user_profile': self.user_profile,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.conversation_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存对话历史失败: {e}")
    
    def add_conversation(self, user_input: str, assistant_response: str, sources: List = None):
        conversation = {
            'id': len(self.conversations) + 1,
            'timestamp': datetime.now().isoformat(),
            'user_input': user_input,
            'assistant_response': assistant_response,
            'sources': sources or []
        }
        
        self.conversations.append(conversation)
        
        if len(self.conversations) > self.max_history:
            self.conversations = self.conversations[-self.max_history:]
        
        self._update_user_profile(user_input)
        self._save_conversation()
    
    def _update_user_profile(self, user_input: str):
        symptoms = self.user_profile.get('symptoms', set())
        
        symptom_keywords = {
            '湿气': ['湿气', '湿重', '湿邪', '痰湿'],
            '失眠': ['失眠', '睡不着', '多梦', '易醒'],
            '头痛': ['头痛', '头疼', '偏头痛', '头晕'],
            '胃痛': ['胃痛', '胃疼', '腹痛', '胃胀'],
            '咳嗽': ['咳嗽', '咳痰', '干咳'],
            '气血不足': ['气虚', '血虚', '气血不足', '乏力'],
        }
        
        for symptom, keywords in symptom_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                symptoms.add(symptom)
        
        self.user_profile['symptoms'] = list(symptoms)
    
    def get_conversation_context(self, current_query: str) -> str:
        context_parts = []
        
        if self.user_profile:
            if self.user_profile.get('symptoms'):
                context_parts.append(f"用户已知症状: {', '.join(self.user_profile['symptoms'])}")
        
        recent_conversations = self.conversations[-3:]
        for conv in recent_conversations:
            context_parts.append(f"历史问题: {conv['user_input']}")
        
        context_parts.append(f"当前问题: {current_query}")
        return "\n".join(context_parts)
    
    def get_conversation_summary(self) -> Dict:
        return {
            'session_id': self.current_session_id,
            'total_conversations': len(self.conversations),
            'user_profile': self.user_profile
        }
    
    def clear_conversation(self):
        self.conversations = []
        self.user_profile = {}
        self.current_session_id = self._generate_session_id()
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._save_conversation()

class WorkingDocumentProcessor:
    """可工作的文档处理器"""
    
    def __init__(self):
        self.supported_formats = ['.pdf', '.txt']
        if MULTI_FORMAT_AVAILABLE:
            self.supported_formats.extend(['.docx', '.doc'])
    
    def process_file(self, file_path: Path, file_name: str):
        try:
            file_size = os.path.getsize(file_path)
            if file_size > CONFIG['MAX_FILE_SIZE']:
                st.warning(f"⚠️ 文件 {file_name} 过大 ({file_size/1024/1024:.1f}MB)")
                return [], []
            
            extension = Path(file_name).suffix.lower()
            
            if extension == '.pdf' and PDF_AVAILABLE:
                return self._process_pdf(file_path, file_name)
            elif extension == '.txt':
                return self._process_txt(file_path, file_name)
            elif extension in ['.docx', '.doc'] and MULTI_FORMAT_AVAILABLE:
                return self._process_word(file_path, file_name)
            else:
                st.warning(f"⚠️ 不支持的文件格式: {extension}")
                return [], []
                
        except Exception as e:
            st.error(f"❌ 处理文件失败 {file_name}: {e}")
            return [], []
    
    def _process_pdf(self, file_path: Path, file_name: str):
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_parts = []
                
                total_pages = len(pdf_reader.pages)
                max_pages = min(total_pages, 100)  # 限制100页
                
                for i in range(max_pages):
                    try:
                        page_text = pdf_reader.pages[i].extract_text()
                        if page_text.strip():
                            text_parts.append(page_text.strip())
                    except Exception as e:
                        logger.warning(f"页面 {i+1} 处理失败: {e}")
                        continue
                
                full_text = '\n'.join(text_parts)
                chunks = self._split_text(full_text)
                
                metadata = []
                for i, chunk in enumerate(chunks):
                    meta = {
                        'source': file_name,
                        'chunk_id': f"{file_name}_{i}",
                        'chunk_index': i,
                        'content': chunk,
                        'upload_time': datetime.now().isoformat(),
                        'file_type': '.pdf',
                        'total_pages': total_pages
                    }
                    metadata.append(meta)
                
                return chunks, metadata
                
        except Exception as e:
            st.error(f"PDF处理失败: {e}")
            return [], []
    
    def _process_txt(self, file_path: Path, file_name: str):
        try:
            encodings = ['utf-8', 'gbk', 'gb2312']
            text = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if text is None:
                st.error(f"❌ 无法读取文本文件 {file_name}")
                return [], []
            
            chunks = self._split_text(text)
            
            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.txt'
                }
                metadata.append(meta)
            
            return chunks, metadata
            
        except Exception as e:
            st.error(f"文本文件处理失败: {e}")
            return [], []
    
    def _process_word(self, file_path: Path, file_name: str):
        try:
            doc = docx.Document(file_path)
            text_parts = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
            
            full_text = '\n'.join(text_parts)
            chunks = self._split_text(full_text)
            
            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.docx'
                }
                metadata.append(meta)
            
            return chunks, metadata
            
        except Exception as e:
            st.error(f"Word文档处理失败: {e}")
            return [], []
    
    def _split_text(self, text: str) -> List[str]:
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']
        
        paragraphs = text.split('\n\n')
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            if len(current_chunk) + len(paragraph) <= chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                
                if len(paragraph) <= chunk_size:
                    current_chunk = paragraph + "\n\n"
                else:
                    # 分割长段落
                    start = 0
                    while start < len(paragraph):
                        end = start + chunk_size
                        if end > len(paragraph):
                            end = len(paragraph)
                        
                        chunk = paragraph[start:end]
                        if end < len(paragraph) and '。' in chunk:
                            last_period = chunk.rfind('。')
                            if last_period > chunk_size // 2:
                                end = start + last_period + 1
                                chunk = paragraph[start:end]
                        
                        chunks.append(chunk.strip())
                        start = end - overlap
                        
                        if start >= len(paragraph):
                            break
                    
                    current_chunk = ""
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return [chunk for chunk in chunks if len(chunk.strip()) > 20]
