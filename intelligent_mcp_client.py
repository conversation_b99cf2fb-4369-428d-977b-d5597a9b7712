#!/usr/bin/env python3
"""
智能MCP客户端 - 连接智能MCP服务
"""

import requests
import json
import logging
from typing import Dict, List, Any, Optional
import streamlit as st
import time

logger = logging.getLogger(__name__)

class IntelligentMCPClient:
    """智能MCP客户端"""
    
    def __init__(self, service_url: str = "http://localhost:8006"):
        self.service_url = service_url
        self.mcp_endpoint = f"{service_url}/mcp"
        self.health_endpoint = f"{service_url}/health"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化客户端"""
        try:
            st.info("🔧 连接智能MCP服务...")
            
            # 检查服务健康状态
            response = requests.get(self.health_endpoint, timeout=5)
            if response.status_code == 200:
                self.initialized = True
                st.success("✅ 智能MCP服务连接成功")
                return True
            else:
                st.error(f"❌ 智能MCP服务响应异常: {response.status_code}")
                return False
                
        except Exception as e:
            st.error(f"❌ 无法连接智能MCP服务: {e}")
            return False
    
    def search_knowledge(self, query: str, domain: str = "medical", max_results: int = 3) -> List[Dict]:
        """搜索知识"""
        if not self.initialized:
            return []
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": domain,
                    "max_results": max_results
                },
                "id": f"search_{int(time.time())}"
            }
            
            response = requests.post(
                self.mcp_endpoint,
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result:
                    return result['result'].get('results', [])
            
            return []
            
        except Exception as e:
            logger.error(f"MCP搜索失败: {e}")
            return []

class IntelligentMCPRetriever:
    """智能MCP检索器 - 替代旧的MCP检索器"""
    
    def __init__(self):
        self.client = IntelligentMCPClient()
        self.initialized = False
    
    def initialize(self) -> bool:
        """初始化检索器"""
        try:
            self.initialized = self.client.initialize()
            return self.initialized
        except Exception as e:
            st.error(f"❌ 智能MCP检索器初始化失败: {e}")
            return False
    
    def search_by_domain(self, query: str, domain: str = "medical", max_results: int = 3) -> List[Dict]:
        """按领域搜索"""
        if not self.initialized:
            return []
        
        try:
            results = self.client.search_knowledge(query, domain, max_results)
            
            # 转换结果格式以兼容现有系统
            formatted_results = []
            for result in results:
                formatted_result = {
                    'title': result.get('title', '智能MCP结果'),
                    'content': result.get('content', ''),
                    'source': result.get('source', 'intelligent_mcp'),
                    'score': result.get('score', 0.8),
                    'domain': result.get('domain', domain),
                    'metadata': result.get('metadata', {})
                }
                formatted_results.append(formatted_result)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"智能MCP搜索失败: {e}")
            return []
    
    def get_service_info(self) -> Dict:
        """获取服务信息"""
        return {
            'name': 'Intelligent TCM MCP Service',
            'version': '2.0.0',
            'description': '智能中医知识检索服务'
        }

# 为了兼容性，创建别名
MCPElasticsearchRetriever = IntelligentMCPRetriever
