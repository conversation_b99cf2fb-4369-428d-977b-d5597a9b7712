#!/usr/bin/env python3
"""
设置隧道的脚本 - 支持多种隧道方案
"""
import subprocess
import sys
import os
import time
import requests
import threading
import socket

def check_port_available(port):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('', port))
            return True
        except:
            return False

def setup_simple_proxy():
    """设置简单的代理服务器"""
    try:
        import http.server
        import socketserver
        from urllib.parse import urlparse
        import urllib.request
        
        class ProxyHandler(http.server.BaseHTTPRequestHandler):
            def do_GET(self):
                self.proxy_request()
            
            def do_POST(self):
                self.proxy_request()
            
            def proxy_request(self):
                # 添加基本认证
                auth_header = self.headers.get('Authorization')
                if not auth_header or not self.check_auth(auth_header):
                    self.send_response(401)
                    self.send_header('WWW-Authenticate', 'Basic realm="TCM RAG System"')
                    self.end_headers()
                    self.wfile.write(b'Authentication required')
                    return
                
                # 代理到本地服务器
                target_url = f"http://localhost:8008{self.path}"
                
                try:
                    # 创建请求
                    req = urllib.request.Request(target_url)
                    
                    # 复制头部
                    for header, value in self.headers.items():
                        if header.lower() not in ['host', 'connection']:
                            req.add_header(header, value)
                    
                    # 添加认证头部
                    import base64
                    credentials = base64.b64encode(b'tcm_user:MVP168918').decode('ascii')
                    req.add_header('Authorization', f'Basic {credentials}')
                    
                    # 处理POST数据
                    if self.command == 'POST':
                        content_length = int(self.headers.get('Content-Length', 0))
                        post_data = self.rfile.read(content_length)
                        req.data = post_data
                    
                    # 发送请求
                    with urllib.request.urlopen(req) as response:
                        # 发送响应
                        self.send_response(response.getcode())
                        
                        # 复制响应头部
                        for header, value in response.headers.items():
                            if header.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(header, value)
                        
                        self.end_headers()
                        
                        # 复制响应内容
                        self.wfile.write(response.read())
                
                except Exception as e:
                    self.send_response(500)
                    self.end_headers()
                    self.wfile.write(f"Proxy error: {str(e)}".encode())
            
            def check_auth(self, auth_header):
                """检查基本认证"""
                try:
                    import base64
                    auth_type, credentials = auth_header.split(' ', 1)
                    if auth_type.lower() != 'basic':
                        return False
                    
                    decoded = base64.b64decode(credentials).decode('utf-8')
                    username, password = decoded.split(':', 1)
                    
                    return username == 'tcm_user' and password == 'MVP168918'
                except:
                    return False
            
            def log_message(self, format, *args):
                # 简化日志输出
                pass
        
        # 找一个可用端口
        proxy_port = 8080
        while not check_port_available(proxy_port) and proxy_port < 8090:
            proxy_port += 1
        
        if not check_port_available(proxy_port):
            print("❌ 无法找到可用端口")
            return None
        
        # 启动代理服务器
        with socketserver.TCPServer(("", proxy_port), ProxyHandler) as httpd:
            print(f"🚀 代理服务器启动在端口 {proxy_port}")
            
            # 获取本机IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            print(f"🌐 本地网络访问地址:")
            print(f"   http://{local_ip}:{proxy_port}")
            print(f"   http://localhost:{proxy_port}")
            print()
            print(f"🔐 访问凭据:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            print()
            print("📋 分享给家人朋友的信息:")
            print("=" * 50)
            print(f"🏥 中医RAG智能诊疗系统")
            print(f"🌐 访问地址: http://{local_ip}:{proxy_port}")
            print(f"🔐 登录信息:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            print("=" * 50)
            print()
            print("💡 使用说明:")
            print("1. 确保设备在同一局域网内")
            print("2. 在手机浏览器中打开上述网址")
            print("3. 输入用户名和密码登录")
            print("4. 即可使用中医智能诊疗功能")
            print()
            print("⚠️ 注意事项:")
            print("- 仅限局域网访问")
            print("- 请妥善保管登录凭据")
            print("- 服务器将保持运行直到程序结束")
            print()
            print("⏳ 代理服务器运行中... (按Ctrl+C停止)")
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 代理服务器已停止")
                return f"http://{local_ip}:{proxy_port}"
    
    except Exception as e:
        print(f"❌ 代理服务器启动失败: {e}")
        return None

def setup_cloudflare_tunnel():
    """尝试设置Cloudflare隧道"""
    try:
        print("🌩️ 尝试使用Cloudflare隧道...")
        
        # 检查cloudflared是否可用
        result = subprocess.run(['cloudflared', '--version'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ cloudflared可用")
            
            # 启动隧道
            cmd = ['cloudflared', 'tunnel', '--url', 'http://localhost:8008']
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            # 等待隧道启动并获取URL
            for _ in range(30):  # 等待30秒
                if process.poll() is not None:
                    break
                
                # 检查输出中的URL
                try:
                    line = process.stderr.readline()
                    if 'https://' in line and 'trycloudflare.com' in line:
                        url = line.split('https://')[1].split()[0]
                        public_url = f"https://{url}"
                        
                        print(f"🌐 Cloudflare隧道已启动！")
                        print(f"📱 公网访问地址: {public_url}")
                        return public_url, process
                except:
                    pass
                
                time.sleep(1)
            
            print("❌ Cloudflare隧道启动超时")
            process.terminate()
            return None, None
        
    except FileNotFoundError:
        print("❌ cloudflared未安装")
    except Exception as e:
        print(f"❌ Cloudflare隧道设置失败: {e}")
    
    return None, None

def main():
    """主函数"""
    print("🚀 隧道设置程序")
    print("=" * 40)
    
    # 检查本地服务器是否运行
    try:
        response = requests.get("http://localhost:8008/api/health", 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            print("✅ 本地服务器运行正常")
        else:
            print("❌ 本地服务器响应异常")
            return
    except:
        print("❌ 本地服务器未运行，请先启动服务器")
        print("💡 运行命令: python simple_ultimate_tcm.py")
        return
    
    print("\n🔧 选择隧道方案:")
    print("1. 局域网代理 (推荐，适合家庭网络)")
    print("2. Cloudflare隧道 (公网访问)")
    print("3. 仅显示本地访问信息")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 启动局域网代理...")
        setup_simple_proxy()
    
    elif choice == "2":
        print("\n🌩️ 尝试Cloudflare隧道...")
        url, process = setup_cloudflare_tunnel()
        
        if url and process:
            print(f"\n🎉 Cloudflare隧道设置完成！")
            print(f"📱 公网访问地址: {url}")
            print(f"🔐 访问凭据:")
            print(f"   用户名: tcm_user")
            print(f"   密码: MVP168918")
            
            try:
                print("\n⏳ 隧道保持运行中... (按Ctrl+C停止)")
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 隧道已停止")
                process.terminate()
        else:
            print("❌ Cloudflare隧道设置失败，回退到局域网代理")
            setup_simple_proxy()
    
    elif choice == "3":
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        
        print(f"\n📋 本地访问信息:")
        print("=" * 50)
        print(f"🏥 中医RAG智能诊疗系统")
        print(f"🌐 本地访问地址: http://localhost:8008")
        print(f"🌐 局域网访问地址: http://{local_ip}:8008")
        print(f"🔐 登录信息:")
        print(f"   用户名: tcm_user")
        print(f"   密码: MVP168918")
        print("=" * 50)
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
