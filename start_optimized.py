"""
内存优化版RAG系统启动脚本
"""
import subprocess
import sys
import psutil
import gc

def check_system_resources():
    """检查系统资源"""
    memory = psutil.virtual_memory()
    print(f"💾 内存状态:")
    print(f"   总内存: {memory.total / (1024**3):.1f} GB")
    print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"   使用率: {memory.percent:.1f}%")
    
    if memory.percent > 90:
        print("⚠️ 警告: 内存使用率过高，可能影响性能")
        print("💡 建议: 关闭其他应用程序释放内存")
        
        response = input("是否继续启动？(y/n): ").lower().strip()
        if response != 'y':
            return False
    
    return True

def optimize_environment():
    """优化环境设置"""
    import os
    
    # 设置环境变量优化内存使用
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'  # 避免tokenizer并行导致的内存问题
    os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'  # 禁用symlink警告
    
    # 强制垃圾回收
    gc.collect()
    
    print("✅ 环境优化完成")

def main():
    print("🚀 内存优化版RAG系统启动")
    print("=" * 40)
    
    # 检查系统资源
    if not check_system_resources():
        print("👋 启动已取消")
        return
    
    # 优化环境
    optimize_environment()
    
    print("\n🌐 正在启动优化版Web界面...")
    print("📝 访问地址: http://localhost:8504")
    print("⚠️ 首次启动可能较慢，请耐心等待...")
    print("💡 如果内存不足，系统会自动使用更小的模型")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app_optimized.py", 
            "--server.address", "localhost",
            "--server.port", "8504",
            "--server.headless", "true",
            "--server.maxUploadSize", "50"  # 限制上传文件大小为50MB
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请尝试关闭其他应用程序释放内存后重试")

if __name__ == "__main__":
    main()
