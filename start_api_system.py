#!/usr/bin/env python3
"""
启动API版本的中医RAG系统
自动检查依赖和DeepSeek API状态
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 100)
    print("🧙‍♂️ 终极中医RAG系统 - API版本启动器")
    print("=" * 100)
    print("🎯 特点:")
    print("   ✅ 使用LM Studio API调用DeepSeek模型")
    print("   ✅ 不需要编译llama-cpp-python")
    print("   ✅ 支持Docker轻量化部署")
    print("   ✅ 跨硬件移植友好")
    print("=" * 100)

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_required_files():
    """检查必要文件"""
    print("📁 检查必要文件...")
    
    required_files = [
        "working_tcm_system_api.py",
        "deepseek_api_manager.py",
        "requirements_api.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    
    try:
        # 检查核心依赖
        import streamlit
        import requests
        import numpy
        import pandas
        print("   ✅ 核心依赖已安装")
        
        # 检查可选依赖
        optional_deps = []
        
        try:
            import PyPDF2
            print("   ✅ PDF处理可用")
        except ImportError:
            optional_deps.append("PyPDF2")
        
        try:
            import faiss
            from sentence_transformers import SentenceTransformer
            print("   ✅ 向量搜索可用")
        except ImportError:
            optional_deps.append("faiss-cpu sentence-transformers")
        
        try:
            import pyttsx3
            import speech_recognition
            print("   ✅ 语音功能可用")
        except ImportError:
            optional_deps.append("pyttsx3 SpeechRecognition")
        
        try:
            from bs4 import BeautifulSoup
            print("   ✅ 网页爬取可用")
        except ImportError:
            optional_deps.append("beautifulsoup4")
        
        if optional_deps:
            print(f"⚠️ 缺少可选依赖: {', '.join(optional_deps)}")
            
            install_choice = input("是否安装缺少的依赖? (y/n): ").lower().strip()
            if install_choice == 'y':
                print("📥 安装依赖中...")
                
                for dep in optional_deps:
                    try:
                        subprocess.run([
                            sys.executable, "-m", "pip", "install", dep
                        ], check=True, capture_output=True)
                        print(f"   ✅ {dep} 安装成功")
                    except subprocess.CalledProcessError as e:
                        print(f"   ⚠️ {dep} 安装失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 核心依赖缺失: {e}")
        print("💡 请运行: pip install -r requirements_api.txt")
        return False

def check_deepseek_api():
    """检查DeepSeek API状态"""
    print("🧠 检查DeepSeek API状态...")
    
    try:
        from deepseek_api_manager import DeepSeekAPIManager
        
        api_manager = DeepSeekAPIManager()
        
        # 检查模型文件
        if not api_manager.check_model_file():
            print("❌ DeepSeek模型文件不存在")
            print("💡 请确保模型文件在正确路径:")
            print(f"   {api_manager.model_path}")
            return False
        
        # 检查LM Studio
        if not api_manager.check_lmstudio_running():
            print("⚠️ LM Studio未运行")
            print("💡 请手动启动LM Studio:")
            print("   1. 打开LM Studio")
            print("   2. 在Local Server选项卡中启动服务器")
            print("   3. 加载DeepSeek-R1-0528-Qwen3-8B-Q4_K_M模型")
            
            wait_choice = input("启动LM Studio后按回车继续，或输入'n'跳过: ").lower().strip()
            if wait_choice == 'n':
                print("⚠️ 跳过DeepSeek API检查，系统将使用备用回答模式")
                return True
            
            # 重新检查
            if not api_manager.check_lmstudio_running():
                print("❌ LM Studio仍未运行")
                return False
        
        print("✅ DeepSeek API可用")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek API检查失败: {e}")
        return False

def create_directories():
    """创建必要目录"""
    print("📂 创建必要目录...")
    
    directories = [
        "working_vector_db",
        "documents",
        "conversations",
        "logs"
    ]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ 创建目录: {dir_name}")
        else:
            print(f"   ✅ 目录已存在: {dir_name}")

def start_streamlit():
    """启动Streamlit应用"""
    print("🚀 启动Streamlit应用...")
    
    try:
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "working_tcm_system_api.py",
            "--server.port=8507",
            "--server.address=0.0.0.0",
            "--theme.base=light",
            "--server.headless=false"
        ]
        
        print("💡 启动命令:")
        print(f"   {' '.join(cmd)}")
        print("")
        print("🌐 访问地址: http://localhost:8507")
        print("📱 移动端访问: http://[您的IP]:8507")
        print("")
        print("⏹️ 按 Ctrl+C 停止服务")
        print("=" * 100)
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查步骤
    checks = [
        ("Python版本", check_python_version),
        ("必要文件", check_required_files),
        ("依赖安装", install_dependencies),
        ("目录创建", create_directories),
        ("DeepSeek API", check_deepseek_api)
    ]
    
    print("🔍 执行启动前检查...")
    print("")
    
    for check_name, check_func in checks:
        print(f"🔄 {check_name}...")
        if not check_func():
            print(f"❌ {check_name} 检查失败")
            
            if check_name == "DeepSeek API":
                continue_choice = input("是否继续启动? (系统将使用备用模式) (y/n): ").lower().strip()
                if continue_choice != 'y':
                    print("👋 启动已取消")
                    return
            else:
                print("💡 请解决上述问题后重试")
                return
        print("")
    
    print("✅ 所有检查完成")
    print("")
    
    # 启动确认
    start_choice = input("是否启动系统? (y/n): ").lower().strip()
    if start_choice == 'y':
        start_streamlit()
    else:
        print("👋 启动已取消")

if __name__ == "__main__":
    main()
