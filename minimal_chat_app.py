#!/usr/bin/env python3
"""
最简化的现代聊天界面 - 直接运行版本
"""
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import uuid
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(title="中医智能助手", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str

# 内置中医知识库
TCM_KNOWLEDGE = {
    "湿气": {
        "definition": "湿气是中医理论中的重要概念，指人体内水液代谢失常所产生的病理产物。",
        "symptoms": "湿气重的表现包括：身体沉重、头昏脑胀、四肢困倦、胸闷腹胀、食欲不振、大便黏腻等。",
        "treatment": "治疗原则以健脾化湿、理气化湿为主，建议适当运动，饮食清淡，避免生冷食物。"
    },
    "气血": {
        "definition": "气血是中医学的核心概念，气为血之帅，血为气之母。",
        "functions": "气具有推动、温煦、防御、固摄、气化等功能；血具有濡养、滋润等作用。",
        "deficiency": "气虚表现为乏力、气短、声低懒言；血虚表现为面色苍白、头晕心悸、失眠多梦。"
    },
    "阴阳": {
        "definition": "阴阳学说是中医理论的哲学基础，认为阴阳是宇宙间相互关联的两个方面。",
        "balance": "在人体中，阴阳的相对平衡是健康的标志，阴阳失调则导致疾病。",
        "characteristics": "阴主静、主寒、主下、主内；阳主动、主热、主上、主外。"
    },
    "四季养生": {
        "spring": "春季养生：春季阳气生发，应顺应自然，早睡早起，适当运动。",
        "summer": "夏季养生：夏季阳气旺盛，应注意清热解暑，保持心情舒畅。",
        "autumn": "秋季养生：秋季阳气收敛，应注意滋阴润燥，调养肺气。",
        "winter": "冬季养生：冬季阳气潜藏，应注意温补肾阳，保存精气。"
    }
}

def generate_response(question: str) -> tuple[str, List[Dict[str, Any]]]:
    """生成回答"""
    question_lower = question.lower()
    sources = []
    
    # 简单的关键词匹配
    if "湿气" in question_lower:
        knowledge = TCM_KNOWLEDGE["湿气"]
        response = f"""## 关于湿气的中医知识

### 🌿 湿气的基本概念
{knowledge['definition']}

### 💡 常见表现
{knowledge['symptoms']}

### 🔧 调理建议
{knowledge['treatment']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
        
        sources = [{'source': '内置中医知识库 - 湿气', 'content': knowledge['definition'], 'score': 1.0}]
        
    elif "气血" in question_lower:
        knowledge = TCM_KNOWLEDGE["气血"]
        response = f"""## 关于气血的中医理论

### 🫖 气血的基本概念
{knowledge['definition']}

### ⚡ 气血的功能
{knowledge['functions']}

### 📋 气血不足的表现
{knowledge['deficiency']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
        
        sources = [{'source': '内置中医知识库 - 气血', 'content': knowledge['definition'], 'score': 1.0}]
        
    elif "阴阳" in question_lower:
        knowledge = TCM_KNOWLEDGE["阴阳"]
        response = f"""## 关于阴阳的中医理论

### ☯️ 阴阳的基本概念
{knowledge['definition']}

### ⚖️ 阴阳平衡
{knowledge['balance']}

### 🔄 阴阳特性
{knowledge['characteristics']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
        
        sources = [{'source': '内置中医知识库 - 阴阳', 'content': knowledge['definition'], 'score': 1.0}]
        
    elif "四季" in question_lower or "养生" in question_lower:
        knowledge = TCM_KNOWLEDGE["四季养生"]
        response = f"""## 四季养生的中医智慧

### 🌸 春季养生
{knowledge['spring']}

### ☀️ 夏季养生
{knowledge['summer']}

### 🍂 秋季养生
{knowledge['autumn']}

### ❄️ 冬季养生
{knowledge['winter']}

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业医师。"""
        
        sources = [{'source': '内置中医知识库 - 四季养生', 'content': '四季养生的基本原则', 'score': 1.0}]
        
    else:
        response = f"""## 🤖 智能助手回复

感谢您的提问：「{question}」

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试询问关于湿气、气血、阴阳、四季养生等中医基础概念
- 使用更具体的中医术语
- 上传相关的PDF文档来扩充知识库

### 📚 我可以帮您了解：
- 🌿 湿气调理相关知识
- 🫖 气血养生理论  
- ☯️ 阴阳平衡概念
- 🌸 四季养生方法

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。"""
        
        sources = []
    
    return response, sources

# 前端HTML
HTML_CONTENT = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 中医智能助手 - 现代聊天界面</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; overflow: hidden;
        }
        .chat-container {
            height: 100vh; display: flex; flex-direction: column;
            max-width: 1000px; margin: 0 auto; background: white;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }
        .chat-header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 20px; text-align: center;
        }
        .chat-main { flex: 1; display: flex; flex-direction: column; }
        .messages-container {
            flex: 1; overflow-y: auto; padding: 20px; background: #fafafa;
        }
        .message { margin-bottom: 20px; display: flex; align-items: flex-start; }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 70%; padding: 12px 16px; border-radius: 18px;
            word-wrap: break-word;
        }
        .message.user .message-content {
            background: #2E8B57; color: white; border-bottom-right-radius: 4px;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #e9ecef; border-bottom-left-radius: 4px;
        }
        .input-container {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; align-items: flex-end; }
        .input-textarea { flex: 1; }
        .input-textarea textarea {
            width: 100%; padding: 12px; border: 1px solid #ddd;
            border-radius: 8px; resize: none; font-size: 16px;
        }
        .send-btn {
            padding: 12px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 8px; cursor: pointer; font-size: 16px;
        }
        .send-btn:hover { background: #228B22; }
        .send-btn:disabled { background: #ccc; cursor: not-allowed; }
        .quick-actions {
            padding: 15px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;
        }
        .quick-btn {
            margin: 5px; padding: 8px 15px; background: white;
            border: 1px solid #ddd; border-radius: 20px; cursor: pointer;
            display: inline-block; font-size: 14px;
        }
        .quick-btn:hover { background: #e9ecef; }
        .typing { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🏥 中医智能助手</h1>
            <p>现代化聊天界面 · 传承千年智慧</p>
        </div>
        
        <div class="quick-actions">
            <span onclick="sendQuickMessage('湿气重有什么表现？')" class="quick-btn">🌿 湿气问题</span>
            <span onclick="sendQuickMessage('气血不足如何调理？')" class="quick-btn">🫖 气血调理</span>
            <span onclick="sendQuickMessage('什么是阴阳平衡？')" class="quick-btn">☯️ 阴阳平衡</span>
            <span onclick="sendQuickMessage('四季养生要点')" class="quick-btn">🌸 四季养生</span>
        </div>
        
        <div class="chat-main">
            <div class="messages-container" id="messagesContainer">
                <div class="message assistant">
                    <div class="message-content">
                        <strong>🤖 助手:</strong> 您好！我是中医智能助手。您可以：<br>
                        • 点击上方快捷按钮查询常见问题<br>
                        • 直接输入您的中医相关问题<br>
                        • 询问湿气、气血、阴阳、养生等话题<br><br>
                        请问有什么可以帮助您的吗？
                    </div>
                </div>
            </div>
            
            <div class="input-container">
                <div class="input-row">
                    <div class="input-textarea">
                        <textarea id="messageInput" rows="2" placeholder="请输入您想了解的中医知识..." onkeydown="handleKeyDown(event)"></textarea>
                    </div>
                    <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isTyping = false;
        
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在思考中...', 'typing');
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                // 移除加载消息
                removeTypingMessage();
                
                // 显示助手回复
                addMessage('assistant', data.response);
                
            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }
        
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\\n/g, '<br>')}`;
            
            messageDiv.appendChild(contentDiv);
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }
        
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }
    </script>
</body>
</html>
"""

# API路由
@app.get("/")
async def read_root():
    """返回聊天界面"""
    return HTMLResponse(content=HTML_CONTENT)

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "features": ["聊天对话", "中医知识库", "现代界面"]
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """聊天接口"""
    try:
        session_id = chat_message.session_id or str(uuid.uuid4())
        response, sources = generate_response(chat_message.message)
        
        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动现代化中医聊天界面...")
    print("🌐 访问地址: http://localhost:8002")
    print("📚 API文档: http://localhost:8002/docs")
    print("💡 功能: 现代聊天界面 + 中医知识问答")
    
    uvicorn.run(
        "minimal_chat_app:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
