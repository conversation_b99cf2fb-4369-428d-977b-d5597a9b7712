#!/usr/bin/env python3
"""
安装MCP+API+RAG系统依赖
"""

import subprocess
import sys
import os
from pathlib import Path

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("🚀 开始安装MCP+API+RAG系统依赖...")
    
    # 核心依赖
    core_packages = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "requests",
        "numpy",
        "torch",
        "transformers",
        "sentence-transformers",
        "faiss-cpu",
        "scikit-learn",
        "jieba"
    ]
    
    # MCP相关依赖
    mcp_packages = [
        "mcp",  # Model Context Protocol
        "asyncio-mqtt",
        "websockets"
    ]
    
    # Elasticsearch依赖
    elasticsearch_packages = [
        "elasticsearch",
        "elasticsearch-dsl"
    ]
    
    # 模型推理依赖
    inference_packages = [
        "llama-cpp-python",  # GGUF模型支持
        "accelerate",  # GPU加速
        "bitsandbytes"  # 量化支持
    ]
    
    # 可选依赖
    optional_packages = [
        "streamlit",  # Web界面
        "gradio",  # 备用界面
        "plotly",  # 可视化
        "pandas"  # 数据处理
    ]
    
    all_packages = core_packages + mcp_packages + elasticsearch_packages + inference_packages + optional_packages
    
    print(f"\n📦 将安装 {len(all_packages)} 个依赖包...")
    
    failed_packages = []
    
    for package in all_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 报告结果
    print(f"\n📊 安装结果:")
    print(f"✅ 成功: {len(all_packages) - len(failed_packages)}")
    print(f"❌ 失败: {len(failed_packages)}")
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 更新pip: python -m pip install --upgrade pip")
        print("3. 手动安装失败的包")
    else:
        print("\n🎉 所有依赖包安装成功！")
    
    print(f"\n📋 下一步:")
    print("1. 确保Elasticsearch已安装并运行")
    print("2. 下载并放置模型文件到 ./models/ 目录")
    print("3. 运行 python integrated_mcp_rag_system.py 测试系统")

if __name__ == "__main__":
    main()
