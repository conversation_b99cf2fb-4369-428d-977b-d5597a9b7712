#!/usr/bin/env python3
"""
快速测试超级智能系统
"""
import asyncio
from simple_ultimate_tcm import SimpleResponseGenerator, SimpleDocumentProcessor, SimpleOnlineCrawler

async def quick_test():
    """快速测试"""
    print("🚀 快速测试超级智能系统")
    
    # 初始化系统
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    tcm_system = SimpleResponseGenerator(doc_processor, crawler)
    
    # 测试问题
    query = "有什么偏方可以治疗头痛吗"
    print(f"📝 测试问题: {query}")
    
    try:
        response, sources, processing_time = await tcm_system.generate_response(query)
        
        print(f"✅ 处理成功 (耗时: {processing_time:.2f}秒)")
        print(f"📚 来源数量: {len(sources)}条")
        print(f"📝 回答长度: {len(response)}字符")
        
        print("\n🤖 回答内容:")
        print("-" * 50)
        print(response)
        print("-" * 50)
        
        # 检查重复
        import re
        sentences = re.split(r'[。！？；]', response)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        unique_sentences = set(sentences)
        duplicate_count = len(sentences) - len(unique_sentences)
        
        if duplicate_count > 0:
            print(f"❌ 发现 {duplicate_count} 处重复内容")
            print("重复的句子:")
            seen = set()
            for sentence in sentences:
                if sentence in seen:
                    print(f"  - {sentence}")
                else:
                    seen.add(sentence)
        else:
            print("✅ 无重复内容")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(quick_test())
