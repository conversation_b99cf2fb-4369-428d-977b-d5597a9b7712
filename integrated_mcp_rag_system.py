#!/usr/bin/env python3
"""
集成MCP+API+RAG系统
本地模型API + Elasticsearch MCP服务 + RAG向量数据库的完美集成
"""

import asyncio
import json
import logging
import requests
from typing import Dict, List, Any, Optional
from pathlib import Path
import subprocess
import time

# MCP客户端导入
try:
    from mcp.client import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_CLIENT_AVAILABLE = True
except ImportError:
    MCP_CLIENT_AVAILABLE = False
    print("⚠️ MCP客户端未安装，请运行: pip install mcp")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntegratedMCPRAGSystem:
    """集成MCP+API+RAG系统"""
    
    def __init__(self):
        self.local_api_url = "http://127.0.0.1:8002"
        self.mcp_session = None
        self.mcp_server_process = None
        self.api_server_process = None
        self.initialized = False
        
        # 系统配置 - 使用用户的实际配置
        self.config = {
            'default_chat_model': 'qwen-7b',  # 使用找到的Qwen模型
            'default_embedding_model': 'bge-m3',  # 使用BGE-M3中文嵌入模型
            'elasticsearch_index': 'tcm_knowledge',
            'max_search_results': 10,
            'api_timeout': 30,
            'github_repo': 'https://github.com/BillHCM7777779/gudaiyishu'  # 用户的GitHub仓库
        }

        # GitHub检索器
        self.github_retriever = None
    
    async def initialize(self) -> bool:
        """初始化整个系统"""
        try:
            logger.info("🚀 初始化集成MCP+API+RAG系统...")
            
            # 1. 启动本地模型API服务器
            if not await self._start_api_server():
                logger.error("❌ API服务器启动失败")
                return False
            
            # 2. 启动Elasticsearch MCP服务器
            if not await self._start_mcp_server():
                logger.error("❌ MCP服务器启动失败")
                return False
            
            # 3. 连接MCP客户端
            if not await self._connect_mcp_client():
                logger.error("❌ MCP客户端连接失败")
                return False
            
            # 4. 初始化GitHub检索器
            if not await self._initialize_github_retriever():
                logger.warning("⚠️ GitHub检索器初始化失败，将跳过线上检索")

            # 5. 测试系统连接
            if not await self._test_system_connectivity():
                logger.error("❌ 系统连接测试失败")
                return False

            self.initialized = True
            logger.info("✅ 集成系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    async def _start_api_server(self) -> bool:
        """启动本地模型API服务器"""
        try:
            logger.info("🔄 启动本地模型API服务器...")
            
            # 检查服务器是否已经运行
            try:
                response = requests.get(f"{self.local_api_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ API服务器已在运行")
                    return True
            except:
                pass
            
            # 启动API服务器
            self.api_server_process = subprocess.Popen([
                "python", "local_model_api_server.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务器启动
            for _ in range(30):  # 等待30秒
                try:
                    response = requests.get(f"{self.local_api_url}/health", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ API服务器启动成功")
                        return True
                except:
                    pass
                await asyncio.sleep(1)
            
            logger.error("❌ API服务器启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ API服务器启动失败: {e}")
            return False
    
    async def _start_mcp_server(self) -> bool:
        """启动Elasticsearch MCP服务器"""
        try:
            if not MCP_CLIENT_AVAILABLE:
                logger.error("❌ MCP客户端不可用")
                return False
            
            logger.info("🔄 启动Elasticsearch MCP服务器...")
            
            # 启动MCP服务器进程
            self.mcp_server_process = subprocess.Popen([
                "python", "elasticsearch_mcp_server.py"
            ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待一下让服务器初始化
            await asyncio.sleep(3)
            
            logger.info("✅ MCP服务器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ MCP服务器启动失败: {e}")
            return False

    async def _initialize_github_retriever(self) -> bool:
        """初始化GitHub检索器"""
        try:
            logger.info("🔄 初始化GitHub检索器...")

            from github_knowledge_retriever import GitHubKnowledgeRetriever

            self.github_retriever = GitHubKnowledgeRetriever(self.config['github_repo'])

            if self.github_retriever.initialize():
                logger.info("✅ GitHub检索器初始化成功")
                return True
            else:
                logger.error("❌ GitHub检索器初始化失败")
                return False

        except Exception as e:
            logger.error(f"❌ GitHub检索器初始化异常: {e}")
            return False

    async def _connect_mcp_client(self) -> bool:
        """连接MCP客户端"""
        try:
            if not MCP_CLIENT_AVAILABLE:
                return False
            
            logger.info("🔄 连接MCP客户端...")
            
            # 创建MCP客户端会话
            server_params = StdioServerParameters(
                command="python",
                args=["elasticsearch_mcp_server.py"]
            )
            
            self.mcp_session = await stdio_client(server_params)
            
            # 初始化会话
            await self.mcp_session.initialize()
            
            logger.info("✅ MCP客户端连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ MCP客户端连接失败: {e}")
            return False
    
    async def _test_system_connectivity(self) -> bool:
        """测试系统连接"""
        try:
            logger.info("🧪 测试系统连接...")
            
            # 测试API服务器
            api_response = requests.get(f"{self.local_api_url}/v1/models", timeout=5)
            if api_response.status_code != 200:
                logger.error("❌ API服务器连接测试失败")
                return False
            
            # 测试MCP服务器
            if self.mcp_session:
                try:
                    tools = await self.mcp_session.list_tools()
                    logger.info(f"✅ MCP工具列表: {[tool.name for tool in tools]}")
                except Exception as e:
                    logger.warning(f"⚠️ MCP工具列表获取失败: {e}")
            
            logger.info("✅ 系统连接测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统连接测试失败: {e}")
            return False
    
    async def intelligent_search(self, query: str, use_mcp: bool = True, use_api: bool = True) -> Dict[str, Any]:
        """智能搜索 - 集成MCP和API"""
        try:
            if not self.initialized:
                raise Exception("系统未初始化")
            
            logger.info(f"🔍 开始智能搜索: {query}")
            
            results = {
                'query': query,
                'mcp_results': [],
                'api_embeddings': [],
                'github_results': [],
                'combined_results': [],
                'summary': {}
            }
            
            # 1. 使用MCP进行Elasticsearch搜索
            if use_mcp and self.mcp_session:
                try:
                    mcp_result = await self.mcp_session.call_tool(
                        "search_elasticsearch",
                        {
                            "query": query,
                            "size": self.config['max_search_results'],
                            "index": self.config['elasticsearch_index']
                        }
                    )
                    
                    if mcp_result.content:
                        mcp_data = json.loads(mcp_result.content[0].text)
                        results['mcp_results'] = mcp_data
                        logger.info(f"✅ MCP搜索完成: {len(mcp_data)} 个结果")
                    
                except Exception as e:
                    logger.error(f"❌ MCP搜索失败: {e}")
            
            # 2. 使用API生成查询嵌入
            if use_api:
                try:
                    embedding_response = requests.post(
                        f"{self.local_api_url}/v1/embeddings",
                        json={
                            "model": self.config['default_embedding_model'],
                            "input": [query]
                        },
                        timeout=self.config['api_timeout']
                    )
                    
                    if embedding_response.status_code == 200:
                        embedding_data = embedding_response.json()
                        results['api_embeddings'] = embedding_data['data']
                        logger.info("✅ API嵌入生成完成")
                    
                except Exception as e:
                    logger.error(f"❌ API嵌入生成失败: {e}")

            # 3. 使用GitHub检索线上知识
            if self.github_retriever:
                try:
                    github_results = self.github_retriever.search_repository(
                        query, max_results=self.config['max_search_results'] // 2
                    )

                    if github_results:
                        # 获取增强结果（包含文件内容）
                        enhanced_github_results = self.github_retriever.get_enhanced_results(github_results)
                        results['github_results'] = enhanced_github_results
                        logger.info(f"✅ GitHub检索完成: {len(enhanced_github_results)} 个结果")
                    else:
                        logger.info("ℹ️ GitHub检索无匹配结果")

                except Exception as e:
                    logger.error(f"❌ GitHub检索失败: {e}")

            # 4. 结合所有结果
            results['combined_results'] = self._combine_search_results(
                results['mcp_results'],
                results['api_embeddings'],
                results['github_results']
            )
            
            # 5. 生成摘要
            results['summary'] = {
                'total_mcp_results': len(results['mcp_results']),
                'total_github_results': len(results['github_results']),
                'embedding_dimension': len(results['api_embeddings'][0]['embedding']) if results['api_embeddings'] else 0,
                'combined_score': self._calculate_combined_score(results['combined_results']),
                'search_quality': 'excellent' if len(results['combined_results']) > 5 else 'good' if len(results['combined_results']) > 0 else 'poor',
                'github_repo': self.config['github_repo']
            }
            
            logger.info(f"🎯 智能搜索完成: {results['summary']}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 智能搜索失败: {e}")
            raise
    
    async def generate_intelligent_response(self, query: str, search_results: Dict[str, Any]) -> str:
        """生成智能回答 - 严格基于数据库+MCP+API聚合的最佳答案"""
        try:
            logger.info("🧠 生成基于数据库+MCP+API聚合的最佳答案...")

            # 严格验证检索结果来源
            mcp_results = search_results.get('mcp_results', [])
            github_results = search_results.get('github_results', [])
            combined_results = search_results.get('combined_results', [])

            if not combined_results and not mcp_results and not github_results:
                return "抱歉，未从数据库、MCP服务或API检索到相关资料，无法提供准确答案。"

            # 构建严格的上下文 - 只使用检索到的资料
            context = self._build_strict_context_from_results(search_results)

            # 构建严格的提示词 - 强调只基于检索结果回答
            messages = [
                {
                    "role": "system",
                    "content": """你是专业的中医智能助手。严格要求：
1. 只能基于提供的检索资料回答问题
2. 不得添加任何未在资料中提及的内容
3. 如果资料不足以回答问题，明确说明
4. 必须标注信息来源（数据库/MCP/GitHub）
5. 严禁输出无关或推测性内容"""
                },
                {
                    "role": "user",
                    "content": f"""问题: {query}

数据库+MCP+API检索到的资料:
{context}

严格要求：请仅基于以上检索资料回答问题，不得添加任何资料外的内容。如果资料不足，请明确说明。"""
                }
            ]

            # 调用本地Qwen模型API
            response = requests.post(
                f"{self.local_api_url}/v1/chat/completions",
                json={
                    "model": self.config['default_chat_model'],
                    "messages": messages,
                    "temperature": 0.3,  # 降低温度，减少创造性
                    "max_tokens": 2048
                },
                timeout=self.config['api_timeout']
            )

            if response.status_code == 200:
                response_data = response.json()
                answer = response_data['choices'][0]['message']['content']

                # 添加来源标注
                source_summary = self._generate_source_summary(search_results)
                final_answer = f"{answer}\n\n{source_summary}"

                logger.info("✅ 基于数据库+MCP+API的最佳答案生成完成")
                return final_answer
            else:
                logger.error(f"❌ API调用失败: {response.status_code}")
                return "抱歉，Qwen模型API调用失败，无法生成答案。"

        except Exception as e:
            logger.error(f"❌ 智能回答生成失败: {e}")
            return f"系统错误：{str(e)}"
    
    async def index_documents_to_elasticsearch(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """将文档索引到Elasticsearch"""
        try:
            if not self.mcp_session:
                raise Exception("MCP会话未建立")
            
            logger.info(f"📥 开始索引 {len(documents)} 个文档...")
            
            results = {
                'total_documents': len(documents),
                'successful_indexes': 0,
                'failed_indexes': 0,
                'errors': []
            }
            
            for i, doc in enumerate(documents):
                try:
                    result = await self.mcp_session.call_tool(
                        "index_document",
                        {
                            "document": doc,
                            "index": self.config['elasticsearch_index']
                        }
                    )
                    
                    if result.content:
                        results['successful_indexes'] += 1
                        logger.info(f"✅ 文档 {i+1} 索引成功")
                    
                except Exception as e:
                    results['failed_indexes'] += 1
                    results['errors'].append(f"文档 {i+1}: {str(e)}")
                    logger.error(f"❌ 文档 {i+1} 索引失败: {e}")
            
            logger.info(f"📊 索引完成: 成功 {results['successful_indexes']}, 失败 {results['failed_indexes']}")
            return results
            
        except Exception as e:
            logger.error(f"❌ 文档索引失败: {e}")
            raise
    
    def _combine_search_results(self, mcp_results: List[Dict], api_embeddings: List[Dict], github_results: List[Dict] = None) -> List[Dict]:
        """结合搜索结果"""
        combined = []

        # 添加MCP结果
        for result in mcp_results:
            combined_result = {
                'id': result.get('id'),
                'score': result.get('score', 0),
                'content': result.get('source', {}).get('content', ''),
                'title': result.get('source', {}).get('title', ''),
                'source': result.get('source', {}).get('source', ''),
                'method': 'elasticsearch_mcp',
                'has_embedding': len(api_embeddings) > 0
            }
            combined.append(combined_result)

        # 添加GitHub结果
        if github_results:
            for result in github_results:
                combined_result = {
                    'id': result.get('path', ''),
                    'score': result.get('relevance_score', 0),
                    'content': result.get('content', ''),
                    'title': result.get('name', ''),
                    'source': f"GitHub: {result.get('path', '')}",
                    'method': 'github_retrieval',
                    'url': result.get('url', ''),
                    'has_embedding': len(api_embeddings) > 0
                }
                combined.append(combined_result)

        return combined
    
    def _calculate_combined_score(self, results: List[Dict]) -> float:
        """计算综合分数"""
        if not results:
            return 0.0
        
        total_score = sum(result.get('score', 0) for result in results)
        return total_score / len(results)
    
    def _build_strict_context_from_results(self, search_results: Dict[str, Any]) -> str:
        """严格构建上下文 - 只使用检索到的资料"""
        context_parts = []

        # 1. MCP数据库结果
        mcp_results = search_results.get('mcp_results', [])
        if mcp_results:
            context_parts.append("【数据库检索结果】")
            for i, result in enumerate(mcp_results[:3], 1):
                source_data = result.get('source', {})
                content = source_data.get('content', '')
                title = source_data.get('title', '无标题')
                score = result.get('score', 0)

                context_parts.append(f"""
{i}. 标题: {title}
内容: {content[:500]}
相关度: {score:.3f}
来源: Elasticsearch数据库
""")

        # 2. GitHub检索结果
        github_results = search_results.get('github_results', [])
        if github_results:
            context_parts.append("\n【GitHub古代医术检索结果】")
            for i, result in enumerate(github_results[:3], 1):
                content = result.get('content', '')
                name = result.get('name', '无标题')
                path = result.get('path', '')
                score = result.get('relevance_score', 0)

                context_parts.append(f"""
{i}. 文件: {name}
路径: {path}
内容: {content[:500]}
相关度: {score:.3f}
来源: GitHub古代医术仓库
""")

        # 3. API嵌入结果
        api_embeddings = search_results.get('api_embeddings', [])
        if api_embeddings:
            context_parts.append(f"\n【API嵌入分析】")
            context_parts.append(f"查询向量维度: {len(api_embeddings[0]['embedding'])}")
            context_parts.append("来源: BGE-M3中文嵌入模型")

        if not context_parts:
            return "未检索到任何相关资料。"

        return "\n".join(context_parts)

    def _generate_source_summary(self, search_results: Dict[str, Any]) -> str:
        """生成来源摘要"""
        summary_parts = ["📊 信息来源摘要:"]

        mcp_count = len(search_results.get('mcp_results', []))
        github_count = len(search_results.get('github_results', []))
        api_embedding = search_results.get('api_embeddings', [])

        if mcp_count > 0:
            summary_parts.append(f"• Elasticsearch数据库: {mcp_count} 条记录")

        if github_count > 0:
            summary_parts.append(f"• GitHub古代医术: {github_count} 条记录")

        if api_embedding:
            summary_parts.append(f"• BGE-M3嵌入分析: 已完成")

        summary_parts.append(f"• 检索仓库: {self.config['github_repo']}")

        return "\n".join(summary_parts)

    def _build_context_from_results(self, search_results: Dict[str, Any]) -> str:
        """从搜索结果构建上下文（保留原方法兼容性）"""
        return self._build_strict_context_from_results(search_results)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'initialized': self.initialized,
            'api_server': False,
            'mcp_server': False,
            'elasticsearch': False,
            'available_models': [],
            'elasticsearch_stats': {}
        }
        
        try:
            # 检查API服务器
            api_response = requests.get(f"{self.local_api_url}/health", timeout=5)
            if api_response.status_code == 200:
                status['api_server'] = True
                models_response = requests.get(f"{self.local_api_url}/v1/models", timeout=5)
                if models_response.status_code == 200:
                    status['available_models'] = [model['id'] for model in models_response.json()['data']]
        except:
            pass
        
        try:
            # 检查MCP和Elasticsearch
            if self.mcp_session:
                status['mcp_server'] = True
                es_stats = await self.mcp_session.call_tool("get_index_stats", {})
                if es_stats.content:
                    status['elasticsearch'] = True
                    status['elasticsearch_stats'] = json.loads(es_stats.content[0].text)
        except:
            pass
        
        return status
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 清理系统资源...")
            
            # 关闭MCP会话
            if self.mcp_session:
                await self.mcp_session.close()
            
            # 终止进程
            if self.mcp_server_process:
                self.mcp_server_process.terminate()
            
            if self.api_server_process:
                self.api_server_process.terminate()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 资源清理失败: {e}")

# 全局系统实例
integrated_system = IntegratedMCPRAGSystem()

async def main():
    """主函数 - 演示系统使用"""
    try:
        # 初始化系统
        if not await integrated_system.initialize():
            print("❌ 系统初始化失败")
            return
        
        # 获取系统状态
        status = await integrated_system.get_system_status()
        print(f"📊 系统状态: {json.dumps(status, ensure_ascii=False, indent=2)}")
        
        # 演示搜索
        query = "肾虚脾虚怎么治疗"
        search_results = await integrated_system.intelligent_search(query)
        print(f"🔍 搜索结果: {json.dumps(search_results['summary'], ensure_ascii=False, indent=2)}")
        
        # 生成回答
        answer = await integrated_system.generate_intelligent_response(query, search_results)
        print(f"🧠 智能回答: {answer}")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 系统运行失败: {e}")
    finally:
        await integrated_system.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
