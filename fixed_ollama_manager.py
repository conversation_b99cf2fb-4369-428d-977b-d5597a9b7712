#!/usr/bin/env python3
"""
修复版Ollama管理器
使用正确的Ollama路径和环境
"""

import requests
import subprocess
import time
import os
import streamlit as st

class FixedOllamaManager:
    """修复版Ollama管理器"""
    
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.model_name = "deepseek-r1:8b"
        self.ollama_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe"
        self.initialized = False
        
    def check_ollama_running(self):
        """检查Ollama API是否运行"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def check_model_available(self):
        """检查模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                for model in models:
                    model_name = model.get('name', '')
                    if 'deepseek-r1' in model_name.lower() or 'deepseek' in model_name.lower():
                        return True, model_name
                        
                return False, None
            return False, None
        except Exception as e:
            st.error(f"检查模型失败: {e}")
            return False, None
    
    def pull_deepseek_model(self):
        """拉取DeepSeek模型"""
        st.info("📥 正在下载DeepSeek-R1模型...")
        st.info("💡 这可能需要几分钟时间，请耐心等待")
        
        try:
            # 使用完整路径调用ollama
            cmd = [self.ollama_exe, "pull", self.model_name]
            
            st.info(f"执行命令: {' '.join(cmd)}")
            
            # 创建进度显示
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 启动下载进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 监控进度
            progress = 0
            while process.poll() is None:
                progress = min(progress + 0.01, 0.9)
                progress_bar.progress(progress)
                status_text.text(f"下载中... {progress*100:.1f}%")
                time.sleep(1)
            
            # 等待完成
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                progress_bar.progress(1.0)
                status_text.text("下载完成!")
                st.success("✅ DeepSeek-R1模型下载完成!")
                return True
            else:
                st.error(f"❌ 模型下载失败")
                st.error(f"错误信息: {stderr}")
                return False
                
        except Exception as e:
            st.error(f"下载异常: {e}")
            return False
    
    def test_model(self):
        """测试模型"""
        try:
            # 使用requests直接调用Ollama API
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": "你好，请简单介绍一下中医。",
                    "stream": False,
                    "options": {
                        "num_predict": 50
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                if content:
                    return True, content
                else:
                    return False, "模型返回空内容"
            else:
                return False, f"API调用失败: {response.status_code}"
                
        except Exception as e:
            return False, f"测试异常: {e}"
    
    def initialize(self):
        """初始化Ollama和DeepSeek模型"""
        st.info("🤖 初始化Ollama DeepSeek引擎...")
        
        # 1. 检查Ollama API
        if not self.check_ollama_running():
            st.error("❌ Ollama API不可用")
            st.info("💡 请确保Ollama服务正在运行")
            return False
        
        st.success("✅ Ollama API运行中")
        
        # 2. 检查模型
        model_available, model_name = self.check_model_available()
        if not model_available:
            st.info("📥 DeepSeek模型未找到，开始下载...")
            if not self.pull_deepseek_model():
                st.error("❌ 模型下载失败")
                return False
            
            # 重新检查
            model_available, model_name = self.check_model_available()
            if model_available:
                self.model_name = model_name
                st.success(f"✅ 模型下载完成: {model_name}")
            else:
                st.error("❌ 模型下载后仍未找到")
                return False
        else:
            self.model_name = model_name
            st.success(f"✅ 找到模型: {model_name}")
        
        # 3. 测试模型
        st.info("🧪 测试模型...")
        success, result = self.test_model()
        if success:
            st.success("✅ DeepSeek-R1模型测试通过!")
            st.info(f"🎯 测试回答: {result[:100]}...")
            self.initialized = True
            return True
        else:
            st.error(f"❌ 模型测试失败: {result}")
            return False
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成回答"""
        if not self.initialized:
            return "DeepSeek模型未初始化"
        
        try:
            st.info("🧠 DeepSeek-R1正在思考...")
            
            # 构建中医专业提示词
            system_prompt = "你是一位专业的中医医生，请用专业、温和的语气回答用户的中医相关问题。"
            full_prompt = f"{system_prompt}\n\n用户问题: {prompt}\n\n回答:"
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens
                    }
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                
                if content:
                    # 清理回答
                    content = self._clean_response(content)
                    st.success("✅ DeepSeek-R1生成完成")
                    return content
                else:
                    return "DeepSeek生成为空，请重试"
            else:
                return f"生成失败: {response.status_code}"
                
        except Exception as e:
            return f"生成异常: {str(e)}"
    
    def _clean_response(self, text: str) -> str:
        """清理生成的回答"""
        import re
        
        # 移除多余的换行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 移除可能的提示词残留
        text = re.sub(r'^(用户|助手|Human|Assistant|回答|问题)[:：]\s*', '', text, flags=re.MULTILINE)
        
        return text.strip()

def main():
    """测试函数"""
    manager = FixedOllamaManager()
    
    print("🤖 修复版Ollama DeepSeek管理器测试")
    print("=" * 40)
    
    if manager.initialize():
        print("✅ 初始化成功!")
        
        # 测试生成
        response = manager.generate_response("什么是中医？", max_tokens=100)
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
