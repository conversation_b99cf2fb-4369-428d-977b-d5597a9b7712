#!/usr/bin/env python3
"""
测试PDF上传和检索功能
"""
import requests
import time

def test_pdf_upload():
    """测试PDF上传功能"""
    print("🧪 测试PDF上传和检索功能")
    print("=" * 50)
    
    # 1. 检查服务器状态
    try:
        response = requests.get('http://localhost:8006/api/health', 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器运行正常")
            print(f"📊 当前文档数: {data.get('documents', 0)}")
        else:
            print("❌ 服务器响应异常")
            return
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 2. 上传测试PDF
    print("\n📤 上传测试PDF文档...")
    
    try:
        # 使用之前创建的PDF文件
        with open('中医诊疗指南.pdf', 'rb') as f:
            files = {'files': ('中医诊疗指南.pdf', f, 'application/pdf')}
            
            response = requests.post(
                'http://localhost:8006/api/upload',
                files=files,
                auth=('tcm_user', 'MVP168918'),
                timeout=30
            )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ PDF上传成功！")
            print(f"📊 处理结果: {data}")
            
            # 检查处理结果
            for result in data.get('results', []):
                if result['status'] == 'success':
                    print(f"   ✅ {result['filename']}: {result['chunks']} 个文档块")
                else:
                    print(f"   ❌ {result['filename']}: {result.get('error', '未知错误')}")
        else:
            print(f"❌ PDF上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return
            
    except Exception as e:
        print(f"❌ PDF上传异常: {e}")
        return
    
    # 3. 等待处理完成
    print("\n⏳ 等待文档处理...")
    time.sleep(3)
    
    # 4. 检查文档数量
    try:
        response = requests.get('http://localhost:8006/api/health', 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            doc_count = data.get('documents', 0)
            print(f"📊 处理后文档数: {doc_count}")
            
            if doc_count > 0:
                print("🎉 PDF文档已成功加载到系统中！")
            else:
                print("⚠️ PDF文档未被正确处理")
        
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
    
    # 5. 测试PDF检索
    print("\n🔍 测试PDF检索功能...")
    
    try:
        payload = {
            "message": "小儿鼻塞的中医治疗方法"
        }
        
        response = requests.post(
            'http://localhost:8006/api/chat',
            json=payload,
            auth=('tcm_user', 'MVP168918'),
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            sources = data.get('sources', [])
            
            # 检查PDF来源
            pdf_sources = [s for s in sources if s.get('type') == 'pdf' or 'pdf' in s.get('source', '').lower()]
            online_sources = [s for s in sources if s.get('type') == 'online']
            
            print(f"📚 总来源数: {len(sources)}")
            print(f"📄 PDF来源数: {len(pdf_sources)}")
            print(f"🌐 在线来源数: {len(online_sources)}")
            
            if pdf_sources:
                print("🎉 PDF检索功能已成功启用！")
                print("📄 PDF检索结果:")
                for i, source in enumerate(pdf_sources[:3], 1):
                    print(f"   {i}. {source.get('source', 'unknown')} (评分: {source.get('score', 0):.3f})")
                    print(f"      内容预览: {source.get('content', '')[:100]}...")
            else:
                print("⚠️ PDF检索功能未检测到结果")
                
            if online_sources:
                print("🌐 在线检索结果:")
                for i, source in enumerate(online_sources[:2], 1):
                    print(f"   {i}. {source.get('source', 'unknown')} (评分: {source.get('score', 0):.3f})")
            
            # 显示回答
            response_text = data.get('response', '')
            print(f"\n💬 系统回答 ({len(response_text)}字符):")
            print(response_text[:300] + "..." if len(response_text) > 300 else response_text)
            
        else:
            print(f"❌ 检索测试失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ PDF检索测试异常: {e}")

if __name__ == "__main__":
    test_pdf_upload()
