#!/usr/bin/env python3
"""
安装语音功能依赖
包括语音输入和语音输出功能
"""

import subprocess
import sys
import os
import platform

def print_voice_banner():
    """打印语音功能横幅"""
    print("=" * 80)
    print("🔊 语音功能依赖安装器")
    print("=" * 80)
    print("📦 将安装以下语音功能:")
    print("   🔊 文本转语音 (pyttsx3)")
    print("   🎤 语音识别 (SpeechRecognition)")
    print("   🎵 音频处理 (PyAudio)")
    print("   🌐 在线语音服务支持")
    print("=" * 80)

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"📥 安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def install_voice_output():
    """安装语音输出功能"""
    print("\n🔊 安装语音输出功能...")
    
    # 安装pyttsx3
    success = install_package("pyttsx3", "文本转语音引擎")
    
    if success:
        # 测试语音输出
        try:
            import pyttsx3
            engine = pyttsx3.init()
            print("✅ 语音输出功能测试成功")
            return True
        except Exception as e:
            print(f"⚠️ 语音输出功能测试失败: {e}")
            return False
    
    return False

def install_voice_input():
    """安装语音输入功能"""
    print("\n🎤 安装语音输入功能...")
    
    # 安装SpeechRecognition
    speech_success = install_package("SpeechRecognition", "语音识别库")
    
    # 安装PyAudio (可能需要特殊处理)
    audio_success = install_pyaudio()
    
    if speech_success:
        # 测试语音识别
        try:
            import speech_recognition as sr
            r = sr.Recognizer()
            print("✅ 语音识别库测试成功")
            
            if audio_success:
                try:
                    m = sr.Microphone()
                    print("✅ 麦克风访问测试成功")
                    return True
                except Exception as e:
                    print(f"⚠️ 麦克风访问测试失败: {e}")
                    print("💡 语音识别可用，但可能需要手动配置麦克风")
                    return True
            else:
                print("⚠️ PyAudio安装失败，语音输入功能受限")
                return True
                
        except Exception as e:
            print(f"⚠️ 语音识别测试失败: {e}")
            return False
    
    return False

def install_pyaudio():
    """安装PyAudio - 需要特殊处理"""
    print("🎵 安装音频处理库...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # Windows系统
        print("🪟 检测到Windows系统")
        
        # 尝试直接安装
        if install_package("PyAudio", "音频输入输出"):
            return True
        
        # 如果失败，尝试安装预编译版本
        print("💡 尝试安装预编译版本...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "https://download.lfd.uci.edu/pythonlibs/archived/PyAudio-0.2.11-cp39-cp39-win_amd64.whl"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ PyAudio预编译版本安装成功")
                return True
        except:
            pass
        
        print("⚠️ PyAudio安装失败，请手动安装:")
        print("   1. 访问 https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
        print("   2. 下载适合您Python版本的whl文件")
        print("   3. 运行: pip install 下载的文件.whl")
        return False
        
    elif system == "darwin":
        # macOS系统
        print("🍎 检测到macOS系统")
        
        # 检查是否安装了Homebrew
        try:
            subprocess.run(["brew", "--version"], capture_output=True, check=True)
            print("✅ 检测到Homebrew")
            
            # 安装portaudio
            print("📦 安装portaudio依赖...")
            subprocess.run(["brew", "install", "portaudio"], capture_output=True)
            
            # 安装PyAudio
            return install_package("PyAudio", "音频输入输出")
            
        except subprocess.CalledProcessError:
            print("⚠️ 未检测到Homebrew，请先安装:")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
            
    elif system == "linux":
        # Linux系统
        print("🐧 检测到Linux系统")
        
        print("💡 请先安装系统依赖:")
        print("   Ubuntu/Debian: sudo apt-get install portaudio19-dev python3-pyaudio")
        print("   CentOS/RHEL: sudo yum install portaudio-devel")
        print("   Fedora: sudo dnf install portaudio-devel")
        
        # 尝试安装
        return install_package("PyAudio", "音频输入输出")
    
    else:
        print(f"⚠️ 未知系统: {system}")
        return install_package("PyAudio", "音频输入输出")

def test_voice_functions():
    """测试语音功能"""
    print("\n🧪 测试语音功能...")
    
    # 测试语音输出
    try:
        import pyttsx3
        engine = pyttsx3.init()
        print("✅ 语音输出功能可用")
        voice_output_available = True
    except Exception as e:
        print(f"❌ 语音输出功能不可用: {e}")
        voice_output_available = False
    
    # 测试语音输入
    try:
        import speech_recognition as sr
        r = sr.Recognizer()
        print("✅ 语音识别库可用")
        
        try:
            m = sr.Microphone()
            print("✅ 麦克风访问可用")
            voice_input_available = True
        except Exception as e:
            print(f"⚠️ 麦克风访问受限: {e}")
            voice_input_available = False
            
    except Exception as e:
        print(f"❌ 语音识别功能不可用: {e}")
        voice_input_available = False
    
    return voice_output_available, voice_input_available

def create_voice_test_script():
    """创建语音功能测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
语音功能测试脚本
"""

def test_voice_output():
    """测试语音输出"""
    try:
        import pyttsx3
        engine = pyttsx3.init()
        engine.say("语音输出功能正常")
        engine.runAndWait()
        print("✅ 语音输出测试成功")
        return True
    except Exception as e:
        print(f"❌ 语音输出测试失败: {e}")
        return False

def test_voice_input():
    """测试语音输入"""
    try:
        import speech_recognition as sr
        r = sr.Recognizer()
        m = sr.Microphone()
        
        print("🎤 请说话...")
        with m as source:
            r.adjust_for_ambient_noise(source)
            audio = r.listen(source, timeout=5, phrase_time_limit=5)
        
        print("🔄 正在识别...")
        text = r.recognize_google(audio, language='zh-CN')
        print(f"✅ 识别结果: {text}")
        return True
        
    except Exception as e:
        print(f"❌ 语音输入测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔊 语音功能测试")
    print("=" * 40)
    
    print("\\n1. 测试语音输出...")
    test_voice_output()
    
    print("\\n2. 测试语音输入...")
    test_voice_input()
    
    print("\\n测试完成！")
'''
    
    with open("test_voice_functions.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("📝 语音测试脚本已创建: test_voice_functions.py")

def main():
    """主函数"""
    print_voice_banner()
    
    print("🚀 开始安装语音功能依赖...")
    
    # 1. 安装语音输出
    voice_output_success = install_voice_output()
    
    # 2. 安装语音输入
    voice_input_success = install_voice_input()
    
    # 3. 测试功能
    voice_output_available, voice_input_available = test_voice_functions()
    
    # 4. 创建测试脚本
    create_voice_test_script()
    
    # 5. 总结
    print("\n" + "=" * 80)
    print("📊 安装结果总结:")
    print(f"   🔊 语音输出: {'✅ 可用' if voice_output_available else '❌ 不可用'}")
    print(f"   🎤 语音输入: {'✅ 可用' if voice_input_available else '❌ 不可用'}")
    
    if voice_output_available and voice_input_available:
        print("\n🎉 语音功能完全可用！")
        print("💡 现在可以在优化版系统中使用语音对话功能")
    elif voice_output_available:
        print("\n⚠️ 仅语音输出可用")
        print("💡 可以听到系统回答，但无法语音输入")
    elif voice_input_available:
        print("\n⚠️ 仅语音输入可用")
        print("💡 可以语音提问，但无法听到回答")
    else:
        print("\n❌ 语音功能不可用")
        print("💡 请检查错误信息并手动安装相关依赖")
    
    print("\n🧪 测试语音功能:")
    print("   python test_voice_functions.py")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
