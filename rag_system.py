"""
RAG系统核心 - 整合检索和生成功能
"""
import json
from typing import List, Dict, Generator
from models.model_manager import model_manager
from document_processor import doc_processor
from session_manager import session_manager
import config

class RAGSystem:
    def __init__(self):
        self.initialized = False
    
    def initialize(self):
        """初始化RAG系统"""
        if self.initialized:
            return True
        
        print("正在初始化RAG系统...")
        
        # 初始化模型
        if not model_manager.initialize_models():
            print("模型初始化失败")
            return False
        
        # 尝试加载已有的向量索引
        if not doc_processor.load_index():
            print("未找到已有索引，需要先处理文档")
        
        # 初始化会话管理器
        session_manager.initialize()
        
        self.initialized = True
        print("RAG系统初始化完成！")
        return True
    
    def process_documents(self, pdf_files: List[str]) -> bool:
        """处理文档并建立索引"""
        return doc_processor.process_documents(pdf_files)
    
    def create_context_prompt(self, query: str, retrieved_docs: List[Dict], history: List[Dict] = None) -> str:
        """创建包含上下文的提示"""
        # 构建检索到的文档上下文
        context_parts = []
        for doc in retrieved_docs:
            context_parts.append(f"文档片段：{doc['content']}")
        
        context = "\n\n".join(context_parts)
        
        # 构建历史对话上下文
        history_context = ""
        if history:
            history_parts = []
            for item in history[-3:]:  # 只取最近3轮对话
                history_parts.append(f"用户：{item['question']}")
                history_parts.append(f"助手：{item['answer']}")
            history_context = "\n".join(history_parts)
        
        # 构建完整提示
        prompt = f"""你是一个专业的中医知识助手，专门回答关于《黄帝内经》和《伤寒论》的问题。

参考文档：
{context}

{f"历史对话：{history_context}" if history_context else ""}

用户问题：{query}

请基于提供的文档内容回答问题，如果文档中没有相关信息，请说明并提供你的专业建议。回答要准确、专业且易懂。

回答："""
        
        return prompt
    
    def retrieve_and_generate(self, query: str, session_id: str = None) -> Dict:
        """检索相关文档并生成回答"""
        if not self.initialized:
            return {"error": "系统未初始化"}
        
        try:
            # 检索相关文档
            retrieved_docs = doc_processor.search_similar_chunks(query)
            
            if not retrieved_docs:
                return {
                    "answer": "抱歉，我在文档中没有找到相关信息。请尝试重新表述您的问题。",
                    "sources": [],
                    "session_id": session_id
                }
            
            # 获取历史对话
            history = []
            if session_id:
                history = session_manager.get_session_history(session_id)
            
            # 创建提示
            prompt = self.create_context_prompt(query, retrieved_docs, history)
            
            # 生成回答
            answer = model_manager.generate_response(prompt, max_length=1024)
            
            # 保存到会话历史
            if session_id:
                session_manager.add_to_history(session_id, query, answer)
            
            # 准备源文档信息
            sources = []
            for doc in retrieved_docs:
                sources.append({
                    "source": doc["source"],
                    "content": doc["content"][:200] + "..." if len(doc["content"]) > 200 else doc["content"],
                    "similarity": doc["similarity_score"]
                })
            
            return {
                "answer": answer,
                "sources": sources,
                "session_id": session_id,
                "retrieved_count": len(retrieved_docs)
            }
            
        except Exception as e:
            print(f"RAG处理错误: {e}")
            return {"error": f"处理请求时出错: {str(e)}"}
    
    def stream_generate(self, query: str, session_id: str = None) -> Generator[str, None, None]:
        """流式生成回答"""
        if not self.initialized:
            yield "系统未初始化"
            return
        
        try:
            # 检索相关文档
            retrieved_docs = doc_processor.search_similar_chunks(query)
            
            if not retrieved_docs:
                yield "抱歉，我在文档中没有找到相关信息。请尝试重新表述您的问题。"
                return
            
            # 获取历史对话
            history = []
            if session_id:
                history = session_manager.get_session_history(session_id)
            
            # 创建提示
            prompt = self.create_context_prompt(query, retrieved_docs, history)
            
            # 流式生成（简化版本，实际实现需要模型支持）
            full_response = model_manager.generate_response(prompt, max_length=1024)
            
            # 模拟流式输出
            words = full_response.split()
            current_response = ""
            
            for word in words:
                current_response += word + " "
                yield current_response.strip()
                
            # 保存到会话历史
            if session_id:
                session_manager.add_to_history(session_id, query, full_response)
                
        except Exception as e:
            yield f"处理请求时出错: {str(e)}"
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            "initialized": self.initialized,
            "models_loaded": {
                "embedding": model_manager.embedding_model is not None,
                "llm": model_manager.llm_model is not None
            },
            "documents_indexed": len(doc_processor.document_chunks) if doc_processor.document_chunks else 0,
            "device": model_manager.device
        }

# 全局RAG系统实例
rag_system = RAGSystem()
