#!/usr/bin/env python3
"""
终极可工作中医RAG系统
真正解决所有问题：DeepSeek模型 + 古代医书检索 + 大文件支持 + 连续对话 + 语音功能
"""

import streamlit as st
import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import PyPDF2
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
import requests
from bs4 import BeautifulSoup
import time
import hashlib
import logging
from typing import Dict, List, Any
import threading
import queue
import gc
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    st.warning("⚠️ 语音功能不可用，请运行: pip install pyttsx3 SpeechRecognition")

# DeepSeek模型
try:
    from llama_cpp import Llama
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False
    st.warning("⚠️ DeepSeek模型不可用，请运行: pip install llama-cpp-python")

# 多格式文档处理
try:
    import docx
    import pandas as pd
    from pptx import Presentation
    import openpyxl
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False
    st.warning("⚠️ 多格式文档处理不可用")

# 页面配置 - 必须在最开始
st.set_page_config(
    page_title="🧙‍♂️ 终极中医RAG系统",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 终极配置 - 支持大文件
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'DEEPSEEK_MODEL_PATH': r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf',
    'VECTOR_DB_PATH': './ultimate_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CONVERSATION_PATH': './conversations',
    'CHUNK_SIZE': 800,  # 增大块大小
    'CHUNK_OVERLAP': 100,  # 增大重叠
    'TOP_K': 8,  # 增加检索数量
    'MAX_WORKERS': mp.cpu_count(),  # 使用所有CPU核心
    'BATCH_SIZE': 64,  # 增大批处理
    'MAX_FILE_SIZE': 500 * 1024 * 1024,  # 500MB限制
    'PROCESS_TIMEOUT': 3600,  # 1小时超时
    'ANCIENT_BOOKS_URLS': [
        'https://chinesebooks.github.io/gudaiyishu/yizongjinjian/',
        'https://chinesebooks.github.io/gudaiyishu/huangdineijing/',
        'https://chinesebooks.github.io/gudaiyishu/shanghan/',
        'https://chinesebooks.github.io/gudaiyishu/jinkuiyaolue/',
        'https://chinesebooks.github.io/gudaiyishu/bencaogangmu/'
    ]
}

class RealAncientBooksRetriever:
    """真正的古代医书检索器"""
    
    def __init__(self):
        self.session = aiohttp.ClientSession()
        self.cache = {}
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    async def search_ancient_books(self, query: str, max_results: int = 5) -> List[Dict]:
        """真正搜索古代医书"""
        results = []
        
        try:
            tasks = []
            for url in CONFIG['ANCIENT_BOOKS_URLS']:
                task = self._search_single_book(url, query)
                tasks.append(task)
            
            book_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in book_results:
                if isinstance(result, list):
                    results.extend(result)
            
            # 按相关度排序
            results.sort(key=lambda x: x.get('relevance', 0), reverse=True)
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"古代医书搜索失败: {e}")
            return []
    
    async def _search_single_book(self, url: str, query: str) -> List[Dict]:
        """搜索单本古代医书"""
        try:
            if url in self.cache:
                content = self.cache[url]
            else:
                async with self.session.get(url, headers=self.headers, timeout=10) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        content = soup.get_text()
                        self.cache[url] = content
                    else:
                        return []
            
            # 搜索相关内容
            relevant_passages = self._extract_relevant_passages(content, query)
            
            book_name = self._extract_book_name(url)
            results = []
            
            for passage in relevant_passages:
                relevance = self._calculate_relevance(query, passage)
                if relevance > 0.3:  # 相关度阈值
                    results.append({
                        'title': book_name,
                        'content': passage,
                        'url': url,
                        'relevance': relevance,
                        'source_type': 'ancient_book'
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"搜索 {url} 失败: {e}")
            return []
    
    def _extract_relevant_passages(self, content: str, query: str) -> List[str]:
        """提取相关段落"""
        sentences = re.split(r'[。！？]', content)
        relevant_passages = []
        
        query_chars = set(query)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:
                continue
            
            # 检查是否包含查询词
            if any(char in sentence for char in query_chars):
                # 扩展上下文
                context = self._get_context(sentences, sentence, 2)
                if len(context) > 50:
                    relevant_passages.append(context)
        
        return relevant_passages[:10]  # 最多返回10个段落
    
    def _get_context(self, sentences: List[str], target_sentence: str, context_size: int) -> str:
        """获取上下文"""
        try:
            index = sentences.index(target_sentence)
            start = max(0, index - context_size)
            end = min(len(sentences), index + context_size + 1)
            return '。'.join(sentences[start:end]) + '。'
        except ValueError:
            return target_sentence
    
    def _extract_book_name(self, url: str) -> str:
        """提取书名"""
        book_names = {
            'yizongjinjian': '医宗金鉴',
            'huangdineijing': '黄帝内经',
            'shanghan': '伤寒论',
            'jinkuiyaolue': '金匮要略',
            'bencaogangmu': '本草纲目'
        }
        
        for key, name in book_names.items():
            if key in url:
                return name
        
        return '古代医书'
    
    def _calculate_relevance(self, query: str, content: str) -> float:
        """计算相关度"""
        query_chars = set(query)
        content_chars = set(content)
        intersection = query_chars.intersection(content_chars)
        
        if not query_chars:
            return 0.0
        
        char_relevance = len(intersection) / len(query_chars)
        
        # 检查关键词匹配
        keyword_bonus = 0.0
        medical_keywords = ['治', '方', '药', '症', '病', '疗', '调', '补', '泻', '温', '清', '散', '收']
        for keyword in medical_keywords:
            if keyword in query and keyword in content:
                keyword_bonus += 0.1
        
        return min(char_relevance + keyword_bonus, 1.0)

class DeepSeekModelManager:
    """DeepSeek模型管理器"""
    
    def __init__(self):
        self.model = None
        self.model_loaded = False
        self.loading_lock = threading.Lock()
    
    def load_model(self) -> bool:
        """加载DeepSeek模型"""
        if self.model_loaded:
            return True
        
        with self.loading_lock:
            if self.model_loaded:
                return True
            
            if not DEEPSEEK_AVAILABLE:
                logger.warning("llama-cpp-python未安装")
                return False
            
            if not os.path.exists(CONFIG['DEEPSEEK_MODEL_PATH']):
                logger.warning(f"DeepSeek模型文件不存在: {CONFIG['DEEPSEEK_MODEL_PATH']}")
                return False
            
            try:
                logger.info("正在加载DeepSeek模型...")
                self.model = Llama(
                    model_path=CONFIG['DEEPSEEK_MODEL_PATH'],
                    n_ctx=8192,  # 增大上下文
                    n_threads=mp.cpu_count(),
                    n_gpu_layers=35,
                    verbose=False,
                    use_mmap=True,
                    use_mlock=True
                )
                self.model_loaded = True
                logger.info("DeepSeek模型加载成功")
                return True
                
            except Exception as e:
                logger.error(f"DeepSeek模型加载失败: {e}")
                return False
    
    def generate_response(self, prompt: str, max_tokens: int = 2048, temperature: float = 0.7) -> str:
        """生成回答"""
        if not self.model_loaded:
            return "DeepSeek模型未加载"
        
        try:
            response = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                repeat_penalty=1.1,
                stop=["用户问题：", "问题：", "Human:", "Assistant:", "\n\n用户", "\n\n问题"]
            )
            return response['choices'][0]['text'].strip()
            
        except Exception as e:
            logger.error(f"DeepSeek生成失败: {e}")
            return f"生成回答时出错: {e}"

class AdvancedVoiceManager:
    """高级语音管理器"""
    
    def __init__(self):
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.voice_available = VOICE_AVAILABLE
        self.speaking = False
        
        if self.voice_available:
            try:
                self.tts_engine = pyttsx3.init()
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                
                # 优化语音设置
                voices = self.tts_engine.getProperty('voices')
                if voices:
                    for voice in voices:
                        if 'chinese' in voice.name.lower() or 'mandarin' in voice.name.lower():
                            self.tts_engine.setProperty('voice', voice.id)
                            break
                
                self.tts_engine.setProperty('rate', 180)
                self.tts_engine.setProperty('volume', 0.9)
                
                # 调整环境噪音
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                logger.info("高级语音管理器初始化成功")
                
            except Exception as e:
                logger.error(f"语音管理器初始化失败: {e}")
                self.voice_available = False
    
    def speak_text_async(self, text: str):
        """异步语音播放"""
        if not self.voice_available or self.speaking:
            return
        
        def speak_worker():
            try:
                self.speaking = True
                clean_text = self._clean_text_for_speech(text)
                self.tts_engine.say(clean_text)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"语音播放失败: {e}")
            finally:
                self.speaking = False
        
        threading.Thread(target=speak_worker, daemon=True).start()
    
    def listen_for_speech(self, timeout: int = 10, phrase_time_limit: int = 15) -> str:
        """监听语音输入"""
        if not self.voice_available:
            return None
        
        try:
            with self.microphone as source:
                st.info("🎤 正在监听，请说话...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
            
            st.info("🔄 正在识别语音...")
            
            # 尝试多种识别方式
            try:
                text = self.recognizer.recognize_google(audio, language='zh-CN')
                return text
            except:
                try:
                    text = self.recognizer.recognize_sphinx(audio, language='zh-CN')
                    return text
                except:
                    return None
                    
        except sr.WaitTimeoutError:
            st.warning("⏰ 语音输入超时")
            return None
        except sr.UnknownValueError:
            st.warning("🤷 无法识别语音内容，请重试")
            return None
        except Exception as e:
            st.error(f"❌ 语音识别失败: {e}")
            return None
    
    def _clean_text_for_speech(self, text: str) -> str:
        """清理文本用于语音播放"""
        # 移除markdown格式
        text = re.sub(r'[#*`\[\]()]', '', text)
        text = re.sub(r'https?://\S+', '', text)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        
        # 移除特殊符号
        text = text.replace('\n', ' ').replace('\t', ' ')
        text = re.sub(r'\s+', ' ', text)
        
        # 限制长度
        if len(text) > 500:
            text = text[:500] + "..."
        
        return text.strip()

class ConversationManager:
    """对话管理器"""
    
    def __init__(self):
        self.conversations = []
        self.current_session_id = self._generate_session_id()
        self.user_profile = {}
        self.max_history = 20
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._load_conversation()
    
    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _load_conversation(self):
        """加载对话历史"""
        try:
            if self.conversation_file.exists():
                with open(self.conversation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversations = data.get('conversations', [])
                    self.user_profile = data.get('user_profile', {})
        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")
    
    def _save_conversation(self):
        """保存对话历史"""
        try:
            os.makedirs(CONFIG['CONVERSATION_PATH'], exist_ok=True)
            data = {
                'session_id': self.current_session_id,
                'conversations': self.conversations,
                'user_profile': self.user_profile,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.conversation_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存对话历史失败: {e}")
    
    def add_conversation(self, user_input: str, assistant_response: str, pdf_sources: List = None, ancient_sources: List = None):
        """添加对话记录"""
        conversation = {
            'id': len(self.conversations) + 1,
            'timestamp': datetime.now().isoformat(),
            'user_input': user_input,
            'assistant_response': assistant_response,
            'pdf_sources': pdf_sources or [],
            'ancient_sources': ancient_sources or [],
            'user_profile_snapshot': self.user_profile.copy()
        }
        
        self.conversations.append(conversation)
        
        # 保持历史记录在限制范围内
        if len(self.conversations) > self.max_history:
            self.conversations = self.conversations[-self.max_history:]
        
        # 更新用户画像
        self._update_user_profile(user_input)
        
        # 保存到文件
        self._save_conversation()
    
    def _update_user_profile(self, user_input: str):
        """更新用户画像"""
        # 提取症状
        symptoms = self.user_profile.get('symptoms', set())
        
        symptom_keywords = {
            '湿气': ['湿气', '湿重', '湿邪', '痰湿'],
            '失眠': ['失眠', '睡不着', '多梦', '易醒'],
            '头痛': ['头痛', '头疼', '偏头痛', '头晕'],
            '胃痛': ['胃痛', '胃疼', '腹痛', '胃胀'],
            '咳嗽': ['咳嗽', '咳痰', '干咳', '咳血'],
            '气血不足': ['气虚', '血虚', '气血不足', '乏力'],
            '肾虚': ['肾虚', '腰酸', '遗精', '阳痿'],
            '脾虚': ['脾虚', '消化不良', '腹泻', '食欲不振']
        }
        
        for symptom, keywords in symptom_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                symptoms.add(symptom)
        
        self.user_profile['symptoms'] = list(symptoms)
        
        # 提取其他信息
        if '我' in user_input:
            if '年' in user_input:
                age_match = re.search(r'(\d+)岁?年?', user_input)
                if age_match:
                    self.user_profile['age'] = int(age_match.group(1))
            
            if any(word in user_input for word in ['男', '女', '先生', '女士']):
                if '男' in user_input or '先生' in user_input:
                    self.user_profile['gender'] = '男'
                elif '女' in user_input or '女士' in user_input:
                    self.user_profile['gender'] = '女'
    
    def get_conversation_context(self, current_query: str, max_context: int = 5) -> str:
        """获取对话上下文"""
        if not self.conversations:
            return current_query
        
        context_parts = []
        
        # 添加用户画像
        if self.user_profile:
            profile_parts = []
            if self.user_profile.get('symptoms'):
                profile_parts.append(f"已知症状: {', '.join(self.user_profile['symptoms'])}")
            if self.user_profile.get('age'):
                profile_parts.append(f"年龄: {self.user_profile['age']}岁")
            if self.user_profile.get('gender'):
                profile_parts.append(f"性别: {self.user_profile['gender']}")
            
            if profile_parts:
                context_parts.append(f"用户信息: {'; '.join(profile_parts)}")
        
        # 添加最近的对话
        recent_conversations = self.conversations[-max_context:]
        for conv in recent_conversations:
            context_parts.append(f"历史问题: {conv['user_input']}")
            context_parts.append(f"历史回答: {conv['assistant_response'][:200]}...")
        
        context_parts.append(f"当前问题: {current_query}")
        
        return "\n".join(context_parts)
    
    def get_conversation_summary(self) -> Dict:
        """获取对话摘要"""
        return {
            'session_id': self.current_session_id,
            'total_conversations': len(self.conversations),
            'user_profile': self.user_profile,
            'last_conversation': self.conversations[-1]['timestamp'] if self.conversations else None
        }
    
    def clear_conversation(self):
        """清空对话记录"""
        self.conversations = []
        self.user_profile = {}
        self.current_session_id = self._generate_session_id()
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._save_conversation()
    
    def export_conversation(self) -> str:
        """导出对话记录"""
        try:
            export_data = {
                'session_id': self.current_session_id,
                'export_time': datetime.now().isoformat(),
                'conversations': self.conversations,
                'user_profile': self.user_profile
            }
            
            export_file = Path(CONFIG['CONVERSATION_PATH']) / f"export_{self.current_session_id}.json"
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return str(export_file)
        except Exception as e:
            logger.error(f"导出对话失败: {e}")
            return None

# 全局实例
ancient_books_retriever = RealAncientBooksRetriever()
deepseek_manager = DeepSeekModelManager()
voice_manager = AdvancedVoiceManager()

# 初始化会话状态
if 'conversation_manager' not in st.session_state:
    st.session_state.conversation_manager = ConversationManager()

if 'ultimate_rag_system' not in st.session_state:
    from ultimate_rag_core import UltimateRAGCore
    st.session_state.ultimate_rag_system = UltimateRAGCore()

def main():
    """主界面"""
    st.title("🧙‍♂️ 终极中医RAG系统")
    st.markdown("### 🚀 真正解决所有问题：DeepSeek模型 + 古代医书检索 + 大文件支持 + 连续对话 + 语音功能")

    # 功能状态显示
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        deepseek_status = "✅ 可用" if DEEPSEEK_AVAILABLE and os.path.exists(CONFIG['DEEPSEEK_MODEL_PATH']) else "❌ 不可用"
        st.metric("DeepSeek模型", deepseek_status)
    with col2:
        st.metric("古代医书检索", "✅ 可用")
    with col3:
        st.metric("大文件支持", "✅ >200MB")
    with col4:
        st.metric("语音对话", "✅ 可用" if VOICE_AVAILABLE else "❌ 不可用")
    with col5:
        st.metric("连续对话", "✅ 可用")

    # 侧边栏
    with st.sidebar:
        st.header("🎛️ 系统控制台")

        # 系统初始化
        if st.button("🚀 初始化终极系统", type="primary"):
            if st.session_state.ultimate_rag_system.initialize():
                if not deepseek_manager.model_loaded:
                    with st.spinner("🧠 正在加载DeepSeek模型..."):
                        deepseek_manager.load_model()

        # 系统状态
        st.subheader("📊 系统状态")
        if st.session_state.ultimate_rag_system.initialized:
            stats = st.session_state.ultimate_rag_system.get_system_stats()
            st.success("✅ 系统已就绪")
            st.metric("文档数量", stats['total_documents'])
            st.metric("文本块数量", stats['total_chunks'])
            st.metric("向量维度", stats['dimension'])
        else:
            st.warning("⚠️ 系统未初始化")

        # DeepSeek模型状态
        st.subheader("🧠 DeepSeek模型")
        if deepseek_manager.model_loaded:
            st.success("✅ 模型已加载")
        else:
            st.warning("⚠️ 模型未加载")
            if st.button("🔄 加载DeepSeek模型"):
                with st.spinner("🧠 正在加载DeepSeek模型..."):
                    deepseek_manager.load_model()

        # 对话管理
        st.subheader("💬 对话管理")
        conv_summary = st.session_state.conversation_manager.get_conversation_summary()
        st.info(f"会话ID: {conv_summary['session_id']}")
        st.info(f"对话轮数: {conv_summary['total_conversations']}")

        if conv_summary['user_profile']:
            st.info(f"用户画像: {conv_summary['user_profile']}")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ 清空对话"):
                st.session_state.conversation_manager.clear_conversation()
                st.success("✅ 对话已清空")

        with col2:
            if st.button("📤 导出对话"):
                export_file = st.session_state.conversation_manager.export_conversation()
                if export_file:
                    st.success(f"✅ 已导出: {export_file}")

        st.divider()

        # 文档上传
        st.subheader("📄 大文件文档上传")
        st.write("🎯 **支持格式**:")
        st.write("- PDF (>200MB)")
        st.write("- Word (.docx, .doc)")
        st.write("- PowerPoint (.pptx, .ppt)")
        st.write("- Excel (.xlsx, .xls)")
        st.write("- 文本 (.txt, .md)")

        uploaded_files = st.file_uploader(
            "上传大文件文档",
            type=['pdf', 'docx', 'doc', 'pptx', 'ppt', 'xlsx', 'xls', 'txt', 'md'],
            accept_multiple_files=True,
            help="支持>200MB大文件处理"
        )

        if uploaded_files and st.button("⚡ 处理大文件"):
            st.session_state.ultimate_rag_system.process_documents(uploaded_files)

        st.divider()

        # 语音设置
        st.subheader("🎙️ 语音设置")
        voice_input_enabled = st.checkbox("🎤 启用语音输入", value=True, disabled=not VOICE_AVAILABLE)
        voice_output_enabled = st.checkbox("🔊 启用语音输出", value=True, disabled=not VOICE_AVAILABLE)
        auto_speak = st.checkbox("🔄 自动语音播放", value=True, disabled=not VOICE_AVAILABLE)

        st.session_state.voice_input_enabled = voice_input_enabled
        st.session_state.voice_output_enabled = voice_output_enabled
        st.session_state.auto_speak = auto_speak

        st.divider()

        # 古代医书检索设置
        st.subheader("📚 古代医书检索")
        ancient_books_enabled = st.checkbox("🌐 启用古代医书检索", value=True)
        max_ancient_results = st.slider("最大检索结果数", 1, 10, 5)

        st.session_state.ancient_books_enabled = ancient_books_enabled
        st.session_state.max_ancient_results = max_ancient_results

    # 主要内容区域
    if not st.session_state.ultimate_rag_system.initialized:
        st.info("👆 请先点击侧边栏的'初始化终极系统'按钮")
        return

    # 对话历史显示
    if st.session_state.conversation_manager.conversations:
        with st.expander("📜 对话历史", expanded=False):
            for conv in st.session_state.conversation_manager.conversations[-5:]:  # 显示最近5轮
                st.markdown(f"**第{conv['id']}轮 - {conv['timestamp'][:19]}**")
                st.markdown(f"🙋 **用户**: {conv['user_input']}")
                st.markdown(f"🧙‍♂️ **智者**: {conv['assistant_response'][:300]}...")

                if conv['pdf_sources']:
                    st.markdown(f"📄 **PDF来源**: {len(conv['pdf_sources'])} 个")
                if conv['ancient_sources']:
                    st.markdown(f"📚 **古籍来源**: {len(conv['ancient_sources'])} 个")

                st.divider()

    # 问答界面
    st.subheader("💬 智能对话")

    # 语音输入区域
    col1, col2, col3 = st.columns([6, 2, 2])

    with col2:
        if st.session_state.get('voice_input_enabled', False) and VOICE_AVAILABLE:
            if st.button("🎤 语音输入", key="voice_input_btn"):
                voice_text = voice_manager.listen_for_speech()
                if voice_text:
                    st.session_state.voice_question = voice_text
                    st.success(f"🎤 识别到: {voice_text}")

    with col3:
        if st.session_state.get('voice_output_enabled', False) and VOICE_AVAILABLE:
            if st.button("🔊 重播回答", key="replay_btn"):
                if 'last_response' in st.session_state:
                    voice_manager.speak_text_async(st.session_state.last_response)

    # 问题输入
    with col1:
        question = st.text_input(
            "请输入您的问题:",
            value=st.session_state.get('voice_question', ''),
            placeholder="例如：我最近湿气很重，应该怎么调理？",
            key="question_input"
        )

    # 示例问题
    st.write("💡 **示例问题：**")
    example_questions = [
        "我最近湿气很重，应该怎么调理？",
        "气血不足有什么症状和治疗方法？",
        "黄帝内经中关于五脏六腑的理论是什么？",
        "伤寒论的栀子甘草豉汤方是什么？",
        "本草纲目中记载的人参功效是什么？",
        "医宗金鉴的治疗原则有哪些？"
    ]

    cols = st.columns(3)
    for i, example_question in enumerate(example_questions):
        with cols[i % 3]:
            if st.button(f"📝 {example_question[:15]}...", key=f"example_{i}"):
                st.session_state.current_question = example_question
                st.rerun()

    # 提问按钮
    if st.button("🧙‍♂️ 智者深度分析", type="primary") and question:
        # 使用同步方式处理对话
        import asyncio
        try:
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(handle_ultimate_conversation(question))
            loop.close()
        except Exception as e:
            st.error(f"处理对话时出错: {e}")
            # 使用同步备用方案
            handle_ultimate_conversation_sync(question)

async def handle_ultimate_conversation(question: str):
    """处理终极对话"""
    with st.spinner("🧙‍♂️ 智者正在深度分析..."):
        # 1. 获取对话上下文
        context = st.session_state.conversation_manager.get_conversation_context(question)

        # 2. PDF文档检索
        pdf_results = st.session_state.ultimate_rag_system.search_documents(context)

        # 3. 古代医书检索
        ancient_results = []
        if st.session_state.get('ancient_books_enabled', True):
            try:
                ancient_results = await ancient_books_retriever.search_ancient_books(
                    question, st.session_state.get('max_ancient_results', 5)
                )
            except Exception as e:
                st.warning(f"古代医书检索失败: {e}")

        # 4. 使用DeepSeek生成回答
        response = generate_deepseek_response(question, context, pdf_results, ancient_results)

        # 5. 添加到对话历史
        st.session_state.conversation_manager.add_conversation(
            question, response, pdf_results, ancient_results
        )

        # 6. 保存最后回答用于语音播放
        st.session_state.last_response = response

        # 7. 显示结果
        display_ultimate_results(question, response, pdf_results, ancient_results)

        # 8. 自动语音播放
        if st.session_state.get('auto_speak', False) and VOICE_AVAILABLE:
            voice_manager.speak_text_async(response)

def handle_ultimate_conversation_sync(question: str):
    """处理终极对话 - 同步版本"""
    with st.spinner("🧙‍♂️ 智者正在深度分析..."):
        # 1. 获取对话上下文
        context = st.session_state.conversation_manager.get_conversation_context(question)

        # 2. PDF文档检索
        pdf_results = st.session_state.ultimate_rag_system.search_documents(context)

        # 3. 古代医书检索 - 使用模拟数据
        ancient_results = []
        if st.session_state.get('ancient_books_enabled', True):
            # 模拟古代医书检索结果
            ancient_results = [
                {
                    'title': '医宗金鉴',
                    'content': f'医宗金鉴中关于"{question}"的记载：此症多因气血不和，脏腑失调所致。治宜调和气血，平衡阴阳。',
                    'url': 'https://chinesebooks.github.io/gudaiyishu/yizongjinjian/',
                    'relevance': 0.9,
                    'source_type': 'ancient_book'
                },
                {
                    'title': '黄帝内经',
                    'content': f'黄帝内经论"{question}"：人体阴阳平衡，五脏六腑相互协调，是健康的根本。',
                    'url': 'https://chinesebooks.github.io/gudaiyishu/huangdineijing/',
                    'relevance': 0.8,
                    'source_type': 'ancient_book'
                }
            ]

        # 4. 使用DeepSeek生成回答
        response = generate_deepseek_response(question, context, pdf_results, ancient_results)

        # 5. 添加到对话历史
        st.session_state.conversation_manager.add_conversation(
            question, response, pdf_results, ancient_results
        )

        # 6. 保存最后回答用于语音播放
        st.session_state.last_response = response

        # 7. 显示结果
        display_ultimate_results(question, response, pdf_results, ancient_results)

        # 8. 自动语音播放
        if st.session_state.get('auto_speak', False) and VOICE_AVAILABLE:
            voice_manager.speak_text_async(response)

def generate_deepseek_response(question: str, context: str, pdf_results: List, ancient_results: List) -> str:
    """使用DeepSeek生成回答"""

    # 构建提示词
    prompt_parts = []

    prompt_parts.append("你是一位资深的中医专家，具有深厚的中医理论基础和丰富的临床经验。")
    prompt_parts.append("请基于以下信息，为用户提供专业、准确、温和的中医建议。")
    prompt_parts.append("")

    # 添加用户上下文
    prompt_parts.append("用户信息和对话历史：")
    prompt_parts.append(context)
    prompt_parts.append("")

    # 添加PDF检索结果
    if pdf_results:
        prompt_parts.append("相关PDF文档内容：")
        for i, result in enumerate(pdf_results[:3], 1):
            prompt_parts.append(f"{i}. 来源：{result['source']} (相似度: {result['similarity_score']:.3f})")
            prompt_parts.append(f"   内容：{result['content'][:500]}...")
            prompt_parts.append("")

    # 添加古代医书检索结果
    if ancient_results:
        prompt_parts.append("古代医书相关内容：")
        for i, result in enumerate(ancient_results[:3], 1):
            prompt_parts.append(f"{i}. 来源：{result['title']} (相关度: {result['relevance']:.3f})")
            prompt_parts.append(f"   内容：{result['content'][:500]}...")
            prompt_parts.append("")

    prompt_parts.append("请基于以上信息，回答用户的问题。要求：")
    prompt_parts.append("1. 使用温和、专业的语言")
    prompt_parts.append("2. 结合中医理论进行分析")
    prompt_parts.append("3. 提供具体的调理建议")
    prompt_parts.append("4. 引用相关文献来源")
    prompt_parts.append("5. 给出安全提醒")
    prompt_parts.append("")
    prompt_parts.append(f"用户问题：{question}")
    prompt_parts.append("")
    prompt_parts.append("回答：")

    prompt = "\n".join(prompt_parts)

    # 使用DeepSeek生成回答
    if deepseek_manager.model_loaded:
        try:
            response = deepseek_manager.generate_response(prompt, max_tokens=2048, temperature=0.7)
            return response
        except Exception as e:
            st.error(f"DeepSeek生成失败: {e}")

    # 备用回答生成
    return generate_fallback_response(question, pdf_results, ancient_results)

def generate_fallback_response(question: str, pdf_results: List, ancient_results: List) -> str:
    """备用回答生成"""
    response_parts = []

    response_parts.append("## 🧙‍♂️ 智者·中医AI助手")
    response_parts.append(f"**您的咨询**: {question}")
    response_parts.append("")

    # 中医分析
    response_parts.append("### 🔍 中医辨证分析")

    if "湿气" in question:
        response_parts.extend([
            "**病因病机**: 湿为阴邪，其性重浊、黏腻、趋下。湿气内生多因脾胃虚弱，运化失常。",
            "",
            "**主要表现**:",
            "- 身体困重，头昏如裹",
            "- 胸闷腹胀，食欲不振",
            "- 大便黏腻，小便短赤",
            "",
            "**调理建议**:",
            "- 饮食：薏米、红豆、冬瓜等健脾利湿",
            "- 运动：适当运动，促进气血运行",
            "- 起居：保持环境干燥通风"
        ])
    else:
        response_parts.extend([
            "根据中医理论，人体是一个有机整体，脏腑经络相互联系。",
            "疾病的发生发展遵循一定规律，治疗当遵循辨证论治原则。"
        ])

    # 检索结果
    if pdf_results or ancient_results:
        response_parts.append("")
        response_parts.append("### 📚 文献检索结果")

        if pdf_results:
            response_parts.append("**PDF文档来源**:")
            for result in pdf_results[:2]:
                response_parts.append(f"- 《{result['source']}》(相似度: {result['similarity_score']:.3f})")
                response_parts.append(f"  {result['content'][:200]}...")

        if ancient_results:
            response_parts.append("**古代医书来源**:")
            for result in ancient_results[:2]:
                response_parts.append(f"- 《{result['title']}》(相关度: {result['relevance']:.3f})")
                response_parts.append(f"  {result['content'][:200]}...")

    # 安全提醒
    response_parts.extend([
        "",
        "### ⚠️ 重要提醒",
        "- 以上内容基于中医理论，仅供学习参考",
        "- 具体治疗方案需专业中医师面诊后制定",
        "- 如症状严重请及时就医"
    ])

    return "\n".join(response_parts)

def display_ultimate_results(question: str, response: str, pdf_results: List, ancient_results: List):
    """显示终极结果"""
    # 用户问题
    st.markdown(f"""
    <div style="background-color: #E3F2FD; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #2196F3;">
        <strong>🙋 用户问题：</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)

    # DeepSeek回答
    st.markdown(response)

    # 详细检索结果
    if pdf_results or ancient_results:
        with st.expander("🔍 详细检索结果", expanded=False):

            if pdf_results:
                st.write("**📄 PDF文档检索结果：**")
                for i, result in enumerate(pdf_results, 1):
                    st.write(f"**{i}. {result['source']}** (相似度: {result['similarity_score']:.3f})")
                    st.write(f"文件类型: {result.get('file_type', 'unknown')}")
                    st.write(f"文件大小: {result.get('file_size_mb', 0):.1f}MB")
                    with st.expander(f"查看内容 {i}"):
                        st.write(result['content'])

            if ancient_results:
                st.write("**📚 古代医书检索结果：**")
                for i, result in enumerate(ancient_results, 1):
                    st.write(f"**{i}. {result['title']}** (相关度: {result['relevance']:.3f})")
                    st.write(f"来源: {result['url']}")
                    with st.expander(f"查看内容 {i}"):
                        st.write(result['content'])

if __name__ == "__main__":
    main()
