#!/usr/bin/env python3
"""
测试优化后的终极中医RAG系统
验证回答质量和文档处理性能
"""

import os
import sys
import time
import requests
from pathlib import Path

def test_system_components():
    """测试系统组件"""
    print("🔍 测试系统组件...")
    
    # 测试DeepSeek模型文件
    model_path = r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    if os.path.exists(model_path):
        model_size = os.path.getsize(model_path) / (1024 * 1024 * 1024)
        print(f"✅ DeepSeek模型: {model_size:.2f} GB")
    else:
        print("❌ DeepSeek模型文件不存在")
    
    # 测试依赖包
    required_packages = [
        'streamlit', 'numpy', 'pandas', 'requests', 'beautifulsoup4',
        'PyPDF2', 'sentence_transformers', 'faiss', 'pyttsx3'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺失依赖: {', '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def test_ancient_books_retrieval():
    """测试古代医书检索"""
    print("\n🔍 测试古代医书检索...")
    
    test_urls = [
        'https://chinesebooks.github.io/gudaiyishu/huangdineijing/',
        'https://chinesebooks.github.io/gudaiyishu/shanghan/',
        'https://chinesebooks.github.io/gudaiyishu/jinkuiyaolue/'
    ]
    
    accessible_count = 0
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {url}")
                accessible_count += 1
            else:
                print(f"❌ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
    
    if accessible_count > 0:
        print(f"✅ 古代医书网站可访问: {accessible_count}/{len(test_urls)}")
        return True
    else:
        print("❌ 所有古代医书网站都无法访问")
        return False

def test_document_processing():
    """测试文档处理"""
    print("\n🔍 测试文档处理...")
    
    # 检查documents目录
    docs_dir = Path("documents")
    if docs_dir.exists():
        pdf_files = list(docs_dir.glob("*.pdf"))
        print(f"📚 发现PDF文档: {len(pdf_files)} 个")
        
        for pdf_file in pdf_files[:3]:  # 只显示前3个
            file_size = pdf_file.stat().st_size / (1024 * 1024)
            print(f"   📄 {pdf_file.name} ({file_size:.1f} MB)")
        
        return len(pdf_files) > 0
    else:
        print("❌ documents目录不存在")
        return False

def test_vector_database():
    """测试向量数据库"""
    print("\n🔍 测试向量数据库...")
    
    vector_db_dir = Path("ultimate_final_vector_db")
    if vector_db_dir.exists():
        index_file = vector_db_dir / "index.faiss"
        metadata_file = vector_db_dir / "metadata.pkl"
        
        if index_file.exists() and metadata_file.exists():
            index_size = index_file.stat().st_size / (1024 * 1024)
            metadata_size = metadata_file.stat().st_size / (1024 * 1024)
            print(f"✅ 向量索引: {index_size:.1f} MB")
            print(f"✅ 元数据: {metadata_size:.1f} MB")
            return True
        else:
            print("❌ 向量数据库文件不完整")
            return False
    else:
        print("⚠️ 向量数据库目录不存在，首次运行正常")
        return True

def create_test_query_scenarios():
    """创建测试查询场景"""
    return [
        {
            "query": "我最近湿气很重，舌苔厚腻，大便粘腻，应该如何调理？",
            "expected_keywords": ["湿气", "健脾", "化湿", "舌苔", "脾虚"],
            "description": "湿气重症状的中医调理"
        },
        {
            "query": "失眠多梦，心烦易怒，口干口苦，怎么治疗？",
            "expected_keywords": ["失眠", "心火", "肝郁", "清心", "安神"],
            "description": "失眠伴情志症状的治疗"
        },
        {
            "query": "头痛头晕，颈肩僵硬，血压偏高，中医如何看待？",
            "expected_keywords": ["头痛", "肝阳", "风阳", "平肝", "潜阳"],
            "description": "头痛高血压的中医分析"
        },
        {
            "query": "慢性胃炎，胃胀胃痛，食欲不振，有什么好的方剂？",
            "expected_keywords": ["胃炎", "脾胃", "健脾", "和胃", "方剂"],
            "description": "慢性胃炎的方剂治疗"
        },
        {
            "query": "咳嗽痰多，色白质稀，怕冷，是什么证型？",
            "expected_keywords": ["咳嗽", "痰湿", "寒痰", "温肺", "化痰"],
            "description": "寒痰咳嗽的证型分析"
        }
    ]

def run_performance_test():
    """运行性能测试"""
    print("\n🚀 运行性能测试...")
    
    # 测试系统启动时间
    start_time = time.time()
    
    try:
        # 模拟导入主要模块
        import streamlit
        import sentence_transformers
        import faiss
        
        load_time = time.time() - start_time
        print(f"✅ 模块加载时间: {load_time:.2f} 秒")
        
        if load_time < 10:
            print("✅ 启动性能良好")
            return True
        else:
            print("⚠️ 启动时间较长，可能影响用户体验")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n" + "=" * 80)
    print("📊 优化后系统测试报告")
    print("=" * 80)
    
    test_results = {}
    
    # 运行各项测试
    test_results['components'] = test_system_components()
    test_results['ancient_books'] = test_ancient_books_retrieval()
    test_results['documents'] = test_document_processing()
    test_results['vector_db'] = test_vector_database()
    test_results['performance'] = run_performance_test()
    
    # 统计结果
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n📈 测试结果汇总:")
    print(f"   ✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"   📊 通过率: {passed_tests/total_tests*100:.1f}%")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 建议
    print(f"\n💡 优化建议:")
    if not test_results['components']:
        print("   - 安装缺失的依赖包")
    if not test_results['ancient_books']:
        print("   - 检查网络连接，确保可以访问古代医书网站")
    if not test_results['documents']:
        print("   - 上传一些中医PDF文档到documents目录")
    if not test_results['performance']:
        print("   - 考虑升级硬件或优化系统配置")
    
    # 系统状态评估
    if passed_tests == total_tests:
        print(f"\n🎉 系统状态: 优秀 - 所有功能正常")
    elif passed_tests >= total_tests * 0.8:
        print(f"\n✅ 系统状态: 良好 - 主要功能正常")
    elif passed_tests >= total_tests * 0.6:
        print(f"\n⚠️ 系统状态: 一般 - 部分功能需要修复")
    else:
        print(f"\n❌ 系统状态: 需要修复 - 多个功能异常")
    
    # 测试查询场景
    print(f"\n🧪 推荐测试查询:")
    scenarios = create_test_query_scenarios()
    for i, scenario in enumerate(scenarios[:3], 1):
        print(f"   {i}. {scenario['description']}")
        print(f"      查询: {scenario['query']}")
        print(f"      期望关键词: {', '.join(scenario['expected_keywords'])}")
        print()
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🧙‍♂️ 终极中医RAG系统 - 优化测试工具")
    print("=" * 80)
    print("🎯 本工具将测试系统的两个关键优化:")
    print("   1. 回答质量优化 - 确保PDF检索和古籍检索正常工作")
    print("   2. 文档处理优化 - 确保上传处理不卡顿")
    print("=" * 80)
    
    # 运行测试
    success = generate_test_report()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试完成！系统已优化，可以正常使用")
        print("💡 建议使用以下命令启动系统:")
        print("   python final_ultimate_launcher.py")
    else:
        print("⚠️ 测试发现问题，请根据建议进行修复")
        print("💡 修复后重新运行测试:")
        print("   python test_optimized_system.py")
    print("=" * 80)

if __name__ == "__main__":
    main()
