#!/usr/bin/env python3
"""
测试各个系统的可用性
"""
import requests
import time

# 测试的端口列表
test_ports = [
    (8005, "终极版轻量系统"),
    (8006, "简化版终极系统"),
    (8518, "增强版超快系统"),
    (8520, "商业版系统"),
    (8001, "后端API系统")
]

def test_system(port, name):
    """测试单个系统"""
    try:
        # 测试健康检查
        health_url = f"http://localhost:{port}/api/health"
        response = requests.get(health_url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {name} (端口 {port}): 正常运行")
            print(f"   状态: {data.get('status', '未知')}")
            print(f"   版本: {data.get('version', '未知')}")
            return True
        else:
            print(f"❌ {name} (端口 {port}): HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {name} (端口 {port}): 连接被拒绝")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ {name} (端口 {port}): 连接超时")
        return False
    except Exception as e:
        print(f"❌ {name} (端口 {port}): 错误 - {e}")
        return False

def test_streamlit_systems():
    """测试Streamlit系统"""
    streamlit_ports = [
        (8518, "增强版超快系统"),
        (8520, "商业版系统")
    ]
    
    for port, name in streamlit_ports:
        try:
            # Streamlit系统通常在根路径响应
            url = f"http://localhost:{port}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {name} (端口 {port}): Streamlit正常运行")
            else:
                print(f"❌ {name} (端口 {port}): HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {name} (端口 {port}): 连接被拒绝")
        except Exception as e:
            print(f"❌ {name} (端口 {port}): 错误 - {e}")

def main():
    print("🔍 测试中医RAG系统可用性...")
    print("=" * 50)
    
    # 测试FastAPI系统
    working_systems = []
    for port, name in test_ports:
        if test_system(port, name):
            working_systems.append((port, name))
        print()
    
    # 测试Streamlit系统
    print("🔍 测试Streamlit系统...")
    test_streamlit_systems()
    print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    
    if working_systems:
        print(f"✅ 发现 {len(working_systems)} 个正常工作的系统:")
        for port, name in working_systems:
            print(f"   🌐 {name}: http://localhost:{port}")
        
        print("\n🎯 推荐使用:")
        if any(port == 8006 for port, _ in working_systems):
            print("   📱 简化版终极系统: http://localhost:8006")
        elif any(port == 8005 for port, _ in working_systems):
            print("   🚀 终极版轻量系统: http://localhost:8005")
        elif working_systems:
            port, name = working_systems[0]
            print(f"   🌟 {name}: http://localhost:{port}")
    else:
        print("❌ 没有发现正常工作的系统")
        print("💡 建议:")
        print("   1. 检查是否有系统正在启动中")
        print("   2. 重新启动系统")
        print("   3. 检查端口是否被占用")

if __name__ == "__main__":
    main()
