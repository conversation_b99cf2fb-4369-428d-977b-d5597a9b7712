#!/usr/bin/env python3
"""
Elasticsearch中医知识检索器
结合线上古代医书和本地文档的高效检索系统
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import streamlit as st
from typing import List, Dict, Any
import hashlib
import os
from pathlib import Path

class ElasticsearchTCMRetriever:
    """Elasticsearch中医知识检索器"""
    
    def __init__(self):
        # Elasticsearch配置 (使用本地或云端)
        self.es_host = "http://localhost:9200"
        self.index_name = "tcm_knowledge"
        
        # 线上古代医书源
        self.online_sources = {
            "gudaiyishu": "https://chinesebooks.github.io/gudaiyishu/",
            "yizongjinjian": "https://chinesebooks.github.io/gudaiyishu/yizongjinjian/"
        }
        
        # 本地缓存
        self.cache_dir = Path("tcm_cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        self.initialized = False
        
    def check_elasticsearch(self):
        """检查Elasticsearch是否可用"""
        try:
            response = requests.get(self.es_host, timeout=5)
            if response.status_code == 200:
                return True, "Elasticsearch运行正常"
            return False, f"Elasticsearch响应异常: {response.status_code}"
        except requests.exceptions.ConnectionError:
            return False, "无法连接到Elasticsearch，将使用本地检索"
        except Exception as e:
            return False, f"Elasticsearch检查失败: {e}"
    
    def setup_elasticsearch_index(self):
        """设置Elasticsearch索引"""
        try:
            # 创建索引映射
            mapping = {
                "mappings": {
                    "properties": {
                        "title": {"type": "text", "analyzer": "ik_max_word"},
                        "content": {"type": "text", "analyzer": "ik_max_word"},
                        "source": {"type": "keyword"},
                        "category": {"type": "keyword"},
                        "url": {"type": "keyword"},
                        "timestamp": {"type": "date"},
                        "keywords": {"type": "keyword"}
                    }
                },
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0
                }
            }
            
            # 删除已存在的索引
            requests.delete(f"{self.es_host}/{self.index_name}")
            
            # 创建新索引
            response = requests.put(
                f"{self.es_host}/{self.index_name}",
                json=mapping,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code in [200, 201]:
                return True, "索引创建成功"
            else:
                return False, f"索引创建失败: {response.text}"
                
        except Exception as e:
            return False, f"索引设置失败: {e}"
    
    def crawl_online_knowledge(self, max_pages=50):
        """爬取线上古代医书知识"""
        st.info("🕷️ 正在爬取线上古代医书知识...")
        
        all_knowledge = []
        
        for source_name, base_url in self.online_sources.items():
            st.info(f"📚 爬取 {source_name}...")
            
            try:
                # 获取主页面
                response = requests.get(base_url, timeout=10)
                response.encoding = 'utf-8'
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取链接
                links = []
                for a in soup.find_all('a', href=True):
                    href = a['href']
                    if href.endswith('.html') or '/yizongjinjian/' in href:
                        full_url = urljoin(base_url, href)
                        links.append(full_url)
                
                # 限制爬取数量
                links = links[:max_pages]
                
                # 爬取每个页面
                for i, url in enumerate(links):
                    try:
                        st.info(f"爬取进度: {i+1}/{len(links)} - {url}")
                        
                        page_response = requests.get(url, timeout=10)
                        page_response.encoding = 'utf-8'
                        page_soup = BeautifulSoup(page_response.text, 'html.parser')
                        
                        # 提取标题
                        title = ""
                        title_tag = page_soup.find('title')
                        if title_tag:
                            title = title_tag.get_text().strip()
                        
                        # 提取正文内容
                        content = ""
                        
                        # 尝试多种内容选择器
                        content_selectors = [
                            'div.content',
                            'div.main',
                            'article',
                            'div.post',
                            'div.entry-content',
                            'main',
                            'body'
                        ]
                        
                        for selector in content_selectors:
                            content_div = page_soup.select_one(selector)
                            if content_div:
                                content = content_div.get_text().strip()
                                break
                        
                        if not content:
                            content = page_soup.get_text().strip()
                        
                        # 清理内容
                        content = self._clean_text(content)
                        
                        if len(content) > 100:  # 只保留有意义的内容
                            knowledge_item = {
                                "title": title,
                                "content": content,
                                "source": source_name,
                                "category": "古代医书",
                                "url": url,
                                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                                "keywords": self._extract_keywords(title + " " + content)
                            }
                            
                            all_knowledge.append(knowledge_item)
                        
                        time.sleep(0.5)  # 避免过于频繁的请求
                        
                    except Exception as e:
                        st.warning(f"爬取页面失败 {url}: {e}")
                        continue
                        
            except Exception as e:
                st.error(f"爬取 {source_name} 失败: {e}")
                continue
        
        st.success(f"✅ 爬取完成，共获取 {len(all_knowledge)} 条知识")
        return all_knowledge
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》]', '', text)
        return text.strip()
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 中医相关关键词
        tcm_keywords = [
            "气血", "阴阳", "五行", "脏腑", "经络", "穴位", "方剂", "药材",
            "症状", "病证", "治疗", "诊断", "脉象", "舌象", "针灸", "推拿",
            "本草", "伤寒", "温病", "内科", "外科", "妇科", "儿科", "养生"
        ]
        
        found_keywords = []
        for keyword in tcm_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def index_to_elasticsearch(self, knowledge_items: List[Dict]):
        """将知识索引到Elasticsearch"""
        if not knowledge_items:
            return False, "没有知识需要索引"
        
        try:
            st.info("📝 正在索引知识到Elasticsearch...")
            
            # 批量索引
            bulk_data = []
            for item in knowledge_items:
                # 生成文档ID
                doc_id = hashlib.md5(item['url'].encode()).hexdigest()
                
                # 添加索引操作
                bulk_data.append(json.dumps({
                    "index": {
                        "_index": self.index_name,
                        "_id": doc_id
                    }
                }))
                bulk_data.append(json.dumps(item))
            
            # 发送批量请求
            bulk_body = '\n'.join(bulk_data) + '\n'
            
            response = requests.post(
                f"{self.es_host}/_bulk",
                data=bulk_body,
                headers={"Content-Type": "application/x-ndjson"}
            )
            
            if response.status_code == 200:
                result = response.json()
                errors = [item for item in result.get('items', []) if 'error' in item.get('index', {})]
                
                if errors:
                    st.warning(f"部分文档索引失败: {len(errors)} 个错误")
                else:
                    st.success(f"✅ 成功索引 {len(knowledge_items)} 条知识")
                
                return True, f"索引完成，{len(knowledge_items) - len(errors)} 条成功"
            else:
                return False, f"批量索引失败: {response.text}"
                
        except Exception as e:
            return False, f"索引异常: {e}"
    
    def search_knowledge(self, query: str, size: int = 10) -> List[Dict]:
        """搜索知识"""
        try:
            # 构建搜索查询
            search_body = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^3", "content^2", "keywords^2"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            },
                            {
                                "match_phrase": {
                                    "content": {
                                        "query": query,
                                        "boost": 2
                                    }
                                }
                            }
                        ]
                    }
                },
                "highlight": {
                    "fields": {
                        "content": {
                            "fragment_size": 150,
                            "number_of_fragments": 3
                        }
                    }
                },
                "size": size,
                "_source": ["title", "content", "source", "category", "url", "keywords"]
            }
            
            response = requests.post(
                f"{self.es_host}/{self.index_name}/_search",
                json=search_body,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                hits = result.get('hits', {}).get('hits', [])
                
                search_results = []
                for hit in hits:
                    source = hit['_source']
                    score = hit['_score']
                    
                    # 添加高亮信息
                    highlights = hit.get('highlight', {}).get('content', [])
                    
                    search_results.append({
                        'title': source.get('title', ''),
                        'content': source.get('content', ''),
                        'source': source.get('source', ''),
                        'category': source.get('category', ''),
                        'url': source.get('url', ''),
                        'keywords': source.get('keywords', []),
                        'score': score,
                        'highlights': highlights
                    })
                
                return search_results
            else:
                st.error(f"搜索失败: {response.text}")
                return []
                
        except Exception as e:
            st.error(f"搜索异常: {e}")
            return []
    
    def initialize(self):
        """初始化检索系统"""
        st.info("🔧 初始化Elasticsearch中医知识检索系统...")
        
        # 1. 检查Elasticsearch
        es_ok, es_msg = self.check_elasticsearch()
        if es_ok:
            st.success("✅ Elasticsearch连接成功")
            
            # 2. 设置索引
            index_ok, index_msg = self.setup_elasticsearch_index()
            if index_ok:
                st.success("✅ 索引设置成功")
                
                # 3. 爬取线上知识
                knowledge_items = self.crawl_online_knowledge()
                
                if knowledge_items:
                    # 4. 索引知识
                    index_result, index_msg = self.index_to_elasticsearch(knowledge_items)
                    if index_result:
                        st.success("✅ 知识库初始化完成")
                        self.initialized = True
                        return True
                    else:
                        st.error(f"❌ 知识索引失败: {index_msg}")
                else:
                    st.warning("⚠️ 未获取到线上知识")
            else:
                st.error(f"❌ 索引设置失败: {index_msg}")
        else:
            st.warning(f"⚠️ {es_msg}")
            st.info("💡 将使用简化的本地检索模式")
            self.initialized = True  # 启用本地模式
            return True
        
        return False

def main():
    """测试函数"""
    retriever = ElasticsearchTCMRetriever()
    
    print("🔍 Elasticsearch中医知识检索器测试")
    print("=" * 50)
    
    if retriever.initialize():
        print("✅ 初始化成功")
        
        # 测试搜索
        results = retriever.search_knowledge("气血不足")
        print(f"搜索结果: {len(results)} 条")
        
        for result in results[:3]:
            print(f"标题: {result['title']}")
            print(f"来源: {result['source']}")
            print(f"评分: {result['score']}")
            print("---")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
