#!/usr/bin/env python3
"""
测试增强版中医RAG系统
"""
import requests
import json
import time

def test_system():
    """测试系统功能"""
    base_url = "http://localhost:8006"
    
    print("🔍 测试增强版中医RAG系统")
    print("=" * 50)
    
    # 1. 健康检查
    print("1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 系统健康")
            print(f"   版本: {health_data.get('version', '未知')}")
            print(f"   文档数: {health_data.get('documents', 0)}")
            print(f"   功能: {', '.join(health_data.get('features', []))}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    # 2. 测试在线搜索功能
    print("\n2. 测试在线搜索功能...")
    test_questions = [
        "栀子甘草豉汤的功效是什么？",
        "脾胃虚弱的症状有哪些？",
        "肾阳虚和肾阴虚的区别",
        "失眠多梦的中医治疗方法"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n   测试问题 {i}: {question}")
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/chat",
                json={"message": question},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                processing_time = time.time() - start_time
                
                print(f"   ✅ 响应成功 ({processing_time:.2f}s)")
                print(f"   📝 回答长度: {len(data.get('response', ''))} 字符")
                print(f"   📚 资源数量: {len(data.get('sources', []))}")
                
                # 显示资源类型
                sources = data.get('sources', [])
                online_count = len([s for s in sources if s.get('type') == 'online'])
                local_count = len([s for s in sources if s.get('type') == 'pdf'])
                print(f"   🌐 在线资源: {online_count} 条")
                print(f"   📁 本地资源: {local_count} 条")
                
                # 显示部分回答
                answer = data.get('response', '')
                if answer:
                    preview = answer[:200] + "..." if len(answer) > 200 else answer
                    print(f"   💬 回答预览: {preview}")
                
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    return True

if __name__ == "__main__":
    test_system()
