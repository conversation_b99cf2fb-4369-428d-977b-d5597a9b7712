#!/usr/bin/env python3
"""
轻量级模型FastAPI服务器
专门用于Qwen2-1.5B-Instruct等轻量级模型 (适合6B以下笔记本)
"""

import asyncio
import logging
import time
import torch
from typing import Dict, List, Any, Optional
from pathlib import Path
import json

# FastAPI相关
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 模型加载
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    from sentence_transformers import SentenceTransformer
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    print("❌ transformers或sentence-transformers未安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API模型定义
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    max_tokens: int = 1024
    stream: bool = False

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class EmbeddingRequest(BaseModel):
    model: str
    input: List[str]

class EmbeddingResponse(BaseModel):
    object: str = "list"
    data: List[Dict[str, Any]]
    model: str
    usage: Dict[str, int]

class LightweightModelServer:
    """轻量级模型服务器 - 适合6B以下笔记本"""
    
    def __init__(self):
        self.app = FastAPI(title="Lightweight Model API Server", version="1.0.0")
        self.setup_cors()
        self.setup_routes()
        
        # 轻量级模型配置 - 使用已下载完成的模型
        self.model_config = {
            'chat_model_paths': [
                './models/chatglm3-6b',  # 首选：ChatGLM3-6B (已完整下载)
                './models/qwen2-1.5b-instruct',  # 备用：Qwen2-1.5B (部分下载)
            ],
            'embedding_model_path': './models/bge-m3',  # BGE-M3 (已完整下载)
            'device': 'cpu',  # 强制CPU，适合笔记本
            'torch_dtype': torch.float16,
            'max_memory_gb': 6  # ChatGLM3-6B需要约6GB
        }
        
        # 模型实例
        self.chat_model = None
        self.chat_tokenizer = None
        self.embedding_model = None
        self.current_model_name = None
        self.initialized = False
    
    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            return {
                "status": "healthy" if self.initialized else "initializing",
                "chat_model_loaded": self.chat_model is not None,
                "embedding_loaded": self.embedding_model is not None,
                "current_model": self.current_model_name,
                "device": self.model_config['device']
            }
        
        @self.app.get("/v1/models")
        async def list_models():
            models = []
            if self.chat_model:
                models.append({
                    "id": "qwen2-1.5b-instruct",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "local"
                })
            if self.embedding_model:
                models.append({
                    "id": "bge-m3",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "local"
                })
            return {"object": "list", "data": models}
        
        @self.app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
        async def chat_completions(request: ChatCompletionRequest):
            if not self.chat_model or not self.chat_tokenizer:
                raise HTTPException(status_code=503, detail="聊天模型未加载")
            
            try:
                # 构建消息
                messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]
                
                # 应用聊天模板
                text = self.chat_tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
                
                # 编码输入
                model_inputs = self.chat_tokenizer([text], return_tensors="pt")
                
                # 生成回答
                with torch.no_grad():
                    generated_ids = self.chat_model.generate(
                        model_inputs.input_ids,
                        max_new_tokens=min(request.max_tokens, 1024),  # 限制最大token数
                        temperature=request.temperature,
                        do_sample=True,
                        pad_token_id=self.chat_tokenizer.eos_token_id,
                        eos_token_id=self.chat_tokenizer.eos_token_id
                    )
                
                # 解码回答
                generated_ids = [
                    output_ids[len(input_ids):] 
                    for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
                ]
                
                response_text = self.chat_tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
                
                # 构建响应
                completion_id = f"chatcmpl-{int(time.time())}"
                
                return ChatCompletionResponse(
                    id=completion_id,
                    created=int(time.time()),
                    model=request.model,
                    choices=[{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_text.strip()
                        },
                        "finish_reason": "stop"
                    }],
                    usage={
                        "prompt_tokens": len(model_inputs.input_ids[0]),
                        "completion_tokens": len(generated_ids[0]),
                        "total_tokens": len(model_inputs.input_ids[0]) + len(generated_ids[0])
                    }
                )
                
            except Exception as e:
                logger.error(f"聊天完成失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/v1/embeddings", response_model=EmbeddingResponse)
        async def create_embeddings(request: EmbeddingRequest):
            if not self.embedding_model:
                raise HTTPException(status_code=503, detail="嵌入模型未加载")
            
            try:
                # 生成嵌入
                embeddings = self.embedding_model.encode(request.input)
                
                # 构建响应
                data = []
                for i, embedding in enumerate(embeddings):
                    data.append({
                        "object": "embedding",
                        "index": i,
                        "embedding": embedding.tolist()
                    })
                
                return EmbeddingResponse(
                    data=data,
                    model=request.model,
                    usage={
                        "prompt_tokens": sum(len(text.split()) for text in request.input),
                        "total_tokens": sum(len(text.split()) for text in request.input)
                    }
                )
                
            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def find_available_chat_model(self) -> Optional[str]:
        """寻找可用的聊天模型"""
        for model_path in self.model_config['chat_model_paths']:
            if Path(model_path).exists():
                # 检查是否有必要的文件
                config_file = Path(model_path) / "config.json"
                if config_file.exists():
                    logger.info(f"✅ 找到可用聊天模型: {model_path}")
                    return model_path
        
        logger.error("❌ 未找到可用的聊天模型")
        return None
    
    async def initialize_models(self):
        """初始化模型"""
        try:
            logger.info("🚀 开始初始化轻量级模型服务器...")
            
            if not MODELS_AVAILABLE:
                logger.error("❌ transformers或sentence-transformers未安装")
                return False
            
            # 1. 加载聊天模型
            chat_model_path = self.find_available_chat_model()
            if not chat_model_path:
                logger.error("❌ 未找到可用的聊天模型")
                return False
            
            logger.info(f"📥 加载聊天模型: {chat_model_path}")
            
            # 加载tokenizer
            self.chat_tokenizer = AutoTokenizer.from_pretrained(
                chat_model_path,
                trust_remote_code=True
            )
            
            # 加载模型
            self.chat_model = AutoModelForCausalLM.from_pretrained(
                chat_model_path,
                torch_dtype=self.model_config['torch_dtype'],
                device_map=self.model_config['device'],
                trust_remote_code=True,
                low_cpu_mem_usage=True  # 减少内存使用
            )
            
            self.chat_model.eval()
            self.current_model_name = Path(chat_model_path).name
            
            logger.info(f"✅ 聊天模型加载成功: {self.current_model_name}")
            
            # 2. 加载嵌入模型
            embedding_path = Path(self.model_config['embedding_model_path'])
            if embedding_path.exists():
                logger.info(f"📥 加载嵌入模型: {embedding_path}")
                self.embedding_model = SentenceTransformer(str(embedding_path))
                logger.info("✅ 嵌入模型加载成功")
            else:
                logger.warning(f"⚠️ 嵌入模型路径不存在: {embedding_path}")
            
            # 3. 测试模型
            logger.info("🧪 测试模型...")
            test_messages = [
                {"role": "system", "content": "你是一个专业的中医助手。"},
                {"role": "user", "content": "你好"}
            ]
            
            text = self.chat_tokenizer.apply_chat_template(
                test_messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            model_inputs = self.chat_tokenizer([text], return_tensors="pt")
            
            with torch.no_grad():
                generated_ids = self.chat_model.generate(
                    model_inputs.input_ids,
                    max_new_tokens=50,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.chat_tokenizer.eos_token_id
                )
            
            generated_ids = [
                output_ids[len(input_ids):] 
                for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            test_response = self.chat_tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            logger.info(f"🎯 测试回答: {test_response.strip()}")
            
            self.initialized = True
            logger.info("🎉 轻量级模型服务器初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            return False
    
    async def start_server(self, host: str = "127.0.0.1", port: int = 8002):
        """启动服务器"""
        if not await self.initialize_models():
            logger.error("❌ 模型初始化失败，无法启动服务器")
            return
        
        logger.info(f"🌐 启动轻量级模型API服务器: http://{host}:{port}")
        logger.info("📋 可用端点:")
        logger.info("  - GET  /health")
        logger.info("  - GET  /v1/models")
        logger.info("  - POST /v1/chat/completions")
        logger.info("  - POST /v1/embeddings")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

# 全局服务器实例
lightweight_server = LightweightModelServer()

async def main():
    """主函数"""
    await lightweight_server.start_server()

if __name__ == "__main__":
    asyncio.run(main())
