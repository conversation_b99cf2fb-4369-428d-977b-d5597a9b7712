#!/usr/bin/env python3
"""
智能RAG检索器 - 解决检索准确性问题
结合多种检索策略：向量检索、关键词匹配、语义重排序
"""

import numpy as np
import faiss
import pickle
import json
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from sentence_transformers import SentenceTransformer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentRAGRetriever:
    """智能RAG检索器"""
    
    def __init__(self, config_path: str = "config.py"):
        self.config = self._load_config(config_path)
        
        # 模型和索引
        self.embedding_model = None
        self.vector_index = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        
        # 数据存储
        self.chunks = []
        self.metadata = []
        self.chunk_embeddings = None
        
        # 中医专业词典
        self.tcm_keywords = self._load_tcm_keywords()
        
        # 初始化标志
        self.initialized = False
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置"""
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("config", config_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)
            
            return {
                'CHUNK_SIZE': getattr(config_module, 'CHUNK_SIZE', 500),
                'CHUNK_OVERLAP': getattr(config_module, 'CHUNK_OVERLAP', 100),
                'TOP_K_RETRIEVAL': getattr(config_module, 'TOP_K_RETRIEVAL', 5),
                'SIMILARITY_THRESHOLD': getattr(config_module, 'SIMILARITY_THRESHOLD', 0.35),
                'MIN_RELEVANCE_SCORE': getattr(config_module, 'MIN_RELEVANCE_SCORE', 0.35),
                'RERANK_THRESHOLD': getattr(config_module, 'RERANK_THRESHOLD', 0.5),
                'EMBEDDING_MODEL': getattr(config_module, 'EMBEDDING_MODEL', 'moka-ai/m3e-base'),
                'VECTOR_DB_PATH': getattr(config_module, 'VECTOR_DB_PATH', Path('vector_db')),
                'DOCUMENTS_DIR': getattr(config_module, 'DOCUMENTS_DIR', Path('documents'))
            }
        except Exception as e:
            logger.warning(f"配置加载失败，使用默认配置: {e}")
            return {
                'CHUNK_SIZE': 500,
                'CHUNK_OVERLAP': 100,
                'TOP_K_RETRIEVAL': 5,
                'SIMILARITY_THRESHOLD': 0.35,
                'MIN_RELEVANCE_SCORE': 0.35,
                'RERANK_THRESHOLD': 0.5,
                'EMBEDDING_MODEL': 'moka-ai/m3e-base',
                'VECTOR_DB_PATH': Path('vector_db'),
                'DOCUMENTS_DIR': Path('documents')
            }
    
    def _load_tcm_keywords(self) -> Dict[str, List[str]]:
        """加载中医专业关键词"""
        return {
            '症状': ['头痛', '发热', '咳嗽', '胸闷', '腹痛', '腰痛', '失眠', '乏力', '食欲不振'],
            '病证': ['感冒', '咳嗽', '哮喘', '胃痛', '腹泻', '便秘', '失眠', '抑郁', '焦虑'],
            '脏腑': ['心', '肝', '脾', '肺', '肾', '胃', '胆', '小肠', '大肠', '膀胱', '三焦'],
            '病理': ['气虚', '血虚', '阴虚', '阳虚', '气滞', '血瘀', '痰湿', '湿热', '寒湿'],
            '治法': ['补气', '养血', '滋阴', '温阳', '理气', '活血', '化痰', '利湿', '清热'],
            '方剂': ['四君子汤', '四物汤', '六味地黄丸', '逍遥散', '补中益气汤', '当归补血汤'],
            '药材': ['人参', '黄芪', '当归', '川芎', '白芍', '熟地', '茯苓', '甘草', '陈皮']
        }
    
    def initialize(self) -> bool:
        """初始化检索器"""
        try:
            logger.info("初始化智能RAG检索器...")
            
            # 初始化嵌入模型
            logger.info("加载嵌入模型...")
            self.embedding_model = SentenceTransformer(self.config['EMBEDDING_MODEL'])
            
            # 加载向量数据库
            if self._load_vector_database():
                logger.info("向量数据库加载成功")
            else:
                logger.warning("向量数据库加载失败，需要重新构建")
                return False
            
            # 初始化TF-IDF
            self._initialize_tfidf()
            
            self.initialized = True
            logger.info("智能RAG检索器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def _load_vector_database(self) -> bool:
        """加载向量数据库"""
        try:
            vector_db_path = Path(self.config['VECTOR_DB_PATH'])
            
            # 加载FAISS索引
            index_file = vector_db_path / "index.faiss"
            if index_file.exists():
                self.vector_index = faiss.read_index(str(index_file))
            else:
                logger.warning("FAISS索引文件不存在")
                return False
            
            # 加载文档块
            chunks_file = vector_db_path / "chunks.pkl"
            if chunks_file.exists():
                with open(chunks_file, 'rb') as f:
                    self.chunks = pickle.load(f)
            else:
                logger.warning("文档块文件不存在")
                return False
            
            # 加载元数据
            metadata_file = vector_db_path / "metadata.pkl"
            if metadata_file.exists():
                with open(metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)
            else:
                # 尝试JSON格式
                json_file = vector_db_path / "metadata.json"
                if json_file.exists():
                    with open(json_file, 'r', encoding='utf-8') as f:
                        self.metadata = json.load(f)
                else:
                    logger.warning("元数据文件不存在")
                    return False
            
            logger.info(f"加载了 {len(self.chunks)} 个文档块")
            return True
            
        except Exception as e:
            logger.error(f"加载向量数据库失败: {e}")
            return False
    
    def _initialize_tfidf(self):
        """初始化TF-IDF向量化器"""
        try:
            if not self.chunks:
                return
            
            # 准备文本数据
            texts = []
            for chunk in self.chunks:
                if isinstance(chunk, dict):
                    text = chunk.get('content', str(chunk))
                else:
                    text = str(chunk)
                texts.append(text)
            
            # 创建TF-IDF向量化器
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=5000,
                stop_words=None,  # 中文不使用英文停用词
                ngram_range=(1, 2),
                tokenizer=lambda x: list(jieba.cut(x))
            )
            
            # 拟合并转换文本
            self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(texts)
            logger.info("TF-IDF初始化完成")
            
        except Exception as e:
            logger.error(f"TF-IDF初始化失败: {e}")
    
    def search(self, query: str, top_k: Optional[int] = None) -> List[Dict]:
        """智能搜索 - 结合多种检索策略"""
        if not self.initialized:
            logger.error("检索器未初始化")
            return []
        
        if top_k is None:
            top_k = self.config['TOP_K_RETRIEVAL']
        
        try:
            # 1. 向量检索
            vector_results = self._vector_search(query, top_k * 2)
            
            # 2. 关键词检索
            keyword_results = self._keyword_search(query, top_k * 2)
            
            # 3. TF-IDF检索
            tfidf_results = self._tfidf_search(query, top_k * 2)
            
            # 4. 结果融合和重排序
            final_results = self._merge_and_rerank(
                query, vector_results, keyword_results, tfidf_results, top_k
            )
            
            return final_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def _vector_search(self, query: str, top_k: int) -> List[Dict]:
        """向量检索"""
        try:
            if not self.vector_index or not self.embedding_model:
                return []
            
            # 生成查询向量
            query_embedding = self.embedding_model.encode([query])
            query_vector = np.array(query_embedding).astype('float32')
            faiss.normalize_L2(query_vector)
            
            # 搜索
            scores, indices = self.vector_index.search(query_vector, top_k)
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if 0 <= idx < len(self.chunks) and score >= self.config['SIMILARITY_THRESHOLD']:
                    result = {
                        'content': self.chunks[idx] if isinstance(self.chunks[idx], str) else self.chunks[idx].get('content', ''),
                        'metadata': self.metadata[idx] if idx < len(self.metadata) else {},
                        'score': float(score),
                        'method': 'vector',
                        'index': idx
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []

    def _keyword_search(self, query: str, top_k: int) -> List[Dict]:
        """关键词检索"""
        try:
            results = []
            query_lower = query.lower()

            # 提取查询中的中医关键词
            query_keywords = self._extract_tcm_keywords(query)

            for idx, chunk in enumerate(self.chunks):
                content = chunk if isinstance(chunk, str) else chunk.get('content', '')
                content_lower = content.lower()

                score = 0

                # 精确匹配
                if query_lower in content_lower:
                    score += 2.0

                # 关键词匹配
                for keyword in query_keywords:
                    if keyword.lower() in content_lower:
                        score += 1.0

                # 中医专业术语匹配
                for category, keywords in self.tcm_keywords.items():
                    for keyword in keywords:
                        if keyword in query and keyword in content:
                            score += 1.5

                if score > 0:
                    result = {
                        'content': content,
                        'metadata': self.metadata[idx] if idx < len(self.metadata) else {},
                        'score': score,
                        'method': 'keyword',
                        'index': idx
                    }
                    results.append(result)

            # 按分数排序
            results.sort(key=lambda x: x['score'], reverse=True)
            return results[:top_k]

        except Exception as e:
            logger.error(f"关键词检索失败: {e}")
            return []

    def _tfidf_search(self, query: str, top_k: int) -> List[Dict]:
        """TF-IDF检索"""
        try:
            if not self.tfidf_vectorizer or self.tfidf_matrix is None:
                return []

            # 转换查询
            query_vector = self.tfidf_vectorizer.transform([query])

            # 计算相似度
            similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()

            # 获取top_k结果
            top_indices = similarities.argsort()[-top_k:][::-1]

            results = []
            for idx in top_indices:
                if similarities[idx] >= self.config['MIN_RELEVANCE_SCORE']:
                    content = self.chunks[idx] if isinstance(self.chunks[idx], str) else self.chunks[idx].get('content', '')
                    result = {
                        'content': content,
                        'metadata': self.metadata[idx] if idx < len(self.metadata) else {},
                        'score': float(similarities[idx]),
                        'method': 'tfidf',
                        'index': idx
                    }
                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"TF-IDF检索失败: {e}")
            return []

    def _extract_tcm_keywords(self, text: str) -> List[str]:
        """提取中医关键词"""
        keywords = []
        text_lower = text.lower()

        for category, keyword_list in self.tcm_keywords.items():
            for keyword in keyword_list:
                if keyword in text_lower:
                    keywords.append(keyword)

        # 使用jieba分词提取更多关键词
        words = jieba.cut(text)
        for word in words:
            if len(word) >= 2 and word not in keywords:
                keywords.append(word)

        return keywords

    def _merge_and_rerank(self, query: str, vector_results: List[Dict],
                         keyword_results: List[Dict], tfidf_results: List[Dict],
                         top_k: int) -> List[Dict]:
        """结果融合和重排序"""
        try:
            # 收集所有结果
            all_results = {}

            # 向量检索结果 (权重: 0.4)
            for result in vector_results:
                idx = result['index']
                if idx not in all_results:
                    all_results[idx] = result.copy()
                    all_results[idx]['combined_score'] = result['score'] * 0.4
                    all_results[idx]['methods'] = [result['method']]
                else:
                    all_results[idx]['combined_score'] += result['score'] * 0.4
                    all_results[idx]['methods'].append(result['method'])

            # 关键词检索结果 (权重: 0.3)
            for result in keyword_results:
                idx = result['index']
                if idx not in all_results:
                    all_results[idx] = result.copy()
                    all_results[idx]['combined_score'] = result['score'] * 0.3
                    all_results[idx]['methods'] = [result['method']]
                else:
                    all_results[idx]['combined_score'] += result['score'] * 0.3
                    if result['method'] not in all_results[idx]['methods']:
                        all_results[idx]['methods'].append(result['method'])

            # TF-IDF检索结果 (权重: 0.3)
            for result in tfidf_results:
                idx = result['index']
                if idx not in all_results:
                    all_results[idx] = result.copy()
                    all_results[idx]['combined_score'] = result['score'] * 0.3
                    all_results[idx]['methods'] = [result['method']]
                else:
                    all_results[idx]['combined_score'] += result['score'] * 0.3
                    if result['method'] not in all_results[idx]['methods']:
                        all_results[idx]['methods'].append(result['method'])

            # 多方法匹配加分
            for idx, result in all_results.items():
                if len(result['methods']) > 1:
                    result['combined_score'] *= 1.2  # 20%加分

            # 语义重排序
            final_results = list(all_results.values())
            final_results = self._semantic_rerank(query, final_results)

            # 按综合分数排序
            final_results.sort(key=lambda x: x['combined_score'], reverse=True)

            return final_results[:top_k]

        except Exception as e:
            logger.error(f"结果融合失败: {e}")
            return []

    def _semantic_rerank(self, query: str, results: List[Dict]) -> List[Dict]:
        """语义重排序"""
        try:
            if not self.embedding_model:
                return results

            # 生成查询嵌入
            query_embedding = self.embedding_model.encode([query])[0]

            # 为每个结果计算语义相似度
            for result in results:
                content = result['content'][:500]  # 限制长度
                content_embedding = self.embedding_model.encode([content])[0]

                # 计算余弦相似度
                semantic_score = np.dot(query_embedding, content_embedding) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(content_embedding)
                )

                # 语义相似度加权
                if semantic_score >= self.config['RERANK_THRESHOLD']:
                    result['combined_score'] *= (1 + semantic_score * 0.3)

                result['semantic_score'] = float(semantic_score)

            return results

        except Exception as e:
            logger.error(f"语义重排序失败: {e}")
            return results

    def get_retrieval_stats(self) -> Dict:
        """获取检索统计信息"""
        # 转换Path对象为字符串以支持JSON序列化
        config_serializable = {}
        for key, value in self.config.items():
            if hasattr(value, '__fspath__'):  # Path对象
                config_serializable[key] = str(value)
            else:
                config_serializable[key] = value

        return {
            'total_chunks': len(self.chunks),
            'total_metadata': len(self.metadata),
            'vector_index_size': self.vector_index.ntotal if self.vector_index else 0,
            'tfidf_features': self.tfidf_matrix.shape[1] if self.tfidf_matrix is not None else 0,
            'config': config_serializable,
            'initialized': self.initialized
        }
