#!/usr/bin/env python3
"""
终极Docker部署管理器
支持跨硬件移植和一键部署
"""

import os
import subprocess
import json
from pathlib import Path
import shutil
import zipfile

class UltimateDockerManager:
    """终极Docker部署管理器"""
    
    def __init__(self):
        self.project_name = "ultimate-tcm-rag"
        self.version = "1.0.0"
        
    def create_dockerfile(self):
        """创建优化的Dockerfile"""
        dockerfile_content = '''# 终极中医RAG系统 - 生产级Docker镜像
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc g++ cmake build-essential \\
    portaudio19-dev python3-pyaudio \\
    espeak espeak-data libespeak1 libespeak-dev \\
    curl wget git \\
    ffmpeg \\
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements_ultimate.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \\
    pip install --no-cache-dir -r requirements_ultimate.txt

# 复制应用代码
COPY ultimate_final_tcm_system.py .
COPY deepseek_api_manager.py .

# 创建必要目录
RUN mkdir -p /app/ultimate_final_vector_db \\
    /app/documents \\
    /app/conversations \\
    /app/logs \\
    /app/models \\
    /app/cache

# 设置权限
RUN chmod -R 755 /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost:8507/_stcore/health || exit 1

# 暴露端口
EXPOSE 8507

# 启动命令
CMD ["python", "-m", "streamlit", "run", "ultimate_final_tcm_system.py", \\
     "--server.port=8507", \\
     "--server.address=0.0.0.0", \\
     "--theme.base=light", \\
     "--server.headless=true", \\
     "--server.enableCORS=false", \\
     "--server.enableXsrfProtection=false"]
'''
        
        with open("Dockerfile.ultimate", "w", encoding="utf-8") as f:
            f.write(dockerfile_content)
        
        print("✅ 创建了终极版Dockerfile")
    
    def create_docker_compose(self):
        """创建docker-compose文件"""
        compose_content = '''version: '3.8'

services:
  ultimate-tcm-rag:
    build:
      context: .
      dockerfile: Dockerfile.ultimate
    container_name: ultimate-tcm-rag
    ports:
      - "8507:8507"
    volumes:
      - ./ultimate_final_vector_db:/app/ultimate_final_vector_db
      - ./documents:/app/documents
      - ./conversations:/app/conversations
      - ./logs:/app/logs
      - ./cache:/app/cache
    environment:
      - PYTHONUNBUFFERED=1
      - STREAMLIT_SERVER_PORT=8507
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8507/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  # 可选：Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: tcm-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - ultimate-tcm-rag
    restart: unless-stopped

networks:
  default:
    name: tcm-network
'''
        
        with open("docker-compose.ultimate.yml", "w", encoding="utf-8") as f:
            f.write(compose_content)
        
        print("✅ 创建了docker-compose文件")
    
    def create_requirements(self):
        """创建requirements文件"""
        requirements_content = '''# 终极中医RAG系统依赖
streamlit>=1.28.0
numpy>=1.24.0
pandas>=2.0.0
requests>=2.31.0
beautifulsoup4>=4.12.0
PyPDF2>=3.0.0
python-docx>=0.8.11
openpyxl>=3.1.0
python-pptx>=0.6.21
sentence-transformers>=2.2.2
faiss-cpu>=1.7.4
pyttsx3>=2.90
SpeechRecognition>=3.10.0
llama-cpp-python>=0.2.0
pyaudio>=0.2.11
'''
        
        with open("requirements_ultimate.txt", "w", encoding="utf-8") as f:
            f.write(requirements_content)
        
        print("✅ 创建了requirements文件")
    
    def create_nginx_config(self):
        """创建Nginx配置"""
        nginx_content = '''events {
    worker_connections 1024;
}

http {
    upstream tcm_app {
        server ultimate-tcm-rag:8507;
    }

    server {
        listen 80;
        server_name _;

        location / {
            proxy_pass http://tcm_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
'''
        
        with open("nginx.conf", "w", encoding="utf-8") as f:
            f.write(nginx_content)
        
        print("✅ 创建了Nginx配置")
    
    def create_deployment_scripts(self):
        """创建部署脚本"""
        
        # 构建脚本
        build_script = '''#!/bin/bash
echo "🚀 构建终极中医RAG系统Docker镜像"

# 构建镜像
docker build -f Dockerfile.ultimate -t ultimate-tcm-rag:latest .

# 标记版本
docker tag ultimate-tcm-rag:latest ultimate-tcm-rag:1.0.0

echo "✅ 镜像构建完成"
docker images | grep ultimate-tcm-rag
'''
        
        with open("build.sh", "w", encoding="utf-8") as f:
            f.write(build_script)
        
        # 启动脚本
        start_script = '''#!/bin/bash
echo "🚀 启动终极中医RAG系统"

# 创建必要目录
mkdir -p ultimate_final_vector_db documents conversations logs cache

# 启动服务
docker-compose -f docker-compose.ultimate.yml up -d

echo "✅ 系统启动完成"
echo "🌐 访问地址: http://localhost:8507"
echo "📊 查看状态: docker-compose -f docker-compose.ultimate.yml ps"
'''
        
        with open("start.sh", "w", encoding="utf-8") as f:
            f.write(start_script)
        
        # 停止脚本
        stop_script = '''#!/bin/bash
echo "⏹️ 停止终极中医RAG系统"

docker-compose -f docker-compose.ultimate.yml down

echo "✅ 系统已停止"
'''
        
        with open("stop.sh", "w", encoding="utf-8") as f:
            f.write(stop_script)
        
        # 设置执行权限
        os.chmod("build.sh", 0o755)
        os.chmod("start.sh", 0o755)
        os.chmod("stop.sh", 0o755)
        
        print("✅ 创建了部署脚本")
    
    def create_export_package(self):
        """创建导出包"""
        package_name = f"{self.project_name}-{self.version}.zip"
        
        with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加核心文件
            files_to_include = [
                "ultimate_final_tcm_system.py",
                "deepseek_api_manager.py",
                "Dockerfile.ultimate",
                "docker-compose.ultimate.yml",
                "requirements_ultimate.txt",
                "nginx.conf",
                "build.sh",
                "start.sh",
                "stop.sh"
            ]
            
            for file in files_to_include:
                if os.path.exists(file):
                    zipf.write(file)
            
            # 添加部署指南
            zipf.writestr("DEPLOYMENT_GUIDE.md", self.create_deployment_guide())
        
        print(f"✅ 创建了导出包: {package_name}")
        return package_name
    
    def create_deployment_guide(self) -> str:
        """创建部署指南"""
        guide_content = '''# 终极中医RAG系统部署指南

## 🚀 快速部署

### 1. 解压部署包
```bash
unzip ultimate-tcm-rag-1.0.0.zip
cd ultimate-tcm-rag-1.0.0
```

### 2. 构建镜像
```bash
chmod +x build.sh
./build.sh
```

### 3. 启动系统
```bash
chmod +x start.sh
./start.sh
```

### 4. 访问系统
- 本地访问: http://localhost:8507
- 如果配置了Nginx: http://localhost

## 🔧 配置说明

### DeepSeek模型配置
1. 确保模型文件存在于指定路径
2. 或者启动LM Studio API服务
3. 系统会自动选择最佳调用方式

### 数据持久化
- 向量数据库: `./ultimate_final_vector_db`
- 文档存储: `./documents`
- 对话历史: `./conversations`
- 日志文件: `./logs`

### 远程访问
1. 安装ngrok: https://ngrok.com/
2. 在系统界面中点击"启动远程访问"
3. 使用生成的公网地址访问

## 🐳 Docker命令

### 查看状态
```bash
docker-compose -f docker-compose.ultimate.yml ps
```

### 查看日志
```bash
docker-compose -f docker-compose.ultimate.yml logs -f
```

### 停止系统
```bash
./stop.sh
```

### 重启系统
```bash
./stop.sh && ./start.sh
```

## 📱 移动端访问

系统支持移动端访问，界面会自动适配手机屏幕。

## 🔒 安全配置

### 1. 设置访问密码
在nginx.conf中添加基本认证

### 2. HTTPS配置
将SSL证书放在./ssl目录下

### 3. 防火墙设置
只开放必要端口（80, 443, 8507）

## 🚨 故障排除

### 1. 容器无法启动
```bash
docker logs ultimate-tcm-rag
```

### 2. 端口冲突
修改docker-compose.ultimate.yml中的端口映射

### 3. 内存不足
调整docker-compose.ultimate.yml中的内存限制

## 📞 技术支持

如有问题，请检查：
1. Docker和docker-compose版本
2. 系统内存是否充足（建议4GB+）
3. 网络连接是否正常
4. 模型文件是否存在

## 🔄 更新系统

1. 下载新版本部署包
2. 停止当前系统: `./stop.sh`
3. 备份数据目录
4. 解压新版本并重新部署
5. 恢复数据目录
'''
        
        return guide_content
    
    def deploy_all(self):
        """一键部署所有文件"""
        print("🚀 开始创建终极部署包...")
        
        self.create_dockerfile()
        self.create_docker_compose()
        self.create_requirements()
        self.create_nginx_config()
        self.create_deployment_scripts()
        package_name = self.create_export_package()
        
        print(f"\n🎉 终极部署包创建完成！")
        print(f"📦 包名: {package_name}")
        print(f"📁 包含文件:")
        print("   - 终极系统代码")
        print("   - Docker配置文件")
        print("   - 部署脚本")
        print("   - 详细部署指南")
        print(f"\n💡 使用方法:")
        print(f"   1. 将 {package_name} 复制到目标服务器")
        print(f"   2. 解压并运行 ./build.sh")
        print(f"   3. 运行 ./start.sh 启动系统")

if __name__ == "__main__":
    manager = UltimateDockerManager()
    manager.deploy_all()
