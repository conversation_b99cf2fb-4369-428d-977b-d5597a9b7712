#!/usr/bin/env python3
"""
手机版TCM系统 - 极简语音+文本聊天界面
专为移动设备优化，通过ngrok远程访问
"""

import streamlit as st
import time
import json
import uuid
from datetime import datetime
from pathlib import Path

# 导入核心功能
try:
    from intelligent_mcp_client import IntelligentMCPRetriever
    from simple_llm_manager import SimpleLLMManager
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置 - 移动端优化
st.set_page_config(
    page_title="🏥 家庭医生助手",
    page_icon="🏥",
    layout="centered",  # 居中布局适合手机
    initial_sidebar_state="collapsed"  # 隐藏侧边栏
)

# 移动端CSS样式
st.markdown("""
<style>
/* 隐藏Streamlit默认元素 */
#MainMenu {visibility: hidden;}
footer {visibility: hidden;}
header {visibility: hidden;}

/* 移动端优化 */
.main .block-container {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
}

/* 聊天界面样式 */
.chat-message {
    padding: 1rem;
    border-radius: 0.8rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
}

.user-message {
    background-color: #007AFF;
    color: white;
    align-self: flex-end;
    margin-left: 20%;
}

.assistant-message {
    background-color: #F2F2F7;
    color: black;
    align-self: flex-start;
    margin-right: 20%;
}

/* 语音按钮样式 */
.voice-button {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    font-size: 2rem;
    color: white;
    margin: 1rem auto;
    display: block;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* 输入框样式 */
.stTextInput > div > div > input {
    border-radius: 25px;
    border: 2px solid #E5E5EA;
    padding: 12px 20px;
    font-size: 16px;
}

/* 发送按钮样式 */
.stButton > button {
    background-color: #007AFF;
    color: white;
    border-radius: 25px;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    width: 100%;
}

/* 标题样式 */
h1 {
    text-align: center;
    color: #007AFF;
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main .block-container {
        padding: 0.5rem;
    }
    
    .chat-message {
        margin-left: 5%;
        margin-right: 5%;
    }
    
    .user-message {
        margin-left: 10%;
    }
    
    .assistant-message {
        margin-right: 10%;
    }
}
</style>
""", unsafe_allow_html=True)

class MobileTCMChat:
    """手机版TCM聊天系统"""
    
    def __init__(self):
        self.session_id = self.get_or_create_session()
        self.retriever = None
        self.llm_manager = None
        self.init_services()
    
    def get_or_create_session(self):
        """获取或创建会话ID"""
        if 'mobile_session_id' not in st.session_state:
            st.session_state.mobile_session_id = str(uuid.uuid4())
        return st.session_state.mobile_session_id
    
    def init_services(self):
        """初始化服务"""
        if 'mobile_retriever' not in st.session_state:
            with st.spinner("🔧 初始化智能检索..."):
                try:
                    self.retriever = IntelligentMCPRetriever()
                    if self.retriever.initialize():
                        st.session_state.mobile_retriever = self.retriever
                    else:
                        st.error("❌ 智能检索初始化失败")
                        return False
                except Exception as e:
                    st.error(f"❌ 检索服务错误: {e}")
                    return False
        else:
            self.retriever = st.session_state.mobile_retriever
        
        if 'mobile_llm' not in st.session_state:
            with st.spinner("🧠 初始化AI模型..."):
                try:
                    self.llm_manager = SimpleLLMManager()
                    st.session_state.mobile_llm = self.llm_manager
                except Exception as e:
                    st.error(f"❌ AI模型错误: {e}")
                    return False
        else:
            self.llm_manager = st.session_state.mobile_llm
        
        return True
    
    def get_chat_history(self):
        """获取聊天历史"""
        if 'mobile_chat_history' not in st.session_state:
            st.session_state.mobile_chat_history = []
        return st.session_state.mobile_chat_history
    
    def add_message(self, role, content):
        """添加消息到历史"""
        history = self.get_chat_history()
        history.append({
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat()
        })
        st.session_state.mobile_chat_history = history
    
    def display_chat_history(self):
        """显示聊天历史"""
        history = self.get_chat_history()
        
        if not history:
            st.markdown("""
            <div style="text-align: center; color: #8E8E93; padding: 2rem;">
                👋 您好！我是您的家庭中医助手<br>
                💬 请输入您的健康问题<br>
                🎤 或点击语音按钮说话
            </div>
            """, unsafe_allow_html=True)
            return
        
        # 显示最近的10条消息
        recent_messages = history[-10:] if len(history) > 10 else history
        
        for msg in recent_messages:
            role = msg['role']
            content = msg['content']
            
            if role == 'user':
                st.markdown(f"""
                <div class="chat-message user-message">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">您</div>
                    <div>{content}</div>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div class="chat-message assistant-message">
                    <div style="font-weight: bold; margin-bottom: 0.5rem; color: #007AFF;">🏥 医生助手</div>
                    <div>{content}</div>
                </div>
                """, unsafe_allow_html=True)
    
    def generate_response(self, user_input):
        """生成AI回复"""
        try:
            # 智能检索相关知识
            with st.spinner("🔍 检索相关知识..."):
                results = self.retriever.search_by_domain(user_input, "medical", 3)
            
            if not results:
                return "抱歉，我没有找到相关的医学知识。请尝试更具体的描述您的症状。"
            
            # 构建上下文
            context = "\n".join([f"参考资料{i+1}: {r.get('content', '')[:200]}..." 
                               for i, r in enumerate(results)])
            
            # 生成回复
            with st.spinner("🧠 AI分析中..."):
                prompt = f"""作为专业的中医助手，请根据以下参考资料回答用户问题。

用户问题: {user_input}

参考资料:
{context}

请提供：
1. 简洁明确的回答
2. 实用的建议
3. 必要时提醒就医

回答要求：
- 语言通俗易懂
- 适合手机阅读
- 200字以内
- 避免过于专业的术语"""

                response = self.llm_manager.generate_response(prompt)
                return response if response else "抱歉，AI服务暂时不可用，请稍后重试。"
        
        except Exception as e:
            return f"处理您的问题时出现错误，请重试。错误信息: {str(e)}"
    
    def voice_input_placeholder(self):
        """语音输入占位符"""
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0;">
            <button class="voice-button" onclick="alert('语音功能开发中，请使用文字输入')">
                🎤
            </button>
            <div style="color: #8E8E93; font-size: 0.9rem; margin-top: 0.5rem;">
                点击语音输入 (开发中)
            </div>
        </div>
        """, unsafe_allow_html=True)

def main():
    """主函数"""
    # 标题
    st.markdown("# 🏥 家庭医生助手")
    
    # 初始化聊天系统
    chat = MobileTCMChat()
    
    # 显示聊天历史
    chat.display_chat_history()
    
    # 语音输入区域
    chat.voice_input_placeholder()
    
    # 文本输入区域
    st.markdown("### 💬 文字咨询")
    
    # 创建输入表单
    with st.form(key="mobile_chat_form", clear_on_submit=True):
        user_input = st.text_input(
            label="请输入您的问题",
            placeholder="例如：失眠多梦怎么办？",
            label_visibility="collapsed"
        )
        
        col1, col2 = st.columns([3, 1])
        with col1:
            submit_button = st.form_submit_button("💬 发送", use_container_width=True)
        with col2:
            clear_button = st.form_submit_button("🗑️ 清空")
    
    # 处理用户输入
    if submit_button and user_input.strip():
        # 添加用户消息
        chat.add_message('user', user_input.strip())
        
        # 生成AI回复
        with st.spinner("🤔 思考中..."):
            response = chat.generate_response(user_input.strip())
        
        # 添加AI回复
        chat.add_message('assistant', response)
        
        # 重新运行以显示新消息
        st.rerun()
    
    # 清空聊天历史
    if clear_button:
        st.session_state.mobile_chat_history = []
        st.rerun()
    
    # 底部信息
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #8E8E93; font-size: 0.8rem;">
        🏥 家庭私人医生小帮手 | 移动版<br>
        ⚠️ 仅供参考，严重症状请及时就医
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
