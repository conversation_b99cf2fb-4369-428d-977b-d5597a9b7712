#!/usr/bin/env python3
"""
Qwen2.5 Ollama API管理器
专门管理适合您硬件的Qwen2.5-7B-Instruct模型
"""

import requests
import subprocess
import time
import streamlit as st

class QwenOllamaManager:
    """Qwen2.5 Ollama API管理器"""
    
    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        self.ollama_exe = r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe"
        self.model_name = "qwen2.5:7b-instruct"
        self.initialized = False
        
    def check_ollama_running(self):
        """检查Ollama API是否运行"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def check_model_available(self):
        """检查Qwen2.5模型是否可用"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                for model in models:
                    model_name = model.get('name', '')
                    # 检查Qwen2.5模型
                    if 'qwen2.5' in model_name.lower() and '7b' in model_name.lower():
                        return True, model_name
                        
                return False, None
            return False, None
        except Exception as e:
            st.error(f"检查模型失败: {e}")
            return False, None
    
    def download_qwen_model(self):
        """下载Qwen2.5模型"""
        st.info("📥 正在下载Qwen2.5-7B-Instruct模型...")
        st.info("💡 这个模型专门优化用于您的硬件配置")
        st.info("🎯 模型大小: 4.4GB，适合32GB内存")
        
        try:
            # 使用完整路径调用ollama
            cmd = [self.ollama_exe, "pull", self.model_name]
            
            st.info(f"执行命令: {' '.join(cmd)}")
            
            # 创建进度显示
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 启动下载进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 监控进度
            progress = 0
            while process.poll() is None:
                progress = min(progress + 0.02, 0.9)
                progress_bar.progress(progress)
                status_text.text(f"下载中... {progress*100:.1f}%")
                time.sleep(2)
            
            # 等待完成
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                progress_bar.progress(1.0)
                status_text.text("下载完成!")
                st.success("✅ Qwen2.5-7B-Instruct模型下载完成!")
                return True
            else:
                st.error(f"❌ 模型下载失败")
                st.error(f"错误信息: {stderr}")
                return False
                
        except Exception as e:
            st.error(f"下载异常: {e}")
            return False
    
    def test_qwen_model(self):
        """测试Qwen2.5模型中医问答能力"""
        try:
            st.info("🧪 测试Qwen2.5中医问答能力...")
            
            # 中医专业测试问题
            test_prompt = """你是一位专业的中医医生。请简单介绍中医的基本理论，包括：
1. 阴阳学说
2. 五行学说
3. 气血理论

请用专业但易懂的语言回答。"""
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": test_prompt,
                    "stream": False,
                    "options": {
                        "num_predict": 300,
                        "temperature": 0.7
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '').strip()
                
                if content and len(content) > 100:
                    st.success("✅ Qwen2.5中医问答测试通过")
                    st.info(f"🎯 测试回答预览: {content[:150]}...")
                    return True, content
                else:
                    st.warning("⚠️ 回答过短")
                    return False, content
            else:
                st.error(f"❌ 测试失败: {response.status_code}")
                return False, ""
                
        except Exception as e:
            st.error(f"测试异常: {e}")
            return False, ""
    
    def initialize(self):
        """初始化Qwen2.5模型"""
        st.info("🤖 初始化Qwen2.5中医智能模型...")
        
        # 1. 检查Ollama API
        if not self.check_ollama_running():
            st.error("❌ Ollama API不可用")
            st.info("💡 请确保Ollama服务正在运行")
            return False
        
        st.success("✅ Ollama API运行中")
        
        # 2. 检查Qwen2.5模型
        model_available, model_name = self.check_model_available()
        if not model_available:
            st.info("📥 Qwen2.5模型未找到，开始下载...")
            if not self.download_qwen_model():
                st.error("❌ 模型下载失败")
                return False
            
            # 重新检查
            model_available, model_name = self.check_model_available()
            if model_available:
                self.model_name = model_name
                st.success(f"✅ 模型下载完成: {model_name}")
            else:
                st.error("❌ 模型下载后仍未找到")
                return False
        else:
            self.model_name = model_name
            st.success(f"✅ 找到Qwen2.5模型: {model_name}")
        
        # 3. 测试模型
        st.info("🧪 测试模型...")
        success, response = self.test_qwen_model()
        if success:
            st.success("✅ Qwen2.5模型测试通过!")
            st.info("🎯 专门优化用于中医问答，适合您的硬件配置")
            self.initialized = True
            return True
        else:
            st.error(f"❌ 模型测试失败: {response}")
            return False
    
    def generate_response(self, prompt, max_tokens=2048, temperature=0.7):
        """生成中医专业回答"""
        if not self.initialized or not self.model_name:
            return "Qwen2.5模型未初始化"
        
        try:
            st.info("🧠 Qwen2.5正在思考...")
            
            # 构建中医专业提示词
            system_prompt = """你是一位经验丰富的中医医生，具有深厚的中医理论基础和丰富的临床经验。
请用专业、温和、易懂的语气回答用户的中医相关问题。

回答时请：
1. 基于中医理论（如阴阳五行、脏腑经络等）
2. 提供实用的建议
3. 必要时引用经典医籍
4. 提醒用户咨询专业医生
5. 回答要准确、专业但易懂"""
            
            full_prompt = f"{system_prompt}\n\n用户问题: {prompt}\n\n中医回答:"
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens,
                        "top_p": 0.9,
                        "repeat_penalty": 1.1
                    }
                },
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                
                if content:
                    content = self._clean_response(content)
                    st.success("✅ Qwen2.5回答完成")
                    return content
                else:
                    return "回答生成为空，请重试"
            else:
                return f"生成失败: {response.status_code}"
                
        except Exception as e:
            return f"生成异常: {str(e)}"
    
    def _clean_response(self, text: str) -> str:
        """清理回答"""
        import re
        
        # 移除多余的换行和空格
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE)
        
        # 移除提示词残留
        text = re.sub(r'^(用户|助手|中医|回答|问题)[:：]\s*', '', text, flags=re.MULTILINE)
        
        return text.strip()
    
    def get_model_info(self):
        """获取模型信息"""
        if not self.model_name:
            return "未加载模型"
        
        return f"Qwen2.5-7B-Instruct - 阿里开发，专门优化用于中文医学问答"

def main():
    """测试函数"""
    manager = QwenOllamaManager()
    
    print("🤖 Qwen2.5 Ollama管理器测试")
    print("=" * 40)
    
    if manager.initialize():
        print("✅ 初始化成功!")
        print(f"当前模型: {manager.get_model_info()}")
        
        # 测试生成
        response = manager.generate_response("什么是气血？如何调理气血不足？", max_tokens=300)
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")

if __name__ == "__main__":
    main()
