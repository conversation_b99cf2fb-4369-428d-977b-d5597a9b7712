#!/usr/bin/env python3
"""
清理旧版本文件
保留最终版本，删除不需要的文件
"""

import os
import shutil
from pathlib import Path

class SystemCleaner:
    """系统清理器"""
    
    def __init__(self):
        # 保留的核心文件
        self.keep_files = {
            # 最终系统文件
            "ultimate_final_tcm_system.py",
            "final_ultimate_launcher.py", 
            "ultimate_docker_manager.py",
            "deepseek_api_manager.py",
            
            # 配置和数据文件
            "requirements_ultimate.txt",
            "Dockerfile.ultimate",
            "docker-compose.ultimate.yml",
            "nginx.conf",
            
            # 脚本文件
            "build.sh",
            "start.sh", 
            "stop.sh",
            "cleanup_old_versions.py",
            
            # 文档文件
            "DEPLOYMENT_GUIDE.md",
            "deployment_guide_api.md",
            
            # 批处理文件
            "启动系统.bat",
            "simple_start.py"
        }
        
        # 保留的目录
        self.keep_dirs = {
            "documents",
            "ultimate_final_vector_db",
            "conversations", 
            "logs",
            "cache",
            "__pycache__"
        }
        
        # 要删除的文件模式
        self.delete_patterns = [
            "app*.py",
            "start_*.py", 
            "test_*.py",
            "simple_*.py",
            "quick_*.py",
            "enhanced_*.py",
            "working_*.py",
            "ultimate_*.py",
            "commercial_*.py",
            "family_*.py",
            "*.html",
            "*.md"
        ]
    
    def analyze_files(self):
        """分析文件"""
        print("🔍 分析当前文件...")
        
        all_files = []
        for file in Path(".").iterdir():
            if file.is_file():
                all_files.append(file.name)
        
        # 分类文件
        keep_files = []
        delete_files = []
        
        for file in all_files:
            if file in self.keep_files:
                keep_files.append(file)
            elif self._should_delete(file):
                delete_files.append(file)
            else:
                keep_files.append(file)  # 默认保留
        
        print(f"📊 文件统计:")
        print(f"   总文件数: {len(all_files)}")
        print(f"   保留文件: {len(keep_files)}")
        print(f"   删除文件: {len(delete_files)}")
        
        return keep_files, delete_files
    
    def _should_delete(self, filename):
        """判断是否应该删除文件"""
        # 检查是否匹配删除模式
        for pattern in self.delete_patterns:
            if pattern.endswith("*.py"):
                prefix = pattern[:-5]
                if filename.startswith(prefix) and filename.endswith(".py"):
                    return True
            elif pattern.endswith("*.html"):
                if filename.endswith(".html"):
                    return True
            elif pattern.endswith("*.md"):
                if filename.endswith(".md") and filename not in self.keep_files:
                    return True
        
        return False
    
    def preview_cleanup(self):
        """预览清理操作"""
        keep_files, delete_files = self.analyze_files()
        
        print("\n" + "=" * 80)
        print("📋 清理预览")
        print("=" * 80)
        
        print("\n✅ 将保留的核心文件:")
        for file in sorted(keep_files):
            if file in self.keep_files:
                print(f"   🔒 {file} (核心文件)")
            else:
                print(f"   📄 {file}")
        
        print("\n❌ 将删除的文件:")
        for file in sorted(delete_files):
            print(f"   🗑️ {file}")
        
        if not delete_files:
            print("   (无文件需要删除)")
        
        return delete_files
    
    def perform_cleanup(self, delete_files):
        """执行清理"""
        if not delete_files:
            print("✅ 无需清理")
            return
        
        print(f"\n🗑️ 开始清理 {len(delete_files)} 个文件...")
        
        deleted_count = 0
        failed_count = 0
        
        for file in delete_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"   ✅ 删除: {file}")
                    deleted_count += 1
                else:
                    print(f"   ⚠️ 文件不存在: {file}")
            except Exception as e:
                print(f"   ❌ 删除失败: {file} - {e}")
                failed_count += 1
        
        print(f"\n📊 清理结果:")
        print(f"   成功删除: {deleted_count} 个文件")
        if failed_count > 0:
            print(f"   删除失败: {failed_count} 个文件")
        
        print("✅ 清理完成！")
    
    def cleanup_old_dirs(self):
        """清理旧目录"""
        print("\n🗂️ 清理旧目录...")
        
        old_dirs = [
            "enhanced_vector_db",
            "optimized_vector_db", 
            "working_vector_db",
            "vector_db",
            "ultimate_vector_db",
            "ultimate_integrated_vector_db",
            "family_tcm_knowledge",
            "online_cache",
            "compliance_logs",
            "user_logs"
        ]
        
        for dir_name in old_dirs:
            if os.path.exists(dir_name) and os.path.isdir(dir_name):
                try:
                    shutil.rmtree(dir_name)
                    print(f"   ✅ 删除目录: {dir_name}")
                except Exception as e:
                    print(f"   ❌ 删除目录失败: {dir_name} - {e}")
    
    def create_final_structure(self):
        """创建最终目录结构"""
        print("\n📁 创建最终目录结构...")
        
        final_dirs = [
            "ultimate_final_vector_db",
            "documents", 
            "conversations",
            "logs",
            "cache"
        ]
        
        for dir_name in final_dirs:
            os.makedirs(dir_name, exist_ok=True)
            print(f"   ✅ 确保目录存在: {dir_name}")
    
    def create_readme(self):
        """创建最终README"""
        readme_content = """# 终极中医RAG系统

## 🎯 系统特性

✅ **智能回答** - DeepSeek模型直接调用，无需LM Studio
✅ **PDF检索** - 支持>500MB大文件，多格式并行处理  
✅ **古籍检索** - 8个权威古代医书网站智能搜索
✅ **语音对话** - 中文语音识别和播放
✅ **连续聊天** - 智能上下文记忆和用户画像
✅ **远程访问** - Ngrok支持，手机端友好
✅ **Docker部署** - 一键跨硬件移植

## 🚀 快速开始

### 方法1: 使用启动器（推荐）
```bash
python final_ultimate_launcher.py
```

### 方法2: 直接启动
```bash
python ultimate_final_tcm_system.py
```

### 方法3: 批处理启动
```bash
双击 启动系统.bat
```

## 🐳 Docker部署

1. 创建部署包:
```bash
python ultimate_docker_manager.py
```

2. 部署到服务器:
```bash
unzip ultimate-tcm-rag-1.0.0.zip
cd ultimate-tcm-rag-1.0.0
./build.sh
./start.sh
```

## 📱 功能说明

- **文档上传**: 支持PDF、Word、Excel、PPT等格式
- **语音功能**: 语音输入问题，AI回答自动播放
- **远程访问**: 通过ngrok分享给异地朋友使用
- **对话管理**: 自动保存对话历史和用户画像

## 🔧 系统要求

- Python 3.8+
- 4GB+ 内存
- DeepSeek模型文件（可选，支持API调用）

## 📞 技术支持

系统会自动检测和安装依赖，按提示操作即可。
"""
        
        with open("README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("✅ 创建了最终README.md")
    
    def run_cleanup(self):
        """运行清理"""
        print("🧹 终极中医RAG系统清理工具")
        print("=" * 50)
        
        # 预览清理
        delete_files = self.preview_cleanup()
        
        if delete_files:
            print(f"\n⚠️ 即将删除 {len(delete_files)} 个文件")
            confirm = input("确认执行清理？(y/N): ").strip().lower()
            
            if confirm == 'y':
                self.perform_cleanup(delete_files)
                self.cleanup_old_dirs()
                self.create_final_structure()
                self.create_readme()
                
                print("\n🎉 系统清理完成！")
                print("📁 最终文件结构:")
                print("   ├── ultimate_final_tcm_system.py (主系统)")
                print("   ├── final_ultimate_launcher.py (启动器)")
                print("   ├── ultimate_docker_manager.py (Docker部署)")
                print("   ├── deepseek_api_manager.py (模型管理)")
                print("   ├── 启动系统.bat (Windows快捷启动)")
                print("   ├── documents/ (文档目录)")
                print("   ├── ultimate_final_vector_db/ (向量数据库)")
                print("   └── conversations/ (对话历史)")
                print("\n💡 现在可以使用 python final_ultimate_launcher.py 启动系统")
            else:
                print("❌ 清理已取消")
        else:
            print("✅ 系统已经是最新状态，无需清理")

def main():
    """主函数"""
    cleaner = SystemCleaner()
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
