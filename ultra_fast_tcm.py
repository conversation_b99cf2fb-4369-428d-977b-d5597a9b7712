#!/usr/bin/env python3
"""
超快启动家庭中医智能助手
完全跳过模型加载，使用简单文本匹配
"""
import streamlit as st
import pickle
import json
from pathlib import Path
from datetime import datetime
import re

# 页面配置
st.set_page_config(
    page_title="家庭中医智能助手 - 超快版",
    page_icon="🏥",
    layout="wide"
)

@st.cache_data
def load_simple_knowledge_base():
    """加载简化的知识库（无需模型）"""
    try:
        vector_db_dir = Path('vector_db')
        
        # 加载文档块
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            chunks = pickle.load(f)
        
        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)
        
        return {
            'chunks': chunks,
            'metadata': metadata,
            'status': 'success',
            'total_chunks': len(chunks)
        }
        
    except Exception as e:
        return {'status': 'error', 'error': str(e)}

def simple_text_search(query, knowledge_base, top_k=3):
    """改进的文本搜索（支持部分匹配和模糊搜索）"""
    try:
        chunks = knowledge_base['chunks']
        metadata = knowledge_base['metadata']

        # 提取查询关键词（支持更多模式）
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', query))
        query_chars = set(query.replace(' ', ''))

        results = []
        for i, chunk in enumerate(chunks):
            score = 0
            matched_keywords = []

            # 1. 完全匹配关键词
            chunk_keywords = set(re.findall(r'[\u4e00-\u9fff]{1,10}', chunk))
            exact_matches = query_keywords.intersection(chunk_keywords)
            if exact_matches:
                score += len(exact_matches) * 2  # 完全匹配权重更高
                matched_keywords.extend(exact_matches)

            # 2. 部分匹配（包含关系）
            for q_word in query_keywords:
                if len(q_word) >= 2:  # 只对长度>=2的词进行部分匹配
                    for c_word in chunk_keywords:
                        if q_word in c_word or c_word in q_word:
                            score += 1
                            if c_word not in matched_keywords:
                                matched_keywords.append(c_word)

            # 3. 字符级匹配（用于单字查询）
            chunk_chars = set(chunk)
            char_matches = query_chars.intersection(chunk_chars)
            if char_matches and len(query.strip()) <= 2:
                score += len(char_matches) * 0.5

            # 4. 直接字符串包含
            if query.strip() in chunk:
                score += 3  # 直接包含得分最高
                matched_keywords.append(query.strip())

            if score > 0:
                # 归一化分数
                normalized_score = min(score / max(len(query_keywords) * 2, 1), 1.0)
                results.append({
                    'content': chunk,
                    'metadata': metadata[i],
                    'score': normalized_score,
                    'matched_keywords': list(set(matched_keywords))
                })

        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]

    except Exception as e:
        st.error(f"搜索失败: {e}")
        return []

def generate_simple_answer(query, search_results):
    """生成简单答案"""
    if not search_results:
        return "抱歉，我没有找到相关信息。请尝试其他问题或检查输入。"
    
    # 收集相关内容
    relevant_content = []
    for i, result in enumerate(search_results[:3], 1):
        content = result['content']
        source = Path(result['metadata']['source']).name
        score = result['score']
        keywords = ', '.join(result['matched_keywords'][:5])
        
        relevant_content.append(f"""
**结果 {i}** - 来源《{source}》 (匹配度: {score:.3f})
匹配关键词: {keywords}

{content[:400]}...
""")
    
    # 生成答案
    answer = f"""根据中医典籍，关于"{query}"的相关信息如下：

{''.join(relevant_content)}

**注意：** 以上信息来自中医经典文献，仅供学习参考。如需医疗建议，请咨询专业中医师。"""
    
    return answer

def main():
    """主界面"""
    # 标题
    st.markdown("""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(90deg, #4CAF50, #45a049); color: white; border-radius: 10px; margin-bottom: 2rem;">
        <h1>🏥 家庭中医智能助手 - 超快版</h1>
        <p>⚡ 秒速启动，无需等待模型加载</p>
        <p>🔍 基于关键词匹配的快速搜索</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 加载知识库
    knowledge_base = load_simple_knowledge_base()
    
    if knowledge_base['status'] == 'error':
        st.error(f"❌ 知识库加载失败: {knowledge_base['error']}")
        st.info("请确保已运行 emergency_fix.py 生成向量数据库")
        return
    
    # 显示系统状态
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("知识块数量", knowledge_base['total_chunks'])
    with col2:
        st.metric("搜索方式", "关键词匹配")
    with col3:
        st.metric("启动速度", "⚡ 超快")
    
    st.divider()
    
    # 常见问题快捷按钮
    st.subheader("🔥 常见问题快速查询")
    col1, col2, col3, col4 = st.columns(4)
    
    common_questions = [
        "栀子甘草豉汤方",
        "防己黄芪汤",
        "甘草汤的作用",
        "栀子的功效"
    ]
    
    selected_question = None
    for i, question in enumerate(common_questions):
        with [col1, col2, col3, col4][i]:
            if st.button(question, key=f"common_{i}"):
                selected_question = question
    
    # 问题输入
    st.subheader("❓ 请输入您的中医问题")
    user_question = st.text_area(
        "您可以询问任何中医相关问题：",
        value=selected_question if selected_question else "",
        height=100,
        placeholder="例如：栀子甘草豉汤方的组成和功效是什么？"
    )
    
    # 查询按钮
    col1, col2 = st.columns([1, 4])
    with col1:
        ask_button = st.button("🔍 立即查询", type="primary")
    with col2:
        if st.button("🗑️ 清空"):
            st.rerun()
    
    # 处理查询
    if ask_button and user_question.strip():
        with st.spinner("🔍 正在快速搜索..."):
            # 搜索相关文档
            search_results = simple_text_search(user_question, knowledge_base, top_k=5)
            
            if search_results:
                # 生成答案
                answer = generate_simple_answer(user_question, search_results)
                
                # 显示答案
                st.markdown("""
                <div style="background: #fff; padding: 1.5rem; border-radius: 10px; border: 1px solid #ddd; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 1rem 0;">
                """, unsafe_allow_html=True)
                
                st.markdown("### 💡 智能回答")
                st.markdown(answer)
                
                st.markdown("</div>", unsafe_allow_html=True)
                
                # 显示详细搜索结果
                with st.expander("📖 详细搜索结果"):
                    for i, result in enumerate(search_results, 1):
                        source = Path(result['metadata']['source']).name
                        st.write(f"**结果 {i}** - 来源: {source}")
                        st.write(f"**匹配度:** {result['score']:.3f}")
                        st.write(f"**匹配关键词:** {', '.join(result['matched_keywords'][:10])}")
                        st.write(f"**内容:** {result['content'][:300]}...")
                        st.divider()
                
                # 记录查询日志
                log_query(user_question, len(search_results))
                
            else:
                st.warning("❌ 未找到相关信息，请尝试其他问题")
    
    elif ask_button and not user_question.strip():
        st.warning("请先输入您的问题")
    
    # 系统说明
    with st.expander("ℹ️ 系统说明"):
        st.markdown("""
        **超快版特点：**
        - ⚡ 秒速启动，无需等待模型加载
        - 🔍 基于关键词匹配的快速搜索
        - 📚 包含完整的中医方剂知识库
        - 💾 轻量级，占用资源少
        
        **搜索原理：**
        - 提取查询中的中文关键词
        - 在知识库中匹配相同关键词
        - 按匹配度排序返回结果
        
        **适用场景：**
        - 快速查询中医方剂
        - 了解中药功效
        - 学习中医基础知识
        """)
    
    # 免责声明
    st.markdown("""
    ---
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 1rem; margin: 1rem 0;">
        <strong>⚠️ 重要提醒：</strong><br>
        本系统提供的信息仅供中医学习参考，不能替代专业医生的诊断和治疗建议。
        如有严重症状或疑虑，请及时就医咨询专业医生。
    </div>
    """, unsafe_allow_html=True)

def log_query(question, results_count):
    """记录查询日志"""
    try:
        log_dir = Path("user_logs")
        log_dir.mkdir(exist_ok=True)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "results_count": results_count,
            "type": "simple_search"
        }
        
        log_file = log_dir / f"queries_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except:
        pass  # 日志记录失败不影响主功能

if __name__ == "__main__":
    main()
