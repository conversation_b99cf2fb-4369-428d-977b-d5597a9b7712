# 🏥 增强版终极中医RAG系统功能说明

## 🎯 核心改进

### 1. **优先在线医学资源搜索** 🌐
- **没有PDF时**：优先从在线医学资源获取信息
- **有PDF时**：智能结合本地PDF和在线资源
- **备用知识库**：内置常见中医问题的专业回答

### 2. **恢复语音功能** 🎤
- **语音输入**：点击🎤按钮进行语音输入
- **语音播报**：自动播报AI回答内容
- **浏览器支持**：使用Web Speech API，支持中文识别

### 3. **智能资源整合** 📚
- **优先级排序**：在线资源优先级高于本地资源
- **相关度计算**：基于关键词匹配计算相关度
- **来源标注**：清楚标注每个回答的资源来源

## 🔧 技术特性

### 在线资源搜索
```python
# 搜索顺序
1. 内置中医知识库（备用）
2. 医宗金鉴在线资源
3. 古代医书数据库
4. 扩展医学网站
```

### 语音交互
```javascript
// 语音识别
- 支持中文语音输入
- 实时转换为文字
- 自动填入输入框

// 语音播报
- 自动播报AI回答
- 清理Markdown格式
- 中文语音合成
```

### 智能回答生成
```python
# 回答结构
1. 🎯 直接回答用户问题
2. 📚 相关资料展示
3. 📖 参考来源列表
4. ⚠️ 医疗免责声明
```

## 📋 内置知识库

### 经典方剂
- **栀子甘草豉汤**：清热除烦，和中降逆
- 组成：栀子、甘草、豆豉
- 主治：热病后虚烦不眠，胸中窒闷

### 常见证候
- **脾胃虚弱**：食欲不振、腹胀、便溏、倦怠乏力
- **肾阳虚vs肾阴虚**：畏寒肢冷vs潮热盗汗
- **失眠多梦**：辨证论治、针灸、食疗

## 🌐 访问方式

### 本地访问
```
http://localhost:8006
```

### 功能测试
```bash
# 健康检查
curl http://localhost:8006/api/health

# 聊天测试
curl -X POST http://localhost:8006/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "栀子甘草豉汤的功效是什么？"}'
```

## 🎮 使用指南

### 1. 语音输入
1. 点击🎤红色按钮
2. 开始说话（支持中文）
3. 语音自动转换为文字
4. 点击发送按钮

### 2. 文档上传
1. 点击"📄 点击上传PDF文档"
2. 选择中医相关PDF文件
3. 系统自动处理和索引
4. 与在线资源智能结合

### 3. 快捷查询
- 🌿 脾胃调理
- 🫖 肾虚调养  
- 😴 失眠治疗
- 💊 经典方剂

## ⚡ 性能特点

- **快速响应**：平均响应时间 < 10秒
- **智能搜索**：关键词匹配 + 相关度排序
- **资源优先**：在线资源 > 本地文档
- **备用机制**：网络失败时使用内置知识

## 🔒 安全提醒

- ⚠️ **仅供学习参考**：不构成医疗建议
- 👨‍⚕️ **专业诊疗**：具体治疗请咨询中医师
- 📚 **文化传承**：专注中医文化学习和传播

## 🚀 部署说明

### 启动服务
```bash
python simple_ultimate_tcm.py
```

### 依赖要求
```
fastapi
uvicorn
requests
beautifulsoup4
PyPDF2
sentence-transformers
numpy
```

### 端口配置
- **默认端口**：8006
- **API文档**：http://localhost:8006/docs
- **健康检查**：http://localhost:8006/api/health

---

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. 浏览器是否支持语音功能
3. PDF文件是否为有效格式
4. 服务器是否正常运行

**系统版本**：3.0.0-simple  
**更新时间**：2025-06-10  
**功能状态**：✅ 在线搜索 ✅ 语音交互 ✅ PDF处理 ✅ 智能问答
