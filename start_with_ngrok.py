#!/usr/bin/env python3
"""
使用ngrok启动增强版中医RAG系统
解决远程访问问题，让异地朋友可以在手机上使用
"""

import subprocess
import sys
import time
import os
import json
import requests
from pathlib import Path

def print_ngrok_banner():
    """打印ngrok横幅"""
    print("=" * 80)
    print("🌐 增强版中医RAG系统 - ngrok远程访问")
    print("=" * 80)
    print("🎯 功能:")
    print("   🔗 创建公网访问链接")
    print("   📱 支持手机端访问")
    print("   🔐 可设置访问密码")
    print("   🌍 异地朋友可直接使用")
    print("   ⚡ 快速分享部署")
    print("=" * 80)

def check_ngrok():
    """检查ngrok是否安装"""
    print("🔍 检查ngrok环境...")
    
    try:
        result = subprocess.run(["ngrok", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ ngrok未正确安装")
            return False
    except FileNotFoundError:
        print("❌ ngrok未找到")
        print("💡 请按以下步骤安装ngrok:")
        print("   1. 访问 https://ngrok.com/download")
        print("   2. 下载适合您系统的版本")
        print("   3. 解压到系统PATH目录")
        print("   4. 注册账号获取authtoken")
        return False

def setup_ngrok_auth():
    """设置ngrok认证"""
    print("\n🔐 设置ngrok认证...")
    
    # 检查是否已有authtoken
    try:
        result = subprocess.run(["ngrok", "config", "check"], capture_output=True, text=True)
        if "valid" in result.stdout.lower():
            print("✅ ngrok认证已配置")
            return True
    except:
        pass
    
    print("⚠️ 需要配置ngrok认证token")
    print("💡 请按以下步骤操作:")
    print("   1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken")
    print("   2. 复制您的authtoken")
    print("   3. 运行: ngrok config add-authtoken YOUR_TOKEN")
    
    authtoken = input("请输入您的ngrok authtoken (或按回车跳过): ").strip()
    
    if authtoken:
        try:
            result = subprocess.run([
                "ngrok", "config", "add-authtoken", authtoken
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ authtoken配置成功")
                return True
            else:
                print(f"❌ authtoken配置失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 配置过程出错: {e}")
            return False
    else:
        print("⚠️ 跳过authtoken配置，可能影响使用")
        return True

def start_streamlit_app():
    """启动Streamlit应用"""
    print("\n🚀 启动Streamlit应用...")
    
    try:
        # 启动应用（后台运行）
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", 
            "quick_enhanced_tcm.py",
            "--server.port=8504",
            "--server.address=0.0.0.0",
            "--theme.base=light",
            "--server.headless=true"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待应用启动
        print("⏳ 等待应用启动...")
        time.sleep(8)
        
        # 检查应用是否启动成功
        try:
            response = requests.get("http://localhost:8504", timeout=5)
            if response.status_code == 200:
                print("✅ Streamlit应用启动成功")
                return process
            else:
                print(f"⚠️ 应用响应异常: {response.status_code}")
                return process
        except requests.exceptions.RequestException:
            print("⚠️ 应用可能仍在启动中...")
            return process
            
    except Exception as e:
        print(f"❌ 启动应用失败: {e}")
        return None

def start_ngrok_tunnel():
    """启动ngrok隧道"""
    print("\n🌐 启动ngrok隧道...")
    
    try:
        # 启动ngrok隧道
        process = subprocess.Popen([
            "ngrok", "http", "8504",
            "--log=stdout"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待隧道建立
        print("⏳ 等待隧道建立...")
        time.sleep(5)
        
        # 获取公网URL
        public_url = get_ngrok_public_url()
        
        if public_url:
            print(f"✅ ngrok隧道启动成功")
            print(f"🔗 公网访问地址: {public_url}")
            print(f"📱 手机访问地址: {public_url}")
            
            # 生成分享信息
            generate_share_info(public_url)
            
            return process, public_url
        else:
            print("⚠️ 无法获取公网URL，但隧道可能已启动")
            return process, None
            
    except Exception as e:
        print(f"❌ 启动ngrok隧道失败: {e}")
        return None, None

def get_ngrok_public_url():
    """获取ngrok公网URL"""
    try:
        # 查询ngrok API
        response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        if response.status_code == 200:
            data = response.json()
            tunnels = data.get('tunnels', [])
            
            for tunnel in tunnels:
                if tunnel.get('proto') == 'https':
                    return tunnel.get('public_url')
                elif tunnel.get('proto') == 'http':
                    return tunnel.get('public_url')
            
        return None
        
    except Exception as e:
        print(f"⚠️ 获取公网URL失败: {e}")
        return None

def generate_share_info(public_url):
    """生成分享信息"""
    share_info = f"""
🎉 增强版中医RAG系统已部署到公网！

🔗 访问地址: {public_url}
📱 手机访问: 直接在手机浏览器中打开上述链接
🔐 访问密码: MVP168918 (如需要)

✨ 系统功能:
   🧙‍♂️ 智者·中医AI助手
   🔍 真正的PDF文档检索
   🌐 古代医书在线搜索
   🔊 语音播放功能
   ⚡ 快速多格式文档解析
   📱 移动端友好界面

💡 使用说明:
   1. 点击"初始化系统"按钮
   2. 上传中医PDF文档(可选)
   3. 开始智能问答

⚠️ 重要提醒:
   - 此链接仅在本次会话有效
   - 请勿分享给不信任的人
   - 系统仅供学习参考，不替代专业医疗
"""
    
    # 保存分享信息
    with open("share_info.txt", "w", encoding="utf-8") as f:
        f.write(share_info.strip())
    
    print("📝 分享信息已保存到: share_info.txt")
    print(share_info)

def monitor_services(streamlit_process, ngrok_process, public_url):
    """监控服务状态"""
    print("\n📊 开始监控服务...")
    print("⏹️ 按 Ctrl+C 停止所有服务")
    
    try:
        while True:
            # 检查Streamlit进程
            if streamlit_process.poll() is not None:
                print("⚠️ Streamlit应用已停止")
                break
            
            # 检查ngrok进程
            if ngrok_process.poll() is not None:
                print("⚠️ ngrok隧道已断开")
                break
            
            # 检查服务可用性
            try:
                if public_url:
                    response = requests.get(public_url, timeout=10)
                    if response.status_code == 200:
                        status = "✅ 在线"
                    else:
                        status = f"⚠️ 异常({response.status_code})"
                else:
                    status = "⚠️ URL未知"
                
                print(f"🌐 服务状态: {status} - {time.strftime('%H:%M:%S')}")
                
            except requests.exceptions.RequestException:
                print(f"❌ 服务不可达 - {time.strftime('%H:%M:%S')}")
            
            time.sleep(30)  # 每30秒检查一次
            
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
    
    # 清理进程
    cleanup_processes(streamlit_process, ngrok_process)

def cleanup_processes(streamlit_process, ngrok_process):
    """清理进程"""
    print("🧹 清理进程...")
    
    try:
        if streamlit_process and streamlit_process.poll() is None:
            streamlit_process.terminate()
            streamlit_process.wait(timeout=5)
            print("✅ Streamlit进程已停止")
    except Exception as e:
        print(f"⚠️ 停止Streamlit进程失败: {e}")
    
    try:
        if ngrok_process and ngrok_process.poll() is None:
            ngrok_process.terminate()
            ngrok_process.wait(timeout=5)
            print("✅ ngrok进程已停止")
    except Exception as e:
        print(f"⚠️ 停止ngrok进程失败: {e}")

def main():
    """主函数"""
    print_ngrok_banner()
    
    # 1. 检查ngrok
    if not check_ngrok():
        input("按回车键退出...")
        return
    
    # 2. 设置认证
    if not setup_ngrok_auth():
        print("⚠️ 认证设置失败，但可以尝试继续")
    
    # 3. 启动Streamlit应用
    streamlit_process = start_streamlit_app()
    if not streamlit_process:
        print("❌ 无法启动Streamlit应用")
        input("按回车键退出...")
        return
    
    # 4. 启动ngrok隧道
    ngrok_process, public_url = start_ngrok_tunnel()
    if not ngrok_process:
        print("❌ 无法启动ngrok隧道")
        streamlit_process.terminate()
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 80)
    print("🎉 远程访问部署完成！")
    print("📱 您的朋友现在可以通过公网链接访问系统")
    print("🔐 建议设置访问密码保护隐私")
    print("=" * 80)
    
    # 5. 监控服务
    monitor_services(streamlit_process, ngrok_process, public_url)
    
    print("👋 服务已停止")

if __name__ == "__main__":
    main()
