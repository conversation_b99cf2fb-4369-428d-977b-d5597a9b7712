# 🚀 MCP+API+RAG集成系统设置指南

## 🎯 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   本地模型API   │◄──►│  MCP服务        │◄──►│  RAG向量数据库  │
│                 │    │ (Elasticsearch) │    │                 │
│ • DeepSeek-R1   │    │ • 智能检索      │    │ • FAISS索引     │
│ • M3E嵌入       │    │ • 文档索引      │    │ • 文档块存储    │
│ • API兼容       │    │ • MCP协议       │    │ • 元数据管理    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 前置要求

### 1. 系统要求
- Python 3.8+
- 8GB+ RAM
- 20GB+ 磁盘空间

### 2. 必需软件
- **Elasticsearch 8.x**
- **Java 11+** (Elasticsearch依赖)

### 3. 模型文件
需要下载以下模型到 `./models/` 目录：
- `deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf` (聊天模型)
- `m3e-base/` (嵌入模型目录)

## 🛠️ 安装步骤

### 步骤1: 安装Elasticsearch

#### Windows:
```bash
# 下载并解压Elasticsearch
# 启动Elasticsearch
bin\elasticsearch.bat
```

#### Linux/Mac:
```bash
# 使用包管理器安装
sudo apt-get install elasticsearch  # Ubuntu
brew install elasticsearch          # macOS

# 启动服务
sudo systemctl start elasticsearch  # Linux
brew services start elasticsearch   # macOS
```

#### Docker方式:
```bash
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.11.0
```

### 步骤2: 验证Elasticsearch
```bash
curl -X GET "localhost:9200/"
```
应该返回Elasticsearch版本信息。

### 步骤3: 安装Python依赖
```bash
python install_mcp_api_dependencies.py
```

### 步骤4: 准备模型文件
```bash
# 创建模型目录
mkdir -p models

# 下载DeepSeek-R1模型 (示例)
# 请从官方渠道下载模型文件

# 下载M3E嵌入模型
git clone https://huggingface.co/moka-ai/m3e-base models/m3e-base
```

### 步骤5: 启动系统
```bash
# 方式1: 使用集成界面
streamlit run start_mcp_api_rag_system.py

# 方式2: 命令行测试
python integrated_mcp_rag_system.py
```

## 🎮 使用指南

### 1. 系统初始化
1. 打开Web界面: http://localhost:8501
2. 点击侧边栏的"🚀 初始化系统"
3. 等待所有组件启动完成

### 2. 检查系统状态
- 点击"🔄 刷新状态"查看各组件状态
- 确保所有组件显示为"✅ 正常"

### 3. 智能问答
1. 在"💬 智能问答"标签页输入问题
2. 选择使用MCP检索和API嵌入
3. 点击"🔍 智能搜索"获得回答

### 4. 文档管理
1. 在"📚 文档管理"标签页上传文档
2. 支持TXT和JSON格式
3. 文档会自动索引到Elasticsearch

## 🔧 配置说明

### API服务器配置
- **端口**: 8002
- **聊天模型**: deepseek-r1
- **嵌入模型**: m3e-base

### MCP服务配置
- **协议**: Model Context Protocol
- **后端**: Elasticsearch
- **索引**: tcm_knowledge

### Elasticsearch配置
- **主机**: localhost:9200
- **索引映射**: 支持中文分词
- **向量存储**: dense_vector字段

## 🧪 测试验证

### 1. 组件测试
```bash
# 测试API服务器
curl http://localhost:8002/health

# 测试Elasticsearch
curl http://localhost:9200/_cluster/health

# 测试MCP服务
python elasticsearch_mcp_server.py
```

### 2. 功能测试
在Web界面的"🧪 系统测试"标签页：
1. 选择预设测试用例
2. 运行功能测试
3. 查看性能指标

### 3. 预期结果
- **搜索准确性**: 高质量匹配结果
- **响应时间**: < 5秒
- **系统稳定性**: 无错误运行

## 🎯 核心特性

### ✅ 真正的MCP协议
- 使用官方MCP SDK
- 标准化工具调用
- 异步通信机制

### ✅ 本地模型API
- OpenAI兼容接口
- 支持GGUF和Transformers
- GPU/CPU自适应

### ✅ Elasticsearch集成
- 全文搜索能力
- 中文分词支持
- 向量相似度搜索

### ✅ RAG向量数据库
- FAISS高效索引
- 文档块管理
- 元数据存储

## 🔍 故障排除

### 常见问题

#### 1. Elasticsearch连接失败
```bash
# 检查服务状态
curl http://localhost:9200/

# 重启服务
sudo systemctl restart elasticsearch
```

#### 2. 模型加载失败
- 检查模型文件路径
- 确认文件完整性
- 查看内存使用情况

#### 3. MCP连接超时
- 检查Python依赖
- 验证MCP服务器启动
- 查看进程状态

#### 4. API调用失败
- 确认API服务器运行
- 检查端口占用
- 验证请求格式

### 日志查看
```bash
# 查看系统日志
tail -f logs/system.log

# 查看Elasticsearch日志
tail -f /var/log/elasticsearch/elasticsearch.log
```

## 📊 性能优化

### 1. 硬件优化
- **CPU**: 多核处理器推荐
- **内存**: 16GB+ 推荐
- **存储**: SSD推荐

### 2. 软件优化
- 调整Elasticsearch堆内存
- 优化模型量化设置
- 配置并发处理数

### 3. 网络优化
- 使用本地部署减少延迟
- 配置连接池
- 启用压缩传输

## 🎉 成功验证

当您看到以下状态时，说明系统配置成功：

1. **Web界面显示**:
   - ✅ API服务器: 运行中
   - ✅ MCP服务器: 运行中  
   - ✅ Elasticsearch: 连接正常
   - ✅ 整体状态: 健康

2. **功能测试通过**:
   - 智能搜索返回相关结果
   - 回答生成质量良好
   - 文档索引成功

3. **性能指标正常**:
   - 平均响应时间 < 5秒
   - 系统资源使用合理
   - 无错误日志

## 🎊 恭喜！

您已经成功搭建了真正的**MCP+API+RAG集成系统**！

这个系统实现了：
- ✅ **真正的MCP协议**通信
- ✅ **本地模型API**服务
- ✅ **Elasticsearch**全文检索
- ✅ **RAG向量数据库**集成
- ✅ **智能问答**功能

现在您可以享受高质量的智能检索和问答服务了！🎉
