"""
会话管理器 - 负责会话历史的保存和恢复
"""
import sqlite3
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import config

class SessionManager:
    def __init__(self):
        self.db_path = config.SESSIONS_DB_PATH
        self.initialized = False
    
    def initialize(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    title TEXT,
                    metadata TEXT
                )
            ''')
            
            # 创建对话历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    question TEXT,
                    answer TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.initialized = True
            print("会话管理器初始化完成")
            return True
            
        except Exception as e:
            print(f"初始化会话管理器失败: {e}")
            return False
    
    def create_session(self, title: str = None) -> str:
        """创建新会话"""
        if not self.initialized:
            self.initialize()
        
        session_id = str(uuid.uuid4())
        
        if title is None:
            title = f"会话 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO sessions (id, title, metadata)
                VALUES (?, ?, ?)
            ''', (session_id, title, json.dumps({})))
            
            conn.commit()
            conn.close()
            
            print(f"创建新会话: {session_id}")
            return session_id
            
        except Exception as e:
            print(f"创建会话失败: {e}")
            return None
    
    def add_to_history(self, session_id: str, question: str, answer: str, metadata: Dict = None):
        """添加对话到历史记录"""
        if not self.initialized:
            return False
        
        if metadata is None:
            metadata = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 添加对话记录
            cursor.execute('''
                INSERT INTO conversation_history (session_id, question, answer, metadata)
                VALUES (?, ?, ?, ?)
            ''', (session_id, question, answer, json.dumps(metadata)))
            
            # 更新会话的最后更新时间
            cursor.execute('''
                UPDATE sessions 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (session_id,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"添加对话历史失败: {e}")
            return False
    
    def get_session_history(self, session_id: str, limit: int = None) -> List[Dict]:
        """获取会话历史"""
        if not self.initialized:
            return []
        
        if limit is None:
            limit = config.MAX_HISTORY_LENGTH
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT question, answer, timestamp, metadata
                FROM conversation_history
                WHERE session_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (session_id, limit))
            
            rows = cursor.fetchall()
            conn.close()
            
            history = []
            for row in rows:
                history.append({
                    "question": row[0],
                    "answer": row[1],
                    "timestamp": row[2],
                    "metadata": json.loads(row[3]) if row[3] else {}
                })
            
            # 按时间正序返回
            return list(reversed(history))
            
        except Exception as e:
            print(f"获取会话历史失败: {e}")
            return []
    
    def get_all_sessions(self) -> List[Dict]:
        """获取所有会话列表"""
        if not self.initialized:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT s.id, s.title, s.created_at, s.updated_at,
                       COUNT(ch.id) as message_count
                FROM sessions s
                LEFT JOIN conversation_history ch ON s.id = ch.session_id
                GROUP BY s.id, s.title, s.created_at, s.updated_at
                ORDER BY s.updated_at DESC
            ''')
            
            rows = cursor.fetchall()
            conn.close()
            
            sessions = []
            for row in rows:
                sessions.append({
                    "id": row[0],
                    "title": row[1],
                    "created_at": row[2],
                    "updated_at": row[3],
                    "message_count": row[4]
                })
            
            return sessions
            
        except Exception as e:
            print(f"获取会话列表失败: {e}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        if not self.initialized:
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除对话历史
            cursor.execute('DELETE FROM conversation_history WHERE session_id = ?', (session_id,))
            
            # 删除会话
            cursor.execute('DELETE FROM sessions WHERE id = ?', (session_id,))
            
            conn.commit()
            conn.close()
            
            print(f"删除会话: {session_id}")
            return True
            
        except Exception as e:
            print(f"删除会话失败: {e}")
            return False
    
    def update_session_title(self, session_id: str, title: str) -> bool:
        """更新会话标题"""
        if not self.initialized:
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE sessions 
                SET title = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (title, session_id))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"更新会话标题失败: {e}")
            return False
    
    def cleanup_old_sessions(self, days: int = 30):
        """清理旧会话"""
        if not self.initialized:
            return False
        
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取要删除的会话ID
            cursor.execute('''
                SELECT id FROM sessions 
                WHERE updated_at < ?
            ''', (cutoff_date,))
            
            old_sessions = cursor.fetchall()
            
            for session in old_sessions:
                session_id = session[0]
                # 删除对话历史
                cursor.execute('DELETE FROM conversation_history WHERE session_id = ?', (session_id,))
                # 删除会话
                cursor.execute('DELETE FROM sessions WHERE id = ?', (session_id,))
            
            conn.commit()
            conn.close()
            
            print(f"清理了 {len(old_sessions)} 个旧会话")
            return True
            
        except Exception as e:
            print(f"清理旧会话失败: {e}")
            return False

# 全局会话管理器实例
session_manager = SessionManager()
