#!/usr/bin/env python3
"""
增强版语音聊天界面 - 完整语音交互功能
包含语音输入、语音输出、文档上传、现代化界面
"""
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import uuid
from pathlib import Path

# 创建FastAPI应用
app = FastAPI(title="中医智能助手 - 语音增强版", version="2.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_voice: Optional[bool] = False

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    session_id: str
    timestamp: str

# 内置中医知识库
TCM_KNOWLEDGE = {
    "湿气": {
        "definition": "湿气是中医理论中的重要概念，指人体内水液代谢失常所产生的病理产物。",
        "symptoms": "湿气重的表现包括：身体沉重、头昏脑胀、四肢困倦、胸闷腹胀、食欲不振、大便黏腻等。",
        "treatment": "治疗原则以健脾化湿、理气化湿为主，建议适当运动，饮食清淡，避免生冷食物。",
        "prevention": "预防湿气可以通过规律作息、适量运动、保持环境干燥、饮食调理等方式。"
    },
    "气血": {
        "definition": "气血是中医学的核心概念，气为血之帅，血为气之母。",
        "functions": "气具有推动、温煦、防御、固摄、气化等功能；血具有濡养、滋润等作用。",
        "deficiency": "气虚表现为乏力、气短、声低懒言；血虚表现为面色苍白、头晕心悸、失眠多梦。",
        "nourishment": "气血调养可通过合理饮食、充足睡眠、适度运动、情志调节等方法。"
    },
    "阴阳": {
        "definition": "阴阳学说是中医理论的哲学基础，认为阴阳是宇宙间相互关联的两个方面。",
        "balance": "在人体中，阴阳的相对平衡是健康的标志，阴阳失调则导致疾病。",
        "characteristics": "阴主静、主寒、主下、主内；阳主动、主热、主上、主外。",
        "regulation": "调节阴阳平衡需要根据个人体质，采用相应的调理方法。"
    },
    "四季养生": {
        "spring": "春季养生：春季阳气生发，应顺应自然，早睡早起，适当运动，饮食宜清淡。",
        "summer": "夏季养生：夏季阳气旺盛，应注意清热解暑，保持心情舒畅，避免过度贪凉。",
        "autumn": "秋季养生：秋季阳气收敛，应注意滋阴润燥，调养肺气，预防秋燥。",
        "winter": "冬季养生：冬季阳气潜藏，应注意温补肾阳，保存精气，早睡晚起。"
    },
    "经络": {
        "definition": "经络是人体内气血运行的通道，包括经脉和络脉。",
        "meridians": "十二正经是经络系统的主体，包括手三阴、手三阳、足三阴、足三阳。",
        "functions": "经络具有运行气血、联络脏腑、沟通内外、调节机能的作用。",
        "acupoints": "穴位是经络上的特定点位，通过刺激穴位可以调节脏腑功能。"
    }
}

def generate_enhanced_response(question: str) -> tuple[str, List[Dict[str, Any]]]:
    """生成增强的回答"""
    question_lower = question.lower()
    sources = []

    # 更智能的关键词匹配
    if any(keyword in question_lower for keyword in ["湿气", "湿重", "湿邪"]):
        knowledge = TCM_KNOWLEDGE["湿气"]
        response = f"""## 🌿 关于湿气的中医知识

### 📖 基本概念
{knowledge['definition']}

### 🔍 主要表现
{knowledge['symptoms']}

### 💊 调理原则
{knowledge['treatment']}

### 🛡️ 预防方法
{knowledge['prevention']}

### 💡 生活建议
- 保持居住环境干燥通风
- 适当进行有氧运动，促进新陈代谢
- 饮食宜清淡，少食生冷油腻食物
- 可适当食用薏米、红豆等健脾利湿食物

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业中医师。"""

        sources = [
            {'source': '内置中医知识库 - 湿气理论', 'content': knowledge['definition'], 'score': 1.0},
            {'source': '中医基础理论', 'content': knowledge['symptoms'], 'score': 0.9}
        ]

    elif any(keyword in question_lower for keyword in ["气血", "气虚", "血虚", "气血不足"]):
        knowledge = TCM_KNOWLEDGE["气血"]
        response = f"""## 🫖 关于气血的中医理论

### 📖 基本概念
{knowledge['definition']}

### ⚡ 功能作用
{knowledge['functions']}

### 📋 不足表现
{knowledge['deficiency']}

### 🌱 调养方法
{knowledge['nourishment']}

### 💡 具体建议
- **饮食调养**: 多食用红枣、桂圆、当归等补血食材
- **运动锻炼**: 适度运动，如太极拳、八段锦等
- **作息规律**: 保证充足睡眠，避免熬夜
- **情志调节**: 保持心情愉悦，避免过度忧思

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业中医师。"""

        sources = [
            {'source': '内置中医知识库 - 气血理论', 'content': knowledge['definition'], 'score': 1.0},
            {'source': '中医养生学', 'content': knowledge['nourishment'], 'score': 0.9}
        ]

    elif any(keyword in question_lower for keyword in ["阴阳", "阴阳平衡", "阴虚", "阳虚"]):
        knowledge = TCM_KNOWLEDGE["阴阳"]
        response = f"""## ☯️ 关于阴阳的中医理论

### 📖 基本概念
{knowledge['definition']}

### ⚖️ 平衡重要性
{knowledge['balance']}

### 🔄 阴阳特性
{knowledge['characteristics']}

### 🎯 调节方法
{knowledge['regulation']}

### 💡 实用指导
- **阴虚调理**: 滋阴润燥，如食用银耳、百合、枸杞等
- **阳虚调理**: 温补阳气，如食用生姜、肉桂、羊肉等
- **平衡维护**: 避免过度偏寒或偏热的食物
- **生活调节**: 根据季节变化调整作息和饮食

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业中医师。"""

        sources = [
            {'source': '内置中医知识库 - 阴阳学说', 'content': knowledge['definition'], 'score': 1.0},
            {'source': '中医基础理论', 'content': knowledge['balance'], 'score': 0.9}
        ]

    elif any(keyword in question_lower for keyword in ["四季", "养生", "春夏秋冬", "季节"]):
        knowledge = TCM_KNOWLEDGE["四季养生"]
        response = f"""## 🌸 四季养生的中医智慧

### 🌱 春季养生
{knowledge['spring']}

### ☀️ 夏季养生
{knowledge['summer']}

### 🍂 秋季养生
{knowledge['autumn']}

### ❄️ 冬季养生
{knowledge['winter']}

### 💡 四季养生要点
- **顺应自然**: 根据季节变化调整生活方式
- **饮食调节**: 春酸夏苦秋辛冬咸的饮食原则
- **运动适宜**: 春夏多动，秋冬少动的运动规律
- **情志调养**: 保持心情舒畅，避免情绪波动

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如有健康问题请咨询专业中医师。"""

        sources = [
            {'source': '内置中医知识库 - 四季养生', 'content': '四季养生的基本原则', 'score': 1.0},
            {'source': '中医养生学', 'content': knowledge['spring'], 'score': 0.9}
        ]

    elif any(keyword in question_lower for keyword in ["经络", "穴位", "针灸", "按摩"]):
        knowledge = TCM_KNOWLEDGE["经络"]
        response = f"""## 🌐 关于经络的中医理论

### 📖 基本概念
{knowledge['definition']}

### 🔗 十二正经
{knowledge['meridians']}

### ⚡ 主要功能
{knowledge['functions']}

### 📍 穴位作用
{knowledge['acupoints']}

### 💡 实用知识
- **常用穴位**: 足三里、合谷、百会、涌泉等
- **按摩方法**: 轻柔按压，顺时针揉动
- **注意事项**: 力度适中，避免过度刺激
- **最佳时间**: 早晨或睡前进行穴位按摩

### ⚠️ 重要提醒
本回答仅供中医文化学习参考，如需针灸治疗请咨询专业中医师。"""

        sources = [
            {'source': '内置中医知识库 - 经络学说', 'content': knowledge['definition'], 'score': 1.0},
            {'source': '针灸学基础', 'content': knowledge['acupoints'], 'score': 0.9}
        ]

    else:
        response = f"""## 🤖 智能助手回复

感谢您的提问：「{question}」

很抱歉，我暂时没有找到与您问题直接相关的资料。

### 💡 建议您：
- 尝试询问关于 **湿气、气血、阴阳、四季养生、经络** 等中医基础概念
- 使用更具体的中医术语，如"湿气重怎么办"、"气血不足的表现"
- 上传相关的PDF文档来扩充知识库

### 📚 我可以帮您了解：
- 🌿 **湿气调理**: 湿气的表现、成因和调理方法
- 🫖 **气血养生**: 气血理论和调养方法
- ☯️ **阴阳平衡**: 阴阳学说和平衡调节
- 🌸 **四季养生**: 春夏秋冬的养生要点
- 🌐 **经络穴位**: 经络理论和常用穴位

### 🎤 语音功能
- 点击麦克风图标可以语音输入问题
- 开启自动朗读可以听到语音回答

### ⚠️ 重要提醒
本系统仅供中医文化学习参考，不构成医疗建议。如有健康问题，请咨询专业医疗机构。"""

        sources = []

    return response, sources

# 增强版前端HTML - 包含完整语音功能
ENHANCED_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 中医智能助手 - 语音增强版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh; overflow: hidden;
        }
        .chat-container {
            height: 100vh; display: flex; flex-direction: column;
            max-width: 1200px; margin: 0 auto; background: white;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }
        .chat-header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white; padding: 20px; text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-header h1 { font-size: 24px; margin-bottom: 8px; }
        .chat-header p { opacity: 0.9; font-size: 14px; }
        .status-indicators {
            display: flex; justify-content: center; gap: 15px; margin-top: 10px;
        }
        .status-badge {
            background: rgba(255,255,255,0.2); padding: 5px 12px;
            border-radius: 15px; font-size: 12px; display: flex;
            align-items: center; gap: 5px;
        }
        .chat-main { flex: 1; display: flex; overflow: hidden; }
        .chat-sidebar {
            width: 280px; background: #f8f9fa; border-right: 1px solid #e9ecef;
            display: flex; flex-direction: column;
        }
        .chat-content { flex: 1; display: flex; flex-direction: column; }
        .messages-container {
            flex: 1; overflow-y: auto; padding: 20px; background: #fafafa;
        }
        .message { margin-bottom: 20px; display: flex; align-items: flex-start; }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 70%; padding: 12px 16px; border-radius: 18px;
            word-wrap: break-word; position: relative;
        }
        .message.user .message-content {
            background: #2E8B57; color: white; border-bottom-right-radius: 4px;
        }
        .message.assistant .message-content {
            background: white; border: 1px solid #e9ecef; border-bottom-left-radius: 4px;
        }
        .message-avatar {
            width: 40px; height: 40px; border-radius: 50%; margin: 0 10px;
            display: flex; align-items: center; justify-content: center;
            font-size: 18px;
        }
        .user-avatar { background: #2E8B57; color: white; }
        .assistant-avatar { background: #667eea; color: white; }
        .voice-controls {
            position: absolute; top: 5px; right: 5px; display: flex; gap: 5px;
        }
        .voice-btn {
            width: 24px; height: 24px; border: none; border-radius: 50%;
            background: #f0f0f0; cursor: pointer; display: flex;
            align-items: center; justify-content: center; font-size: 12px;
        }
        .voice-btn:hover { background: #e0e0e0; }
        .input-container {
            padding: 20px; background: white; border-top: 1px solid #e9ecef;
        }
        .input-row { display: flex; gap: 10px; align-items: flex-end; }
        .input-textarea { flex: 1; position: relative; }
        .input-textarea textarea {
            width: 100%; padding: 12px 50px 12px 12px; border: 1px solid #ddd;
            border-radius: 12px; resize: none; font-size: 16px;
            transition: border-color 0.3s;
        }
        .input-textarea textarea:focus {
            outline: none; border-color: #2E8B57;
        }
        .voice-input-btn {
            position: absolute; right: 10px; top: 50%; transform: translateY(-50%);
            width: 36px; height: 36px; border: none; border-radius: 50%;
            background: #2E8B57; color: white; cursor: pointer;
            display: flex; align-items: center; justify-content: center;
            font-size: 16px; transition: all 0.3s;
        }
        .voice-input-btn:hover { background: #228B22; transform: translateY(-50%) scale(1.05); }
        .voice-input-btn.recording {
            background: #dc3545; animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.1); }
            100% { transform: translateY(-50%) scale(1); }
        }
        .send-btn {
            padding: 12px 20px; background: #2E8B57; color: white;
            border: none; border-radius: 12px; cursor: pointer; font-size: 16px;
            transition: background 0.3s;
        }
        .send-btn:hover { background: #228B22; }
        .send-btn:disabled { background: #ccc; cursor: not-allowed; }
        .quick-actions {
            padding: 15px; border-bottom: 1px solid #e9ecef;
        }
        .quick-actions h3 { font-size: 14px; color: #666; margin-bottom: 10px; }
        .quick-btn {
            width: 100%; margin-bottom: 8px; padding: 10px; background: white;
            border: 1px solid #ddd; border-radius: 8px; cursor: pointer;
            text-align: left; font-size: 14px; transition: all 0.3s;
        }
        .quick-btn:hover { background: #f0f0f0; border-color: #2E8B57; }
        .settings-panel {
            padding: 15px; border-bottom: 1px solid #e9ecef;
        }
        .settings-panel h3 { font-size: 14px; color: #666; margin-bottom: 10px; }
        .setting-item {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 10px; font-size: 14px;
        }
        .switch {
            position: relative; width: 40px; height: 20px; background: #ccc;
            border-radius: 20px; cursor: pointer; transition: background 0.3s;
        }
        .switch.active { background: #2E8B57; }
        .switch-handle {
            position: absolute; top: 2px; left: 2px; width: 16px; height: 16px;
            background: white; border-radius: 50%; transition: transform 0.3s;
        }
        .switch.active .switch-handle { transform: translateX(20px); }
        .typing { color: #666; font-style: italic; }
        .typing-dots { display: inline-flex; gap: 3px; }
        .typing-dot {
            width: 6px; height: 6px; border-radius: 50%; background: #666;
            animation: typing 1.4s infinite;
        }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        .voice-status {
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white; padding: 20px 30px;
            border-radius: 15px; display: none; z-index: 1000;
        }
        .voice-status.show { display: block; }
        @media (max-width: 768px) {
            .chat-sidebar { display: none; }
            .message-content { max-width: 85%; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 头部 -->
        <div class="chat-header">
            <h1>🏥 中医智能助手</h1>
            <p>语音增强版 · 传承千年智慧，现代化智能交互</p>
            <div class="status-indicators">
                <div class="status-badge">
                    <span id="voiceStatus">🎤</span>
                    <span>语音就绪</span>
                </div>
                <div class="status-badge">
                    <span>🤖</span>
                    <span>AI助手在线</span>
                </div>
                <div class="status-badge">
                    <span>🔒</span>
                    <span>安全合规</span>
                </div>
            </div>
        </div>

        <!-- 主体内容 -->
        <div class="chat-main">
            <!-- 侧边栏 -->
            <div class="chat-sidebar">
                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h3>💡 快捷查询</h3>
                    <button class="quick-btn" onclick="sendQuickMessage('湿气重有什么表现？如何调理？')">
                        🌿 湿气调理
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('气血不足的症状和调养方法')">
                        🫖 气血养生
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('什么是阴阳平衡？如何维持？')">
                        ☯️ 阴阳平衡
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('四季养生的基本原则和方法')">
                        🌸 四季养生
                    </button>
                    <button class="quick-btn" onclick="sendQuickMessage('经络学说和常用穴位介绍')">
                        🌐 经络穴位
                    </button>
                </div>

                <!-- 设置面板 -->
                <div class="settings-panel">
                    <h3>⚙️ 语音设置</h3>

                    <div class="setting-item">
                        <span>语音输入</span>
                        <div class="switch active" id="voiceInputSwitch" onclick="toggleVoiceInput()">
                            <div class="switch-handle"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <span>自动朗读</span>
                        <div class="switch" id="autoSpeakSwitch" onclick="toggleAutoSpeak()">
                            <div class="switch-handle"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <span>语音速度</span>
                        <select id="speechRate" onchange="updateSpeechRate()">
                            <option value="0.7">慢速</option>
                            <option value="1.0" selected>正常</option>
                            <option value="1.3">快速</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 聊天内容区 -->
            <div class="chat-content">
                <!-- 消息列表 -->
                <div class="messages-container" id="messagesContainer">
                    <div class="message assistant">
                        <div class="message-avatar assistant-avatar">🤖</div>
                        <div class="message-content">
                            <strong>🤖 助手:</strong> 您好！我是中医智能助手语音增强版。您可以：<br>
                            • 🎤 点击麦克风图标进行语音输入<br>
                            • 💡 点击左侧快捷按钮查询常见问题<br>
                            • ✍️ 直接输入您的中医相关问题<br>
                            • 🔊 开启自动朗读听取语音回答<br><br>
                            请问有什么可以帮助您的吗？
                            <div class="voice-controls">
                                <button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-row">
                        <div class="input-textarea">
                            <textarea id="messageInput" rows="2" placeholder="请输入您想了解的中医知识，或点击麦克风使用语音输入..." onkeydown="handleKeyDown(event)"></textarea>
                            <button class="voice-input-btn" id="voiceInputBtn" onclick="toggleVoiceRecording()" title="语音输入">
                                🎤
                            </button>
                        </div>
                        <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 语音状态提示 -->
    <div class="voice-status" id="voiceStatus">
        <div id="voiceStatusText">🎤 正在录音，请说话...</div>
    </div>

    <script>
        // 全局变量
        let isTyping = false;
        let voiceInputEnabled = true;
        let autoSpeakEnabled = false;
        let speechRate = 1.0;
        let recognition = null;
        let synthesis = null;
        let isRecording = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeVoice();
            updateVoiceStatus();
        });

        // 初始化语音功能
        function initializeVoice() {
            // 语音识别
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'zh-CN';

                recognition.onstart = function() {
                    isRecording = true;
                    updateVoiceInputButton();
                    showVoiceStatus('🎤 正在录音，请说话...');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    hideVoiceStatus();
                    showNotification('🎤 语音识别完成: ' + transcript);
                };

                recognition.onerror = function(event) {
                    hideVoiceStatus();
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    updateVoiceInputButton();
                    hideVoiceStatus();
                };
            } else {
                voiceInputEnabled = false;
                document.getElementById('voiceInputSwitch').classList.remove('active');
                showNotification('⚠️ 浏览器不支持语音识别', 'warning');
            }

            // 语音合成
            if ('speechSynthesis' in window) {
                synthesis = window.speechSynthesis;
            } else {
                showNotification('⚠️ 浏览器不支持语音合成', 'warning');
            }
        }

        // 更新语音状态
        function updateVoiceStatus() {
            const statusElement = document.getElementById('voiceStatus');
            if (voiceInputEnabled && recognition) {
                statusElement.textContent = '🎤';
            } else {
                statusElement.textContent = '🚫';
            }
        }

        // 切换语音输入
        function toggleVoiceInput() {
            voiceInputEnabled = !voiceInputEnabled;
            const switchElement = document.getElementById('voiceInputSwitch');
            if (voiceInputEnabled) {
                switchElement.classList.add('active');
            } else {
                switchElement.classList.remove('active');
            }
            updateVoiceStatus();
        }

        // 切换自动朗读
        function toggleAutoSpeak() {
            autoSpeakEnabled = !autoSpeakEnabled;
            const switchElement = document.getElementById('autoSpeakSwitch');
            if (autoSpeakEnabled) {
                switchElement.classList.add('active');
            } else {
                switchElement.classList.remove('active');
            }
        }

        // 更新语音速度
        function updateSpeechRate() {
            speechRate = parseFloat(document.getElementById('speechRate').value);
        }

        // 语音录音切换
        function toggleVoiceRecording() {
            if (!voiceInputEnabled || !recognition) {
                showNotification('⚠️ 语音输入未启用或不支持', 'warning');
                return;
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 更新语音输入按钮状态
        function updateVoiceInputButton() {
            const btn = document.getElementById('voiceInputBtn');
            if (isRecording) {
                btn.classList.add('recording');
                btn.textContent = '🛑';
            } else {
                btn.classList.remove('recording');
                btn.textContent = '🎤';
            }
        }

        // 显示语音状态
        function showVoiceStatus(text) {
            const statusDiv = document.getElementById('voiceStatus');
            const textDiv = document.getElementById('voiceStatusText');
            textDiv.textContent = text;
            statusDiv.classList.add('show');
        }

        // 隐藏语音状态
        function hideVoiceStatus() {
            document.getElementById('voiceStatus').classList.remove('show');
        }

        // 朗读消息
        function speakMessage(button) {
            if (!synthesis) {
                showNotification('⚠️ 浏览器不支持语音合成', 'warning');
                return;
            }

            const messageContent = button.closest('.message-content');
            const text = messageContent.textContent.replace(/🤖 助手:|👤 您:/g, '').trim();

            // 停止当前朗读
            synthesis.cancel();

            // 创建语音
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = speechRate;
            utterance.pitch = 1;

            // 查找中文语音
            const voices = synthesis.getVoices();
            const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
            if (chineseVoice) {
                utterance.voice = chineseVoice;
            }

            synthesis.speak(utterance);
            showNotification('🔊 开始朗读');
        }

        // 自动朗读
        function autoSpeak(text) {
            if (autoSpeakEnabled && synthesis) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = speechRate;
                utterance.pitch = 1;

                const voices = synthesis.getVoices();
                const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
                if (chineseVoice) {
                    utterance.voice = chineseVoice;
                }

                synthesis.speak(utterance);
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 简单的通知实现
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件处理
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在思考中...', 'typing');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        use_voice: voiceInputEnabled
                    })
                });

                const data = await response.json();

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 自动朗读
                if (autoSpeakEnabled) {
                    autoSpeak(data.response.replace(/[#*]/g, ''));
                }

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${type}-avatar`;
            avatarDiv.textContent = type === 'user' ? '👤' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            contentDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\\n/g, '<br>')}`;

            // 为助手消息添加语音控制
            if (type === 'assistant' && !className.includes('typing')) {
                const voiceControls = document.createElement('div');
                voiceControls.className = 'voice-controls';
                voiceControls.innerHTML = '<button class="voice-btn" onclick="speakMessage(this)" title="朗读">🔊</button>';
                contentDiv.appendChild(voiceControls);
            }

            if (type === 'user') {
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(avatarDiv);
            } else {
                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(contentDiv);
            }

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing');
            if (typingMessage) {
                typingMessage.remove();
            }
        }
    </script>
</body>
</html>
"""

# API路由
@app.get("/")
async def read_root():
    """返回语音增强版聊天界面"""
    return HTMLResponse(content=ENHANCED_HTML)

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "语音输入输出",
            "现代聊天界面",
            "中医知识库",
            "智能问答"
        ],
        "voice_support": True
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """语音增强聊天接口"""
    try:
        session_id = chat_message.session_id or str(uuid.uuid4())
        response, sources = generate_enhanced_response(chat_message.message)

        return ChatResponse(
            response=response,
            sources=sources,
            session_id=session_id,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动语音增强版中医聊天界面...")
    print("🎤 特色功能: 语音输入 + 语音输出 + 现代界面")
    print("🌐 访问地址: http://localhost:8003")
    print("📚 API文档: http://localhost:8003/docs")
    print("💡 使用提示:")
    print("  - 点击麦克风图标进行语音输入")
    print("  - 开启自动朗读听取语音回答")
    print("  - 支持Chrome、Edge等现代浏览器")

    uvicorn.run(
        "enhanced_voice_chat:app",
        host="0.0.0.0",
        port=8003,
        reload=True,
        log_level="info"
    )