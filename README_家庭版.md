# 🏥 家庭中医智能助手

> 24/7为您和家人提供专业中医知识查询服务

## 🌟 项目特色

### 为什么选择家庭中医智能助手？

- **🏠 专为家庭设计**: 简单易用，老人小孩都能轻松操作
- **📚 知识库可扩展**: 支持上传任意中医PDF文档，越用越智能
- **👨‍👩‍👧‍👦 多用户支持**: 家人朋友各自使用，互不干扰
- **🔒 隐私安全**: 所有数据本地存储，不上传云端
- **⏰ 24/7服务**: 笔记本不关机，随时随地查询中医知识
- **💡 智能回答**: 基于真实中医文献，提供专业可靠的建议

## 🚀 快速开始

### 1. 一键启动

```bash
python start_family_tcm.py
```

### 2. 浏览器访问

- **本机**: http://localhost:8511
- **手机/平板**: http://您的电脑IP:8511

### 3. 开始使用

1. 选择身份（爸爸/妈妈/爷爷/奶奶等）
2. 输入中医问题
3. 获得专业回答

## 📱 使用场景

### 🏠 日常家庭场景

```
👩 妈妈: "孩子感冒了，中医怎么看？"
🤖 助手: "根据《伤寒论》，小儿感冒分风寒、风热两型..."

👴 爷爷: "老是失眠，有什么中医调理方法？"
🤖 助手: "中医认为失眠多因心神不安，可用甘麦大枣汤..."

👨 爸爸: "工作压力大，中医如何调理？"
🤖 助手: "肝气郁结是现代人常见问题，建议疏肝解郁..."
```

### 📚 知识学习场景

```
🧑‍🎓 学生: "五行学说在中医中的应用？"
🤖 助手: "五行学说指导脏腑关系、病理传变、治疗原则..."

👥 朋友: "什么是气血津液？"
🤖 助手: "气血津液是人体生命活动的基本物质..."
```

## 📖 知识库建设

### 推荐上传的文档类型

#### 📜 中医经典 (必备)
- 《黄帝内经》- 理论基础
- 《伤寒论》- 外感病
- 《金匮要略》- 内科病
- 《温病条辨》- 温病学

#### 💊 方剂药物
- 《方剂学》教材
- 《中药学》教材  
- 《中医方剂大辞典》
- 各类中成药说明

#### 🏥 临床实践
- 名老中医医案
- 专科诊疗指南
- 现代中医研究

#### 🍽️ 养生保健
- 《食疗本草》
- 四季养生指南
- 体质调理方案

### 批量上传文档

```bash
# 方法1: Web界面上传
访问 "知识库管理" -> "添加中医文档" -> 选择多个PDF

# 方法2: 批量处理
python batch_document_processor.py
```

## 🔧 系统配置

### 网络访问设置

#### 局域网共享 (推荐)

1. **查看电脑IP地址**:
   ```bash
   # Windows
   ipconfig
   
   # Mac/Linux  
   ifconfig
   ```

2. **家人访问方式**:
   - 手机浏览器: `http://*************:8511`
   - 平板电脑: `http://*************:8511`
   - 其他电脑: `http://*************:8511`

3. **防火墙设置**:
   - Windows: 允许8511端口通过防火墙
   - Mac: 系统偏好设置 -> 安全性 -> 防火墙

### 性能优化

#### 硬件建议
- **CPU**: i5或同等级别以上
- **内存**: 8GB+ (最低4GB)
- **存储**: SSD硬盘 (提升响应速度)
- **网络**: 有线网络连接

#### 软件优化
```bash
# 关闭不必要程序
# 定期清理系统垃圾
# 保持系统更新
```

## 📊 使用统计

### 系统会自动记录

- **📈 使用频率**: 每日查询次数统计
- **👥 用户活跃**: 家庭成员使用情况
- **🔥 热门问题**: 最常询问的中医问题
- **📚 知识覆盖**: 文档利用率分析

### 查看统计信息

访问Web界面 -> "使用统计" 标签页

## 🛡️ 安全与隐私

### 数据安全

- ✅ **本地存储**: 所有数据保存在您的电脑上
- ✅ **不上传云端**: 查询记录不会泄露
- ✅ **局域网访问**: 仅限家庭网络内使用
- ✅ **定期备份**: 自动备份重要数据

### 隐私保护

- 🔒 用户查询记录仅本地保存
- 🔒 支持匿名使用模式
- 🔒 可随时清除历史记录
- 🔒 不收集个人敏感信息

## ⚠️ 重要声明

### 医疗免责

本系统提供的信息仅供中医知识学习参考，具有以下限制：

- ❌ **不能替代医生**: 无法替代专业医生诊断
- ❌ **不提供处方**: 不提供具体用药剂量
- ❌ **不诊断疾病**: 不能确诊具体疾病
- ✅ **知识参考**: 提供中医理论知识参考
- ✅ **学习工具**: 作为中医学习辅助工具

### 使用建议

1. **轻症参考**: 轻微不适可参考调理建议
2. **重症就医**: 严重症状请及时就医
3. **结合实际**: 结合个人体质情况
4. **专业咨询**: 重要问题咨询中医师

## 🔧 故障排除

### 常见问题

#### Q: 启动失败怎么办？
```bash
# 检查Python版本
python --version  # 需要3.8+

# 重新安装依赖
pip install -r requirements.txt

# 查看错误日志
cat user_logs/tcm_system_*.log
```

#### Q: 访问速度慢？
- 检查电脑内存使用情况
- 关闭其他占用资源的程序
- 重启应用程序

#### Q: 手机无法访问？
- 确认电脑和手机在同一WiFi网络
- 检查防火墙设置
- 确认IP地址正确

#### Q: 上传文档失败？
- 检查PDF文件是否损坏
- 确认文件大小不超过200MB
- 查看处理日志了解具体错误

## 📞 技术支持

### 自助解决

1. **查看日志**: `user_logs/` 目录下的日志文件
2. **重新初始化**: 删除 `vector_db/` 目录重新启动
3. **清理缓存**: 删除临时文件重新处理

### 系统维护

```bash
# 每周执行一次
python maintenance.py

# 备份知识库
python backup_system.py

# 清理日志文件
python cleanup_logs.py
```

## 🎯 最佳实践

### 部署建议

1. **专用电脑**: 使用一台专门的电脑作为家庭服务器
2. **稳定电源**: 配置UPS不间断电源
3. **定期维护**: 每周检查系统运行状态
4. **网络稳定**: 使用有线网络连接

### 使用技巧

1. **详细描述**: 描述症状时尽量详细具体
2. **多角度提问**: 从不同角度询问同一问题
3. **关键词搜索**: 使用中医专业术语提问
4. **历史记录**: 查看之前的问答记录

## 📈 未来规划

### 功能扩展

- 🎤 **语音交互**: 支持语音问答
- 📸 **图像识别**: 中药材图片识别
- 📱 **移动APP**: 开发手机应用
- 🤖 **智能诊断**: 症状智能分析

### 知识扩展

- 🏥 **专科知识**: 妇科、儿科、骨科
- 🍽️ **食疗药膳**: 养生食谱推荐
- 💆 **推拿按摩**: 穴位按摩指导
- 🧘 **养生功法**: 太极、气功指导

---

**🏥 让中医智慧走进千家万户，为家人健康保驾护航！**

> 如有问题或建议，欢迎反馈交流
