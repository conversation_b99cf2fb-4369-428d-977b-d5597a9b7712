# 中医RAG系统问答质量优化报告

## 问题描述

用户反馈中医RAG系统能够成功处理《金匮要略》文档，但是在问答环节出现以下问题：
1. 提问后没有得到预期的高质量答案
2. 系统返回的回答过于简单或不相关
3. 无法充分利用已处理的中医文献内容

## 问题分析

通过深入分析，发现了以下核心问题：

### 1. 输入长度超限问题
- **现象**: `Input length of input_ids is 2373, but max_length is set to 1024`
- **原因**: 检索到的文档内容过长，超过了模型的最大输入长度限制
- **影响**: 导致生成过程出错，无法产生有意义的回答

### 2. 模型生成参数配置问题
- **现象**: 使用`max_length`参数导致长度冲突
- **原因**: `max_length`包含输入和输出的总长度，当输入过长时会出现问题
- **影响**: 生成过程频繁报错

### 3. 检索内容质量问题
- **现象**: 检索到版权页、目录等无关内容
- **原因**: 缺乏内容过滤机制
- **影响**: 降低了回答的相关性和准确性

### 4. LLM模型适配问题
- **现象**: DialoGPT模型生成的回答质量不佳
- **原因**: DialoGPT主要用于对话，不太适合知识问答任务
- **影响**: 即使技术问题解决，回答质量仍然不理想

## 解决方案

### 1. 创建增强版RAG系统 (`enhanced_rag_system.py`)

#### 严格的长度控制
```python
self.max_context_length = 400  # 进一步减少上下文长度
self.max_retrieved_docs = 2    # 进一步限制检索文档数量
self.max_prompt_length = 600   # 限制总提示词长度
```

#### 内容过滤机制
```python
def filter_relevant_content(self, docs: List[Dict]) -> List[Dict]:
    """过滤相关内容，移除版权页等无关信息"""
    irrelevant_keywords = [
        "版权页", "图书在版编目", "CIP", "ISBN", "定价", "著作权",
        "出版社", "印刷", "标准书号", "版次", "印次"
    ]
```

#### 优化的提示词生成
- 使用更简洁的提示词模板
- 动态截断过长内容
- 移除历史对话以节省空间

### 2. 修复模型生成参数 (`models/model_manager.py`)

#### 使用max_new_tokens替代max_length
```python
def generate_response(self, prompt, max_new_tokens=100, temperature=0.7):
    # 使用max_new_tokens避免长度冲突
    outputs = self.llm_model.generate(
        inputs,
        max_new_tokens=max_new_tokens,  # 只限制新生成的token数量
        temperature=temperature,
        attention_mask=torch.ones_like(inputs)  # 添加attention_mask
    )
```

#### 输入长度预处理
```python
# 截断输入以确保不超过模型限制
max_input_length = 800
if len(prompt) > max_input_length:
    prompt = prompt[:max_input_length] + "..."
```

### 3. 基于规则的回答生成器 (`simple_answer_generator.py`)

当LLM模型效果不佳时，使用基于规则的生成器作为备选方案：

#### 中医知识库
```python
self.tcm_keywords = {
    "湿气": {
        "symptoms": ["身体沉重", "头昏", "胸闷", "食欲不振"],
        "treatment": ["健脾祛湿", "利水渗湿", "芳香化湿"],
        "herbs": ["茯苓", "白术", "陈皮", "半夏", "薏苡仁"]
    },
    # ... 更多中医概念
}
```

#### 智能回答生成
- 关键词提取和匹配
- 基于问题类型的回答模板
- 结合检索到的文献内容

### 4. 混合生成策略

```python
# 尝试使用LLM生成回答
llm_answer = model_manager.generate_response(prompt, max_new_tokens=150)

# 如果LLM回答质量不好，使用简单生成器
if not llm_answer or len(llm_answer.strip()) < 20:
    context = "\n".join([doc["content"] for doc in relevant_docs])
    answer = simple_generator.generate_answer_from_context(query, context)
```

## 测试结果

### 修复前
- 系统报错：输入长度超限
- 回答质量：无意义的错误信息
- 用户体验：无法正常使用

### 修复后
- 系统稳定：无长度超限错误
- 回答质量：提供有意义的中医知识回答
- 用户体验：能够获得实用的中医信息

### 测试案例
1. **问题**: "身体湿气严重的表现是什么？该如何治疗？"
   **回答**: "湿气的主要表现包括：身体沉重、头昏、胸闷、食欲不振、大便粘腻、舌苔厚腻。根据《金匮要略》等中医经典：..."

2. **问题**: "脾胃虚弱有什么症状？"
   **回答**: "脾胃的主要表现包括：食欲不振、腹胀、便溏、乏力、面色萎黄。根据《金匮要略》等中医经典：..."

## 技术改进总结

1. **长度管理**: 实现了严格的输入输出长度控制
2. **内容过滤**: 自动过滤无关内容，提高回答相关性
3. **参数优化**: 使用更合适的生成参数避免技术错误
4. **备选策略**: 当LLM效果不佳时使用规则生成器
5. **用户体验**: 提供稳定可靠的问答服务

## 部署状态

- ✅ 增强RAG系统已部署
- ✅ Web界面已更新使用增强系统
- ✅ 应用运行在 http://localhost:8510
- ✅ 所有功能测试通过

## 后续优化建议

1. **模型升级**: 考虑使用更适合中文知识问答的模型
2. **知识库扩展**: 增加更多中医概念和治疗方案
3. **上下文优化**: 进一步优化文档检索和排序算法
4. **用户反馈**: 收集用户反馈持续改进回答质量
