#!/usr/bin/env python3
"""
启动终极商业化中医RAG系统
确保所有依赖都正确安装和配置
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True

def install_requirements():
    """安装必要的依赖包"""
    requirements = [
        "streamlit>=1.28.0",
        "sentence-transformers>=2.2.0",
        "llama-cpp-python>=0.2.0",
        "faiss-cpu>=1.7.0",
        "PyPDF2>=3.0.0",
        "numpy>=1.21.0",
        "requests>=2.28.0",
        "beautifulsoup4>=4.11.0",
        "torch>=1.13.0"
    ]
    
    print("📦 检查并安装依赖包...")
    
    for requirement in requirements:
        try:
            package_name = requirement.split(">=")[0]
            __import__(package_name.replace("-", "_"))
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"📥 安装 {requirement}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", requirement])
                print(f"✅ {requirement} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {requirement} 安装失败")
                return False
    
    return True

def check_deepseek_model():
    """检查DeepSeek模型文件"""
    model_path = r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf'
    
    if os.path.exists(model_path):
        print(f"✅ DeepSeek模型文件存在: {model_path}")
        return True
    else:
        print(f"❌ DeepSeek模型文件不存在: {model_path}")
        print("请确保模型文件路径正确，或者修改 ultimate_commercial_tcm.py 中的 DEEPSEEK_MODEL_PATH")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "./ultimate_vector_db",
        "./documents",
        "./uploads",
        "./logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 目录已创建: {directory}")

def check_gpu_support():
    """检查GPU支持"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ GPU可用: {torch.cuda.get_device_name(0)}")
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("⚠️ GPU不可用，将使用CPU模式")
            return False
    except ImportError:
        print("⚠️ PyTorch未安装，无法检查GPU状态")
        return False

def main():
    """主函数"""
    print("🚀 启动终极商业化中医RAG系统")
    print("=" * 50)
    
    # 1. 检查Python版本
    if not check_python_version():
        return
    
    # 2. 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    # 3. 检查DeepSeek模型
    if not check_deepseek_model():
        print("⚠️ 模型文件检查失败，但系统仍可启动（功能受限）")
    
    # 4. 创建目录
    create_directories()
    
    # 5. 检查GPU支持
    check_gpu_support()
    
    print("\n" + "=" * 50)
    print("🎉 系统检查完成！")
    print("📝 使用说明：")
    print("   1. 系统启动后，点击'初始化系统'按钮")
    print("   2. 上传中医PDF文档到知识库")
    print("   3. 开始智能问答")
    print("\n💡 特色功能：")
    print("   ✅ 真正的PDF文档检索")
    print("   ✅ DeepSeek模型智能回答")
    print("   ✅ 在线中医资源整合")
    print("   ✅ 商业级用户体验")
    
    print("\n🚀 正在启动Streamlit应用...")
    
    try:
        # 启动Streamlit应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_commercial_tcm.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--theme.base=light"
        ])
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
