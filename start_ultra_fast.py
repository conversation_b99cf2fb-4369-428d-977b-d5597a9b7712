"""
超快速RAG系统启动脚本 - AMD GPU优化版
"""
import subprocess
import sys
import psutil
import gc
import os

def setup_ultra_environment():
    """设置超快速环境"""
    print("🔧 配置超快速环境...")
    
    # AMD GPU优化环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:64'
    os.environ['TOKENIZERS_PARALLELISM'] = 'true'  # 启用并行tokenizer
    os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
    os.environ['PYTHONHASHSEED'] = '0'
    
    # 多线程优化
    cpu_count = os.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(min(cpu_count, 8))
    os.environ['MKL_NUM_THREADS'] = str(min(cpu_count, 8))
    os.environ['NUMEXPR_NUM_THREADS'] = str(min(cpu_count, 4))
    
    # 内存优化
    gc.collect()
    
    print("✅ 超快速环境配置完成")

def check_system_readiness():
    """检查系统就绪状态"""
    print("🔍 检查系统就绪状态...")
    
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"💾 内存: {memory.percent:.1f}% 使用 ({memory.available / (1024**3):.1f} GB 可用)")
    print(f"🖥️ CPU: {cpu_percent:.1f}% 使用")
    
    # 检查GPU配置
    try:
        from gpu_optimizer import setup_gpu_optimization
        gpu_config = setup_gpu_optimization()
        print(f"🚀 GPU配置: {gpu_config['device_name']}")
        print(f"⚡ 优化级别: {gpu_config['optimization_level']}")
    except Exception as e:
        print(f"⚠️ GPU配置检查失败: {e}")
    
    # 系统建议
    if memory.percent > 85:
        print("⚠️ 内存使用过高，建议清理后启动")
        return False
    
    if cpu_percent > 80:
        print("⚠️ CPU使用过高，可能影响性能")
    
    print("✅ 系统状态良好")
    return True

def main():
    print("🚀 超快速RAG系统启动 - AMD GPU优化版")
    print("=" * 50)
    
    # 检查系统状态
    if not check_system_readiness():
        response = input("系统状态不佳，是否继续启动？(y/n): ").lower().strip()
        if response != 'y':
            print("👋 启动已取消")
            return
    
    # 配置环境
    setup_ultra_environment()
    
    print("\n🚀 启动超快速RAG系统...")
    print("📝 访问地址: http://localhost:8507")
    print("\n⚡ 超快速特性:")
    print("   🧵 并行PDF提取 - 多线程处理")
    print("   🔄 并行向量化 - 批量嵌入")
    print("   🏗️ 超快速索引 - 优化算法")
    print("   💾 并行保存 - 同步写入")
    print("   🧠 AMD GPU优化 - 智能加速")
    
    print(f"\n📊 性能预期 (43MB文件):")
    print(f"   🔥 超快速版: 2-4分钟")
    print(f"   📈 比原版提升: 5-10倍")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app_ultra_fast.py", 
            "--server.address", "localhost",
            "--server.port", "8507",
            "--server.headless", "true",
            "--server.maxUploadSize", "100",  # 100MB限制
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用超快速RAG系统！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查依赖安装和系统配置")

if __name__ == "__main__":
    main()
