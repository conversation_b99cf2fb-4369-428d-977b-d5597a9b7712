"""
强化中医PDF处理器 - 支持多种PDF格式
"""
import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
import numpy as np
import faiss
import pickle
import json
import time
import gc
import threading
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import config
from gpu_optimizer import setup_gpu_optimization

class RobustTCMProcessor:
    def __init__(self):
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        
        # 获取GPU优化配置
        self.gpu_config = setup_gpu_optimization()
        self.batch_size = self.gpu_config['batch_size']
        self.chunk_size = self.gpu_config['chunk_size']
        
        print(f"🏥 强化中医RAG处理器初始化")
        print(f"   设备: {self.gpu_config['device_name']}")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   块大小: {self.chunk_size}")
    
    def extract_pdf_multiple_methods(self, pdf_path: str) -> str:
        """使用多种方法提取PDF文本"""
        print(f"📄 使用多种方法提取PDF: {pdf_path}")
        
        methods = [
            ("PyMuPDF", self.extract_with_pymupdf),
            ("pdfplumber", self.extract_with_pdfplumber),
            ("PyPDF2", self.extract_with_pypdf2)
        ]
        
        best_text = ""
        best_length = 0
        
        for method_name, extract_func in methods:
            try:
                print(f"   尝试 {method_name} 方法...")
                text = extract_func(pdf_path)
                text_length = len(text.strip())
                
                print(f"   {method_name}: 提取了 {text_length} 字符")
                
                if text_length > best_length:
                    best_text = text
                    best_length = text_length
                    print(f"   ✅ {method_name} 效果最好")
                
            except Exception as e:
                print(f"   ❌ {method_name} 失败: {e}")
                continue
        
        if best_length > 100:  # 至少100字符才算成功
            print(f"✅ 最佳提取结果: {best_length} 字符")
            return best_text
        else:
            print(f"❌ 所有方法都失败，只提取到 {best_length} 字符")
            return ""
    
    def extract_with_pymupdf(self, pdf_path: str) -> str:
        """使用PyMuPDF提取"""
        doc = fitz.open(pdf_path)
        text = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            text += page_text + "\n"
        
        doc.close()
        return self.clean_tcm_text(text)
    
    def extract_with_pdfplumber(self, pdf_path: str) -> str:
        """使用pdfplumber提取"""
        text = ""
        
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        
        return self.clean_tcm_text(text)
    
    def extract_with_pypdf2(self, pdf_path: str) -> str:
        """使用PyPDF2提取"""
        text = ""
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page in pdf_reader.pages:
                try:
                    page_text = page.extract_text()
                    text += page_text + "\n"
                except:
                    continue
        
        return self.clean_tcm_text(text)
    
    def clean_tcm_text(self, text: str) -> str:
        """清理中医文本"""
        if not text:
            return ""
        
        # 移除多余空白
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if len(line) > 2:  # 过滤太短的行
                # 中医文本特殊字符处理
                line = line.replace('　', ' ')  # 全角空格
                line = line.replace('，', '，')  # 统一逗号
                line = line.replace('。', '。')  # 统一句号
                cleaned_lines.append(line)
        
        text = '\n'.join(cleaned_lines)
        
        # 移除重复的空行
        while '\n\n\n' in text:
            text = text.replace('\n\n\n', '\n\n')
        
        return text
    
    def split_tcm_text_smart(self, text: str) -> List[str]:
        """智能分割中医文本"""
        print("🔪 智能分割中医文本...")
        start_time = time.time()
        
        if len(text) < 50:
            print(f"❌ 文本太短({len(text)}字符)，无法分割")
            return []
        
        chunks = []
        chunk_size = self.chunk_size
        overlap = chunk_size // 10
        
        # 中医文本分割点（按重要性排序）
        tcm_split_patterns = [
            '。\n',     # 句号+换行 - 最重要
            '。',       # 句号
            '；\n',     # 分号+换行
            '；',       # 分号
            '\n\n',     # 段落
            '，',       # 逗号
            '：',       # 冒号
            '、',       # 顿号
        ]
        
        start = 0
        text_length = len(text)
        
        while start < text_length:
            end = start + chunk_size
            if end >= text_length:
                end = text_length
                chunk = text[start:end].strip()
                if len(chunk) > 20:  # 至少20字符
                    chunks.append(chunk)
                break
            
            # 寻找最佳分割点
            best_split = end
            for pattern in tcm_split_patterns:
                split_pos = text.rfind(pattern, start + chunk_size//2, end)
                if split_pos != -1:
                    best_split = split_pos + len(pattern)
                    break
            
            chunk = text[start:best_split].strip()
            if len(chunk) > 20:
                chunks.append(chunk)
            
            start = best_split - overlap
            if start < 0:
                start = best_split
        
        split_time = time.time() - start_time
        print(f"✅ 中医文本分割完成，耗时: {split_time:.2f}秒")
        print(f"📊 总块数: {len(chunks)}")
        
        # 显示前几个块的样本
        if chunks:
            print("📝 文本块样本:")
            for i, chunk in enumerate(chunks[:3]):
                print(f"   块{i+1}: {chunk[:50]}...")
        
        return chunks
    
    def create_tcm_embeddings_robust(self, texts: List[str]) -> np.ndarray:
        """创建中医文本嵌入 - 强化版"""
        print("🔄 生成中医文本向量...")
        start_time = time.time()
        
        from models.model_manager import model_manager
        
        embeddings = []
        batch_size = self.batch_size
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            print(f"   处理批次 {batch_num}/{total_batches} ({len(batch_texts)} 个中医文本)")
            
            try:
                batch_embeddings = []
                for j, text in enumerate(batch_texts):
                    try:
                        embedding = model_manager.get_embedding(text)
                        batch_embeddings.append(embedding)
                    except Exception as e:
                        print(f"     文本 {j+1} 嵌入失败: {e}")
                        # 使用零向量作为备用
                        batch_embeddings.append(np.zeros(768))
                
                embeddings.extend(batch_embeddings)
                
                # 每5个批次清理内存
                if batch_num % 5 == 0:
                    gc.collect()
                    
            except Exception as e:
                print(f"   批次 {batch_num} 处理失败: {e}")
                # 整个批次失败，使用零向量
                for _ in batch_texts:
                    embeddings.append(np.zeros(768))
        
        embeddings_array = np.array(embeddings).astype('float32')
        
        embedding_time = time.time() - start_time
        print(f"✅ 中医向量化完成，耗时: {embedding_time:.2f}秒")
        print(f"📊 向量维度: {embeddings_array.shape}")
        
        return embeddings_array
    
    def process_tcm_document_robust(self, pdf_path: str) -> bool:
        """强化处理中医文档"""
        print(f"🏥 开始强化处理中医文档: {pdf_path}")
        total_start_time = time.time()
        
        try:
            # 1. 多方法提取文本
            print("📄 第1步: 多方法提取中医文献...")
            text = self.extract_pdf_multiple_methods(pdf_path)
            if not text or len(text) < 100:
                print(f"❌ 文本提取失败或内容太少({len(text)}字符)")
                return False
            
            # 2. 智能分割
            print("🔪 第2步: 智能分割中医文本...")
            chunks = self.split_tcm_text_smart(text)
            if not chunks:
                print("❌ 文本分割失败")
                return False
            
            # 3. 创建元数据
            print("📋 第3步: 创建中医文档元数据...")
            metadata = []
            for i, chunk in enumerate(chunks):
                metadata.append({
                    "source": pdf_path,
                    "chunk_id": i,
                    "chunk_index": i,
                    "content": chunk,
                    "document_type": "中医经典",
                    "processed_time": time.time(),
                    "text_length": len(chunk)
                })
            
            # 4. 生成嵌入
            print("🔄 第4步: 生成中医文本向量...")
            embeddings = self.create_tcm_embeddings_robust(chunks)
            
            # 5. 创建索引
            print("🏗️ 第5步: 创建中医知识索引...")
            if not self.create_tcm_index_robust(embeddings):
                return False
            
            # 6. 保存数据
            print("💾 第6步: 保存中医知识库...")
            self.document_chunks = chunks
            self.chunk_metadata = metadata
            self.save_tcm_data_robust()
            
            total_time = time.time() - total_start_time
            
            print(f"🎉 中医文档强化处理完成！")
            print(f"⏱️ 总耗时: {total_time:.2f}秒")
            print(f"📊 处理了 {len(chunks)} 个中医文本块")
            print(f"📝 平均块长度: {sum(len(c) for c in chunks) / len(chunks):.0f} 字符")
            print(f"⚡ 处理速度: {len(chunks)/total_time:.1f} 块/秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 中医文档强化处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_tcm_index_robust(self, embeddings: np.ndarray) -> bool:
        """创建强化中医索引"""
        print("🏗️ 创建强化中医知识索引...")
        start_time = time.time()
        
        try:
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)
            
            # 归一化向量
            faiss.normalize_L2(embeddings)
            
            # 添加向量
            self.vector_index.add(embeddings)
            
            index_time = time.time() - start_time
            print(f"✅ 强化中医索引完成，耗时: {index_time:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 强化索引创建失败: {e}")
            return False
    
    def save_tcm_data_robust(self):
        """保存强化中医数据"""
        try:
            index_path = Path(config.VECTOR_DB_PATH)
            index_path.mkdir(parents=True, exist_ok=True)
            
            # 保存FAISS索引
            faiss.write_index(self.vector_index, str(index_path / "vector_index.faiss"))
            
            # 保存文档块
            with open(index_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # 保存元数据
            with open(index_path / "metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.chunk_metadata, f, ensure_ascii=False, indent=2)
            
            print("✅ 强化中医知识库保存完成")
            
        except Exception as e:
            print(f"❌ 强化数据保存失败: {e}")

# 全局强化中医RAG处理器
robust_tcm_processor = RobustTCMProcessor()

def process_tcm_pdf_robust(pdf_path: str) -> bool:
    """强化处理中医PDF"""
    return robust_tcm_processor.process_tcm_document_robust(pdf_path)

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        pdf_file = sys.argv[1]
        success = process_tcm_pdf_robust(pdf_file)
        if success:
            print("🎉 中医文档强化处理成功！")
        else:
            print("❌ 中医文档强化处理失败")
    else:
        print("用法: python robust_tcm_processor.py <中医PDF文件路径>")
