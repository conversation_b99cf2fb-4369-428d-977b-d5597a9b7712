#!/usr/bin/env python3
"""
启动局域网代理服务器
让家人朋友可以通过局域网访问中医RAG系统
"""
import http.server
import socketserver
import socket
import urllib.request
import base64
import sys
import time

class TCMProxyHandler(http.server.BaseHTTPRequestHandler):
    """中医RAG系统代理处理器"""
    
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def do_OPTIONS(self):
        self.proxy_request()
    
    def proxy_request(self):
        """代理请求到本地服务器"""
        try:
            # 构建目标URL
            target_url = f"http://localhost:8008{self.path}"
            
            # 创建请求
            req = urllib.request.Request(target_url, method=self.command)
            
            # 添加认证头部
            credentials = base64.b64encode(b'tcm_user:MVP168918').decode('ascii')
            req.add_header('Authorization', f'Basic {credentials}')
            
            # 复制其他头部
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection', 'authorization']:
                    req.add_header(header, value)
            
            # 处理POST/PUT数据
            if self.command in ['POST', 'PUT', 'PATCH']:
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    req.data = post_data
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                # 发送响应状态
                self.send_response(response.getcode())
                
                # 复制响应头部
                for header, value in response.headers.items():
                    if header.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header, value)
                
                # 添加CORS头部
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                
                self.end_headers()
                
                # 复制响应内容
                content = response.read()
                self.wfile.write(content)
        
        except urllib.error.HTTPError as e:
            # HTTP错误
            self.send_response(e.code)
            self.end_headers()
            self.wfile.write(f"HTTP Error: {e.code} {e.reason}".encode())
        
        except Exception as e:
            # 其他错误
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(f"代理错误: {str(e)}".encode('utf-8'))
    
    def log_message(self, format, *args):
        """简化日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        # 备用方法
        return socket.gethostbyname(socket.gethostname())

def check_port_available(port):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind(('', port))
            return True
        except:
            return False

def main():
    """主函数"""
    print("🚀 中医RAG系统 - 局域网代理服务器")
    print("=" * 50)
    
    # 检查本地服务器是否运行
    try:
        import requests
        response = requests.get("http://localhost:8008/api/health", 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 本地服务器运行正常")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
        else:
            print("❌ 本地服务器响应异常")
            return
    except Exception as e:
        print("❌ 本地服务器未运行，请先启动服务器")
        print("💡 运行命令: python simple_ultimate_tcm.py")
        print(f"错误详情: {e}")
        return
    
    # 找一个可用端口
    proxy_port = 8080
    while not check_port_available(proxy_port) and proxy_port < 8090:
        proxy_port += 1
    
    if not check_port_available(proxy_port):
        print("❌ 无法找到可用端口 (8080-8089)")
        return
    
    # 获取本机IP
    local_ip = get_local_ip()
    
    print(f"\n🌐 代理服务器配置:")
    print(f"   本地端口: {proxy_port}")
    print(f"   本机IP: {local_ip}")
    print(f"   目标服务: localhost:8008")
    
    try:
        # 启动代理服务器
        with socketserver.TCPServer(("", proxy_port), TCMProxyHandler) as httpd:
            print(f"\n🎉 代理服务器启动成功！")
            print("=" * 50)
            print(f"🏥 中医RAG智能诊疗系统")
            print(f"🌐 局域网访问地址: http://{local_ip}:{proxy_port}")
            print(f"🔐 系统已内置认证，无需额外登录")
            print("=" * 50)
            print()
            print("📱 分享给家人朋友:")
            print(f"   访问地址: http://{local_ip}:{proxy_port}")
            print("   (确保设备在同一WiFi网络)")
            print()
            print("💡 功能特点:")
            print("   • DeepSeek-R1智能诊疗分析")
            print("   • 在线医学资源检索")
            print("   • PDF文档上传分析")
            print("   • 语音交互支持")
            print("   • 会话历史管理")
            print()
            print("⚠️ 注意事项:")
            print("   • 仅限局域网访问")
            print("   • 请确保网络安全")
            print("   • 建议仅供家人朋友使用")
            print()
            print("⏳ 代理服务器运行中... (按Ctrl+C停止)")
            print("-" * 50)
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print("\n🛑 代理服务器已停止")
                print("👋 感谢使用中医RAG智能诊疗系统！")
    
    except Exception as e:
        print(f"❌ 代理服务器启动失败: {e}")

if __name__ == "__main__":
    main()
