#!/usr/bin/env python3
"""
下载m3e-base模型到本地
确保向量数据库可以离线使用
"""

import os
import sys
import requests
from pathlib import Path
import zipfile
import shutil
from huggingface_hub import snapshot_download
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_m3e_model():
    """下载m3e-base模型到本地"""
    
    model_name = "moka-ai/m3e-base"
    local_model_path = "./models/m3e-base"
    
    print("🤖 开始下载m3e-base中文嵌入模型...")
    print("=" * 50)
    
    try:
        # 创建模型目录
        os.makedirs(local_model_path, exist_ok=True)
        
        print(f"📁 模型将保存到: {os.path.abspath(local_model_path)}")
        
        # 使用huggingface_hub下载模型
        print("🔄 正在从HuggingFace下载模型文件...")
        
        snapshot_download(
            repo_id=model_name,
            local_dir=local_model_path,
            local_dir_use_symlinks=False,
            resume_download=True
        )
        
        print("✅ m3e-base模型下载完成！")
        
        # 验证模型文件
        required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
        missing_files = []
        
        for file_name in required_files:
            file_path = Path(local_model_path) / file_name
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"  ✅ {file_name} ({size_mb:.2f} MB)")
            else:
                missing_files.append(file_name)
                print(f"  ❌ {file_name} (缺失)")
        
        if missing_files:
            print(f"⚠️ 缺失文件: {', '.join(missing_files)}")
            return False
        
        print("\n🎉 模型下载和验证完成！")
        print(f"📊 模型路径: {os.path.abspath(local_model_path)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        print("\n💡 备选方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 手动下载模型文件")
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        print("\n🧪 测试模型加载...")
        
        from sentence_transformers import SentenceTransformer
        
        local_model_path = "./models/m3e-base"
        
        # 加载本地模型
        model = SentenceTransformer(local_model_path)
        
        # 测试编码
        test_texts = ["中医治疗", "湿气重", "气血不足"]
        embeddings = model.encode(test_texts)
        
        print(f"✅ 模型加载成功！")
        print(f"📊 嵌入维度: {embeddings.shape[1]}")
        print(f"🧪 测试文本数量: {len(test_texts)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🏥 TCM RAG系统 - m3e-base模型下载器")
    print("=" * 50)
    
    # 检查依赖
    try:
        import huggingface_hub
        import sentence_transformers
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: pip install huggingface_hub sentence-transformers")
        return False
    
    # 下载模型
    if download_m3e_model():
        # 测试模型
        if test_model_loading():
            print("\n🎉 m3e-base模型准备完成！")
            print("✅ 现在可以启动TCM RAG系统了")
            return True
        else:
            print("\n⚠️ 模型下载成功但加载测试失败")
            return False
    else:
        print("\n❌ 模型下载失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
