#!/usr/bin/env python3
"""
测试优化后的智能回答功能
"""
import requests
import json
import time

def test_intelligent_chat(query: str, description: str = ""):
    """测试智能聊天功能"""
    print(f"\n🧪 测试: {description or query}")
    print("-" * 50)
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8006/api/chat",
            json={"message": query},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ 响应成功 (耗时: {processing_time:.2f}s)")
            print(f"📝 查询: {query}")
            print(f"🤖 回答长度: {len(data.get('response', ''))}")
            print(f"📚 来源数量: {len(data.get('sources', []))}")
            
            # 分析来源类型
            sources = data.get('sources', [])
            online_count = len([s for s in sources if s.get('type') == 'online'])
            local_count = len([s for s in sources if s.get('type') == 'pdf'])
            
            print(f"🌐 在线资源: {online_count}条")
            print(f"📁 本地资源: {local_count}条")
            
            # 显示回答内容（前200字符）
            response_text = data.get('response', '')
            preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
            print(f"💬 回答预览:\n{preview}")
            
            # 检查回答质量
            quality_score = 0
            if "🔍" in response_text:
                quality_score += 1
            if "权威" in response_text or "资料" in response_text:
                quality_score += 1
            if "中医" in response_text:
                quality_score += 1
            if len(response_text) > 100:
                quality_score += 1
            if online_count > 0:
                quality_score += 2  # 在线资源加分
            
            print(f"⭐ 质量评分: {quality_score}/6")
            
            return {
                'success': True,
                'response_length': len(response_text),
                'sources_count': len(sources),
                'online_sources': online_count,
                'local_sources': local_count,
                'processing_time': processing_time,
                'quality_score': quality_score
            }
            
        else:
            print(f"❌ 请求失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return {'success': False, 'error': f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🚀 智能回答功能测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        ("睡眠怎么办", "睡眠问题咨询"),
        ("失眠多梦的中医治疗", "失眠治疗方案"),
        ("栀子甘草豉汤的功效", "经典方剂查询"),
        ("脾胃虚弱怎么调理", "脾胃调理方法"),
        ("肾阳虚的症状", "肾阳虚症状"),
        ("头痛的中医治疗", "头痛治疗"),
        ("感冒发烧怎么办", "感冒发烧处理"),
        ("胃痛的原因", "胃痛病因分析")
    ]
    
    results = []
    
    for query, description in test_cases:
        result = test_intelligent_chat(query, description)
        results.append((query, result))
        time.sleep(2)  # 避免请求过快
    
    # 统计结果
    print("\n📊 测试结果统计")
    print("=" * 60)
    
    success_count = 0
    total_response_length = 0
    total_sources = 0
    total_online_sources = 0
    total_processing_time = 0
    total_quality_score = 0
    
    for query, result in results:
        if result.get('success'):
            success_count += 1
            total_response_length += result.get('response_length', 0)
            total_sources += result.get('sources_count', 0)
            total_online_sources += result.get('online_sources', 0)
            total_processing_time += result.get('processing_time', 0)
            total_quality_score += result.get('quality_score', 0)
    
    total_tests = len(test_cases)
    
    print(f"✅ 成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    
    if success_count > 0:
        print(f"📝 平均回答长度: {total_response_length/success_count:.0f} 字符")
        print(f"📚 平均来源数量: {total_sources/success_count:.1f} 条")
        print(f"🌐 平均在线资源: {total_online_sources/success_count:.1f} 条")
        print(f"⏱️ 平均处理时间: {total_processing_time/success_count:.2f} 秒")
        print(f"⭐ 平均质量评分: {total_quality_score/success_count:.1f}/6")
    
    # 详细结果
    print("\n📋 详细结果")
    print("-" * 60)
    
    for query, result in results:
        status = "✅" if result.get('success') else "❌"
        quality = result.get('quality_score', 0)
        online = result.get('online_sources', 0)
        
        print(f"{status} {query}")
        if result.get('success'):
            print(f"   质量: {quality}/6, 在线资源: {online}条")
        else:
            print(f"   错误: {result.get('error', '未知错误')}")
    
    # 评估结果
    print("\n🎯 功能评估")
    print("-" * 60)
    
    if success_count >= 7:  # 至少7/8成功
        print("🎉 智能回答功能运行良好！")
        
        if total_online_sources >= success_count * 0.8:  # 80%的查询有在线资源
            print("🌐 在线检索功能正常，能够有效获取权威医学资源")
        else:
            print("⚠️ 在线检索功能需要优化，部分查询未获取到在线资源")
        
        if total_quality_score / success_count >= 4:  # 平均质量4分以上
            print("⭐ 回答质量良好，内容专业且结构清晰")
        else:
            print("💡 回答质量有待提升，建议优化内容生成逻辑")
        
        if total_processing_time / success_count <= 3:  # 平均3秒以内
            print("⚡ 响应速度良好，适合实时对话")
        else:
            print("🐌 响应速度较慢，建议优化处理流程")
    
    elif success_count >= 5:  # 5-6个成功
        print("⚠️ 智能回答功能基本可用，但需要进一步优化")
    else:
        print("❌ 智能回答功能存在问题，需要检查系统配置")
    
    return success_count >= 7

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
