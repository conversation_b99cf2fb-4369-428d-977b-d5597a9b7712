#!/usr/bin/env python3
"""
快速测试系统修复
"""

import requests
import time

def test_intelligent_mcp():
    """测试智能MCP服务"""
    print("🧪 测试智能MCP服务")
    print("=" * 40)
    
    # 检查服务状态
    try:
        response = requests.get('http://localhost:8006/health', timeout=5)
        if response.status_code == 200:
            print("✅ 智能MCP服务运行正常")
        else:
            print(f"❌ 服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        return False
    
    # 测试不同问题
    test_cases = [
        "失眠多梦怎么办",
        "肚子疼湿气重怎么治疗",
        "头痛头晕怎么缓解",
        "咳嗽有痰怎么治疗"
    ]
    
    for i, query in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {query}")
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": "medical",
                    "max_results": 1
                },
                "id": f"test_{i}"
            }
            
            response = requests.post(
                'http://localhost:8006/mcp',
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and result['result'].get('results'):
                    first_result = result['result']['results'][0]
                    title = first_result.get('title', '')
                    content = first_result.get('content', '')
                    score = first_result.get('score', 0)
                    
                    print(f"   ✅ 返回结果: {title}")
                    print(f"   📊 评分: {score:.3f}")
                    print(f"   📝 内容: {content[:100]}...")
                    
                    # 检查针对性
                    query_keywords = query.replace('怎么办', '').replace('怎么治疗', '').replace('怎么缓解', '')
                    if any(keyword in title + content for keyword in query_keywords.split()):
                        print(f"   🎯 针对性: 良好")
                    else:
                        print(f"   ⚠️ 针对性: 需改进")
                else:
                    print("   ❌ 无返回结果")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
    
    return True

def test_client_import():
    """测试客户端导入"""
    print("\n🔧 测试客户端导入")
    print("=" * 40)
    
    try:
        from intelligent_mcp_client import IntelligentMCPRetriever
        print("✅ 智能MCP客户端导入成功")
        
        # 测试初始化
        retriever = IntelligentMCPRetriever()
        if retriever.initialize():
            print("✅ 智能MCP检索器初始化成功")
            
            # 测试搜索
            results = retriever.search_by_domain("失眠多梦", "medical", 1)
            if results:
                print(f"✅ 搜索测试成功，返回 {len(results)} 个结果")
                print(f"   第一个结果: {results[0].get('title', 'N/A')}")
                return True
            else:
                print("⚠️ 搜索无结果")
                return False
        else:
            print("❌ 智能MCP检索器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 导入或测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 快速测试系统修复")
    print("🎯 目标：验证智能MCP服务和客户端工作正常")
    print()
    
    # 测试1：MCP服务
    service_ok = test_intelligent_mcp()
    
    # 测试2：客户端导入
    client_ok = test_client_import()
    
    print("\n" + "=" * 50)
    print("📊 测试总结")
    
    if service_ok and client_ok:
        print("🎉 所有测试通过！系统修复成功")
        print("💡 现在可以启动主系统测试")
        print("运行: python 启动TCM系统.py")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        if not service_ok:
            print("   - MCP服务问题")
        if not client_ok:
            print("   - 客户端问题")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ 成功' if success else '❌ 失败'}")
    input("按回车键退出...")
