#!/usr/bin/env python3
"""
全面系统诊断 - 检查所有功能是否正常工作
"""

import os
import sys
import requests
import time
from pathlib import Path
import subprocess

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = {
        'faiss-cpu': 'faiss',
        'sentence-transformers': 'sentence_transformers', 
        'streamlit': 'streamlit',
        'PyPDF2': 'PyPDF2',
        'requests': 'requests',
        'numpy': 'numpy',
        'pandas': 'pandas'
    }
    
    missing = []
    for package, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 缺失")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ 缺失依赖: {', '.join(missing)}")
        print(f"安装命令: pip install {' '.join(missing)}")
        return False
    
    print("✅ 所有依赖包正常")
    return True

def check_model_files():
    """检查模型文件"""
    print("\n🔍 检查模型文件...")
    
    model_path = Path('./models/m3e-base')
    
    if not model_path.exists():
        print(f"   ❌ 模型目录不存在: {model_path}")
        print("   💡 需要下载m3e-base模型")
        return False
    
    # 检查模型文件
    required_files = ['config.json', 'pytorch_model.bin', 'tokenizer.json']
    missing_files = []
    
    for file in required_files:
        if not (model_path / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"   ❌ 缺失模型文件: {missing_files}")
        return False
    
    print("   ✅ m3e-base模型文件完整")
    return True

def check_vector_database():
    """检查向量数据库"""
    print("\n🔍 检查向量数据库...")
    
    db_path = Path('./ultimate_final_vector_db')
    
    if not db_path.exists():
        print(f"   ⚠️ 向量数据库目录不存在: {db_path}")
        print("   💡 需要上传文档来创建向量数据库")
        return False
    
    index_file = db_path / 'index.faiss'
    metadata_file = db_path / 'metadata.pkl'
    
    if not index_file.exists():
        print(f"   ❌ 向量索引文件不存在: {index_file}")
        return False
    
    if not metadata_file.exists():
        print(f"   ❌ 元数据文件不存在: {metadata_file}")
        return False
    
    # 检查文件大小
    index_size = index_file.stat().st_size / 1024 / 1024  # MB
    print(f"   ✅ 向量索引文件: {index_size:.1f} MB")
    
    try:
        import pickle
        with open(metadata_file, 'rb') as f:
            metadata = pickle.load(f)
        print(f"   ✅ 元数据记录: {len(metadata)} 条")
        return True
    except Exception as e:
        print(f"   ❌ 元数据文件损坏: {e}")
        return False

def check_mcp_service():
    """检查MCP服务"""
    print("\n🔍 检查MCP服务...")
    
    # 检查MCP服务文件
    mcp_file = Path('./fastmcp_elasticsearch_service.py')
    if not mcp_file.exists():
        print(f"   ❌ MCP服务文件不存在: {mcp_file}")
        return False
    
    # 检查MCP客户端文件
    client_file = Path('./mcp_client.py')
    if not client_file.exists():
        print(f"   ❌ MCP客户端文件不存在: {client_file}")
        return False
    
    # 尝试连接MCP服务
    try:
        response = requests.get('http://localhost:8003/health', timeout=5)
        if response.status_code == 200:
            print("   ✅ MCP服务运行中")
            service_info = response.json()
            print(f"   📊 服务状态: {service_info.get('status', 'unknown')}")
            return True
        else:
            print(f"   ❌ MCP服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ MCP服务未启动")
        print("   💡 需要启动MCP服务: python fastmcp_elasticsearch_service.py")
        return False
    except Exception as e:
        print(f"   ❌ MCP服务检查失败: {e}")
        return False

def test_vector_retrieval():
    """测试向量检索功能"""
    print("\n🧪 测试向量检索...")
    
    try:
        from ultimate_final_tcm_system import UltimateVectorDatabase, CONFIG
        
        # 创建向量数据库实例
        vector_db = UltimateVectorDatabase()
        
        # 初始化
        if not vector_db.initialize():
            print("   ❌ 向量数据库初始化失败")
            return False
        
        print("   ✅ 向量数据库初始化成功")
        
        # 测试搜索
        test_query = "肾虚脾虚怎么治疗"
        results = vector_db.search(test_query, top_k=5)
        
        print(f"   📊 搜索结果: {len(results)} 条")
        print(f"   🎯 查询: {test_query}")
        print(f"   ⚙️ 阈值: {CONFIG['MIN_RELEVANCE_SCORE']}")
        
        if results:
            best_result = results[0]
            print(f"   🏆 最佳匹配: {best_result.get('similarity', 0):.3f}")
            print(f"   📄 来源: {best_result.get('source', 'unknown')}")
            return True
        else:
            print("   ⚠️ 未找到匹配结果，可能阈值过高或数据不匹配")
            return False
            
    except Exception as e:
        print(f"   ❌ 向量检索测试失败: {e}")
        return False

def test_mcp_retrieval():
    """测试MCP检索功能"""
    print("\n🧪 测试MCP检索...")
    
    try:
        # 先检查服务是否运行
        response = requests.get('http://localhost:8003/health', timeout=5)
        if response.status_code != 200:
            print("   ❌ MCP服务未运行")
            return False
        
        # 测试搜索
        mcp_request = {
            "method": "search_knowledge",
            "params": {
                "query": "肾虚脾虚怎么治疗",
                "domain": "medical",
                "max_results": 5,
                "search_type": "comprehensive"
            },
            "id": "test_001"
        }
        
        response = requests.post(
            'http://localhost:8003/mcp',
            json=mcp_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result:
                results = result['result'].get('results', [])
                print(f"   ✅ MCP检索成功: {len(results)} 条结果")
                
                if results:
                    best_result = results[0]
                    print(f"   🏆 最佳匹配: {best_result.get('title', 'unknown')}")
                    print(f"   📊 评分: {best_result.get('score', 0):.3f}")
                    return True
                else:
                    print("   ⚠️ MCP返回空结果")
                    return False
            else:
                print(f"   ❌ MCP返回错误: {result.get('error', 'unknown')}")
                return False
        else:
            print(f"   ❌ MCP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ MCP检索测试失败: {e}")
        return False

def start_mcp_service():
    """启动MCP服务"""
    print("\n🚀 尝试启动MCP服务...")
    
    try:
        # 检查端口是否被占用
        try:
            response = requests.get('http://localhost:8003/health', timeout=2)
            print("   ℹ️ MCP服务已在运行")
            return True
        except:
            pass
        
        # 启动服务
        process = subprocess.Popen(
            [sys.executable, 'fastmcp_elasticsearch_service.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务启动
        print("   ⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查服务是否启动成功
        try:
            response = requests.get('http://localhost:8003/health', timeout=5)
            if response.status_code == 200:
                print("   ✅ MCP服务启动成功")
                return True
            else:
                print("   ❌ MCP服务启动失败")
                return False
        except:
            print("   ❌ MCP服务无法连接")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动MCP服务失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🔧 开始全面系统诊断")
    print("=" * 60)
    
    # 诊断项目
    checks = [
        ("依赖包检查", check_dependencies),
        ("模型文件检查", check_model_files),
        ("向量数据库检查", check_vector_database),
        ("MCP服务检查", check_mcp_service),
    ]
    
    # 执行基础检查
    basic_passed = 0
    for name, check_func in checks:
        if check_func():
            basic_passed += 1
    
    print(f"\n📊 基础检查结果: {basic_passed}/{len(checks)} 通过")
    
    # 如果MCP服务未运行，尝试启动
    if basic_passed < len(checks):
        if not check_mcp_service():
            start_mcp_service()
    
    # 功能测试
    print("\n" + "=" * 60)
    print("🧪 功能测试")
    
    tests = [
        ("向量检索测试", test_vector_retrieval),
        ("MCP检索测试", test_mcp_retrieval),
    ]
    
    test_passed = 0
    for name, test_func in tests:
        if test_func():
            test_passed += 1
    
    print(f"\n📊 功能测试结果: {test_passed}/{len(tests)} 通过")
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断总结")
    
    total_checks = len(checks) + len(tests)
    total_passed = basic_passed + test_passed
    
    if total_passed == total_checks:
        print("🎉 所有检查通过！系统运行正常！")
        print("\n💡 建议:")
        print("   1. 运行 streamlit run ultimate_final_tcm_system.py")
        print("   2. 上传一些中医相关的PDF文档")
        print("   3. 测试查询 '肾虚脾虚怎么治疗'")
    else:
        print(f"⚠️ {total_checks - total_passed} 项检查失败")
        print("\n🔧 修复建议:")
        
        if not check_dependencies():
            print("   1. 安装缺失的依赖包")
        if not check_model_files():
            print("   2. 下载m3e-base模型到 ./models/m3e-base/")
        if not check_vector_database():
            print("   3. 上传PDF文档创建向量数据库")
        if not check_mcp_service():
            print("   4. 启动MCP服务: python fastmcp_elasticsearch_service.py")
    
    return total_passed == total_checks

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
