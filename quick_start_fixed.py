#!/usr/bin/env python3
"""
快速启动修复版RAG系统
使用我们之前修复的向量数据库
"""
import streamlit as st
import pickle
import faiss
import numpy as np
from pathlib import Path
from sklearn.feature_extraction.text import TfidfVectorizer
from datetime import datetime
import json

# 页面配置
st.set_page_config(
    page_title="家庭中医智能助手 - 修复版",
    page_icon="🏥",
    layout="wide"
)

@st.cache_resource
def load_fixed_rag_system():
    """加载修复后的RAG系统 - 优化版本"""
    try:
        vector_db_dir = Path('vector_db')

        # 快速检查文件是否存在
        required_files = ['chunks.pkl', 'metadata.pkl', 'index.faiss']
        for file_name in required_files:
            if not (vector_db_dir / file_name).exists():
                return {'status': 'error', 'error': f'缺少文件: {file_name}'}

        # 加载数据（使用更快的方式）
        with open(vector_db_dir / 'chunks.pkl', 'rb') as f:
            chunks = pickle.load(f)

        with open(vector_db_dir / 'metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)

        # 延迟加载FAISS索引（只在需要时加载）
        index_path = str(vector_db_dir / 'index.faiss')

        # 快速创建TF-IDF模型（减少特征数量以提高速度）
        vectorizer = TfidfVectorizer(max_features=256, stop_words=None, max_df=0.95, min_df=1)
        vectorizer.fit(chunks)

        return {
            'chunks': chunks,
            'metadata': metadata,
            'index_path': index_path,  # 延迟加载
            'index': None,  # 延迟加载
            'vectorizer': vectorizer,
            'status': 'success'
        }

    except Exception as e:
        return {'status': 'error', 'error': str(e)}

def search_documents(query, rag_system, top_k=3):
    """搜索文档 - 优化版本"""
    try:
        chunks = rag_system['chunks']
        metadata = rag_system['metadata']
        vectorizer = rag_system['vectorizer']

        # 延迟加载FAISS索引
        if rag_system['index'] is None:
            rag_system['index'] = faiss.read_index(rag_system['index_path'])

        index = rag_system['index']

        # 获取查询嵌入
        query_embedding = vectorizer.transform([query]).toarray()
        query_embedding = np.array(query_embedding, dtype=np.float32)

        # 归一化查询向量
        faiss.normalize_L2(query_embedding)

        # 搜索
        distances, indices = index.search(query_embedding, top_k)

        results = []
        for distance, idx in zip(distances[0], indices[0]):
            if idx < len(chunks) and idx >= 0:
                similarity = 1.0 / (1.0 + distance)
                results.append({
                    'content': chunks[idx],
                    'metadata': metadata[idx],
                    'score': float(similarity),
                    'distance': float(distance)
                })

        return results
    except Exception as e:
        st.error(f"搜索失败: {e}")
        return []

def generate_answer(query, search_results):
    """生成答案"""
    if not search_results:
        return "抱歉，我没有找到相关信息。请尝试其他问题或检查输入。"
    
    # 收集相关内容
    relevant_content = []
    for result in search_results[:3]:
        content = result['content']
        source = Path(result['metadata']['source']).name
        score = result['score']
        relevant_content.append(f"**来源《{source}》** (相似度: {score:.3f}):\n{content}\n")
    
    # 生成答案
    answer = f"""根据中医典籍，关于"{query}"的相关信息如下：

{chr(10).join(relevant_content)}

**注意：** 以上信息来自中医经典文献，仅供学习参考。如需医疗建议，请咨询专业中医师。"""
    
    return answer

def main():
    """主界面"""
    # 标题
    st.markdown("""
    <div style="text-align: center; padding: 1rem; background: linear-gradient(90deg, #4CAF50, #45a049); color: white; border-radius: 10px; margin-bottom: 2rem;">
        <h1>🏥 家庭中医智能助手 - 修复版</h1>
        <p>基于修复后的向量数据库，支持栀子甘草豉汤等方剂查询</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 加载RAG系统
    rag_system = load_fixed_rag_system()
    
    if rag_system['status'] == 'error':
        st.error(f"❌ 系统加载失败: {rag_system['error']}")
        st.info("请确保已运行 emergency_fix.py 生成向量数据库")
        return
    
    # 显示系统状态
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("知识块数量", len(rag_system['chunks']))
    with col2:
        st.metric("向量维度", rag_system['index'].d)
    with col3:
        st.metric("系统状态", "✅ 正常")
    
    st.divider()
    
    # 常见问题快捷按钮
    st.subheader("🔥 常见问题快速查询")
    col1, col2, col3, col4 = st.columns(4)
    
    common_questions = [
        "栀子甘草豉汤方",
        "防己黄芪汤",
        "甘草汤的作用",
        "栀子的功效"
    ]
    
    selected_question = None
    for i, question in enumerate(common_questions):
        with [col1, col2, col3, col4][i]:
            if st.button(question, key=f"common_{i}"):
                selected_question = question
    
    # 问题输入
    st.subheader("❓ 请输入您的中医问题")
    user_question = st.text_area(
        "您可以询问任何中医相关问题：",
        value=selected_question if selected_question else "",
        height=100,
        placeholder="例如：栀子甘草豉汤方的组成和功效是什么？"
    )
    
    # 查询按钮
    col1, col2 = st.columns([1, 4])
    with col1:
        ask_button = st.button("🔍 立即查询", type="primary")
    with col2:
        if st.button("🗑️ 清空"):
            st.rerun()
    
    # 处理查询
    if ask_button and user_question.strip():
        with st.spinner("🤔 正在搜索相关信息..."):
            # 搜索相关文档
            search_results = search_documents(user_question, rag_system, top_k=5)
            
            if search_results:
                # 生成答案
                answer = generate_answer(user_question, search_results)
                
                # 显示答案
                st.markdown("""
                <div style="background: #fff; padding: 1.5rem; border-radius: 10px; border: 1px solid #ddd; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin: 1rem 0;">
                """, unsafe_allow_html=True)
                
                st.markdown("### 💡 智能回答")
                st.markdown(answer)
                
                st.markdown("</div>", unsafe_allow_html=True)
                
                # 显示详细搜索结果
                with st.expander("📖 详细搜索结果"):
                    for i, result in enumerate(search_results, 1):
                        source = Path(result['metadata']['source']).name
                        st.write(f"**结果 {i}** - 来源: {source}")
                        st.write(f"**相似度:** {result['score']:.3f}")
                        st.write(f"**内容:** {result['content'][:300]}...")
                        st.divider()
                
                # 记录查询日志
                log_query(user_question, len(search_results))
                
            else:
                st.warning("❌ 未找到相关信息，请尝试其他问题")
    
    elif ask_button and not user_question.strip():
        st.warning("请先输入您的问题")
    
    # 免责声明
    st.markdown("""
    ---
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 1rem; margin: 1rem 0;">
        <strong>⚠️ 重要提醒：</strong><br>
        本系统提供的信息仅供中医学习参考，不能替代专业医生的诊断和治疗建议。
        如有严重症状或疑虑，请及时就医咨询专业医生。
    </div>
    """, unsafe_allow_html=True)

def log_query(question, results_count):
    """记录查询日志"""
    try:
        log_dir = Path("user_logs")
        log_dir.mkdir(exist_ok=True)
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "results_count": results_count,
            "type": "query"
        }
        
        log_file = log_dir / f"queries_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    except:
        pass  # 日志记录失败不影响主功能

if __name__ == "__main__":
    main()
