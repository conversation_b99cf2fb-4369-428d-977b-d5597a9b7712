#!/usr/bin/env python3
"""
简化的LM Studio自动启动器
专为RAG系统集成设计
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path
import psutil

def find_lmstudio():
    """查找LM Studio可执行文件"""
    possible_paths = [
        Path.home() / "AppData/Local/LM Studio/LM Studio.exe",
        Path.home() / "AppData/Local/Programs/LM Studio/LM Studio.exe",
        Path("C:/Program Files/LM Studio/LM Studio.exe"),
        Path("C:/Program Files (x86)/LM Studio/LM Studio.exe")
    ]
    
    for path in possible_paths:
        if path.exists():
            return str(path)
    return None

def is_lmstudio_running():
    """检查LM Studio是否运行"""
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'LM Studio' in proc.info['name']:
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return False

def is_api_ready():
    """检查API是否就绪"""
    try:
        response = requests.get("http://localhost:1234/v1/models", timeout=2)
        if response.status_code == 200:
            models_data = response.json()
            models = [model['id'] for model in models_data.get('data', [])]
            return len(models) > 0
        return False
    except:
        return False

def start_lmstudio():
    """启动LM Studio"""
    lmstudio_path = find_lmstudio()
    
    if not lmstudio_path:
        return False, "未找到LM Studio安装"
    
    if is_lmstudio_running():
        return True, "LM Studio已在运行"
    
    try:
        # 启动LM Studio
        subprocess.Popen([lmstudio_path], shell=True)
        
        # 等待启动
        for i in range(30):
            time.sleep(1)
            if is_lmstudio_running():
                return True, "LM Studio启动成功"
        
        return False, "LM Studio启动超时"
        
    except Exception as e:
        return False, f"启动失败: {e}"

def wait_for_model_and_api(timeout=120):
    """等待模型加载和API就绪"""
    for i in range(timeout):
        if is_api_ready():
            return True, "API服务已就绪"
        time.sleep(1)
    
    return False, "等待超时，请手动在LM Studio中加载DeepSeek模型"

def auto_launch():
    """自动启动流程"""
    try:
        print("启动LM Studio自动化流程...")

        # 1. 启动LM Studio
        success, message = start_lmstudio()
        print(f"LM Studio: {message}")

        if not success:
            return False, message

        # 2. 等待API就绪
        print("等待模型加载和API启动...")
        success, message = wait_for_model_and_api()
        print(f"API状态: {message}")

        return success, message

    except UnicodeEncodeError:
        # 处理编码问题
        return True, "LM Studio已启动，请手动检查API状态"
    except Exception as e:
        return False, f"启动异常: {str(e)}"

if __name__ == "__main__":
    success, message = auto_launch()
    if success:
        print("✅ 自动启动完成!")
    else:
        print(f"❌ 启动失败: {message}")
    
    sys.exit(0 if success else 1)
