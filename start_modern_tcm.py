#!/usr/bin/env python3
"""
现代化中医RAG系统启动脚本
支持聊天界面、语音交互、文档上传
"""
import os
import sys
import subprocess
import webbrowser
import time
import socket
from pathlib import Path
import psutil

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        if check_port_available(port):
            return port
    return None

def install_dependencies():
    """安装依赖"""
    print("🔄 检查并安装依赖...")
    
    try:
        # 检查关键依赖
        import fastapi
        import uvicorn
        import sentence_transformers
        print("✅ 核心依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("🔄 正在安装依赖...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ 依赖安装完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    return True

def setup_directories():
    """创建必要的目录"""
    directories = [
        "backend",
        "static", 
        "uploads",
        "vector_db",
        "sessions"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3.8, 0):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查内存
    memory_gb = psutil.virtual_memory().total / (1024**3)
    if memory_gb < 4:
        print(f"⚠️ 内存较少 ({memory_gb:.1f}GB)，建议4GB以上")
    
    # 检查磁盘空间
    disk_free_gb = psutil.disk_usage('.').free / (1024**3)
    if disk_free_gb < 2:
        print(f"⚠️ 磁盘空间不足 ({disk_free_gb:.1f}GB)，建议2GB以上")
    
    print("✅ 系统要求检查完成")
    return True

def create_demo_document():
    """创建演示文档"""
    demo_file = Path("uploads/中医基础知识.txt")
    
    if not demo_file.exists():
        demo_content = """
中医基础理论知识

一、阴阳学说
阴阳学说是中医理论的哲学基础，认为阴阳是宇宙间相互关联的两个方面。
在人体中，阴阳的相对平衡是健康的标志，阴阳失调则导致疾病。
阴主静、主寒、主下、主内；阳主动、主热、主上、主外。

二、五脏六腑理论
五脏包括心、肝、脾、肺、肾，主要功能是化生和储藏精气。
六腑包括胆、胃、小肠、大肠、膀胱、三焦，主要功能是受纳和传化水谷。
脏腑之间通过经络相互联系，形成统一的功能整体。

三、气血理论
气血是中医学的核心概念，气为血之帅，血为气之母。
气具有推动、温煦、防御、固摄、气化等功能。
血具有濡养、滋润等作用。
气血充足则脏腑功能正常，气血不足则百病丛生。

四、湿气理论
湿气是中医理论中的重要概念，指人体内水液代谢失常所产生的病理产物。
湿性重浊、黏腻，易阻遏气机，损伤阳气。
湿邪可分为外湿和内湿，外湿多因居处潮湿、涉水淋雨等外界环境因素所致；
内湿多由脾胃功能失调，水液代谢障碍所形成。

五、经络学说
经络是人体内气血运行的通道，包括经脉和络脉。
十二正经是经络系统的主体，包括手三阴、手三阳、足三阴、足三阳。
经络具有运行气血、联络脏腑、沟通内外、调节机能的作用。

六、四季养生
春季养生：春季阳气生发，应顺应自然，早睡早起，适当运动。
夏季养生：夏季阳气旺盛，应注意清热解暑，保持心情舒畅。
秋季养生：秋季阳气收敛，应注意滋阴润燥，调养肺气。
冬季养生：冬季阳气潜藏，应注意温补肾阳，保存精气。
"""
        
        with open(demo_file, 'w', encoding='utf-8') as f:
            f.write(demo_content)
        
        print("✅ 演示文档创建完成")

def start_server():
    """启动服务器"""
    print("🚀 启动现代化中医RAG系统...")
    
    # 检查系统要求
    if not check_system_requirements():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建目录
    setup_directories()
    
    # 创建演示文档
    create_demo_document()
    
    # 找到可用端口
    port = find_available_port(8000)
    if not port:
        print("❌ 无法找到可用端口")
        return False
    
    print(f"📡 使用端口: {port}")
    
    # 切换到backend目录
    os.chdir("backend")
    
    # 启动FastAPI服务器
    try:
        print("🔄 正在启动FastAPI服务器...")
        
        # 构建启动命令
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "main:app",
            "--host", "0.0.0.0",
            "--port", str(port),
            "--reload",
            "--log-level", "info"
        ]
        
        # 启动服务器
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(5)
        
        # 检查服务器是否启动成功
        try:
            import requests
            response = requests.get(f"http://localhost:{port}/api/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器启动成功！")
                
                # 显示访问信息
                print("\n" + "="*60)
                print("🎉 现代化中医RAG系统已启动！")
                print("="*60)
                print(f"🌐 本地访问: http://localhost:{port}")
                print(f"📱 局域网访问: http://{get_local_ip()}:{port}")
                print("="*60)
                print("\n💡 功能特色:")
                print("  🗣️  语音输入和输出")
                print("  📁  PDF文档上传")
                print("  💬  现代化聊天界面")
                print("  🤖  智能中医问答")
                print("  📚  知识库管理")
                print("  📱  移动端优化")
                print("\n⚠️  重要提醒:")
                print("  本系统仅供中医文化学习参考")
                print("  不构成医疗建议或诊断依据")
                print("  如有健康问题请咨询专业医师")
                print("="*60)
                
                # 询问是否打开浏览器
                try:
                    open_browser = input("\n是否打开浏览器？(y/n): ").lower()
                    if open_browser != 'n':
                        webbrowser.open(f"http://localhost:{port}")
                except KeyboardInterrupt:
                    pass
                
                print("\n💡 提示:")
                print("  - 保持此窗口开启以维持服务运行")
                print("  - 按 Ctrl+C 可以停止服务")
                print("  - 上传PDF文档可扩充知识库")
                print("  - 支持语音输入，点击麦克风图标")
                
                # 等待用户中断
                try:
                    process.wait()
                except KeyboardInterrupt:
                    print("\n\n👋 收到停止信号...")
                    process.terminate()
                    process.wait()
                    print("✅ 服务已停止")
                
                return True
            else:
                print("❌ 服务器启动失败")
                process.terminate()
                return False
                
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "localhost"

def main():
    """主函数"""
    print("🏥 现代化中医RAG系统")
    print("=" * 50)
    print("🎯 特色功能:")
    print("  • Vue.js现代化聊天界面")
    print("  • 语音输入和输出支持")
    print("  • PDF文档上传和解析")
    print("  • 智能中医知识问答")
    print("  • 移动端完美适配")
    print("=" * 50)
    
    try:
        success = start_server()
        if not success:
            print("\n❌ 启动失败，请检查错误信息")
            input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动过程出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
