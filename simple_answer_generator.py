"""
简单的基于规则的回答生成器
当LLM模型效果不好时的备选方案
"""
import re
from typing import List, Dict

class SimpleAnswerGenerator:
    def __init__(self):
        # 中医关键词映射
        self.tcm_keywords = {
            "湿气": {
                "symptoms": ["身体沉重", "头昏", "胸闷", "食欲不振", "大便粘腻", "舌苔厚腻"],
                "treatment": ["健脾祛湿", "利水渗湿", "芳香化湿"],
                "herbs": ["茯苓", "白术", "陈皮", "半夏", "薏苡仁"]
            },
            "阴阳": {
                "theory": ["阴阳对立", "阴阳互根", "阴阳消长", "阴阳转化"],
                "application": ["诊断疾病", "指导治疗", "养生保健"]
            },
            "五行": {
                "elements": ["木", "火", "土", "金", "水"],
                "organs": ["肝", "心", "脾", "肺", "肾"],
                "application": ["脏腑关系", "病理传变", "治疗原则"]
            },
            "脾胃": {
                "functions": ["运化水谷", "升清降浊", "统血"],
                "symptoms": ["食欲不振", "腹胀", "便溏", "乏力", "面色萎黄"],
                "treatment": ["健脾益气", "和胃降逆", "温中散寒"]
            },
            "气血": {
                "qi": ["推动", "温煦", "防御", "固摄", "气化"],
                "blood": ["濡养", "滋润"],
                "relationship": ["气为血帅", "血为气母", "气血同源"]
            }
        }
    
    def extract_keywords(self, query: str) -> List[str]:
        """提取查询中的关键词"""
        keywords = []
        for keyword in self.tcm_keywords.keys():
            if keyword in query:
                keywords.append(keyword)
        return keywords
    
    def generate_answer_from_context(self, query: str, context: str) -> str:
        """基于上下文生成回答"""
        # 提取关键词
        keywords = self.extract_keywords(query)
        
        if not keywords:
            return self.generate_general_answer(query, context)
        
        # 基于关键词生成回答
        answer_parts = []
        
        for keyword in keywords:
            if keyword in self.tcm_keywords:
                keyword_info = self.tcm_keywords[keyword]
                
                if "症状" in query or "表现" in query:
                    if "symptoms" in keyword_info:
                        symptoms = "、".join(keyword_info["symptoms"])
                        answer_parts.append(f"{keyword}的主要表现包括：{symptoms}。")
                
                elif "治疗" in query or "如何" in query:
                    if "treatment" in keyword_info:
                        treatments = "、".join(keyword_info["treatment"])
                        answer_parts.append(f"{keyword}的治疗原则主要是：{treatments}。")
                    if "herbs" in keyword_info:
                        herbs = "、".join(keyword_info["herbs"])
                        answer_parts.append(f"常用药物包括：{herbs}等。")
                
                elif "学说" in query or "理论" in query:
                    if "theory" in keyword_info:
                        theory = "、".join(keyword_info["theory"])
                        answer_parts.append(f"{keyword}学说的核心内容包括：{theory}。")
                    if "application" in keyword_info:
                        application = "、".join(keyword_info["application"])
                        answer_parts.append(f"在中医中的应用主要体现在：{application}。")
                
                elif "应用" in query:
                    if "application" in keyword_info:
                        application = "、".join(keyword_info["application"])
                        answer_parts.append(f"{keyword}在中医中的应用包括：{application}。")
                    if "organs" in keyword_info:
                        organs = "、".join(keyword_info["organs"])
                        answer_parts.append(f"对应的脏腑为：{organs}。")
        
        if answer_parts:
            # 添加上下文信息
            context_summary = self.extract_relevant_context(context, keywords)
            if context_summary:
                answer_parts.append(f"\n根据《金匮要略》等中医经典：{context_summary}")
            
            return "".join(answer_parts)
        
        return self.generate_general_answer(query, context)
    
    def extract_relevant_context(self, context: str, keywords: List[str]) -> str:
        """从上下文中提取相关信息"""
        if not context:
            return ""
        
        # 查找包含关键词的句子
        sentences = re.split(r'[。！？]', context)
        relevant_sentences = []
        
        for sentence in sentences:
            for keyword in keywords:
                if keyword in sentence and len(sentence.strip()) > 10:
                    relevant_sentences.append(sentence.strip())
                    break
        
        if relevant_sentences:
            # 返回最相关的1-2句
            return "。".join(relevant_sentences[:2]) + "。"
        
        return ""
    
    def generate_general_answer(self, query: str, context: str) -> str:
        """生成通用回答"""
        if not context:
            return "抱歉，我在中医文献中没有找到相关信息。建议咨询专业中医师获得准确的诊疗建议。"
        
        # 提取上下文的关键信息
        context_summary = context[:200] + "..." if len(context) > 200 else context
        
        return f"根据中医理论和文献记载：{context_summary}\n\n建议结合个人体质和具体症状，咨询专业中医师获得个性化的诊疗方案。"

# 全局实例
simple_generator = SimpleAnswerGenerator()
