#!/usr/bin/env python3
"""
测试PDF上传功能，启用PDF检索
"""
import requests
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

def create_test_pdf():
    """创建一个测试PDF文档"""
    try:
        # 创建PDF内容
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        
        # 添加中医内容
        content = [
            "中医诊疗指南",
            "",
            "小儿鼻塞的中医治疗",
            "",
            "病因分析：",
            "1. 外感风寒：鼻塞流清涕，多因感受风寒之邪",
            "2. 肺热壅盛：鼻干鼻塞，多因肺热上炎",
            "3. 脾胃虚弱：鼻涕清稀，多因脾胃运化失常",
            "",
            "治疗方法：",
            "1. 风寒型：用辛温解表法，方用荆防败毒散",
            "2. 肺热型：用清热宣肺法，方用麻杏石甘汤",
            "3. 脾虚型：用健脾益气法，方用参苓白术散",
            "",
            "穴位治疗：",
            "主穴：迎香、印堂、鼻通",
            "配穴：合谷、风池、肺俞",
            "",
            "注意事项：",
            "1. 保持室内空气流通",
            "2. 避免接触过敏原",
            "3. 加强体质锻炼",
            "4. 饮食清淡，忌辛辣刺激"
        ]
        
        # 写入内容
        y = 750
        for line in content:
            p.drawString(50, y, line.encode('utf-8').decode('utf-8'))
            y -= 20
        
        p.save()
        buffer.seek(0)
        return buffer.getvalue()
        
    except Exception as e:
        print(f"创建PDF失败: {e}")
        # 创建简单的文本内容
        content = """中医诊疗指南

小儿鼻塞的中医治疗

病因分析：
1. 外感风寒：鼻塞流清涕，多因感受风寒之邪
2. 肺热壅盛：鼻干鼻塞，多因肺热上炎  
3. 脾胃虚弱：鼻涕清稀，多因脾胃运化失常

治疗方法：
1. 风寒型：用辛温解表法，方用荆防败毒散
2. 肺热型：用清热宣肺法，方用麻杏石甘汤
3. 脾虚型：用健脾益气法，方用参苓白术散

穴位治疗：
主穴：迎香、印堂、鼻通
配穴：合谷、风池、肺俞

注意事项：
1. 保持室内空气流通
2. 避免接触过敏原
3. 加强体质锻炼
4. 饮食清淡，忌辛辣刺激"""
        
        return content.encode('utf-8')

def upload_test_pdf():
    """上传测试PDF文档"""
    try:
        print("🔧 创建测试PDF文档...")
        pdf_content = create_test_pdf()
        
        print("📤 上传PDF文档到系统...")
        
        # 准备文件
        files = {
            'files': ('中医诊疗指南.pdf', pdf_content, 'application/pdf')
        }
        
        # 上传文件
        response = requests.post(
            'http://localhost:8006/api/upload',
            files=files,
            auth=('tcm_user', 'MVP168918'),
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ PDF文档上传成功！")
            print(f"📄 处理结果: {data.get('message', '成功')}")
            print(f"📊 处理文件数: {data.get('processed_files', 0)}")
            return True
        else:
            print(f"❌ PDF上传失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ PDF上传异常: {e}")
        return False

def test_pdf_search():
    """测试PDF检索功能"""
    try:
        print("\n🧪 测试PDF检索功能...")
        
        # 测试查询
        query = "小女孩晚上老是鼻子怎么回事"
        
        payload = {
            "message": query
        }
        
        response = requests.post(
            'http://localhost:8006/api/chat',
            json=payload,
            auth=('tcm_user', 'MVP168918'),
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            sources = data.get('sources', [])
            
            # 检查PDF来源
            pdf_sources = [s for s in sources if s.get('type') == 'pdf' or s.get('source_type') == 'pdf']
            
            print(f"✅ PDF检索测试完成")
            print(f"📚 总来源数: {len(sources)}")
            print(f"📄 PDF来源数: {len(pdf_sources)}")
            
            if pdf_sources:
                print("🎉 PDF检索功能已启用！")
                for i, source in enumerate(pdf_sources[:3], 1):
                    print(f"   {i}. {source.get('source', 'unknown')} (评分: {source.get('score', 0):.3f})")
                return True
            else:
                print("⚠️ PDF检索功能未检测到结果")
                return False
        else:
            print(f"❌ 检索测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ PDF检索测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启用PDF检索功能")
    print("=" * 40)
    
    # 检查服务器状态
    try:
        response = requests.get('http://localhost:8006/api/health', 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务器运行正常")
            print(f"📊 当前文档数: {data.get('documents', 0)}")
        else:
            print("❌ 服务器响应异常")
            return
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 上传测试PDF
    if upload_test_pdf():
        print("\n⏳ 等待文档处理...")
        import time
        time.sleep(5)  # 等待处理完成
        
        # 测试PDF检索
        if test_pdf_search():
            print("\n🎉 PDF检索功能启用成功！")
        else:
            print("\n⚠️ PDF检索功能需要进一步调试")
    else:
        print("\n❌ PDF上传失败，无法启用检索功能")

if __name__ == "__main__":
    main()
