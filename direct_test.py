#!/usr/bin/env python3
"""
直接测试系统功能
不通过API，直接调用核心功能
"""
import asyncio
import time
from simple_ultimate_tcm import SimpleResponseGenerator, SimpleDocumentProcessor, SimpleOnlineCrawler

async def direct_test():
    """直接测试系统功能"""
    print("🧪 直接功能测试")
    print("=" * 50)
    
    # 初始化组件
    print("🔧 初始化组件...")
    doc_processor = SimpleDocumentProcessor()
    crawler = SimpleOnlineCrawler()
    response_generator = SimpleResponseGenerator(doc_processor, crawler)
    
    # 测试问题
    query = "小女孩晚上老是鼻子怎么回事"
    print(f"📝 测试问题: {query}")
    print("-" * 30)
    
    try:
        print("🚀 开始生成回答...")
        start_time = time.time()
        
        # 直接调用生成回答方法
        response, sources, processing_time = await response_generator.generate_response(query)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"✅ 回答生成完成！")
        print(f"⏱️ 总处理时间: {total_time:.2f}秒")
        print(f"📊 API报告时间: {processing_time:.2f}秒")
        print("-" * 50)
        
        # 分析结果
        print("📊 结果分析:")
        print(f"📝 回答长度: {len(response)} 字符")
        print(f"📚 来源数量: {len(sources)} 条")
        
        # 检查功能启用情况
        print("\n🔍 功能检查:")
        print("-" * 30)
        
        # 1. 检查在线检索
        online_sources = [s for s in sources if s.get('type') == 'online' or s.get('source_type') == 'online']
        print(f"🌐 在线检索: {'✅ 启用' if online_sources else '❌ 未启用'} ({len(online_sources)}条)")
        
        if online_sources:
            print("   在线来源:")
            for i, source in enumerate(online_sources[:3], 1):
                source_name = source.get('source', 'unknown')
                score = source.get('score', 0)
                print(f"   {i}. {source_name} (评分: {score:.3f})")
        
        # 2. 检查PDF检索
        pdf_sources = [s for s in sources if s.get('type') == 'pdf' or s.get('source_type') == 'pdf']
        print(f"📄 PDF检索: {'✅ 启用' if pdf_sources else '❌ 未启用'} ({len(pdf_sources)}条)")
        
        # 3. 检查DeepSeek模型特征
        deepseek_indicators = [
            "DeepSeek" in response,
            "🧠" in response,
            "智能分析" in response,
            len(response) > 200,  # 足够详细
            "##" in response,  # 结构化
            "症状分析" in response or "治疗建议" in response
        ]
        deepseek_score = sum(deepseek_indicators)
        print(f"🤖 DeepSeek模型: {'✅ 启用' if deepseek_score >= 2 else '❌ 未启用'} (评分: {deepseek_score}/6)")
        
        print(f"   DeepSeek特征检查:")
        features = ["DeepSeek标识", "🧠图标", "智能分析", "详细内容", "结构化", "专业术语"]
        for i, (feature, indicator) in enumerate(zip(features, deepseek_indicators)):
            print(f"   • {feature}: {'✅' if indicator else '❌'}")
        
        # 显示回答内容
        print(f"\n💬 回答内容 ({len(response)}字符):")
        print("-" * 30)
        print(response[:800] + "..." if len(response) > 800 else response)
        
        # 显示来源详情
        if sources:
            print(f"\n📚 来源详情 ({len(sources)}条):")
            print("-" * 30)
            for i, source in enumerate(sources[:5], 1):
                source_type = source.get('type', source.get('source_type', 'unknown'))
                source_name = source.get('source', source.get('filename', 'unknown'))
                content_preview = source.get('content', '')[:100]
                score = source.get('score', 0)
                
                print(f"{i}. 【{source_type.upper()}】{source_name}")
                print(f"   评分: {score:.3f}")
                print(f"   内容: {content_preview}...")
                print()
        
        # 总体评估
        print("🎯 总体评估:")
        print("-" * 30)
        
        total_features = 3  # 在线检索、PDF检索、DeepSeek模型
        enabled_features = sum([
            len(online_sources) > 0,
            len(pdf_sources) > 0,
            deepseek_score >= 2
        ])
        
        print(f"功能启用率: {enabled_features}/{total_features} ({enabled_features/total_features*100:.1f}%)")
        
        if enabled_features == total_features:
            print("🎉 所有功能都已正常启用！")
        elif enabled_features >= 2:
            print("⚠️ 大部分功能已启用，但仍有改进空间")
        else:
            print("❌ 多个功能未正常启用，需要检查配置")
        
        # 具体建议
        print("\n💡 改进建议:")
        if len(online_sources) == 0:
            print("• 在线检索未启用，检查网络连接和爬虫配置")
        if len(pdf_sources) == 0:
            print("• PDF检索未启用，可上传PDF文档测试")
        if deepseek_score < 2:
            print("• DeepSeek模型特征不明显，检查模型配置")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(direct_test())
