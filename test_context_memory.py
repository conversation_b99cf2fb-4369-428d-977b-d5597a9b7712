#!/usr/bin/env python3
"""
测试上下文记忆功能
"""
import requests
import json
import time

def test_context_memory():
    """测试上下文记忆功能"""
    url = "http://localhost:8006/api/chat"
    session_id = None
    
    # 测试对话序列：先问方剂，再问相关问题
    conversation = [
        {
            "question": "栀子甘草豉汤的功效是什么？",
            "expected_keywords": ["栀子甘草豉汤", "清热除烦", "和中降逆"]
        },
        {
            "question": "这个方剂的组成有哪些？",
            "expected_keywords": ["栀子", "甘草", "豆豉"]
        },
        {
            "question": "它主要治疗什么病症？",
            "expected_keywords": ["虚烦不眠", "胸中窒闷", "懊憹"]
        },
        {
            "question": "四君子汤的功效是什么？",
            "expected_keywords": ["四君子汤", "益气健脾"]
        },
        {
            "question": "这个方剂和前面的有什么区别？",
            "expected_keywords": ["四君子汤", "栀子甘草豉汤"]
        }
    ]
    
    print("🧠 测试上下文记忆功能")
    print("=" * 50)
    
    for i, turn in enumerate(conversation, 1):
        question = turn["question"]
        expected = turn["expected_keywords"]
        
        print(f"\n📝 对话 {i}: {question}")
        print(f"🎯 期望关键词: {', '.join(expected)}")
        print("-" * 30)
        
        try:
            payload = {"message": question}
            if session_id:
                payload["session_id"] = session_id
            
            start_time = time.time()
            response = requests.post(url, 
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get('session_id')  # 保存会话ID
                
                print(f"✅ 响应成功 (耗时: {end_time - start_time:.2f}s)")
                print(f"🔗 会话ID: {session_id}")
                
                # 分析回答内容
                answer = data.get('response', '')
                sources = data.get('sources', [])
                
                # 检查是否包含期望的关键词
                found_keywords = []
                for keyword in expected:
                    if keyword in answer:
                        found_keywords.append(keyword)
                
                print(f"🔍 找到关键词: {found_keywords}")
                print(f"📚 来源数量: {len(sources)}")
                
                # 显示回答摘要
                print(f"🤖 回答摘要: {answer[:200]}...")
                
                # 评估上下文理解
                if i > 1 and any(word in question for word in ['这个', '它', '该', '前面的']):
                    if found_keywords:
                        print("✅ 上下文理解: 成功")
                    else:
                        print("❌ 上下文理解: 失败")
                
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n🎉 上下文记忆测试完成！")
    print(f"📊 最终会话ID: {session_id}")

def test_session_isolation():
    """测试会话隔离"""
    print("\n🔒 测试会话隔离功能")
    print("-" * 30)
    
    url = "http://localhost:8006/api/chat"
    
    # 会话1
    print("📱 会话1: 询问栀子甘草豉汤")
    response1 = requests.post(url, 
        json={"message": "栀子甘草豉汤的功效是什么？"},
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response1.status_code == 200:
        data1 = response1.json()
        session1_id = data1.get('session_id')
        print(f"✅ 会话1 ID: {session1_id}")
    
    time.sleep(1)
    
    # 会话2（新会话）
    print("\n📱 会话2: 询问四君子汤")
    response2 = requests.post(url, 
        json={"message": "四君子汤的功效是什么？"},
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response2.status_code == 200:
        data2 = response2.json()
        session2_id = data2.get('session_id')
        print(f"✅ 会话2 ID: {session2_id}")
    
    time.sleep(1)
    
    # 在会话1中问指代问题
    print("\n📱 会话1: 问指代问题")
    response3 = requests.post(url, 
        json={
            "message": "这个方剂的组成有哪些？",
            "session_id": session1_id
        },
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response3.status_code == 200:
        data3 = response3.json()
        answer3 = data3.get('response', '')
        print(f"🤖 回答包含栀子甘草豉汤: {'栀子甘草豉汤' in answer3 or '栀子' in answer3}")
    
    time.sleep(1)
    
    # 在会话2中问指代问题
    print("\n📱 会话2: 问指代问题")
    response4 = requests.post(url, 
        json={
            "message": "这个方剂的组成有哪些？",
            "session_id": session2_id
        },
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    if response4.status_code == 200:
        data4 = response4.json()
        answer4 = data4.get('response', '')
        print(f"🤖 回答包含四君子汤: {'四君子汤' in answer4 or '人参' in answer4}")
    
    # 验证会话隔离
    if session1_id != session2_id:
        print("✅ 会话隔离: 成功（不同会话ID）")
    else:
        print("❌ 会话隔离: 失败（相同会话ID）")

if __name__ == "__main__":
    print("🚀 开始测试上下文记忆和会话管理")
    print("=" * 60)
    
    # 1. 测试上下文记忆
    test_context_memory()
    
    # 2. 测试会话隔离
    test_session_isolation()
    
    print("\n🎉 所有测试完成！")
    print("=" * 60)
    print("💡 现在您可以在浏览器中测试:")
    print("   1. 先问'栀子甘草豉汤的功效是什么？'")
    print("   2. 再问'这个方剂的组成有哪些？'")
    print("   3. 系统应该能理解'这个方剂'指的是栀子甘草豉汤")
    print("   4. 使用'新建会话'按钮测试会话隔离")
    print("   5. 使用语音输入功能")
