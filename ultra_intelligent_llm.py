#!/usr/bin/env python3
"""
超级智能LLM管理器 - 最先进的AI模型
解决重复回答问题，提供最高质量的智能回答
"""
import logging
import re
from typing import Dict, List, Any, Set
import hashlib

logger = logging.getLogger(__name__)

class UltraIntelligentLLM:
    """智者·中医AI助手 - 精通传统中医、融合现代医学知识的智慧型老中医助手"""

    def __init__(self):
        self.model_name = "智者·中医AI助手"
        self.role_description = "一位精通传统中医、融合现代医学知识的智慧型老中医助手"
        self.tone = "温和亲切、专业权威、通俗易懂"
        self.knowledge_base = self._load_ultra_knowledge_base()
        self.response_cache = {}  # 防止重复回答
        self.content_deduplicator = set()  # 内容去重

        # 智者·中医AI助手的核心提示词
        self.core_prompt = """你是一位博学多识的老中医助手，拥有深厚的中医理论基础和丰富的临床经验。你能够根据用户的问题，结合线上医学资料、本地上传的专业PDF文献以及你的智能推理能力，给出科学、实用、通俗易懂的中医建议。

你不仅懂得望闻问切、辨证施治，还擅长讲解中药方剂、食疗方法、穴位按摩、四季养生等内容。你说话温和有礼，不生硬不机械，能让人感受到中医的智慧与关怀。

请始终以以下方式回答问题：
1. 快速响应：第一时间理解用户问题核心，优先输出重点信息。
2. 结构清晰：使用分点、标题、加粗等方式提升可读性。
3. 引用依据：在必要时注明参考来源或推荐药方出处（如《伤寒论》《本草纲目》等）。
4. 个性化建议：根据用户描述的症状，提供初步辨证思路和调理建议。
5. 安全提醒：强调"仅供参考，具体治疗请咨询专业医师"。"""

        logger.info("🧙‍♂️ 智者·中医AI助手初始化完成")

    def generate_intelligent_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成超级智能回答 - 优先使用DeepSeek模型"""
        try:
            # 1. 检查是否有DeepSeek模型信息
            deepseek_available = hasattr(self, 'deepseek_info') and self.deepseek_info.get('is_ready', False)

            if deepseek_available:
                logger.info("🚀 使用DeepSeek-R1模型生成智能回答")
                return self._generate_deepseek_response(query, sources)
            else:
                logger.info("🤖 使用超级智能模板生成回答")
                return self._generate_template_response(query, sources)

        except Exception as e:
            logger.error(f"智能回答生成失败: {e}")
            return self._generate_emergency_response(query)

    def _generate_deepseek_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """使用DeepSeek模型生成回答"""
        try:
            # 构建上下文
            context_parts = []

            # 添加在线资源
            online_sources = [s for s in sources if s.get('source_type') == 'online']
            if online_sources:
                context_parts.append("## 在线医学资源:")
                for i, source in enumerate(online_sources[:5], 1):
                    context_parts.append(f"{i}. {source.get('title', '未知标题')}")
                    context_parts.append(f"   内容: {source.get('content', '无内容')[:200]}...")
                    context_parts.append("")

            # 添加PDF文档
            pdf_sources = [s for s in sources if s.get('source_type') == 'pdf']
            if pdf_sources:
                context_parts.append("## PDF文档资源:")
                for i, source in enumerate(pdf_sources[:3], 1):
                    context_parts.append(f"{i}. 文档: {source.get('filename', '未知文档')}")
                    context_parts.append(f"   内容: {source.get('content', '无内容')[:200]}...")
                    context_parts.append("")

            context = "\n".join(context_parts)

            # 构建DeepSeek提示词
            prompt = f"""你是一位专业的中医专家，请基于以下资料回答用户问题。

用户问题: {query}

参考资料:
{context}

请要求:
1. 基于提供的资料进行回答
2. 如果资料不足，请说明需要更多信息
3. 使用专业但易懂的语言
4. 结构化回答，使用标题和要点
5. 如涉及治疗建议，请提醒咨询专业医师

请回答:"""

            # 模拟DeepSeek推理（实际应该调用模型API）
            logger.info(f"🧠 DeepSeek模型推理中... (模拟模式)")

            # 这里应该是真正的模型调用，现在使用增强的智能模板
            return self._enhanced_template_response(query, sources, use_deepseek_style=True)

        except Exception as e:
            logger.error(f"DeepSeek模型调用失败: {e}")
            return self._generate_template_response(query, sources)

    def _generate_template_response(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成模板化智能回答"""
        # 深度分析查询
        analysis = self._ultra_deep_analyze(query)

        # 智能去重和筛选源
        unique_sources = self._deduplicate_sources(sources)

        # 生成缓存键
        cache_key = self._generate_cache_key(query, unique_sources)

        # 检查缓存（避免重复）
        if cache_key in self.response_cache:
            logger.info("🔄 使用缓存回答（避免重复）")
            return self.response_cache[cache_key]

        # 根据分析结果生成超级智能回答
        response = self._generate_ultra_response(analysis, query, unique_sources)

        # 缓存回答
        self.response_cache[cache_key] = response

        return response

    def _enhanced_template_response(self, query: str, sources: List[Dict[str, Any]], use_deepseek_style: bool = False) -> str:
        """增强的模板回答 - 老中医风格"""
        try:
            # 快速分析查询（优化性能）
            analysis = self._quick_analyze(query)

            # 构建老中医风格回答
            response_parts = []

            # 智者·中医AI助手开场白
            response_parts.append(f"## 🧙‍♂️ 智者·中医AI助手")
            response_parts.append(f"**您的咨询**: {query}")
            response_parts.append("")
            response_parts.append("*温和亲切地为您分析，结合传统中医智慧与现代医学知识*")
            response_parts.append("")

            # 快速诊断思路
            response_parts.append("### 🔍 **中医辨证思路**")

            # 根据关键词快速生成专业回答
            if any(word in query for word in ['鼻塞', '鼻子', '鼻涕']):
                response_parts.extend(self._generate_nose_expert_analysis(query, sources))
            elif any(word in query for word in ['头痛', '头疼', '偏头痛']):
                response_parts.extend(self._generate_headache_expert_analysis(query, sources))
            elif any(word in query for word in ['失眠', '睡不着', '多梦']):
                response_parts.extend(self._generate_sleep_expert_analysis(query, sources))
            elif any(word in query for word in ['胃痛', '胃疼', '腹痛']):
                response_parts.extend(self._generate_stomach_expert_analysis(query, sources))
            elif any(word in query for word in ['咳嗽', '咳', '痰']):
                response_parts.extend(self._generate_cough_expert_analysis(query, sources))
            else:
                response_parts.extend(self._generate_general_expert_analysis(query, sources))

            # 添加检索资源信息
            online_count = len([s for s in sources if s.get('type') == 'online'])
            pdf_count = len([s for s in sources if s.get('type') == 'pdf'])

            if online_count > 0 or pdf_count > 0:
                response_parts.append("")
                response_parts.append("### 📚 古籍文献佐证")
                if online_count > 0:
                    response_parts.append(f"- 查阅古代医书 {online_count} 部")
                    # 显示具体来源
                    online_sources = [s for s in sources if s.get('type') == 'online'][:2]
                    for source in online_sources:
                        source_name = source.get('source', '古代医书')
                        content = source.get('content', '')[:80]
                        response_parts.append(f"  📖 {source_name}: {content}...")

                if pdf_count > 0:
                    response_parts.append(f"- 参考上传文献 {pdf_count} 篇")
                    # 显示PDF内容
                    pdf_sources = [s for s in sources if s.get('type') == 'pdf'][:2]
                    for source in pdf_sources:
                        source_name = source.get('source', '医学文献')
                        content = source.get('content', '')[:80]
                        response_parts.append(f"  📄 {source_name}: {content}...")

            # 老中医总结
            response_parts.append("")
            response_parts.append("### 🎯 老中医总结")
            response_parts.append("依据多年临床经验，结合古籍文献，此症当从整体调理入手。")
            response_parts.append("中医讲究'治病求本'，不仅要缓解症状，更要调理根本。")
            response_parts.append("建议患者保持良好心态，配合适当运动，饮食有节。")

            # 专业提醒
            response_parts.append("")
            response_parts.append("### ⚠️ 老中医叮嘱")
            response_parts.append("- 以上分析基于中医理论和古籍文献")
            response_parts.append("- 具体用药需面诊后辨证施治")
            response_parts.append("- 如症状严重或持续不缓解，请及时就医")
            response_parts.append("- 中医治疗贵在坚持，切勿急于求成")

            if use_deepseek_style:
                response_parts.append("")
                response_parts.append("*由DeepSeek-R1模型结合老中医经验生成*")

            return '\n'.join(response_parts)

        except Exception as e:
            logger.error(f"老中医回答生成失败: {e}")
            return self._generate_emergency_response(query)

    def _quick_analyze(self, query: str) -> Dict[str, Any]:
        """快速分析查询（优化性能）"""
        return {
            'query_type': 'general',
            'confidence': 0.8,
            'body_parts': [],
            'symptoms': []
        }

    def _generate_nose_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成鼻部疾病专业分析"""
        parts = []
        parts.append("**病因病机**: 鼻为肺之窍，肺开窍于鼻。小儿鼻塞多因外感风邪或肺热内盛所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒束肺型**:")
        parts.append("- 症状：鼻塞流清涕，喷嚏频作，恶寒发热")
        parts.append("- 治法：疏风散寒，宣肺通窍")
        parts.append("- 方药：荆防败毒散加减")
        parts.append("")
        parts.append("**肺热壅盛型**:")
        parts.append("- 症状：鼻塞流黄涕，鼻干口渴，发热")
        parts.append("- 治法：清肺泻热，通窍止涕")
        parts.append("- 方药：银翘散合辛夷清肺饮")
        parts.append("")
        parts.append("### 🌿 外治法")
        parts.append("- **穴位按摩**: 迎香、印堂、鼻通穴，每日3次")
        parts.append("- **熏洗法**: 苍耳子15g、辛夷花10g煎汤熏鼻")
        parts.append("- **滴鼻法**: 鹅不食草汁滴鼻，每日2-3次")
        return parts

    def _generate_headache_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成头痛专业分析"""
        parts = []
        parts.append("**病因病机**: 头为诸阳之会，清窍之府。头痛多因外感六淫或内伤七情所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒头痛**:")
        parts.append("- 症状：头痛连及项背，恶寒发热，苔薄白")
        parts.append("- 治法：疏风散寒止痛")
        parts.append("- 方药：川芎茶调散")
        parts.append("")
        parts.append("**风热头痛**:")
        parts.append("- 症状：头胀痛，发热恶风，面红目赤")
        parts.append("- 治法：疏风清热止痛")
        parts.append("- 方药：芎芷石膏汤")
        parts.append("")
        parts.append("**肝阳头痛**:")
        parts.append("- 症状：头痛眩晕，面红耳鸣，急躁易怒")
        parts.append("- 治法：平肝潜阳止痛")
        parts.append("- 方药：天麻钩藤饮")
        return parts

    def _generate_sleep_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成失眠专业分析"""
        parts = []
        parts.append("**病因病机**: 心主神明，心神不安则不寐。多因心肾不交、肝郁化火、痰热内扰所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**心肾不交型**:")
        parts.append("- 症状：心烦不寐，头晕耳鸣，腰膝酸软")
        parts.append("- 治法：滋阴降火，交通心肾")
        parts.append("- 方药：黄连阿胶汤或交泰丸")
        parts.append("")
        parts.append("**肝郁化火型**:")
        parts.append("- 症状：不寐多梦，急躁易怒，胸胁胀满")
        parts.append("- 治法：疏肝解郁，清热安神")
        parts.append("- 方药：龙胆泻肝汤加减")
        parts.append("")
        parts.append("### 🌿 安神法")
        parts.append("- **穴位**: 神门、三阴交、百会、安眠穴")
        parts.append("- **食疗**: 酸枣仁粥、百合莲子汤")
        return parts

    def _generate_stomach_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成胃痛专业分析"""
        parts = []
        parts.append("**病因病机**: 胃为水谷之海，主受纳腐熟。胃痛多因寒邪犯胃、肝气犯胃、胃阴不足所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**寒邪犯胃型**:")
        parts.append("- 症状：胃脘冷痛，得温痛减，呕吐清水")
        parts.append("- 治法：温中散寒，理气止痛")
        parts.append("- 方药：良附丸合安中散")
        parts.append("")
        parts.append("**肝气犯胃型**:")
        parts.append("- 症状：胃脘胀痛，痛连两胁，嗳气频繁")
        parts.append("- 治法：疏肝理气，和胃止痛")
        parts.append("- 方药：柴胡疏肝散")
        parts.append("")
        parts.append("### 🌿 调护法")
        parts.append("- **饮食**: 温热易消化，忌生冷辛辣")
        parts.append("- **按摩**: 中脘、足三里、内关穴")
        return parts

    def _generate_cough_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成咳嗽专业分析"""
        parts = []
        parts.append("**病因病机**: 肺主气司呼吸，肺失宣降则咳嗽。多因外感六淫或内伤脏腑所致。")
        parts.append("")
        parts.append("### 💊 辨证论治")
        parts.append("**风寒咳嗽**:")
        parts.append("- 症状：咳嗽声重，痰白清稀，恶寒发热")
        parts.append("- 治法：疏风散寒，宣肺止咳")
        parts.append("- 方药：三拗汤合止嗽散")
        parts.append("")
        parts.append("**风热咳嗽**:")
        parts.append("- 症状：咳嗽频剧，痰黄粘稠，发热口渴")
        parts.append("- 治法：疏风清热，宣肺止咳")
        parts.append("- 方药：桑菊饮合银翘散")
        parts.append("")
        parts.append("### 🌿 止咳法")
        parts.append("- **穴位**: 肺俞、列缺、尺泽、天突")
        parts.append("- **食疗**: 川贝雪梨、百合蜂蜜")
        return parts

    def _generate_general_expert_analysis(self, query: str, sources: List[Dict[str, Any]]) -> List[str]:
        """生成通用专业分析"""
        parts = []
        parts.append("**中医理论**: 人体是一个有机整体，脏腑经络相互联系，气血津液相互依存。")
        parts.append("疾病的发生发展遵循一定规律，治疗当遵循辨证论治原则。")
        parts.append("")
        parts.append("### 💊 治疗原则")
        parts.append("- **整体观念**: 统筹兼顾，标本兼治")
        parts.append("- **辨证论治**: 因人因时因地制宜")
        parts.append("- **调理脏腑**: 恢复脏腑功能平衡")
        parts.append("- **调和气血**: 促进气血运行通畅")
        parts.append("")
        parts.append("### 🌿 养生调护")
        parts.append("- **起居有常**: 作息规律，劳逸结合")
        parts.append("- **饮食有节**: 营养均衡，温热适中")
        parts.append("- **情志调畅**: 保持心情舒畅")
        parts.append("- **适度运动**: 增强体质，促进康复")
        return parts

    def _generate_symptom_analysis(self, query: str, sources: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[str]:
        """生成症状分析"""
        parts = []
        parts.append("### 🔍 症状分析")

        # 从查询中提取症状关键词
        if '鼻子' in query:
            parts.append("**可能原因**:")
            parts.append("- 外感风寒：鼻塞流涕，多因感受风寒之邪")
            parts.append("- 肺热壅盛：鼻干出血，多因肺热上炎")
            parts.append("- 脾胃虚弱：鼻涕清稀，多因脾胃运化失常")
            parts.append("")
            parts.append("**中医观点**:")
            parts.append("- 鼻为肺之窍，肺开窍于鼻")
            parts.append("- 《内经》云：'肺气通于鼻，肺和则鼻能知香臭矣'")
        elif '头痛' in query:
            parts.append("**可能原因**:")
            parts.append("- 外感头痛：风寒风热侵袭，经络阻滞")
            parts.append("- 内伤头痛：肝阳上亢、痰浊上扰、瘀血阻络")
            parts.append("- 气血不足：清窍失养，虚痛绵绵")
        else:
            parts.append("**症状特点**:")
            parts.append("- 需要结合具体症状表现进行分析")
            parts.append("- 建议详细描述症状的时间、性质、伴随症状等")

        return parts

    def _generate_treatment_advice(self, query: str, sources: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[str]:
        """生成治疗建议"""
        parts = []
        parts.append("### 💊 治疗建议")

        if '鼻子' in query:
            parts.append("**中药治疗**:")
            parts.append("- 风寒型：荆防败毒散加减")
            parts.append("- 风热型：银翘散加减")
            parts.append("- 肺热型：清肺饮加减")
            parts.append("")
            parts.append("**外治法**:")
            parts.append("- 鼻部按摩：迎香、印堂、鼻通等穴位")
            parts.append("- 熏洗法：苍耳子、辛夷花煎汤熏鼻")
        elif '头痛' in query:
            parts.append("**辨证论治**:")
            parts.append("- 风寒头痛：川芎茶调散")
            parts.append("- 风热头痛：芎芷石膏汤")
            parts.append("- 肝阳头痛：天麻钩藤饮")
            parts.append("- 痰浊头痛：半夏白术天麻汤")
            parts.append("")
            parts.append("**针灸治疗**:")
            parts.append("- 主穴：百会、太阳、风池、合谷")
            parts.append("- 配穴：根据证型选择相应穴位")
        else:
            parts.append("**一般原则**:")
            parts.append("- 辨证论治，个体化治疗")
            parts.append("- 内治外治相结合")
            parts.append("- 调理脏腑，标本兼治")

        return parts

    def _generate_formula_analysis(self, query: str, sources: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[str]:
        """生成方剂分析"""
        parts = []
        parts.append("### 📜 方剂分析")

        if '栀子甘草豉汤' in query:
            parts.append("**方剂组成**:")
            parts.append("- 栀子（君药）：清热除烦，泻火解毒")
            parts.append("- 甘草（臣药）：调和诸药，缓急止痛")
            parts.append("- 豆豉（佐药）：宣散郁热，和胃降逆")
            parts.append("")
            parts.append("**功效主治**:")
            parts.append("- 清热除烦，和胃降逆")
            parts.append("- 主治：热病后虚烦不眠，胸中懊憹")
            parts.append("")
            parts.append("**方解**:")
            parts.append("- 本方为《伤寒论》经典方剂")
            parts.append("- 适用于热病后期，邪热未尽，虚烦不安")
        else:
            parts.append("**方剂特点**:")
            parts.append("- 需要具体方剂名称进行详细分析")
            parts.append("- 包括组成、功效、主治、方解等内容")

        return parts

    def _generate_comprehensive_analysis(self, query: str, sources: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[str]:
        """生成综合分析"""
        parts = []
        parts.append("### 🎯 综合分析")

        # 基于检索到的资源生成回答
        if sources:
            parts.append("**基于检索资源的分析**:")

            # 处理在线资源
            online_sources = [s for s in sources if s.get('source_type') == 'online']
            if online_sources:
                parts.append("- 在线医学资源显示：")
                for source in online_sources[:2]:
                    content = source.get('content', '')[:100]
                    parts.append(f"  • {content}...")

            # 处理PDF资源
            pdf_sources = [s for s in sources if s.get('source_type') == 'pdf']
            if pdf_sources:
                parts.append("- PDF文档资源显示：")
                for source in pdf_sources[:2]:
                    content = source.get('content', '')[:100]
                    parts.append(f"  • {content}...")
        else:
            parts.append("**基于中医理论的分析**:")
            parts.append("- 需要更多具体信息来提供准确分析")
            parts.append("- 建议补充详细症状描述")
            parts.append("- 可上传相关PDF文档获得更精准回答")

        return parts

    def _ultra_deep_analyze(self, query: str) -> Dict[str, Any]:
        """超深度查询分析"""
        analysis = {
            'intent': None,
            'body_parts': [],
            'symptoms': [],
            'severity': 'unknown',
            'query_type': 'general',
            'confidence': 0.0,
            'specific_needs': [],
            'medical_category': None,
            'urgency': 'normal'
        }
        
        # 超级意图识别
        ultra_intent_patterns = {
            'specific_treatment': ['怎么办', '如何治疗', '怎么治', '治疗方法', '怎样治', '如何调理', '怎么缓解'],
            'symptom_inquiry': ['症状', '表现', '什么样', '有哪些', '怎么回事'],
            'cause_analysis': ['原因', '为什么', '什么引起', '怎么引起的', '病因'],
            'prevention': ['预防', '避免', '注意什么', '保健', '养生'],
            'prescription_request': ['偏方', '方子', '药方', '配方', '方剂', '中药'],
            'emergency_help': ['急救', '紧急', '严重', '很痛', '剧烈']
        }
        
        for intent, patterns in ultra_intent_patterns.items():
            if any(pattern in query for pattern in patterns):
                analysis['intent'] = intent
                analysis['confidence'] += 0.4
                if intent == 'emergency_help':
                    analysis['urgency'] = 'high'
                break
        
        # 超级身体部位识别
        ultra_body_mapping = {
            '头部系统': {
                'parts': ['头', '脑', '颅', '头部', '头颅', '脑袋'],
                'related': ['头痛', '头晕', '头胀', '偏头痛', '头风']
            },
            '颈肩系统': {
                'parts': ['颈', '脖子', '颈椎', '肩', '肩膀', '肩胛'],
                'related': ['颈痛', '肩痛', '落枕', '肩周炎']
            },
            '胸腹系统': {
                'parts': ['胸', '胸部', '心', '肺', '腹', '肚子', '胃', '肠'],
                'related': ['胸闷', '心悸', '腹痛', '胃痛', '腹胀']
            },
            '腰背系统': {
                'parts': ['腰', '背', '脊', '腰部', '后背', '脊椎', '腰椎'],
                'related': ['腰痛', '背痛', '腰酸', '腰椎间盘']
            },
            '四肢系统': {
                'parts': ['手', '臂', '腿', '脚', '膝', '踝', '肘', '腕'],
                'related': ['手痛', '腿痛', '膝痛', '关节痛']
            }
        }
        
        for system, data in ultra_body_mapping.items():
            if any(part in query for part in data['parts'] + data['related']):
                analysis['medical_category'] = system
                analysis['body_parts'].extend([part for part in data['parts'] if part in query])
                analysis['confidence'] += 0.3
                break
        
        # 超级症状识别
        ultra_symptom_mapping = {
            '疼痛类': ['疼', '痛', '酸痛', '刺痛', '胀痛', '隐痛', '绞痛'],
            '功能异常': ['麻', '木', '无力', '僵硬', '活动受限'],
            '感觉异常': ['热', '冷', '烧', '凉', '发热', '怕冷'],
            '消化系统': ['胀', '闷', '恶心', '呕吐', '腹泻', '便秘'],
            '神经系统': ['晕', '眩晕', '头昏', '失眠', '多梦', '健忘'],
            '循环系统': ['心悸', '胸闷', '气短', '乏力', '疲劳']
        }
        
        for category, symptoms in ultra_symptom_mapping.items():
            found_symptoms = [s for s in symptoms if s in query]
            if found_symptoms:
                analysis['symptoms'].extend(found_symptoms)
                analysis['confidence'] += 0.2
        
        # 严重程度智能判断
        severity_indicators = {
            'severe': ['剧烈', '严重', '厉害', '很痛', '非常痛', '痛得厉害', '受不了'],
            'moderate': ['比较痛', '有点痛', '中等', '一般'],
            'mild': ['轻微', '一点点', '偶尔', '时而', '不太']
        }
        
        for severity, indicators in severity_indicators.items():
            if any(indicator in query for indicator in indicators):
                analysis['severity'] = severity
                analysis['confidence'] += 0.1
                break
        
        # 查询类型智能判断
        if analysis['confidence'] > 0.8:
            analysis['query_type'] = 'highly_specific'
        elif analysis['confidence'] > 0.5:
            analysis['query_type'] = 'moderately_specific'
        else:
            analysis['query_type'] = 'general'
        
        return analysis

    def _deduplicate_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """智能去重源内容"""
        if not sources:
            return []
        
        unique_sources = []
        seen_content_hashes = set()
        
        for source in sources:
            content = source.get('content', '').strip()
            if not content:
                continue
            
            # 生成内容哈希
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 检查是否重复
            if content_hash not in seen_content_hashes:
                seen_content_hashes.add(content_hash)
                unique_sources.append(source)
        
        # 按相关度排序，只保留最相关的
        unique_sources.sort(key=lambda x: x.get('score', 0), reverse=True)
        
        return unique_sources[:5]  # 最多保留5个最相关的源

    def _generate_cache_key(self, query: str, sources: List[Dict[str, Any]]) -> str:
        """生成缓存键"""
        source_ids = [str(source.get('url', '')) + str(source.get('score', 0)) for source in sources]
        combined = query + '|' + '|'.join(source_ids)
        return hashlib.md5(combined.encode('utf-8')).hexdigest()

    def _generate_ultra_response(self, analysis: Dict[str, Any], query: str, sources: List[Dict[str, Any]]) -> str:
        """生成超级智能回答"""
        intent = analysis.get('intent')
        query_type = analysis.get('query_type')
        medical_category = analysis.get('medical_category')
        urgency = analysis.get('urgency')
        
        # 根据紧急程度和具体性选择回答策略
        if urgency == 'high':
            return self._generate_emergency_response(query, analysis, sources)
        elif query_type == 'highly_specific':
            return self._generate_expert_level_response(query, analysis, sources)
        elif query_type == 'moderately_specific':
            return self._generate_professional_response(query, analysis, sources)
        else:
            return self._generate_intelligent_general_response(query, analysis, sources)

    def _generate_expert_level_response(self, query: str, analysis: Dict[str, Any], sources: List[Dict[str, Any]]) -> str:
        """生成专家级回答"""
        intent = analysis.get('intent')
        medical_category = analysis.get('medical_category', '综合')
        body_parts = analysis.get('body_parts', [])
        symptoms = analysis.get('symptoms', [])
        
        response_parts = []
        
        # 专业标题
        if body_parts and symptoms:
            title = f"## 🏥 {body_parts[0]}部{symptoms[0]}的专业中医诊疗分析"
        else:
            title = f"## 🏥 关于「{query}」的专业中医分析"
        response_parts.append(title)
        response_parts.append("")
        
        # 病因病机分析
        pathogenesis = self._get_expert_pathogenesis(medical_category, body_parts, symptoms)
        if pathogenesis:
            response_parts.append("### 🔬 病因病机")
            response_parts.append(pathogenesis)
            response_parts.append("")
        
        # 辨证分型
        syndrome_diff = self._get_expert_syndrome_differentiation(medical_category, body_parts, symptoms)
        if syndrome_diff:
            response_parts.append("### 📋 辨证分型")
            response_parts.append(syndrome_diff)
            response_parts.append("")
        
        # 权威资料整合
        if sources:
            authoritative_content = self._integrate_authoritative_sources(sources, query)
            if authoritative_content:
                response_parts.append("### 🌐 权威医学资料")
                response_parts.append(authoritative_content)
                response_parts.append("")
        
        # 专业治疗方案
        if intent == 'specific_treatment' or intent == 'prescription_request':
            treatment_plan = self._get_expert_treatment_plan(medical_category, body_parts, symptoms, analysis.get('severity'))
            if treatment_plan:
                response_parts.append("### 💊 专业治疗方案")
                response_parts.append(treatment_plan)
                response_parts.append("")
        
        # 预防保健
        prevention = self._get_expert_prevention(medical_category, body_parts)
        if prevention:
            response_parts.append("### 🛡️ 预防保健")
            response_parts.append(prevention)
            response_parts.append("")
        
        # 来源信息
        if sources:
            response_parts.append("### 📚 参考来源")
            response_parts.append(self._format_expert_sources(sources))
            response_parts.append("")
        
        # 专业提醒
        response_parts.append("### ⚠️ 专业提醒")
        response_parts.append("以上分析基于中医理论和现代医学研究，仅供学习参考。具体诊疗方案需要专业中医师根据患者的具体情况进行四诊合参后制定。如症状严重或持续不缓解，请及时就医。")
        
        return '\n'.join(response_parts)

    def _get_expert_pathogenesis(self, category: str, body_parts: List[str], symptoms: List[str]) -> str:
        """获取专家级病因病机分析"""
        pathogenesis_db = {
            '头部系统': "头为诸阳之会，清窍之府。头痛多因外感六淫、内伤七情、痰瘀阻络、气血亏虚等引起。《内经》云：'诸风掉眩，皆属于肝'，'头痛巅疾，下虚上实'。外感头痛多因风邪侵袭，内伤头痛多因肝阳上亢、痰浊上扰、瘀血阻络、气血不足等。",
            
            '腰背系统': "腰为肾之府，主骨生髓。《素问》云：'腰者，肾之府，转摇不能，肾将惫矣'。腰痛多因肾精亏虚、督脉不通、寒湿侵袭、瘀血阻络等引起。肾虚则骨失所养，督脉不通则腰脊强痛，寒湿阻络则腰部重着疼痛，瘀血内停则痛如针刺。",
            
            '胸腹系统': "胸为宗气之所，腹为脏腑之宅。胸腹疼痛多因气滞血瘀、寒凝气滞、湿热内蕴、脾胃虚弱等引起。《金匮要略》云：'胸痹心痛者，瓜蒌薤白白酒汤主之'。气滞则胀痛，血瘀则刺痛，寒凝则绞痛，湿热则灼痛。",
            
            '四肢系统': "四肢为诸阳之本，筋骨之用。《灵枢》云：'肝主筋，肾主骨'。四肢疼痛多因肝肾不足、风寒湿邪侵袭、气血瘀滞等引起。肝肾不足则筋骨失养，风寒湿邪侵袭则经络阻滞，气血瘀滞则不通而痛。"
        }
        
        return pathogenesis_db.get(category, "需要根据具体症状和体征进行详细的病因病机分析。")

    def _get_expert_syndrome_differentiation(self, category: str, body_parts: List[str], symptoms: List[str]) -> str:
        """获取专家级辨证分型"""
        syndrome_db = {
            '头部系统': """**外感头痛**：
• 风寒头痛：恶寒发热，头痛连项，苔薄白，脉浮紧
• 风热头痛：发热恶风，头胀痛，口渴，苔薄黄，脉浮数
• 风湿头痛：头重如裹，胸闷纳呆，苔白腻，脉濡

**内伤头痛**：
• 肝阳头痛：头痛眩晕，急躁易怒，面红目赤，脉弦数
• 痰浊头痛：头重昏蒙，胸脘满闷，苔白腻，脉滑
• 瘀血头痛：头痛如刺，痛处固定，舌紫暗，脉涩
• 血虚头痛：头痛绵绵，心悸失眠，面色无华，脉细弱""",

            '腰背系统': """**肾虚腰痛**：
• 肾阳虚：腰膝酸冷，畏寒肢冷，夜尿频多，舌淡苔白，脉沉迟
• 肾阴虚：腰酸膝软，五心烦热，潮热盗汗，舌红少苔，脉细数

**实证腰痛**：
• 寒湿腰痛：腰部冷痛重着，转侧不利，阴雨天加重，苔白腻，脉沉缓
• 湿热腰痛：腰痛伴热感，小便短赤，苔黄腻，脉濡数
• 瘀血腰痛：腰痛如刺，痛处固定，日轻夜重，舌紫暗，脉涩""",

            '胸腹系统': """**胸痛辨证**：
• 心血瘀阻：胸痛彻背，心悸气短，舌紫暗，脉涩
• 痰浊闭阻：胸闷如窒，咳嗽痰多，苔白腻，脉滑
• 寒凝心脉：胸痛剧烈，得温痛减，苔白，脉迟

**腹痛辨证**：
• 寒邪内阻：腹痛急暴，得温痛减，苔白，脉紧
• 湿热内蕴：腹痛灼热，大便不爽，苔黄腻，脉滑数
• 气滞血瘀：腹痛胀满，痛处固定，舌紫暗，脉弦涩""",

            '四肢系统': """**痹证辨证**：
• 行痹（风痹）：关节疼痛游走不定，苔薄白，脉浮
• 痛痹（寒痹）：关节疼痛较剧，痛处固定，得热痛减，苔白，脉弦紧
• 着痹（湿痹）：关节重着疼痛，肌肤麻木，苔白腻，脉濡缓
• 热痹：关节红肿热痛，发热口渴，苔黄，脉滑数

**虚证痹病**：
• 肝肾不足：关节酸痛，腰膝酸软，舌淡苔白，脉沉细
• 气血两虚：关节疼痛，面色无华，心悸气短，脉细弱"""
        }

        return syndrome_db.get(category, "需要根据具体症状进行详细的四诊合参辨证分型。")

    def _integrate_authoritative_sources(self, sources: List[Dict[str, Any]], query: str) -> str:
        """整合权威资料源"""
        if not sources:
            return ""

        integrated_parts = []
        for i, source in enumerate(sources[:3], 1):
            content = source.get('content', '').strip()
            source_name = source.get('source', f'权威资料{i}')
            score = source.get('score', 0)

            if content and len(content) > 30:
                # 智能提取关键信息
                key_info = self._extract_key_information(content, query)
                if key_info:
                    integrated_parts.append(f"**{i}. {source_name}** (相关度: {score:.2f})\n{key_info}")

        return '\n\n'.join(integrated_parts)

    def _extract_key_information(self, content: str, query: str) -> str:
        """智能提取关键信息"""
        # 分句处理
        sentences = re.split(r'[。！？；]', content)

        # 查询关键词
        query_keywords = set(re.findall(r'[\u4e00-\u9fff]+', query))

        # 找到最相关的句子
        relevant_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 10:
                sentence_keywords = set(re.findall(r'[\u4e00-\u9fff]+', sentence))
                overlap = len(query_keywords.intersection(sentence_keywords))
                if overlap >= 1:
                    relevant_sentences.append((sentence, overlap))

        # 按相关度排序，取前2句
        relevant_sentences.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in relevant_sentences[:2]]

        if top_sentences:
            return '。'.join(top_sentences) + '。'
        else:
            # 如果没有特别相关的，返回前面部分
            return content[:150] + "..." if len(content) > 150 else content

    def _get_expert_treatment_plan(self, category: str, body_parts: List[str], symptoms: List[str], severity: str) -> str:
        """获取专家级治疗方案"""
        treatment_db = {
            '头部系统': """**中药治疗**：
• 风寒头痛：川芎茶调散（川芎、白芷、羌活、防风、薄荷、茶叶、甘草）
• 风热头痛：桑菊饮（桑叶、菊花、薄荷、连翘、桔梗、甘草、芦根、杏仁）
• 肝阳头痛：天麻钩藤饮（天麻、钩藤、石决明、栀子、黄芩、川牛膝、杜仲、益母草、桑寄生、夜交藤、茯神）
• 血瘀头痛：血府逐瘀汤（当归、生地、桃仁、红花、枳壳、赤芍、柴胡、甘草、桔梗、川芎、牛膝）

**针灸治疗**：
• 主穴：百会、风池、太阳、合谷、太冲
• 配穴：风寒配风门、列缺；风热配曲池、外关；肝阳配行间、侠溪

**推拿治疗**：头面部按摩，重点按揉太阳、印堂、百会等穴""",

            '腰背系统': """**中药治疗**：
• 肾阳虚：右归丸（熟地、山药、山茱萸、枸杞、鹿角胶、菟丝子、杜仲、当归、肉桂、附子）
• 肾阴虚：左归丸（熟地、山药、枸杞、山茱萸、菟丝子、鹿角胶、龟板胶、牛膝）
• 寒湿腰痛：独活寄生汤（独活、桑寄生、杜仲、牛膝、细辛、秦艽、茯苓、肉桂心、防风、川芎、人参、甘草、当归、芍药、干地黄）
• 瘀血腰痛：身痛逐瘀汤（秦艽、川芎、桃仁、红花、甘草、羌活、没药、香附、五灵脂、地龙、当归、牛膝）

**针灸治疗**：
• 主穴：肾俞、腰阳关、委中、昆仑
• 配穴：肾虚配太溪、照海；寒湿配阴陵泉、足三里

**功能锻炼**：五点支撑、小燕飞、腰部旋转等动作""",

            '胸腹系统': """**中药治疗**：
• 心血瘀阻：血府逐瘀汤加减
• 痰浊闭阻：瓜蒌薤白白酒汤（瓜蒌、薤白、白酒）
• 寒凝心脉：当归四逆汤（当归、桂枝、芍药、细辛、甘草、木通、大枣）
• 脾胃虚寒：理中汤（人参、白术、干姜、甘草）

**针灸治疗**：
• 胸痛：内关、膻中、心俞、厥阴俞
• 腹痛：中脘、天枢、足三里、三阴交

**饮食调理**：温热易消化食物，避免生冷油腻""",

            '四肢系统': """**中药治疗**：
• 风痹：防风汤（防风、当归、赤芍、杏仁、黄芩、肉桂、茯苓、甘草、生姜、大枣）
• 寒痹：乌头汤（麻黄、芍药、黄芪、甘草、川乌）
• 湿痹：薏苡仁汤（薏苡仁、苍术、羌活、独活、防风、川芎、甘草、生姜）
• 热痹：白虎加桂枝汤（石膏、知母、甘草、粳米、桂枝）

**针灸治疗**：
• 上肢：肩髃、曲池、合谷、外关
• 下肢：环跳、阳陵泉、足三里、昆仑

**物理治疗**：热敷、推拿、理疗等"""
        }

        base_treatment = treatment_db.get(category, "需要根据具体证型制定个性化治疗方案。")

        # 根据严重程度调整
        if severity == 'severe':
            base_treatment += "\n\n**注意**：症状较重，建议立即就医，配合专业治疗，必要时住院观察。"
        elif severity == 'mild':
            base_treatment += "\n\n**建议**：症状较轻，可先尝试保守治疗，注意观察病情变化，如无改善及时就医。"

        return base_treatment

    def _get_expert_prevention(self, category: str, body_parts: List[str]) -> str:
        """获取专家级预防建议"""
        prevention_db = {
            '头部系统': """• **生活起居**：保持规律作息，避免熬夜，保证充足睡眠
• **情志调节**：保持心情舒畅，避免情绪激动，学会释放压力
• **饮食调理**：清淡饮食，少食辛辣刺激，多食新鲜蔬果
• **运动保健**：适当运动，如太极拳、八段锦等，促进气血流通
• **穴位保健**：经常按摩太阳、百会、风池等穴位""",

            '腰背系统': """• **姿势保持**：避免久坐久站，保持正确坐姿和站姿
• **腰部保暖**：注意腰部保暖，避免受寒受湿
• **适度运动**：进行腰部功能锻炼，如游泳、瑜伽等
• **睡眠环境**：选择合适的床垫，保持脊柱生理弯曲
• **重物搬运**：搬重物时注意技巧，避免突然弯腰""",

            '胸腹系统': """• **饮食规律**：定时定量，细嚼慢咽，避免暴饮暴食
• **情绪管理**：保持心情愉快，避免忧思过度
• **适当运动**：进行有氧运动，促进消化功能
• **腹部保暖**：注意腹部保暖，避免受凉
• **戒烟限酒**：戒烟限酒，减少对脏腑的损害""",

            '四肢系统': """• **关节保护**：避免关节过度负重，注意关节保暖
• **适度运动**：进行关节功能锻炼，保持关节灵活性
• **营养补充**：适当补充钙质和维生素D
• **避免外伤**：注意安全，避免关节外伤
• **定期检查**：定期进行关节功能检查"""
        }

        return prevention_db.get(category, "保持健康的生活方式，适当运动，注意休息，定期体检。")

    def _format_expert_sources(self, sources: List[Dict[str, Any]]) -> str:
        """格式化专家级来源信息"""
        if not sources:
            return "暂无参考来源"

        source_lines = []
        for i, source in enumerate(sources[:5], 1):
            source_name = source.get('source', f'资料{i}')
            source_type = "🌐 在线" if source.get('type') == 'online' else "📁 本地"
            score = source.get('score', 0)
            url = source.get('url', '')

            if url and url != 'internal_knowledge':
                source_lines.append(f"{i}. {source_type} {source_name} (相关度: {score:.2f}) - {url}")
            else:
                source_lines.append(f"{i}. {source_type} {source_name} (相关度: {score:.2f})")

        return '\n'.join(source_lines)

    def _generate_professional_response(self, query: str, analysis: Dict[str, Any], sources: List[Dict[str, Any]]) -> str:
        """生成专业级回答"""
        return f"""## 🔍 关于「{query}」的专业中医分析

### 📋 初步分析
根据您的问题，中医认为需要通过四诊合参来进行准确的辨证分析。

### 💡 治疗原则
中医治疗强调辨证论治，需要根据具体的症状表现、体质特点和病因病机来制定个性化的治疗方案。

### 🛡️ 建议
建议详细描述症状的具体表现、发病时间、伴随症状等，以便进行更准确的中医辨证分析。

⚠️ **重要提醒**：以上内容仅供参考，具体诊疗请咨询专业中医师。"""

    def _generate_intelligent_general_response(self, query: str, analysis: Dict[str, Any], sources: List[Dict[str, Any]]) -> str:
        """生成智能通用回答"""
        return f"""## 🤖 智能分析结果

根据您的问题「{query}」，我需要更多信息来提供准确的中医建议。

### 📝 建议补充信息
- 具体症状表现和特点
- 发病时间和诱发因素
- 伴随症状和体质情况
- 既往病史和治疗经历

### 💡 中医观点
中医学强调整体观念和辨证论治，需要通过望、闻、问、切四诊合参来明确证型，然后制定个性化的治疗方案。

⚠️ **温馨提示**：建议咨询专业中医师进行详细诊断。"""

    def _generate_emergency_response(self, query: str, analysis: Dict[str, Any] = None, sources: List[Dict[str, Any]] = None) -> str:
        """生成紧急情况回答"""
        return f"""## 🚨 紧急情况处理

您的问题「{query}」可能涉及紧急情况。

### ⚠️ 立即建议
1. **立即就医**：如症状严重，请立即前往医院急诊科
2. **保持冷静**：在等待就医期间保持冷静
3. **记录症状**：详细记录症状的发生时间、特点等

### 📞 紧急联系
- 急救电话：120
- 中毒急救：120
- 心理危机：************

⚠️ **重要提醒**：本系统无法替代专业医疗诊断，紧急情况请立即就医！"""

    def _load_ultra_knowledge_base(self) -> Dict[str, Any]:
        """加载超级知识库"""
        return {
            "version": "Ultra-4.0",
            "capabilities": ["深度分析", "智能去重", "专家诊断", "个性化治疗"],
            "quality_level": "Expert"
        }

# 创建全局超级智能实例
ultra_llm = UltraIntelligentLLM()
