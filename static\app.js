// Vue.js 应用 - 中医智能助手
const { createApp } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const app = createApp({
    data() {
        return {
            // 消息相关
            messages: [],
            currentMessage: '',
            isTyping: false,
            sessionId: null,
            
            // 语音相关
            voiceEnabled: true,
            autoSpeak: false,
            isRecording: false,
            recognition: null,
            synthesis: null,
            
            // 文档相关
            documents: [],
            showDocuments: false,
            uploadUrl: '/api/upload',
            
            // 快捷操作
            quickActions: [
                { icon: '🌿', text: '湿气调理', message: '湿气重有什么表现？如何调理？' },
                { icon: '🫖', text: '气血养生', message: '气血不足的症状和调养方法' },
                { icon: '🍃', text: '阴阳平衡', message: '什么是阴阳平衡？如何维持？' },
                { icon: '🌸', text: '四季养生', message: '四季养生的基本原则和方法' },
                { icon: '🏥', text: '中医基础', message: '中医基础理论的核心概念' },
                { icon: '🌱', text: '经络穴位', message: '经络学说和常用穴位介绍' }
            ],
            
            // WebSocket连接
            websocket: null,
            useWebSocket: false
        }
    },
    
    mounted() {
        this.initializeApp();
        this.loadDocuments();
        this.initializeVoice();
        
        // 添加欢迎消息
        this.addMessage('assistant', '您好！我是中医智能助手，可以为您解答中医相关问题。您可以：\n\n• 🎤 使用语音输入问题\n• 📁 上传PDF文档扩充知识库\n• 💡 点击左侧快捷查询\n• ✍️ 直接输入您的问题\n\n请问有什么可以帮助您的吗？');
    },
    
    beforeUnmount() {
        if (this.websocket) {
            this.websocket.close();
        }
        if (this.recognition) {
            this.recognition.stop();
        }
    },
    
    methods: {
        // 初始化应用
        initializeApp() {
            // 生成会话ID
            this.sessionId = this.generateSessionId();
            
            // 检查浏览器支持
            this.checkBrowserSupport();
            
            // 初始化WebSocket（可选）
            if (this.useWebSocket) {
                this.initializeWebSocket();
            }
        },
        
        // 生成会话ID
        generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        },
        
        // 检查浏览器支持
        checkBrowserSupport() {
            // 检查语音识别支持
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                this.voiceEnabled = false;
                console.warn('浏览器不支持语音识别');
            }
            
            // 检查语音合成支持
            if (!('speechSynthesis' in window)) {
                this.autoSpeak = false;
                console.warn('浏览器不支持语音合成');
            }
        },
        
        // 初始化语音功能
        initializeVoice() {
            if (!this.voiceEnabled) return;
            
            // 语音识别
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (SpeechRecognition) {
                this.recognition = new SpeechRecognition();
                this.recognition.continuous = false;
                this.recognition.interimResults = false;
                this.recognition.lang = 'zh-CN';
                
                this.recognition.onstart = () => {
                    this.isRecording = true;
                    ElMessage.info('🎤 正在录音，请说话...');
                };
                
                this.recognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript;
                    this.currentMessage = transcript;
                    ElMessage.success('🎤 语音识别完成');
                };
                
                this.recognition.onerror = (event) => {
                    ElMessage.error('语音识别失败: ' + event.error);
                    this.isRecording = false;
                };
                
                this.recognition.onend = () => {
                    this.isRecording = false;
                };
            }
            
            // 语音合成
            if ('speechSynthesis' in window) {
                this.synthesis = window.speechSynthesis;
            }
        },
        
        // 初始化WebSocket
        initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/chat`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                ElMessage.error('连接失败，将使用HTTP模式');
                this.useWebSocket = false;
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
            };
        },
        
        // 处理WebSocket消息
        handleWebSocketMessage(data) {
            switch (data.type) {
                case 'status':
                    // 显示状态信息
                    break;
                case 'response':
                    this.isTyping = false;
                    this.addMessage('assistant', data.data.response, data.data.sources);
                    if (this.autoSpeak) {
                        this.speakText(data.data.response);
                    }
                    break;
                case 'error':
                    this.isTyping = false;
                    ElMessage.error(data.data);
                    break;
            }
        },
        
        // 发送消息
        async sendMessage() {
            if (!this.currentMessage.trim() || this.isTyping) return;
            
            const message = this.currentMessage.trim();
            this.currentMessage = '';
            
            // 添加用户消息
            this.addMessage('user', message);
            this.isTyping = true;
            
            try {
                if (this.useWebSocket && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    // 使用WebSocket
                    this.websocket.send(JSON.stringify({
                        type: 'chat',
                        message: message,
                        session_id: this.sessionId
                    }));
                } else {
                    // 使用HTTP API
                    await this.sendHttpMessage(message);
                }
            } catch (error) {
                this.isTyping = false;
                ElMessage.error('发送失败: ' + error.message);
            }
        },
        
        // 发送HTTP消息
        async sendHttpMessage(message) {
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: this.sessionId,
                        use_voice: this.voiceEnabled
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                this.isTyping = false;
                
                // 添加助手回复
                this.addMessage('assistant', data.response, data.sources);
                
                // 自动朗读
                if (this.autoSpeak) {
                    this.speakText(data.response);
                }
                
            } catch (error) {
                this.isTyping = false;
                throw error;
            }
        },
        
        // 添加消息
        addMessage(type, content, sources = []) {
            const message = {
                id: Date.now() + Math.random(),
                type: type,
                content: content,
                sources: sources || [],
                timestamp: new Date().toLocaleTimeString()
            };
            
            this.messages.push(message);
            
            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },
        
        // 滚动到底部
        scrollToBottom() {
            const container = this.$refs.messagesContainer;
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        },
        
        // 格式化消息
        formatMessage(content) {
            // 简单的Markdown格式化
            return content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>')
                .replace(/#{1,6}\s*(.*)/g, '<h3>$1</h3>');
        },
        
        // 快捷消息
        sendQuickMessage(message) {
            this.currentMessage = message;
            this.sendMessage();
        },
        
        // 语音输入切换
        toggleVoiceInput() {
            if (!this.recognition) {
                ElMessage.warning('浏览器不支持语音识别');
                return;
            }
            
            if (this.isRecording) {
                this.recognition.stop();
            } else {
                this.recognition.start();
            }
        },
        
        // 语音朗读
        speakText(text) {
            if (!this.synthesis) return;
            
            // 停止当前朗读
            this.synthesis.cancel();
            
            // 创建语音
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 0.8;
            utterance.pitch = 1;
            
            // 查找中文语音
            const voices = this.synthesis.getVoices();
            const chineseVoice = voices.find(voice => voice.lang.includes('zh'));
            if (chineseVoice) {
                utterance.voice = chineseVoice;
            }
            
            this.synthesis.speak(utterance);
        },
        
        // 清空对话
        clearChat() {
            ElMessageBox.confirm('确定要清空所有对话记录吗？', '确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.messages = [];
                this.sessionId = this.generateSessionId();
                ElMessage.success('对话已清空');
            }).catch(() => {});
        },
        
        // 加载文档列表
        async loadDocuments() {
            try {
                const response = await fetch('/api/documents');
                const data = await response.json();
                this.documents = data.documents || [];
            } catch (error) {
                console.error('加载文档列表失败:', error);
            }
        },
        
        // 上传前检查
        beforeUpload(file) {
            const isValidType = ['application/pdf', 'application/msword', 
                               'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                               'text/plain'].includes(file.type);
            
            if (!isValidType) {
                ElMessage.error('只支持PDF、Word和文本文件！');
                return false;
            }
            
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                ElMessage.error('文件大小不能超过10MB！');
                return false;
            }
            
            return true;
        },
        
        // 上传成功
        handleUploadSuccess(response) {
            ElMessage.success('文档上传成功！');
            this.loadDocuments();
            
            // 添加系统消息
            const successCount = response.results.filter(r => r.status === 'success').length;
            this.addMessage('assistant', `📁 已成功处理 ${successCount} 个文档，知识库已更新。您现在可以询问相关内容了！`);
        },
        
        // 上传失败
        handleUploadError(error) {
            ElMessage.error('文档上传失败: ' + error.message);
        }
    }
});

// 使用Element Plus
app.use(ElementPlus);

// 挂载应用
app.mount('#app');
