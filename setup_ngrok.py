#!/usr/bin/env python3
"""
设置ngrok隧道的脚本
"""
import subprocess
import sys
import os
import time
import requests

def check_ngrok_installed():
    """检查ngrok是否已安装"""
    try:
        result = subprocess.run(['ngrok', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ ngrok已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ ngrok未安装")
    return False

def install_ngrok():
    """安装ngrok"""
    print("🔧 正在安装ngrok...")
    
    # 尝试使用pip安装pyngrok
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyngrok'], check=True)
        print("✅ pyngrok安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ pyngrok安装失败")
        return False

def setup_ngrok_tunnel():
    """设置ngrok隧道"""
    try:
        from pyngrok import ngrok
        
        print("🚀 启动ngrok隧道...")
        
        # 启动隧道，指向本地8008端口
        public_url = ngrok.connect(8008, auth="tcm_user:MVP168918")
        
        print(f"🌐 ngrok隧道已启动！")
        print(f"📱 公网访问地址: {public_url}")
        print(f"🔐 访问凭据:")
        print(f"   用户名: tcm_user")
        print(f"   密码: MVP168918")
        print()
        print("📋 分享给家人朋友的信息:")
        print("=" * 50)
        print(f"🏥 中医RAG智能诊疗系统")
        print(f"🌐 访问地址: {public_url}")
        print(f"🔐 登录信息:")
        print(f"   用户名: tcm_user")
        print(f"   密码: MVP168918")
        print("=" * 50)
        print()
        print("💡 使用说明:")
        print("1. 在手机浏览器中打开上述网址")
        print("2. 输入用户名和密码登录")
        print("3. 即可使用中医智能诊疗功能")
        print("4. 支持语音交互和文档上传")
        print()
        print("⚠️ 注意事项:")
        print("- 请妥善保管登录凭据")
        print("- 仅供家人朋友使用")
        print("- 隧道将保持运行直到程序结束")
        
        return public_url
        
    except ImportError:
        print("❌ pyngrok未安装，正在安装...")
        if install_ngrok():
            return setup_ngrok_tunnel()
        else:
            return None
    except Exception as e:
        print(f"❌ ngrok隧道启动失败: {e}")
        return None

def test_tunnel_access(url):
    """测试隧道访问"""
    try:
        print("🧪 测试隧道访问...")
        
        # 测试健康检查接口
        health_url = f"{url}/api/health"
        response = requests.get(health_url, auth=('tcm_user', 'MVP168918'), timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 隧道访问测试成功！")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
            return True
        else:
            print(f"❌ 隧道访问测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 隧道访问测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ngrok隧道设置程序")
    print("=" * 40)
    
    # 检查本地服务器是否运行
    try:
        response = requests.get("http://localhost:8008/api/health", 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            print("✅ 本地服务器运行正常")
        else:
            print("❌ 本地服务器响应异常")
            return
    except:
        print("❌ 本地服务器未运行，请先启动服务器")
        print("💡 运行命令: python simple_ultimate_tcm.py")
        return
    
    # 设置ngrok隧道
    public_url = setup_ngrok_tunnel()
    
    if public_url:
        # 测试隧道访问
        if test_tunnel_access(public_url):
            print("\n🎉 ngrok隧道设置完成！")
            print("📱 现在可以将访问信息分享给家人朋友了")
            
            # 保持隧道运行
            try:
                print("\n⏳ 隧道保持运行中... (按Ctrl+C停止)")
                while True:
                    time.sleep(60)
                    print(f"🔄 隧道运行中: {public_url}")
            except KeyboardInterrupt:
                print("\n🛑 隧道已停止")
        else:
            print("❌ 隧道设置失败")
    else:
        print("❌ 无法设置ngrok隧道")

if __name__ == "__main__":
    main()
