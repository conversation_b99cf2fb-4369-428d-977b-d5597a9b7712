#!/usr/bin/env python3
"""
模型部署方案分析
找到最适合您需求的DeepSeek部署方式
"""

def analyze_deployment_options():
    """分析部署选项"""
    
    options = {
        "LM Studio": {
            "需要启动软件": "是 - 需要打开LM Studio GUI",
            "服务方式": "GUI应用 + API服务",
            "优点": [
                "✅ 图形界面，易于管理",
                "✅ 自动模型管理",
                "✅ 内置API服务器",
                "✅ 您已经安装并有模型",
                "✅ 稳定可靠"
            ],
            "缺点": [
                "❌ 需要手动启动GUI",
                "❌ 占用桌面资源"
            ],
            "启动方式": "手动打开LM Studio → 启动服务器",
            "推荐度": "⭐⭐⭐⭐⭐"
        },
        
        "Transformers (直接调用)": {
            "需要启动软件": "否 - 直接在Python中调用",
            "服务方式": "Python库直接调用",
            "优点": [
                "✅ 无需额外软件",
                "✅ 直接集成到代码",
                "✅ 完全自动化",
                "✅ 支持Qwen3架构"
            ],
            "缺点": [
                "❌ 需要下载16GB模型",
                "❌ 首次加载较慢",
                "❌ 占用大量内存"
            ],
            "启动方式": "代码自动加载",
            "推荐度": "⭐⭐⭐⭐"
        },
        
        "Ollama": {
            "需要启动软件": "是 - 需要启动Ollama服务",
            "服务方式": "后台服务 + API",
            "优点": [
                "✅ 命令行管理",
                "✅ 自动模型管理",
                "✅ 轻量级服务"
            ],
            "缺点": [
                "❌ 需要手动启动服务",
                "❌ Windows支持较新"
            ],
            "启动方式": "命令行启动服务",
            "推荐度": "⭐⭐⭐"
        },
        
        "vLLM/SGLang": {
            "需要启动软件": "是 - 需要启动推理服务器",
            "服务方式": "专用推理服务器",
            "优点": [
                "✅ 最高性能",
                "✅ 生产级稳定"
            ],
            "缺点": [
                "❌ 需要手动启动服务",
                "❌ 配置复杂",
                "❌ 主要针对GPU"
            ],
            "启动方式": "命令行启动服务器",
            "推荐度": "⭐⭐"
        }
    }
    
    print("🔍 模型部署方案对比分析")
    print("=" * 80)
    
    for name, info in options.items():
        print(f"\n🛠️  {name}")
        print(f"   需要启动软件: {info['需要启动软件']}")
        print(f"   服务方式: {info['服务方式']}")
        print(f"   启动方式: {info['启动方式']}")
        print(f"   推荐度: {info['推荐度']}")
        print("   优点:")
        for pro in info['优点']:
            print(f"     {pro}")
        print("   缺点:")
        for con in info['缺点']:
            print(f"     {con}")
    
    return options

def recommend_best_solution():
    """推荐最佳解决方案"""
    print("\n" + "=" * 80)
    print("💡 基于您的需求分析")
    print("=" * 80)
    
    print("🎯 您的情况:")
    print("   ✅ 已安装LM Studio")
    print("   ✅ 已有DeepSeek模型")
    print("   ✅ 希望最小化额外软件启动")
    print("   ✅ 需要稳定可靠的服务")
    
    print("\n🏆 最佳推荐方案:")
    print("1️⃣  **LM Studio + 自动化启动脚本** (推荐)")
    print("   - 利用您现有的LM Studio和模型")
    print("   - 创建自动启动脚本")
    print("   - 最小化手动操作")
    
    print("\n2️⃣  **Transformers直接调用** (备选)")
    print("   - 完全无需额外软件")
    print("   - 但需要重新下载模型")
    
    return "LM Studio + 自动化"

def create_lmstudio_automation():
    """创建LM Studio自动化方案"""
    print("\n🔧 创建LM Studio自动化解决方案...")
    
    # 1. LM Studio启动脚本
    startup_script = '''@echo off
echo 🚀 启动LM Studio服务...

REM 检查LM Studio是否已运行
tasklist /FI "IMAGENAME eq LM Studio.exe" 2>NUL | find /I /N "LM Studio.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ LM Studio已在运行
) else (
    echo 📥 启动LM Studio...
    start "" "C:\\Users\\<USER>\\AppData\\Local\\LM Studio\\LM Studio.exe"
    echo ⏳ 等待LM Studio启动...
    timeout /t 10 /nobreak >nul
)

echo 🔄 检查API服务状态...
curl -s http://localhost:1234/v1/models >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LM Studio API服务已就绪
) else (
    echo ⚠️ 请在LM Studio中手动启动服务器
    echo 💡 操作步骤:
    echo    1. 打开LM Studio
    echo    2. 点击"Local Server"
    echo    3. 选择DeepSeek模型
    echo    4. 点击"Start Server"
)

pause
'''
    
    with open("start_lmstudio.bat", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    # 2. Python集成脚本
    integration_script = '''
import requests
import time
import subprocess
import os
from pathlib import Path

class LMStudioManager:
    """LM Studio自动化管理器"""
    
    def __init__(self):
        self.api_base = "http://localhost:1234/v1"
        self.lmstudio_path = self.find_lmstudio_path()
    
    def find_lmstudio_path(self):
        """查找LM Studio安装路径"""
        possible_paths = [
            Path.home() / "AppData/Local/LM Studio/LM Studio.exe",
            Path("C:/Program Files/LM Studio/LM Studio.exe"),
            Path("C:/Program Files (x86)/LM Studio/LM Studio.exe")
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        return None
    
    def is_lmstudio_running(self):
        """检查LM Studio是否运行"""
        try:
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq LM Studio.exe'],
                capture_output=True, text=True
            )
            return "LM Studio.exe" in result.stdout
        except:
            return False
    
    def is_api_ready(self):
        """检查API是否就绪"""
        try:
            response = requests.get(f"{self.api_base}/models", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_lmstudio(self):
        """启动LM Studio"""
        if not self.lmstudio_path:
            print("❌ 未找到LM Studio安装路径")
            return False
        
        if self.is_lmstudio_running():
            print("✅ LM Studio已在运行")
        else:
            print("🚀 启动LM Studio...")
            try:
                subprocess.Popen([self.lmstudio_path])
                time.sleep(10)  # 等待启动
            except Exception as e:
                print(f"❌ 启动失败: {e}")
                return False
        
        return True
    
    def wait_for_api(self, timeout=60):
        """等待API就绪"""
        print("⏳ 等待LM Studio API就绪...")
        
        for i in range(timeout):
            if self.is_api_ready():
                print("✅ LM Studio API已就绪")
                return True
            time.sleep(1)
            if i % 10 == 0:
                print(f"   等待中... ({i}/{timeout}秒)")
        
        print("❌ API启动超时")
        print("💡 请手动在LM Studio中启动服务器:")
        print("   1. 打开LM Studio")
        print("   2. 点击'Local Server'")
        print("   3. 选择DeepSeek模型")
        print("   4. 点击'Start Server'")
        return False
    
    def get_available_models(self):
        """获取可用模型"""
        try:
            response = requests.get(f"{self.api_base}/models")
            if response.status_code == 200:
                models = response.json()
                return [model['id'] for model in models.get('data', [])]
            return []
        except:
            return []
    
    def generate_response(self, prompt, model=None, max_tokens=512, temperature=0.7):
        """生成回答"""
        try:
            models = self.get_available_models()
            if not models:
                return "❌ 没有可用的模型"
            
            if model is None:
                model = models[0]  # 使用第一个可用模型
            
            response = requests.post(
                f"{self.api_base}/chat/completions",
                json={
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": max_tokens,
                    "temperature": temperature
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"❌ API调用失败: {response.status_code}"
                
        except Exception as e:
            return f"❌ 生成失败: {e}"
    
    def initialize(self):
        """初始化LM Studio服务"""
        print("🔧 初始化LM Studio服务...")
        
        # 启动LM Studio
        if not self.start_lmstudio():
            return False
        
        # 等待API就绪
        if not self.wait_for_api():
            return False
        
        # 检查可用模型
        models = self.get_available_models()
        if models:
            print(f"✅ 找到 {len(models)} 个可用模型:")
            for model in models:
                print(f"   - {model}")
            return True
        else:
            print("⚠️ 没有找到可用模型，请在LM Studio中加载模型")
            return False

# 使用示例
if __name__ == "__main__":
    manager = LMStudioManager()
    
    if manager.initialize():
        print("\\n🧪 测试生成...")
        response = manager.generate_response("中医是什么？")
        print(f"回答: {response}")
    else:
        print("❌ 初始化失败")
'''
    
    with open("lmstudio_manager.py", "w", encoding="utf-8") as f:
        f.write(integration_script)
    
    print("✅ LM Studio自动化脚本已创建:")
    print("   📄 start_lmstudio.bat - Windows启动脚本")
    print("   🐍 lmstudio_manager.py - Python集成管理器")

def main():
    """主函数"""
    print("🤖 DeepSeek模型部署方案分析")
    print("=" * 60)
    
    # 分析选项
    options = analyze_deployment_options()
    
    # 推荐方案
    recommended = recommend_best_solution()
    
    # 创建自动化方案
    create_lmstudio_automation()
    
    print("\n" + "=" * 80)
    print("🎉 解决方案已准备就绪!")
    print("=" * 80)
    print("💡 推荐使用方案: LM Studio + 自动化")
    print()
    print("🚀 下一步:")
    print("1. 运行: start_lmstudio.bat (启动LM Studio)")
    print("2. 测试: python lmstudio_manager.py")
    print("3. 集成到RAG系统")
    print()
    print("🎯 优势:")
    print("✅ 利用您现有的LM Studio和模型")
    print("✅ 最小化手动操作")
    print("✅ 稳定可靠的API服务")
    print("✅ 无需重新下载模型")

if __name__ == "__main__":
    main()
