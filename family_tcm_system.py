"""
家庭中医智能助手系统
为家人朋友提供24/7中医知识查询服务
"""
import os
import sys
import time
import json
import logging
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime
import streamlit as st
from enhanced_rag_system import enhanced_rag_system
from session_manager import session_manager
from document_processor import doc_processor

class FamilyTCMSystem:
    def __init__(self):
        self.system_name = "家庭中医智能助手"
        self.version = "1.0.0"
        self.initialized = False
        self.knowledge_base_path = Path("family_tcm_knowledge")
        self.user_logs_path = Path("user_logs")
        self.setup_directories()
        self.setup_logging()
    
    def setup_directories(self):
        """设置系统目录"""
        self.knowledge_base_path.mkdir(exist_ok=True)
        self.user_logs_path.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.knowledge_base_path / "documents").mkdir(exist_ok=True)
        (self.knowledge_base_path / "processed").mkdir(exist_ok=True)
        (self.knowledge_base_path / "backups").mkdir(exist_ok=True)
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.user_logs_path / f"tcm_system_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def initialize_system(self) -> bool:
        """初始化系统"""
        try:
            self.logger.info("🏥 初始化家庭中医智能助手系统...")
            
            # 初始化增强RAG系统
            if not enhanced_rag_system.initialize():
                self.logger.error("❌ RAG系统初始化失败")
                return False
            
            self.initialized = True
            self.logger.info("✅ 家庭中医智能助手系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 系统初始化失败: {e}")
            return False
    
    def add_tcm_documents(self, pdf_files: List[str]) -> Dict:
        """批量添加中医文档到知识库"""
        results = {
            "success": [],
            "failed": [],
            "total_processed": 0,
            "total_chunks": 0
        }
        
        self.logger.info(f"📚 开始处理 {len(pdf_files)} 个中医文档...")
        
        for pdf_file in pdf_files:
            try:
                self.logger.info(f"📄 处理文档: {pdf_file}")
                
                # 使用强化处理器处理PDF
                from robust_tcm_processor import process_tcm_pdf_robust
                success = process_tcm_pdf_robust(pdf_file)
                
                if success:
                    results["success"].append(pdf_file)
                    self.logger.info(f"✅ 文档处理成功: {pdf_file}")
                else:
                    results["failed"].append(pdf_file)
                    self.logger.error(f"❌ 文档处理失败: {pdf_file}")
                
                results["total_processed"] += 1
                
            except Exception as e:
                results["failed"].append(pdf_file)
                self.logger.error(f"❌ 处理文档时出错 {pdf_file}: {e}")
        
        # 重新加载索引
        if results["success"]:
            doc_processor.load_index()
            results["total_chunks"] = len(doc_processor.document_chunks)
            self.logger.info(f"📊 知识库更新完成，共包含 {results['total_chunks']} 个知识块")
        
        return results
    
    def query_tcm_knowledge(self, question: str, user_id: str = "anonymous") -> Dict:
        """查询中医知识"""
        try:
            # 记录用户查询
            self.log_user_query(user_id, question)
            
            # 获取或创建用户会话
            session_id = f"{user_id}_{datetime.now().strftime('%Y%m%d')}"
            
            # 使用增强RAG系统回答
            result = enhanced_rag_system.retrieve_and_generate(question, session_id)
            
            # 记录回答
            if "answer" in result:
                self.log_user_answer(user_id, question, result["answer"])
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 查询处理失败: {e}")
            return {"error": f"查询处理失败: {str(e)}"}
    
    def log_user_query(self, user_id: str, question: str):
        """记录用户查询"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "question": question,
            "type": "query"
        }
        
        log_file = self.user_logs_path / f"queries_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    
    def log_user_answer(self, user_id: str, question: str, answer: str):
        """记录系统回答"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "question": question,
            "answer": answer,
            "type": "answer"
        }
        
        log_file = self.user_logs_path / f"answers_{datetime.now().strftime('%Y%m')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        try:
            stats = {
                "system_name": self.system_name,
                "version": self.version,
                "initialized": self.initialized,
                "knowledge_base": {
                    "total_documents": 0,
                    "total_chunks": len(doc_processor.document_chunks) if doc_processor.document_chunks else 0,
                    "last_updated": None
                },
                "usage_stats": self.get_usage_stats(),
                "system_health": self.check_system_health()
            }
            
            # 统计文档数量
            docs_path = self.knowledge_base_path / "documents"
            if docs_path.exists():
                stats["knowledge_base"]["total_documents"] = len(list(docs_path.glob("*.pdf")))
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ 获取系统统计失败: {e}")
            return {"error": str(e)}
    
    def get_usage_stats(self) -> Dict:
        """获取使用统计"""
        try:
            today = datetime.now().strftime('%Y%m%d')
            this_month = datetime.now().strftime('%Y%m')
            
            # 统计今日查询
            today_queries = 0
            query_file = self.user_logs_path / f"queries_{this_month}.jsonl"
            if query_file.exists():
                with open(query_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line)
                            if entry["timestamp"].startswith(today):
                                today_queries += 1
                        except:
                            continue
            
            return {
                "today_queries": today_queries,
                "active_users": self.count_active_users(),
                "uptime": self.get_uptime()
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def count_active_users(self) -> int:
        """统计活跃用户数"""
        try:
            today = datetime.now().strftime('%Y%m%d')
            users = set()
            
            this_month = datetime.now().strftime('%Y%m')
            query_file = self.user_logs_path / f"queries_{this_month}.jsonl"
            
            if query_file.exists():
                with open(query_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            entry = json.loads(line)
                            if entry["timestamp"].startswith(today):
                                users.add(entry["user_id"])
                        except:
                            continue
            
            return len(users)
            
        except:
            return 0
    
    def get_uptime(self) -> str:
        """获取系统运行时间"""
        # 简化版本，实际可以记录启动时间
        return "24/7 运行中"

    def get_network_access_info(self) -> Dict:
        """获取网络访问信息"""
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            return {
                "local_access": "http://localhost:8511",
                "network_access": f"http://{local_ip}:8511",
                "hostname": hostname,
                "local_ip": local_ip
            }
        except:
            return {
                "local_access": "http://localhost:8511",
                "network_access": "获取失败",
                "hostname": "未知",
                "local_ip": "未知"
            }
    
    def check_system_health(self) -> Dict:
        """检查系统健康状态"""
        health = {
            "status": "healthy",
            "issues": []
        }
        
        try:
            # 检查RAG系统
            if not enhanced_rag_system.initialized:
                health["issues"].append("RAG系统未初始化")
                health["status"] = "warning"
            
            # 检查知识库
            if not doc_processor.document_chunks:
                health["issues"].append("知识库为空")
                health["status"] = "warning"
            
            # 检查磁盘空间
            import shutil
            free_space = shutil.disk_usage('.').free / (1024**3)  # GB
            if free_space < 1:  # 小于1GB
                health["issues"].append(f"磁盘空间不足: {free_space:.1f}GB")
                health["status"] = "warning"
            
            if health["issues"]:
                health["status"] = "warning" if len(health["issues"]) < 3 else "error"
            
        except Exception as e:
            health["status"] = "error"
            health["issues"].append(f"健康检查失败: {str(e)}")
        
        return health
    
    def backup_knowledge_base(self) -> bool:
        """备份知识库"""
        try:
            import shutil
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = self.knowledge_base_path / "backups" / f"backup_{timestamp}"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 备份向量数据库
            vector_db_path = Path("vector_db")
            if vector_db_path.exists():
                shutil.copytree(vector_db_path, backup_dir / "vector_db")
            
            self.logger.info(f"✅ 知识库备份完成: {backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 知识库备份失败: {e}")
            return False

# 全局家庭中医系统实例
family_tcm_system = FamilyTCMSystem()
