#!/usr/bin/env python3
"""
终极中医RAG系统 - API版本
使用LM Studio API调用DeepSeek模型，支持Docker移植
"""

# 页面配置 - 必须在最开始
import streamlit as st
st.set_page_config(
    page_title="🧙‍♂️ 智者·中医AI助手 (API版)",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import numpy as np
import requests
from bs4 import BeautifulSoup
import time
import logging
from typing import Dict, List, Any
import threading
import gc

# 导入我们的API管理器
from deepseek_api_manager import DeepSeekAPIManager

# 文档处理
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    st.warning("⚠️ PDF处理不可用，请运行: pip install PyPDF2")

try:
    import docx
    import pandas as pd
    MULTI_FORMAT_AVAILABLE = True
except ImportError:
    MULTI_FORMAT_AVAILABLE = False

# 向量搜索
try:
    import faiss
    from sentence_transformers import SentenceTransformer
    VECTOR_SEARCH_AVAILABLE = True
except ImportError:
    VECTOR_SEARCH_AVAILABLE = False
    st.warning("⚠️ 向量搜索不可用，请运行: pip install faiss-cpu sentence-transformers")

# 语音功能
try:
    import pyttsx3
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    st.warning("⚠️ 语音功能不可用，请运行: pip install pyttsx3 SpeechRecognition")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'VECTOR_DB_PATH': './working_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CONVERSATION_PATH': './conversations',
    'CHUNK_SIZE': 800,
    'CHUNK_OVERLAP': 100,
    'TOP_K': 5,
    'BATCH_SIZE': 32,
    'MAX_FILE_SIZE': 200 * 1024 * 1024,  # 200MB
    'ANCIENT_BOOKS_URLS': [
        'https://chinesebooks.github.io/gudaiyishu/yizongjinjian/',
        'https://chinesebooks.github.io/gudaiyishu/huangdineijing/',
        'https://chinesebooks.github.io/gudaiyishu/shanghan/',
        'https://chinesebooks.github.io/gudaiyishu/jinkuiyaolue/',
        'https://chinesebooks.github.io/gudaiyishu/bencaogangmu/'
    ]
}

class WorkingVoiceManager:
    """语音管理器"""

    def __init__(self):
        self.tts_engine = None
        self.recognizer = None
        self.microphone = None
        self.voice_available = VOICE_AVAILABLE

        if self.voice_available:
            try:
                self.tts_engine = pyttsx3.init()
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()

                self.tts_engine.setProperty('rate', 180)
                self.tts_engine.setProperty('volume', 0.9)

                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)

                logger.info("语音管理器初始化成功")

            except Exception as e:
                logger.error(f"语音管理器初始化失败: {e}")
                self.voice_available = False

    def speak_text_async(self, text: str):
        """异步语音播放"""
        if not self.voice_available:
            return

        def speak_worker():
            try:
                clean_text = re.sub(r'[#*`\[\]()]', '', text)
                clean_text = re.sub(r'https?://\S+', '', clean_text)
                clean_text = clean_text.replace('\n', ' ').strip()

                if len(clean_text) > 300:
                    clean_text = clean_text[:300] + "..."

                self.tts_engine.say(clean_text)
                self.tts_engine.runAndWait()
            except Exception as e:
                logger.error(f"语音播放失败: {e}")

        threading.Thread(target=speak_worker, daemon=True).start()

    def listen_for_speech(self, timeout: int = 10) -> str:
        """监听语音输入"""
        if not self.voice_available:
            return None

        try:
            with self.microphone as source:
                st.info("🎤 正在监听，请说话...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=15)

            st.info("🔄 正在识别语音...")
            text = self.recognizer.recognize_google(audio, language='zh-CN')
            return text

        except sr.WaitTimeoutError:
            st.warning("⏰ 语音输入超时")
            return None
        except sr.UnknownValueError:
            st.warning("🤷 无法识别语音内容，请重试")
            return None
        except Exception as e:
            st.error(f"❌ 语音识别失败: {e}")
            return None

class WorkingConversationManager:
    """对话管理器"""

    def __init__(self):
        self.conversations = []
        self.current_session_id = self._generate_session_id()
        self.user_profile = {}
        self.max_history = 20

        os.makedirs(CONFIG['CONVERSATION_PATH'], exist_ok=True)
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._load_conversation()

    def _generate_session_id(self) -> str:
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def _load_conversation(self):
        try:
            if self.conversation_file.exists():
                with open(self.conversation_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversations = data.get('conversations', [])
                    self.user_profile = data.get('user_profile', {})
        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")

    def _save_conversation(self):
        try:
            data = {
                'session_id': self.current_session_id,
                'conversations': self.conversations,
                'user_profile': self.user_profile,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.conversation_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存对话历史失败: {e}")

    def add_conversation(self, user_input: str, assistant_response: str, pdf_sources: List = None, ancient_sources: List = None):
        conversation = {
            'id': len(self.conversations) + 1,
            'timestamp': datetime.now().isoformat(),
            'user_input': user_input,
            'assistant_response': assistant_response,
            'pdf_sources': pdf_sources or [],
            'ancient_sources': ancient_sources or []
        }

        self.conversations.append(conversation)

        if len(self.conversations) > self.max_history:
            self.conversations = self.conversations[-self.max_history:]

        self._update_user_profile(user_input)
        self._save_conversation()

    def _update_user_profile(self, user_input: str):
        symptoms = self.user_profile.get('symptoms', set())

        symptom_keywords = {
            '湿气': ['湿气', '湿重', '湿邪', '痰湿'],
            '失眠': ['失眠', '睡不着', '多梦', '易醒'],
            '头痛': ['头痛', '头疼', '偏头痛', '头晕'],
            '胃痛': ['胃痛', '胃疼', '腹痛', '胃胀'],
            '咳嗽': ['咳嗽', '咳痰', '干咳'],
            '气血不足': ['气虚', '血虚', '气血不足', '乏力'],
        }

        for symptom, keywords in symptom_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                symptoms.add(symptom)

        self.user_profile['symptoms'] = list(symptoms)

    def get_conversation_context(self, current_query: str) -> str:
        context_parts = []

        if self.user_profile:
            if self.user_profile.get('symptoms'):
                context_parts.append(f"用户已知症状: {', '.join(self.user_profile['symptoms'])}")

        recent_conversations = self.conversations[-3:]
        for conv in recent_conversations:
            context_parts.append(f"历史问题: {conv['user_input']}")

        context_parts.append(f"当前问题: {current_query}")
        return "\n".join(context_parts)

    def get_conversation_summary(self) -> Dict:
        return {
            'session_id': self.current_session_id,
            'total_conversations': len(self.conversations),
            'user_profile': self.user_profile
        }

    def clear_conversation(self):
        self.conversations = []
        self.user_profile = {}
        self.current_session_id = self._generate_session_id()
        self.conversation_file = Path(CONFIG['CONVERSATION_PATH']) / f"session_{self.current_session_id}.json"
        self._save_conversation()

class AncientBooksRetriever:
    """古代医书检索器"""

    def __init__(self):
        self.cache = {}
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

    def search_ancient_books(self, query: str, max_results: int = 3) -> List[Dict]:
        """搜索古代医书"""
        results = []

        try:
            for url in CONFIG['ANCIENT_BOOKS_URLS']:
                book_results = self._search_single_book(url, query)
                results.extend(book_results)

            # 按相关度排序
            results.sort(key=lambda x: x.get('relevance', 0), reverse=True)
            return results[:max_results]

        except Exception as e:
            logger.error(f"古代医书搜索失败: {e}")
            return []

    def _search_single_book(self, url: str, query: str) -> List[Dict]:
        """搜索单本古代医书"""
        try:
            if url in self.cache:
                content = self.cache[url]
            else:
                response = requests.get(url, headers=self.headers, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    content = soup.get_text()
                    self.cache[url] = content
                else:
                    return []

            # 搜索相关内容
            relevant_passages = self._extract_relevant_passages(content, query)

            book_name = self._extract_book_name(url)
            results = []

            for passage in relevant_passages:
                relevance = self._calculate_relevance(query, passage)
                if relevance > 0.3:
                    results.append({
                        'title': book_name,
                        'content': passage,
                        'url': url,
                        'relevance': relevance,
                        'source_type': 'ancient_book'
                    })

            return results

        except Exception as e:
            logger.error(f"搜索 {url} 失败: {e}")
            return []

    def _extract_relevant_passages(self, content: str, query: str) -> List[str]:
        """提取相关段落"""
        sentences = re.split(r'[。！？]', content)
        relevant_passages = []

        query_chars = set(query)

        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) < 10:
                continue

            if any(char in sentence for char in query_chars):
                context = self._get_context(sentences, sentence, 2)
                if len(context) > 50:
                    relevant_passages.append(context)

        return relevant_passages[:5]

    def _get_context(self, sentences: List[str], target_sentence: str, context_size: int) -> str:
        """获取上下文"""
        try:
            index = sentences.index(target_sentence)
            start = max(0, index - context_size)
            end = min(len(sentences), index + context_size + 1)
            return '。'.join(sentences[start:end]) + '。'
        except ValueError:
            return target_sentence

    def _extract_book_name(self, url: str) -> str:
        """提取书名"""
        book_names = {
            'yizongjinjian': '医宗金鉴',
            'huangdineijing': '黄帝内经',
            'shanghan': '伤寒论',
            'jinkuiyaolue': '金匮要略',
            'bencaogangmu': '本草纲目'
        }

        for key, name in book_names.items():
            if key in url:
                return name

        return '古代医书'

    def _calculate_relevance(self, query: str, content: str) -> float:
        """计算相关度"""
        query_chars = set(query)
        content_chars = set(content)
        intersection = query_chars.intersection(content_chars)

        if not query_chars:
            return 0.0

        char_relevance = len(intersection) / len(query_chars)

        # 检查关键词匹配
        keyword_bonus = 0.0
        medical_keywords = ['治', '方', '药', '症', '病', '疗', '调', '补']
        for keyword in medical_keywords:
            if keyword in query and keyword in content:
                keyword_bonus += 0.1

        return min(char_relevance + keyword_bonus, 1.0)

class WorkingDocumentProcessor:
    """文档处理器"""

    def __init__(self):
        self.supported_formats = ['.pdf', '.txt']
        if MULTI_FORMAT_AVAILABLE:
            self.supported_formats.extend(['.docx', '.doc'])

    def process_file(self, file_path: Path, file_name: str):
        try:
            file_size = os.path.getsize(file_path)
            if file_size > CONFIG['MAX_FILE_SIZE']:
                st.warning(f"⚠️ 文件 {file_name} 过大 ({file_size/1024/1024:.1f}MB)")
                return [], []

            extension = Path(file_name).suffix.lower()

            if extension == '.pdf' and PDF_AVAILABLE:
                return self._process_pdf(file_path, file_name)
            elif extension == '.txt':
                return self._process_txt(file_path, file_name)
            elif extension in ['.docx', '.doc'] and MULTI_FORMAT_AVAILABLE:
                return self._process_word(file_path, file_name)
            else:
                st.warning(f"⚠️ 不支持的文件格式: {extension}")
                return [], []

        except Exception as e:
            st.error(f"❌ 处理文件失败 {file_name}: {e}")
            return [], []

    def _process_pdf(self, file_path: Path, file_name: str):
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_parts = []

                total_pages = len(pdf_reader.pages)
                max_pages = min(total_pages, 100)

                for i in range(max_pages):
                    try:
                        page_text = pdf_reader.pages[i].extract_text()
                        if page_text.strip():
                            text_parts.append(page_text.strip())
                    except Exception as e:
                        logger.warning(f"页面 {i+1} 处理失败: {e}")
                        continue

                full_text = '\n'.join(text_parts)
                chunks = self._split_text(full_text)

                metadata = []
                for i, chunk in enumerate(chunks):
                    meta = {
                        'source': file_name,
                        'chunk_id': f"{file_name}_{i}",
                        'chunk_index': i,
                        'content': chunk,
                        'upload_time': datetime.now().isoformat(),
                        'file_type': '.pdf',
                        'total_pages': total_pages
                    }
                    metadata.append(meta)

                return chunks, metadata

        except Exception as e:
            st.error(f"PDF处理失败: {e}")
            return [], []

    def _process_txt(self, file_path: Path, file_name: str):
        try:
            encodings = ['utf-8', 'gbk', 'gb2312']
            text = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    break
                except UnicodeDecodeError:
                    continue

            if text is None:
                st.error(f"❌ 无法读取文本文件 {file_name}")
                return [], []

            chunks = self._split_text(text)

            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': '.txt'
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            st.error(f"文本文件处理失败: {e}")
            return [], []

    def _split_text(self, text: str) -> List[str]:
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']

        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            if len(current_chunk) + len(paragraph) <= chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())

                if len(paragraph) <= chunk_size:
                    current_chunk = paragraph + "\n\n"
                else:
                    start = 0
                    while start < len(paragraph):
                        end = start + chunk_size
                        if end > len(paragraph):
                            end = len(paragraph)

                        chunk = paragraph[start:end]
                        if end < len(paragraph) and '。' in chunk:
                            last_period = chunk.rfind('。')
                            if last_period > chunk_size // 2:
                                end = start + last_period + 1
                                chunk = paragraph[start:end]

                        chunks.append(chunk.strip())
                        start = end - overlap

                        if start >= len(paragraph):
                            break

                    current_chunk = ""

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return [chunk for chunk in chunks if len(chunk.strip()) > 20]

class WorkingVectorDatabase:
    """向量数据库"""

    def __init__(self):
        self.embedding_model = None
        self.index = None
        self.metadata = []
        self.db_path = Path(CONFIG['VECTOR_DB_PATH'])
        self.db_path.mkdir(exist_ok=True)

        if VECTOR_SEARCH_AVAILABLE:
            self._initialize_embedding_model()
            self._load_database()

    def _initialize_embedding_model(self):
        try:
            self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
            logger.info(f"嵌入模型加载成功: {CONFIG['EMBEDDING_MODEL']}")
        except Exception as e:
            logger.error(f"嵌入模型加载失败: {e}")
            st.error(f"❌ 嵌入模型加载失败: {e}")

    def _load_database(self):
        try:
            index_file = self.db_path / "faiss_index.bin"
            metadata_file = self.db_path / "metadata.pkl"

            if index_file.exists() and metadata_file.exists():
                self.index = faiss.read_index(str(index_file))
                with open(metadata_file, 'rb') as f:
                    self.metadata = pickle.load(f)
                logger.info(f"向量数据库加载成功，包含 {len(self.metadata)} 个文档块")
            else:
                logger.info("未找到现有数据库，将创建新的数据库")
        except Exception as e:
            logger.error(f"数据库加载失败: {e}")

    def _save_database(self):
        try:
            if self.index is not None:
                index_file = self.db_path / "faiss_index.bin"
                metadata_file = self.db_path / "metadata.pkl"

                faiss.write_index(self.index, str(index_file))
                with open(metadata_file, 'wb') as f:
                    pickle.dump(self.metadata, f)

                logger.info("向量数据库保存成功")
        except Exception as e:
            logger.error(f"数据库保存失败: {e}")

    def add_documents(self, chunks: List[str], metadata: List[Dict]):
        if not VECTOR_SEARCH_AVAILABLE or not self.embedding_model:
            st.error("❌ 向量搜索功能不可用")
            return False

        try:
            embeddings = self.embedding_model.encode(chunks, batch_size=CONFIG['BATCH_SIZE'])

            if self.index is None:
                dimension = embeddings.shape[1]
                self.index = faiss.IndexFlatIP(dimension)

            faiss.normalize_L2(embeddings)
            self.index.add(embeddings.astype('float32'))
            self.metadata.extend(metadata)

            self._save_database()
            return True

        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            st.error(f"❌ 添加文档失败: {e}")
            return False

    def search(self, query: str, top_k: int = CONFIG['TOP_K']) -> List[Dict]:
        if not VECTOR_SEARCH_AVAILABLE or not self.embedding_model or self.index is None:
            return []

        try:
            query_embedding = self.embedding_model.encode([query])
            faiss.normalize_L2(query_embedding)

            scores, indices = self.index.search(query_embedding.astype('float32'), top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.metadata):
                    result = self.metadata[idx].copy()
                    result['similarity'] = float(score)
                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []

# 初始化全局组件
@st.cache_resource
def initialize_components():
    """初始化所有组件"""
    components = {}

    # 初始化DeepSeek API
    components['deepseek_api'] = DeepSeekAPIManager()

    # 初始化其他组件
    components['voice_manager'] = WorkingVoiceManager()
    components['conversation_manager'] = WorkingConversationManager()
    components['document_processor'] = WorkingDocumentProcessor()
    components['vector_db'] = WorkingVectorDatabase()
    components['ancient_books'] = AncientBooksRetriever()

    return components

def generate_intelligent_response(query: str, pdf_results: List[Dict], ancient_results: List[Dict], conversation_context: str, deepseek_api: DeepSeekAPIManager) -> str:
    """生成智能回答"""

    # 构建提示词
    prompt_parts = []

    # 系统角色
    prompt_parts.append("""你是一位资深的中医专家，具有深厚的理论基础和丰富的临床经验。请根据以下信息为用户提供专业、准确、有用的中医建议。

请注意：
1. 基于提供的文献资料和古籍内容回答
2. 结合现代医学知识，但以中医理论为主
3. 提供具体的治疗建议和方剂推荐
4. 强调个体差异，建议专业诊断
5. 回答要结构清晰，易于理解""")

    # 添加PDF检索结果
    if pdf_results:
        prompt_parts.append("\n📚 相关PDF文档内容：")
        for i, result in enumerate(pdf_results[:3], 1):
            prompt_parts.append(f"{i}. 来源：{result.get('source', '未知')}")
            prompt_parts.append(f"   内容：{result.get('content', '')[:300]}...")
            prompt_parts.append(f"   相似度：{result.get('similarity', 0):.2f}")

    # 添加古籍检索结果
    if ancient_results:
        prompt_parts.append("\n📜 古代医书相关内容：")
        for i, result in enumerate(ancient_results[:3], 1):
            prompt_parts.append(f"{i}. 出处：{result.get('title', '古代医书')}")
            prompt_parts.append(f"   内容：{result.get('content', '')[:300]}...")

    # 添加对话上下文
    if conversation_context:
        prompt_parts.append(f"\n💬 对话上下文：\n{conversation_context}")

    # 添加用户问题
    prompt_parts.append(f"\n❓ 用户问题：{query}")

    prompt_parts.append("""\n请基于以上信息，提供专业的中医建议。回答格式：

## 🔍 问题分析
[分析用户问题的中医病机]

## 📋 诊断建议
[从中医角度的可能诊断]

## 💊 治疗方案
[具体的治疗建议，包括方剂、穴位等]

## ⚠️ 注意事项
[用药注意事项和生活建议]

## 📚 文献依据
[引用的古籍或现代研究]""")

    full_prompt = "\n".join(prompt_parts)

    # 调用DeepSeek API生成回答
    try:
        response = deepseek_api.generate_response(
            full_prompt,
            max_tokens=2048,
            temperature=0.7
        )

        if "出错" in response or "失败" in response:
            # 如果API调用失败，提供备用回答
            return generate_fallback_response(query, pdf_results, ancient_results)

        return response

    except Exception as e:
        logger.error(f"DeepSeek API调用失败: {e}")
        return generate_fallback_response(query, pdf_results, ancient_results)

def generate_fallback_response(query: str, pdf_results: List[Dict], ancient_results: List[Dict]) -> str:
    """生成备用回答（当DeepSeek不可用时）"""

    response_parts = []

    response_parts.append("## 🔍 基于检索结果的分析")
    response_parts.append(f"针对您的问题「{query}」，我为您检索了相关的中医文献资料：")

    if pdf_results:
        response_parts.append("\n### 📚 PDF文档检索结果：")
        for i, result in enumerate(pdf_results[:3], 1):
            response_parts.append(f"**{i}. {result.get('source', '文档')}** (相似度: {result.get('similarity', 0):.2f})")
            response_parts.append(f"{result.get('content', '')[:200]}...")
            response_parts.append("")

    if ancient_results:
        response_parts.append("### 📜 古代医书相关内容：")
        for i, result in enumerate(ancient_results[:3], 1):
            response_parts.append(f"**{i}. {result.get('title', '古籍')}**")
            response_parts.append(f"{result.get('content', '')[:200]}...")
            response_parts.append("")

    if not pdf_results and not ancient_results:
        response_parts.append("抱歉，暂未找到直接相关的文献资料。建议您：")
        response_parts.append("1. 尝试使用更具体的中医术语")
        response_parts.append("2. 上传相关的中医PDF文档")
        response_parts.append("3. 咨询专业中医师")

    response_parts.append("\n## ⚠️ 重要提醒")
    response_parts.append("以上内容仅供参考，具体诊断和治疗请咨询专业中医师。")

    return "\n".join(response_parts)

def main():
    """主函数"""

    # 初始化组件
    components = initialize_components()

    # 页面标题
    st.title("🧙‍♂️ 智者·中医AI助手 (API版)")
    st.markdown("---")

    # 检查DeepSeek API状态
    with st.sidebar:
        st.header("🧠 DeepSeek模型状态")

        if st.button("🔄 检查模型状态"):
            with st.spinner("检查中..."):
                if components['deepseek_api'].initialize():
                    st.success("✅ DeepSeek API可用")
                    if components['deepseek_api'].test_generation():
                        st.success("✅ 生成功能正常")
                    else:
                        st.warning("⚠️ 生成功能异常")
                else:
                    st.error("❌ DeepSeek API不可用")
                    st.info("💡 请确保LM Studio运行并加载了DeepSeek模型")

        st.markdown("---")

        # 对话管理
        st.header("💬 对话管理")

        conv_summary = components['conversation_manager'].get_conversation_summary()
        st.info(f"会话ID: {conv_summary['session_id']}")
        st.info(f"对话数: {conv_summary['total_conversations']}")

        if conv_summary['user_profile'].get('symptoms'):
            st.info(f"已识别症状: {', '.join(conv_summary['user_profile']['symptoms'])}")

        if st.button("🗑️ 清空对话"):
            components['conversation_manager'].clear_conversation()
            st.success("对话已清空")
            st.rerun()

        st.markdown("---")

        # 语音功能
        st.header("🎤 语音功能")

        voice_enabled = st.checkbox("启用语音播放", value=True)

        if st.button("🎤 语音输入") and components['voice_manager'].voice_available:
            with st.spinner("正在监听..."):
                speech_text = components['voice_manager'].listen_for_speech()
                if speech_text:
                    st.session_state['voice_input'] = speech_text
                    st.success(f"识别到: {speech_text}")

    # 主界面
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("💬 智能对话")

        # 获取用户输入
        user_input = st.text_area(
            "请输入您的中医问题：",
            value=st.session_state.get('voice_input', ''),
            height=100,
            placeholder="例如：我最近失眠多梦，舌苔厚腻，应该如何调理？"
        )

        # 清除语音输入缓存
        if 'voice_input' in st.session_state:
            del st.session_state['voice_input']

        if st.button("🚀 获取智能建议", type="primary"):
            if user_input.strip():
                with st.spinner("🧠 AI正在思考中..."):

                    # 1. PDF文档检索
                    pdf_results = components['vector_db'].search(user_input)

                    # 2. 古代医书检索
                    ancient_results = components['ancient_books'].search_ancient_books(user_input)

                    # 3. 获取对话上下文
                    conversation_context = components['conversation_manager'].get_conversation_context(user_input)

                    # 4. 生成智能回答
                    response = generate_intelligent_response(
                        user_input,
                        pdf_results,
                        ancient_results,
                        conversation_context,
                        components['deepseek_api']
                    )

                    # 5. 显示回答
                    st.markdown("### 🧙‍♂️ AI中医师回答：")
                    st.markdown(response)

                    # 6. 语音播放
                    if voice_enabled and components['voice_manager'].voice_available:
                        components['voice_manager'].speak_text_async(response)

                    # 7. 保存对话
                    components['conversation_manager'].add_conversation(
                        user_input,
                        response,
                        pdf_results,
                        ancient_results
                    )

                    # 8. 显示检索结果
                    if pdf_results or ancient_results:
                        with st.expander("📚 检索到的相关资料", expanded=False):
                            if pdf_results:
                                st.subheader("📄 PDF文档结果")
                                for i, result in enumerate(pdf_results[:3], 1):
                                    st.write(f"**{i}. {result.get('source', '未知')}** (相似度: {result.get('similarity', 0):.2f})")
                                    st.write(result.get('content', '')[:300] + "...")
                                    st.write("---")

                            if ancient_results:
                                st.subheader("📜 古代医书结果")
                                for i, result in enumerate(ancient_results[:3], 1):
                                    st.write(f"**{i}. {result.get('title', '古籍')}**")
                                    st.write(result.get('content', '')[:300] + "...")
                                    st.write("---")
            else:
                st.warning("请输入您的问题")

    with col2:
        st.header("📚 文档管理")

        # 文档上传
        uploaded_files = st.file_uploader(
            "上传中医文档",
            type=['pdf', 'txt', 'docx'],
            accept_multiple_files=True,
            help="支持PDF、TXT、DOCX格式，最大200MB"
        )

        if uploaded_files:
            if st.button("📥 处理上传的文档"):
                with st.spinner("处理文档中..."):
                    success_count = 0

                    for uploaded_file in uploaded_files:
                        # 保存文件
                        file_path = Path(CONFIG['DOCUMENTS_PATH']) / uploaded_file.name
                        file_path.parent.mkdir(exist_ok=True)

                        with open(file_path, 'wb') as f:
                            f.write(uploaded_file.getbuffer())

                        # 处理文件
                        chunks, metadata = components['document_processor'].process_file(file_path, uploaded_file.name)

                        if chunks and metadata:
                            if components['vector_db'].add_documents(chunks, metadata):
                                success_count += 1
                                st.success(f"✅ {uploaded_file.name} 处理成功")
                            else:
                                st.error(f"❌ {uploaded_file.name} 向量化失败")
                        else:
                            st.error(f"❌ {uploaded_file.name} 处理失败")

                    if success_count > 0:
                        st.success(f"🎉 成功处理 {success_count} 个文档")

        # 数据库状态
        st.subheader("📊 数据库状态")
        if components['vector_db'].metadata:
            st.info(f"已索引文档块: {len(components['vector_db'].metadata)}")

            # 显示文档列表
            sources = set(meta.get('source', '未知') for meta in components['vector_db'].metadata)
            st.write("📄 已索引文档:")
            for source in sorted(sources):
                count = sum(1 for meta in components['vector_db'].metadata if meta.get('source') == source)
                st.write(f"- {source} ({count} 块)")
        else:
            st.info("暂无已索引文档")

    # 对话历史
    if components['conversation_manager'].conversations:
        st.header("📜 对话历史")

        with st.expander("查看历史对话", expanded=False):
            for conv in reversed(components['conversation_manager'].conversations[-5:]):
                st.write(f"**时间:** {conv['timestamp']}")
                st.write(f"**问题:** {conv['user_input']}")
                st.write(f"**回答:** {conv['assistant_response'][:200]}...")
                st.write("---")

if __name__ == "__main__":
    main()