#!/usr/bin/env python3
"""
语音功能测试脚本
"""

def test_voice_output():
    """测试语音输出"""
    try:
        import pyttsx3
        engine = pyttsx3.init()
        engine.say("语音输出功能正常")
        engine.runAndWait()
        print("✅ 语音输出测试成功")
        return True
    except Exception as e:
        print(f"❌ 语音输出测试失败: {e}")
        return False

def test_voice_input():
    """测试语音输入"""
    try:
        import speech_recognition as sr
        r = sr.Recognizer()
        m = sr.Microphone()
        
        print("🎤 请说话...")
        with m as source:
            r.adjust_for_ambient_noise(source)
            audio = r.listen(source, timeout=5, phrase_time_limit=5)
        
        print("🔄 正在识别...")
        text = r.recognize_google(audio, language='zh-CN')
        print(f"✅ 识别结果: {text}")
        return True
        
    except Exception as e:
        print(f"❌ 语音输入测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔊 语音功能测试")
    print("=" * 40)
    
    print("\n1. 测试语音输出...")
    test_voice_output()
    
    print("\n2. 测试语音输入...")
    test_voice_input()
    
    print("\n测试完成！")
