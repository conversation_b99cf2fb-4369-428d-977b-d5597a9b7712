<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试</title>
</head>
<body>
    <h1>最小化系统状态测试</h1>
    <div id="systemStatus">加载中...</div>
    <button onclick="loadSystemStatus()">刷新状态</button>
    
    <script>
        console.log('脚本开始执行...');
        
        // 加载系统状态
        async function loadSystemStatus() {
            console.log('loadSystemStatus 函数被调用');
            const statusElement = document.getElementById('systemStatus');
            
            try {
                console.log('开始fetch请求...');
                statusElement.innerHTML = '正在加载...';
                
                const response = await fetch('/api/health');
                console.log('fetch响应:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('解析的数据:', data);

                statusElement.innerHTML = `
                    <div>状态: ${data.status}</div>
                    <div>版本: ${data.version}</div>
                    <div>文档: ${data.documents} 个</div>
                    <div>功能: ${data.features ? data.features.length : 0} 项</div>
                `;
                
                console.log('状态更新完成');

            } catch (error) {
                console.error('错误:', error);
                statusElement.innerHTML = `错误: ${error.message}`;
            }
        }
        
        // 页面加载完成后自动加载状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            setTimeout(() => {
                console.log('开始自动加载状态...');
                loadSystemStatus();
            }, 1000);
        });
        
        console.log('脚本执行完成');
    </script>
</body>
</html>
