"""
中医RAG系统启动脚本
"""
import subprocess
import sys
import psutil
import gc
import os

def setup_tcm_environment():
    """设置中医RAG环境"""
    print("🏥 配置中医RAG环境...")
    
    # AMD GPU优化环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:64'
    os.environ['TOKENIZERS_PARALLELISM'] = 'true'
    os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'
    os.environ['PYTHONHASHSEED'] = '0'
    
    # 多线程优化
    cpu_count = os.cpu_count()
    os.environ['OMP_NUM_THREADS'] = str(min(cpu_count, 8))
    os.environ['MKL_NUM_THREADS'] = str(min(cpu_count, 8))
    os.environ['NUMEXPR_NUM_THREADS'] = str(min(cpu_count, 4))
    
    # 内存优化
    gc.collect()
    
    print("✅ 中医RAG环境配置完成")

def check_tcm_system():
    """检查中医RAG系统状态"""
    print("🔍 检查中医RAG系统状态...")
    
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    print(f"💾 内存: {memory.percent:.1f}% 使用 ({memory.available / (1024**3):.1f} GB 可用)")
    print(f"🖥️ CPU: {cpu_percent:.1f}% 使用")
    
    # 检查GPU配置
    try:
        from gpu_optimizer import setup_gpu_optimization
        gpu_config = setup_gpu_optimization()
        print(f"🚀 处理设备: {gpu_config['device_name']}")
        print(f"⚡ 优化级别: {gpu_config['optimization_level']}")
        print(f"📦 批处理大小: {gpu_config['batch_size']}")
        print(f"📏 块大小: {gpu_config['chunk_size']}")
    except Exception as e:
        print(f"⚠️ GPU配置检查失败: {e}")
    
    # 检查中医文档
    import os
    docs_dir = "documents"
    if os.path.exists(docs_dir):
        files = [f for f in os.listdir(docs_dir) if f.endswith(('.pdf', '.txt'))]
        print(f"📚 发现 {len(files)} 个中医文档:")
        for file in files[:5]:  # 显示前5个
            print(f"   - {file}")
        if len(files) > 5:
            print(f"   ... 还有 {len(files) - 5} 个文档")
    
    # 系统建议
    if memory.percent > 85:
        print("⚠️ 内存使用过高，建议清理后启动")
        return False
    
    if cpu_percent > 80:
        print("⚠️ CPU使用过高，可能影响性能")
    
    print("✅ 中医RAG系统状态良好")
    return True

def main():
    print("🏥 中医RAG系统启动")
    print("=" * 50)
    
    # 检查系统状态
    if not check_tcm_system():
        response = input("系统状态不佳，是否继续启动？(y/n): ").lower().strip()
        if response != 'y':
            print("👋 启动已取消")
            return
    
    # 配置环境
    setup_tcm_environment()
    
    print("\n🏥 启动中医RAG系统...")
    print("📝 访问地址: http://localhost:8509")
    print("\n🌟 中医RAG系统特色:")
    print("   📚 支持中医经典文献处理")
    print("   🔍 智能中医文本分割")
    print("   🧠 中医知识向量化")
    print("   💬 中医问答对话")
    print("   🚀 AMD GPU优化加速")
    
    print(f"\n📖 支持的中医文档格式:")
    print(f"   📄 PDF格式: 《伤寒论》《金匮要略》《黄帝内经》等")
    print(f"   📝 TXT格式: 中医基础理论、方剂学、诊断学等")
    
    print(f"\n⚡ 处理性能预期:")
    print(f"   📝 TXT文件: 几秒到几十秒")
    print(f"   📄 PDF文件: 1-5分钟（取决于大小和格式）")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app_ultra_fast.py", 
            "--server.address", "localhost",
            "--server.port", "8509",
            "--server.headless", "true",
            "--server.maxUploadSize", "100",  # 100MB限制
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 感谢使用中医RAG系统！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查依赖安装和系统配置")

if __name__ == "__main__":
    main()
