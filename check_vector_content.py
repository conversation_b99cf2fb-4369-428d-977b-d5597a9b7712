#!/usr/bin/env python3
"""
检查向量数据库中的实际内容
"""

import pickle
import numpy as np
from pathlib import Path
import faiss

def check_vector_database_content():
    """检查向量数据库内容"""
    print("🔍 检查向量数据库内容...")
    
    db_path = Path('./ultimate_final_vector_db')
    metadata_file = db_path / 'metadata.pkl'
    index_file = db_path / 'index.faiss'
    
    if not metadata_file.exists():
        print("❌ 元数据文件不存在")
        return
    
    if not index_file.exists():
        print("❌ 索引文件不存在")
        return
    
    # 加载元数据
    try:
        with open(metadata_file, 'rb') as f:
            metadata = pickle.load(f)
        
        print(f"📊 总记录数: {len(metadata)}")
        
        # 分析内容类型
        sources = {}
        keywords_count = {}
        
        for i, meta in enumerate(metadata[:10]):  # 检查前10条
            source = meta.get('source', 'unknown')
            content = meta.get('content', '')
            
            # 统计来源
            sources[source] = sources.get(source, 0) + 1
            
            # 检查关键词
            keywords = ['肾虚', '脾虚', '中医', '治疗', '方剂']
            for keyword in keywords:
                if keyword in content:
                    keywords_count[keyword] = keywords_count.get(keyword, 0) + 1
            
            print(f"\n📄 记录 {i+1}:")
            print(f"   来源: {source}")
            print(f"   内容长度: {len(content)} 字符")
            print(f"   内容预览: {content[:100]}...")
        
        print(f"\n📊 来源统计:")
        for source, count in sources.items():
            print(f"   {source}: {count} 条")
        
        print(f"\n🔍 关键词统计:")
        for keyword, count in keywords_count.items():
            print(f"   {keyword}: {count} 次")
        
        # 加载向量索引
        index = faiss.read_index(str(index_file))
        print(f"\n🧮 向量索引信息:")
        print(f"   向量维度: {index.d}")
        print(f"   向量数量: {index.ntotal}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_vector_search_with_different_thresholds():
    """测试不同阈值的搜索效果"""
    print("\n🧪 测试不同阈值的搜索效果...")
    
    try:
        from ultimate_final_tcm_system import UltimateVectorDatabase
        
        vector_db = UltimateVectorDatabase()
        if not vector_db.initialize():
            print("❌ 向量数据库初始化失败")
            return
        
        test_queries = [
            "肾虚脾虚怎么治疗",
            "中医",
            "治疗",
            "方剂",
            "健康"
        ]
        
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        for query in test_queries:
            print(f"\n🔍 查询: {query}")
            
            for threshold in thresholds:
                # 临时修改阈值
                original_threshold = vector_db.min_relevance_score
                vector_db.min_relevance_score = threshold
                
                results = vector_db.search(query, top_k=5)
                
                print(f"   阈值 {threshold}: {len(results)} 条结果")
                if results:
                    best_similarity = results[0].get('similarity', 0)
                    print(f"      最佳相似度: {best_similarity:.3f}")
                
                # 恢复原阈值
                vector_db.min_relevance_score = original_threshold
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def restart_mcp_service():
    """重启MCP服务以应用修改"""
    print("\n🔄 重启MCP服务...")
    
    import subprocess
    import sys
    import time
    import requests
    
    try:
        # 尝试停止现有服务
        try:
            requests.post('http://localhost:8003/shutdown', timeout=5)
            time.sleep(2)
        except:
            pass
        
        # 启动新服务
        process = subprocess.Popen(
            [sys.executable, 'fastmcp_elasticsearch_service.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务启动
        time.sleep(5)
        
        # 检查服务状态
        response = requests.get('http://localhost:8003/health', timeout=5)
        if response.status_code == 200:
            print("✅ MCP服务重启成功")
            return True
        else:
            print("❌ MCP服务重启失败")
            return False
            
    except Exception as e:
        print(f"❌ 重启失败: {e}")
        return False

def test_mcp_with_kidney_spleen_query():
    """测试MCP对肾虚脾虚查询的响应"""
    print("\n🧪 测试MCP肾虚脾虚查询...")
    
    import requests
    
    try:
        # 测试搜索
        mcp_request = {
            "method": "search_knowledge",
            "params": {
                "query": "肾虚脾虚怎么治疗",
                "domain": "medical",
                "max_results": 5,
                "search_type": "comprehensive"
            },
            "id": "test_kidney_spleen"
        }
        
        response = requests.post(
            'http://localhost:8003/mcp',
            json=mcp_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'result' in result:
                results = result['result'].get('results', [])
                print(f"   ✅ 返回 {len(results)} 条结果")
                
                for i, res in enumerate(results, 1):
                    title = res.get('title', 'unknown')
                    score = res.get('score', 0)
                    content = res.get('content', '')
                    
                    print(f"   {i}. {title} (评分: {score:.3f})")
                    print(f"      内容: {content[:80]}...")
                    
                    # 检查是否包含肾虚脾虚相关内容
                    if '肾虚' in content and '脾虚' in content:
                        print(f"      ✅ 包含肾虚脾虚相关内容")
                    elif '肾虚' in content or '脾虚' in content:
                        print(f"      ⚠️ 部分相关")
                    else:
                        print(f"      ❌ 不相关")
                
                return True
            else:
                print(f"   ❌ MCP返回错误: {result.get('error', 'unknown')}")
                return False
        else:
            print(f"   ❌ MCP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 深度检查向量数据库和MCP服务")
    print("=" * 60)
    
    # 检查向量数据库内容
    check_vector_database_content()
    
    # 测试不同阈值
    test_vector_search_with_different_thresholds()
    
    # 重启MCP服务
    restart_mcp_service()
    
    # 测试MCP查询
    test_mcp_with_kidney_spleen_query()
    
    print("\n" + "=" * 60)
    print("📋 检查完成")

if __name__ == "__main__":
    main()
