"""
模型管理器 - 负责下载和加载模型
"""
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoModel
from sentence_transformers import SentenceTransformer
import requests
from pathlib import Path
import config

class ModelManager:
    def __init__(self):
        self.embedding_model = None
        self.llm_model = None
        self.llm_tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
    
    def download_llama_model(self):
        """下载轻量级语言模型"""
        print("正在下载语言模型...")
        try:
            # 使用更轻量的模型，适合CPU运行
            model_name = "microsoft/DialoGPT-medium"  # 轻量级对话模型

            print(f"下载模型: {model_name}")
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float32,  # CPU使用float32
                cache_dir=str(config.LLM_MODEL_PATH)
            )

            # 添加pad_token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            self.llm_tokenizer = tokenizer
            self.llm_model = model
            print("语言模型下载完成！")
            return True

        except Exception as e:
            print(f"下载语言模型失败: {e}")
            # 备用方案：使用更小的模型
            return self.download_backup_model()
    
    def download_backup_model(self):
        """下载备用的轻量级模型"""
        try:
            print("使用备用模型: microsoft/DialoGPT-medium")
            model_name = "microsoft/DialoGPT-medium"
            
            self.llm_tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.llm_model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # 添加pad_token
            if self.llm_tokenizer.pad_token is None:
                self.llm_tokenizer.pad_token = self.llm_tokenizer.eos_token
            
            print("备用模型加载完成！")
            return True
        except Exception as e:
            print(f"加载备用模型失败: {e}")
            return False
    
    def load_embedding_model(self):
        """加载中文嵌入模型"""
        try:
            print("正在加载嵌入模型...")
            # 首先尝试加载中文模型
            try:
                print(f"尝试加载中文模型: {config.EMBEDDING_MODEL}")
                self.embedding_model = SentenceTransformer(config.EMBEDDING_MODEL, device=self.device)
                print("中文嵌入模型加载完成！")
            except Exception as e:
                # 备用：使用轻量级英文模型
                print(f"中文模型加载失败: {e}")
                print("使用备用模型...")
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2', device=self.device)
                print("备用嵌入模型加载完成！")
            return True
        except Exception as e:
            print(f"加载嵌入模型失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def initialize_models(self):
        """初始化所有模型"""
        print("开始初始化模型...")
        
        # 加载嵌入模型
        if not self.load_embedding_model():
            return False
        
        # 下载并加载LLM模型
        if not self.download_llama_model():
            return False
        
        print("所有模型初始化完成！")
        return True
    
    def get_embedding(self, text):
        """获取文本嵌入"""
        if self.embedding_model is None:
            raise ValueError("嵌入模型未加载")
        return self.embedding_model.encode([text])[0]
    
    def generate_response(self, prompt, max_new_tokens=100, temperature=0.7):
        """生成回复 - 使用max_new_tokens避免长度问题"""
        if self.llm_model is None or self.llm_tokenizer is None:
            raise ValueError("LLM模型未加载")

        try:
            # 截断输入以确保不超过模型限制
            max_input_length = 800  # 为生成留出空间
            if len(prompt) > max_input_length:
                prompt = prompt[:max_input_length] + "..."

            inputs = self.llm_tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=800)

            # 检查输入长度
            input_length = inputs.shape[1]
            if input_length > 800:
                print(f"⚠️ 输入过长 ({input_length}), 截断到800")
                inputs = inputs[:, :800]

            with torch.no_grad():
                outputs = self.llm_model.generate(
                    inputs,
                    max_new_tokens=max_new_tokens,  # 使用max_new_tokens
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.llm_tokenizer.pad_token_id,
                    eos_token_id=self.llm_tokenizer.eos_token_id,
                    attention_mask=torch.ones_like(inputs)  # 添加attention_mask
                )

            response = self.llm_tokenizer.decode(outputs[0], skip_special_tokens=True)
            # 移除输入部分，只返回生成的内容
            response = response[len(prompt):].strip()

            # 如果回答为空，提供默认回答
            if not response or len(response.strip()) < 5:
                response = "根据中医理论，这个问题需要结合具体情况分析。"

            return response

        except Exception as e:
            print(f"生成回复时出错: {e}")
            return "根据中医理论，这个问题需要结合具体情况来分析。建议咨询专业中医师。"

# 全局模型管理器实例
model_manager = ModelManager()
