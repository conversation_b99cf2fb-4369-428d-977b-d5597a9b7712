"""
家庭中医智能助手启动脚本
24/7为家人朋友提供中医知识查询服务
"""
import os
import sys
import subprocess
import time
from pathlib import Path
import psutil

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查内存
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    if available_gb < 4:
        print(f"⚠️ 可用内存较少: {available_gb:.1f}GB，建议4GB+")
    else:
        print(f"✅ 内存充足: {available_gb:.1f}GB 可用")
    
    # 检查磁盘空间
    disk = psutil.disk_usage('.')
    free_gb = disk.free / (1024**3)
    if free_gb < 2:
        print(f"⚠️ 磁盘空间不足: {free_gb:.1f}GB，建议2GB+")
    else:
        print(f"✅ 磁盘空间充足: {free_gb:.1f}GB 可用")
    
    return True

def setup_family_environment():
    """设置家庭中医助手环境"""
    print("🏥 设置家庭中医智能助手环境...")
    
    # 创建必要目录
    directories = [
        "family_tcm_knowledge",
        "family_tcm_knowledge/documents",
        "family_tcm_knowledge/processed", 
        "family_tcm_knowledge/backups",
        "user_logs",
        "vector_db"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    
    # 检查依赖 - 修复包名映射
    required_packages = {
        "streamlit": "streamlit",
        "torch": "torch",
        "transformers": "transformers",
        "sentence-transformers": "sentence_transformers",
        "faiss-cpu": "faiss",
        "numpy": "numpy",
        "pandas": "pandas",
        "PyPDF2": "PyPDF2",
        "scikit-learn": "sklearn"
    }

    print("📦 检查依赖包...")
    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 环境设置完成")
    return True

def show_welcome_message():
    """显示欢迎信息"""
    print("\n" + "="*60)
    print("🏥 家庭中医智能助手")
    print("="*60)
    print("🌟 功能特色:")
    print("   📚 24/7中医知识查询服务")
    print("   👨‍👩‍👧‍👦 支持多用户（家人朋友）")
    print("   📄 支持批量上传中医PDF文档")
    print("   🧠 智能问答，引用经典文献")
    print("   📊 使用统计和健康监控")
    print("   💾 自动备份知识库")
    print("\n👥 适用人群:")
    print("   🏠 家庭成员日常中医咨询")
    print("   👴 老人健康问题查询")
    print("   👶 儿童常见病症了解")
    print("   🤝 朋友中医知识分享")
    print("\n📖 支持文档类型:")
    print("   📜 中医经典：《黄帝内经》《伤寒论》《金匮要略》等")
    print("   💊 方剂大全：各类中药方剂")
    print("   🏥 医案集锦：名医医案")
    print("   📚 中医教材：基础理论、诊断学等")
    print("="*60)

def check_port_availability(port=8511):
    """检查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8511):
    """找到可用端口"""
    for port in range(start_port, start_port + 10):
        if check_port_availability(port):
            return port
    return None

def main():
    """主函数"""
    show_welcome_message()
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败")
        input("按回车键退出...")
        return
    
    # 设置环境
    if not setup_family_environment():
        print("\n❌ 环境设置失败")
        input("按回车键退出...")
        return
    
    # 用户确认
    print(f"\n🚀 准备启动家庭中医智能助手...")
    print("📝 系统将在浏览器中打开Web界面")
    print("🔒 所有数据都保存在本地，保护隐私安全")
    print("⏰ 系统支持24/7运行，为家人提供随时查询服务")
    
    response = input("\n是否现在启动系统？(y/n): ").lower().strip()
    if response != 'y':
        print("👋 启动已取消")
        return
    
    # 查找可用端口
    port = find_available_port()
    if not port:
        print("❌ 无法找到可用端口")
        return
    
    print(f"\n🏥 启动家庭中医智能助手...")
    print(f"📝 访问地址: http://localhost:{port}")
    print(f"🌐 局域网访问: http://您的IP地址:{port}")
    print("\n💡 使用提示:")
    print("   1. 首次使用需要初始化系统（约1-2分钟）")
    print("   2. 可以上传中医PDF文档扩充知识库")
    print("   3. 支持多人同时使用，每人选择身份即可")
    print("   4. 系统会记录使用日志，便于统计分析")
    print("   5. 建议定期备份知识库数据")
    
    print(f"\n⚠️ 重要提醒:")
    print("   - 本系统仅供中医知识学习参考")
    print("   - 不能替代专业医生诊断治疗")
    print("   - 如有严重症状请及时就医")
    
    print(f"\n🔧 技术信息:")
    print(f"   - 端口: {port}")
    print(f"   - 数据目录: ./family_tcm_knowledge")
    print(f"   - 日志目录: ./user_logs")
    print(f"   - 支持Ctrl+C停止服务")
    
    try:
        # 启动Streamlit应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "family_web_interface.py",
            "--server.address", "0.0.0.0",  # 允许局域网访问
            "--server.port", str(port),
            "--server.headless", "true",
            "--server.maxUploadSize", "200",  # 200MB上传限制
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false",
            "--theme.base", "light",
            "--theme.primaryColor", "#4CAF50"
        ])
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用家庭中医智能助手！")
        print("💾 所有数据已保存，下次启动时会自动加载")
        print("🏥 祝您和家人身体健康！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请检查依赖安装和系统配置")
        print("🔧 如需帮助，请查看错误日志")

if __name__ == "__main__":
    main()
