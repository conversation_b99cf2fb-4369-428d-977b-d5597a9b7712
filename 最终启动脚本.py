#!/usr/bin/env python3
"""
最终启动脚本 - 确保智能回答系统正常工作
"""

import subprocess
import sys
import time
import webbrowser
import requests
from pathlib import Path

def check_and_start_intelligent_mcp():
    """检查并启动智能MCP服务"""
    print("🔍 检查智能MCP服务状态...")
    
    # 检查服务是否已运行
    try:
        response = requests.get('http://localhost:8006/health', timeout=3)
        if response.status_code == 200:
            print("✅ 智能MCP服务已运行")
            return True
    except:
        pass
    
    print("🚀 启动智能MCP服务...")
    try:
        process = subprocess.Popen(
            [sys.executable, "intelligent_mcp_service.py"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        
        print(f"✅ 智能MCP服务已启动 (PID: {process.pid})")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        for i in range(10):
            time.sleep(1)
            try:
                response = requests.get('http://localhost:8006/health', timeout=2)
                if response.status_code == 200:
                    print("✅ 智能MCP服务启动成功")
                    return True
            except:
                continue
        
        print("⚠️ 智能MCP服务启动超时，但可能仍在启动中")
        return True
        
    except Exception as e:
        print(f"❌ 启动智能MCP服务失败: {e}")
        return False

def test_intelligent_responses():
    """测试智能回答功能"""
    print("\n🧪 测试智能回答功能...")
    
    test_cases = [
        "失眠多梦怎么办",
        "肚子疼湿气重怎么治疗"
    ]
    
    for query in test_cases:
        print(f"   测试: {query}")
        
        try:
            mcp_request = {
                "method": "search_knowledge",
                "params": {
                    "query": query,
                    "domain": "medical",
                    "max_results": 1
                },
                "id": "test"
            }
            
            response = requests.post(
                'http://localhost:8006/mcp',
                json=mcp_request,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'result' in result and result['result'].get('results'):
                    first_result = result['result']['results'][0]
                    title = first_result.get('title', '')
                    print(f"   ✅ 返回: {title}")
                else:
                    print("   ❌ 无返回结果")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🏥 家庭私人医生小帮手 - 最终启动")
    print("🎯 确保智能回答系统正常工作")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    main_file = current_dir / "ultimate_final_tcm_system.py"
    
    if not main_file.exists():
        print("❌ 未找到主程序文件")
        print(f"当前目录: {current_dir}")
        input("按回车键退出...")
        return False
    
    print("✅ 找到主程序文件")
    
    # 1. 启动智能MCP服务
    if not check_and_start_intelligent_mcp():
        print("⚠️ 智能MCP服务启动失败，但系统仍可运行")
    
    # 2. 测试智能回答
    test_intelligent_responses()
    
    # 3. 启动主系统
    print("\n🌐 启动主系统...")
    print("💡 系统将在浏览器中自动打开")
    print("🔗 访问地址: http://localhost:8501")
    print()
    print("🎯 测试建议:")
    print("   1. 输入: 失眠多梦怎么办")
    print("   2. 输入: 肚子疼湿气重怎么治疗")
    print("   3. 检查回答是否针对性强，无模板化内容")
    print()
    
    try:
        # 自动打开浏览器
        time.sleep(2)
        webbrowser.open('http://localhost:8501')
        
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ultimate_final_tcm_system.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("📋 使用说明:")
    print("1. 此脚本会自动启动智能MCP服务")
    print("2. 然后启动主系统并打开浏览器")
    print("3. 测试时请注意回答的针对性")
    print("4. 按 Ctrl+C 停止系统")
    print()
    
    success = main()
    
    if not success:
        print("\n❌ 启动失败")
        print("💡 故障排除:")
        print("1. 检查端口8501和8006是否被占用")
        print("2. 检查Python环境和依赖")
        print("3. 尝试手动运行: python intelligent_mcp_service.py")
        input("按回车键退出...")
    
    sys.exit(0 if success else 1)
