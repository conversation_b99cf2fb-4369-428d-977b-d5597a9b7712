#!/usr/bin/env python3
"""
GGUF模型FastAPI服务器
专门用于加载DeepSeek-R1-Qwen3-8B-Q4_0.gguf模型
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
import json

# FastAPI相关
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# GGUF模型加载
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    print("❌ llama-cpp-python未安装，请运行: pip install llama-cpp-python")

# 嵌入模型
try:
    from sentence_transformers import SentenceTransformer
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False
    print("❌ sentence-transformers未安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API模型定义
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: float = 0.7
    max_tokens: int = 2048
    stream: bool = False

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]

class EmbeddingRequest(BaseModel):
    model: str
    input: List[str]

class EmbeddingResponse(BaseModel):
    object: str = "list"
    data: List[Dict[str, Any]]
    model: str
    usage: Dict[str, int]

class GGUFModelServer:
    """GGUF模型服务器"""
    
    def __init__(self):
        self.app = FastAPI(title="GGUF Model API Server", version="1.0.0")
        self.setup_cors()
        self.setup_routes()
        
        # 模型配置
        self.model_config = {
            'gguf_model_path': './models/deepseek-ai_DeepSeek-R1-0528-Qwen3-8B-Q4_0.gguf',
            'embedding_model_path': './models/bge-m3',
            'context_length': 4096,
            'n_threads': 4,  # 适合笔记本的线程数
            'n_gpu_layers': 0  # CPU推理，适合笔记本
        }
        
        # 模型实例
        self.llama_model = None
        self.embedding_model = None
        self.initialized = False
    
    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            return {
                "status": "healthy" if self.initialized else "initializing",
                "model_loaded": self.llama_model is not None,
                "embedding_loaded": self.embedding_model is not None
            }
        
        @self.app.get("/v1/models")
        async def list_models():
            models = []
            if self.llama_model:
                models.append({
                    "id": "deepseek-r1-qwen3-8b",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "local"
                })
            if self.embedding_model:
                models.append({
                    "id": "bge-m3",
                    "object": "model", 
                    "created": int(time.time()),
                    "owned_by": "local"
                })
            return {"object": "list", "data": models}
        
        @self.app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
        async def chat_completions(request: ChatCompletionRequest):
            if not self.llama_model:
                raise HTTPException(status_code=503, detail="模型未加载")
            
            try:
                # 构建提示词
                prompt = self._build_chat_prompt(request.messages)
                
                # 生成回答
                response = self.llama_model(
                    prompt,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    stop=["</s>", "<|im_end|>", "<|endoftext|>"],
                    echo=False
                )
                
                # 构建响应
                completion_id = f"chatcmpl-{int(time.time())}"
                
                return ChatCompletionResponse(
                    id=completion_id,
                    created=int(time.time()),
                    model=request.model,
                    choices=[{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response["choices"][0]["text"].strip()
                        },
                        "finish_reason": "stop"
                    }],
                    usage={
                        "prompt_tokens": response["usage"]["prompt_tokens"],
                        "completion_tokens": response["usage"]["completion_tokens"],
                        "total_tokens": response["usage"]["total_tokens"]
                    }
                )
                
            except Exception as e:
                logger.error(f"聊天完成失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/v1/embeddings", response_model=EmbeddingResponse)
        async def create_embeddings(request: EmbeddingRequest):
            if not self.embedding_model:
                raise HTTPException(status_code=503, detail="嵌入模型未加载")
            
            try:
                # 生成嵌入
                embeddings = self.embedding_model.encode(request.input)
                
                # 构建响应
                data = []
                for i, embedding in enumerate(embeddings):
                    data.append({
                        "object": "embedding",
                        "index": i,
                        "embedding": embedding.tolist()
                    })
                
                return EmbeddingResponse(
                    data=data,
                    model=request.model,
                    usage={
                        "prompt_tokens": sum(len(text.split()) for text in request.input),
                        "total_tokens": sum(len(text.split()) for text in request.input)
                    }
                )
                
            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def _build_chat_prompt(self, messages: List[ChatMessage]) -> str:
        """构建聊天提示词"""
        prompt_parts = []
        
        for message in messages:
            if message.role == "system":
                prompt_parts.append(f"System: {message.content}")
            elif message.role == "user":
                prompt_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                prompt_parts.append(f"Assistant: {message.content}")
        
        prompt_parts.append("Assistant:")
        return "\n".join(prompt_parts)
    
    async def initialize_models(self):
        """初始化模型"""
        try:
            logger.info("🚀 开始初始化GGUF模型服务器...")
            
            # 检查模型文件
            gguf_path = Path(self.model_config['gguf_model_path'])
            if not gguf_path.exists():
                logger.error(f"❌ GGUF模型文件不存在: {gguf_path}")
                return False
            
            if not LLAMA_CPP_AVAILABLE:
                logger.error("❌ llama-cpp-python未安装")
                return False
            
            # 加载GGUF模型
            logger.info(f"📥 加载GGUF模型: {gguf_path}")
            logger.info(f"📊 模型大小: {gguf_path.stat().st_size / (1024**3):.1f}GB")
            
            self.llama_model = Llama(
                model_path=str(gguf_path),
                n_ctx=self.model_config['context_length'],
                n_threads=self.model_config['n_threads'],
                n_gpu_layers=self.model_config['n_gpu_layers'],
                verbose=False
            )
            
            logger.info("✅ GGUF模型加载成功")
            
            # 加载嵌入模型
            if EMBEDDING_AVAILABLE:
                embedding_path = Path(self.model_config['embedding_model_path'])
                if embedding_path.exists():
                    logger.info(f"📥 加载嵌入模型: {embedding_path}")
                    self.embedding_model = SentenceTransformer(str(embedding_path))
                    logger.info("✅ 嵌入模型加载成功")
                else:
                    logger.warning(f"⚠️ 嵌入模型路径不存在: {embedding_path}")
            
            # 测试模型
            logger.info("🧪 测试模型...")
            test_response = self.llama_model(
                "你好，请简单介绍一下中医。",
                max_tokens=100,
                temperature=0.7
            )
            
            test_text = test_response["choices"][0]["text"].strip()
            logger.info(f"🎯 测试回答: {test_text[:50]}...")
            
            self.initialized = True
            logger.info("🎉 GGUF模型服务器初始化完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            return False
    
    async def start_server(self, host: str = "127.0.0.1", port: int = 8002):
        """启动服务器"""
        if not await self.initialize_models():
            logger.error("❌ 模型初始化失败，无法启动服务器")
            return
        
        logger.info(f"🌐 启动GGUF模型API服务器: http://{host}:{port}")
        logger.info("📋 可用端点:")
        logger.info("  - GET  /health")
        logger.info("  - GET  /v1/models") 
        logger.info("  - POST /v1/chat/completions")
        logger.info("  - POST /v1/embeddings")
        
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()

# 全局服务器实例
gguf_server = GGUFModelServer()

async def main():
    """主函数"""
    await gguf_server.start_server()

if __name__ == "__main__":
    asyncio.run(main())
