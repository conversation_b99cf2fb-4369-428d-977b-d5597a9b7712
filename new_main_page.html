<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 终极中医RAG系统</title>
    <style>
        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }
        
        html, body {
            height: 100%;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .app-header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .app-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .app-header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .app-body {
            flex: 1;
            display: flex;
            min-height: 0;
        }
        
        .sidebar {
            width: 280px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            flex-shrink: 0;
            overflow-y: auto;
            padding: 15px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .app-body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                max-height: 200px;
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }

            .chat-container {
                flex: 1;
                min-height: 400px;
            }
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
            min-height: 300px;
        }
        
        .chat-input {
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 15px 20px;
            flex-shrink: 0;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .input-group textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            min-height: 44px;
            max-height: 120px;
        }
        
        .input-group textarea:focus {
            outline: none;
            border-color: #2E8B57;
            box-shadow: 0 0 0 2px rgba(46, 139, 87, 0.2);
        }
        
        .voice-button, .send-button {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s;
            min-width: 50px;
            height: 44px;
        }
        
        .voice-button {
            background: #6c757d;
            color: white;
        }
        
        .voice-button:hover {
            background: #5a6268;
        }
        
        .voice-button.recording {
            background: #dc3545;
            animation: pulse 1s infinite;
        }

        .voice-button.disabled {
            background: #6c757d;
            opacity: 0.6;
        }

        .voice-button.active {
            background: #28a745;
        }
        
        .send-button {
            background: #2E8B57;
            color: white;
            min-width: 80px;
        }
        
        .send-button:hover {
            background: #228B22;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .message {
            margin-bottom: 15px;
        }
        
        .message.user {
            text-align: right;
        }
        
        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }
        
        .message.user .message-bubble {
            background: #2E8B57;
            color: white;
        }
        
        .message.assistant .message-bubble {
            background: white;
            border: 1px solid #ddd;
            color: #333;
        }
        
        .sidebar-section {
            margin-bottom: 20px;
        }
        
        .sidebar-section h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .quick-button {
            width: 100%;
            padding: 10px 12px;
            margin-bottom: 6px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            text-align: left;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .quick-button:hover {
            background: #f0f0f0;
            border-color: #2E8B57;
        }
        
        .session-info {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ddd;
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .session-info div {
            margin-bottom: 4px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            background: #2E8B57;
            color: white;
            border-radius: 6px;
            transform: translateX(400px);
            transition: transform 0.3s;
            z-index: 1000;
            font-size: 14px;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        .notification.success {
            background: #28a745;
        }
        
        .typing-indicator {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1>🏥 终极中医RAG系统</h1>
            <p>集成PDF检索 + 在线医学爬取 + 智能问答 + 语音交互</p>
        </div>
        
        <div class="app-body">
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>💡 快捷查询</h3>
                    <button class="quick-button" onclick="sendQuickMessage('脾胃虚弱的症状和调理方法')">
                        🌿 脾胃调理
                    </button>
                    <button class="quick-button" onclick="sendQuickMessage('肾阳虚和肾阴虚的区别及治疗')">
                        🫖 肾虚调养
                    </button>
                    <button class="quick-button" onclick="sendQuickMessage('失眠多梦的中医治疗方案')">
                        😴 失眠治疗
                    </button>
                    <button class="quick-button" onclick="sendQuickMessage('栀子甘草豉汤的功效和应用')">
                        💊 经典方剂
                    </button>
                </div>
                
                <div class="sidebar-section">
                    <h3>💬 会话管理</h3>
                    <div class="session-info" id="sessionInfo">
                        <div>当前会话: <span id="currentSession">新会话</span></div>
                        <div>消息数量: <span id="messageCount">0</span></div>
                    </div>
                    <button class="quick-button" onclick="startNewSession()">
                        🆕 新建会话
                    </button>
                    <button class="quick-button" onclick="clearChat()">
                        🗑️ 清空聊天
                    </button>
                </div>
                
                <div class="sidebar-section">
                    <h3>📊 系统状态</h3>
                    <div class="session-info" id="systemStatus">
                        <div>状态: <span style="color: green;">✅ healthy</span></div>
                        <div>版本: 3.0.0</div>
                        <div>文档: <span id="docCount">0</span> 个</div>
                        <div>功能: 4 项</div>
                    </div>
                    <button class="quick-button" onclick="loadSystemStatus()">
                        🔄 刷新状态
                    </button>
                </div>
            </div>
            
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-bubble">
                            <strong>🤖 助手:</strong> 您好！我是终极中医RAG系统。我可以：<br>
                            🔍 搜索您上传的PDF文档<br>
                            🌐 爬取医宗金鉴等在线资源<br>
                            💬 提供智能中医问答<br>
                            🎤 支持语音输入和播报<br><br>
                            请问有什么可以帮助您的吗？
                        </div>
                    </div>
                </div>
                
                <div class="chat-input">
                    <div class="input-group">
                        <textarea id="messageInput" 
                                rows="1" 
                                placeholder="请输入您的中医问题..."
                                onkeydown="handleKeyDown(event)"
                                oninput="autoResize(this)"></textarea>
                        <button class="voice-button" onclick="toggleVoiceInput()" id="voiceBtn" title="语音输入">🎤</button>
                        <button class="voice-button" onclick="toggleVoiceOutput()" id="voiceOutputBtn" title="语音播放开关">🔊</button>
                        <button class="send-button" onclick="sendMessage()" id="sendBtn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="notification" id="notification">
        <div id="notificationText"></div>
    </div>

    <script>
        let currentSessionId = null;
        let isTyping = false;
        let recognition = null;
        let isRecording = false;
        let messageCount = 0;
        let voiceOutputEnabled = true;
        let speechSynthesis = window.speechSynthesis;

        // 自动调整文本框高度
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notificationText');

            if (notification && text) {
                text.textContent = message;
                notification.className = `notification ${type} show`;

                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }

        // 加载系统状态
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/health?t=' + Date.now());

                if (response.ok) {
                    const data = await response.json();

                    // 更新文档数量
                    const docCountElement = document.getElementById('docCount');
                    if (docCountElement) {
                        docCountElement.textContent = data.documents || 0;
                    }

                    // 更新完整状态
                    const statusElement = document.getElementById('systemStatus');
                    if (statusElement) {
                        statusElement.innerHTML = `
                            <div>状态: <span style="color: green;">✅ ${data.status}</span></div>
                            <div>版本: ${data.version}</div>
                            <div>文档: ${data.documents} 个</div>
                            <div>功能: ${data.features ? data.features.length : 0} 项</div>
                        `;
                    }

                    showNotification('✅ 系统状态已更新', 'success');
                } else {
                    throw new Error('HTTP ' + response.status);
                }

            } catch (error) {
                console.error('❌ 系统状态加载失败:', error);
                showNotification('❌ 系统状态加载失败', 'error');
            }
        }

        // 会话管理功能
        function startNewSession() {
            currentSessionId = null;
            messageCount = 0;
            updateSessionInfo();
            showNotification('🆕 已开始新会话', 'success');
        }

        function clearChat() {
            const container = document.getElementById('chatMessages');
            // 保留欢迎消息
            const welcomeMessage = container.querySelector('.message.assistant');
            container.innerHTML = '';
            if (welcomeMessage) {
                container.appendChild(welcomeMessage);
            }
            messageCount = 0;
            updateSessionInfo();
            showNotification('🗑️ 聊天记录已清空', 'success');
        }

        function updateSessionInfo() {
            const sessionElement = document.getElementById('currentSession');
            const countElement = document.getElementById('messageCount');

            if (sessionElement) {
                sessionElement.textContent = currentSessionId ?
                    currentSessionId.substring(0, 8) + '...' : '新会话';
            }

            if (countElement) {
                countElement.textContent = messageCount;
            }
        }

        // 快捷消息
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            input.style.height = 'auto';

            // 显示加载状态
            isTyping = true;
            document.getElementById('sendBtn').disabled = true;
            addMessage('assistant', '正在智能检索中...', 'typing-indicator');

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });

                const data = await response.json();

                // 更新会话ID
                currentSessionId = data.session_id;

                // 移除加载消息
                removeTypingMessage();

                // 显示助手回复
                addMessage('assistant', data.response);

                // 更新消息计数和会话信息
                messageCount += 2; // 用户消息 + 助手回复
                updateSessionInfo();

                // 显示处理时间
                showNotification(`⚡ 处理完成 (${data.processing_time.toFixed(2)}s)`, 'success');

            } catch (error) {
                removeTypingMessage();
                addMessage('assistant', '抱歉，发生了错误: ' + error.message);
                showNotification('❌ 请求失败: ' + error.message, 'error');
            } finally {
                isTyping = false;
                document.getElementById('sendBtn').disabled = false;
            }
        }

        // 添加消息
        function addMessage(type, content, className = '') {
            const container = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type} ${className}`;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';

            const prefix = type === 'user' ? '👤 您:' : '🤖 助手:';
            bubbleDiv.innerHTML = `<strong>${prefix}</strong> ${content.replace(/\n/g, '<br>')}`;

            messageDiv.appendChild(bubbleDiv);
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;

            // 如果是助手回复且不是打字指示器，则播放语音
            if (type === 'assistant' && className !== 'typing-indicator' && content !== '正在智能检索中...') {
                setTimeout(() => {
                    speakText(content);
                }, 500); // 延迟500ms播放，让用户看到消息
            }
        }

        // 移除打字指示器
        function removeTypingMessage() {
            const typingMessage = document.querySelector('.typing-indicator');
            if (typingMessage) {
                typingMessage.remove();
            }
        }

        // 语音识别功能
        function initSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.lang = 'zh-CN';
                recognition.continuous = false;
                recognition.interimResults = false;

                recognition.onstart = function() {
                    isRecording = true;
                    document.getElementById('voiceBtn').classList.add('recording');
                    showNotification('🎤 正在录音...', 'info');
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('messageInput').value = transcript;
                    autoResize(document.getElementById('messageInput'));
                    showNotification('✅ 语音识别完成', 'success');
                };

                recognition.onerror = function(event) {
                    showNotification('❌ 语音识别失败: ' + event.error, 'error');
                };

                recognition.onend = function() {
                    isRecording = false;
                    document.getElementById('voiceBtn').classList.remove('recording');
                };

                return true;
            }
            return false;
        }

        function toggleVoiceInput() {
            if (!recognition) {
                if (!initSpeechRecognition()) {
                    showNotification('❌ 您的浏览器不支持语音识别', 'error');
                    return;
                }
            }

            if (isRecording) {
                recognition.stop();
            } else {
                recognition.start();
            }
        }

        // 语音播放开关
        function toggleVoiceOutput() {
            voiceOutputEnabled = !voiceOutputEnabled;
            const btn = document.getElementById('voiceOutputBtn');

            if (voiceOutputEnabled) {
                btn.classList.add('active');
                btn.classList.remove('disabled');
                btn.title = '语音播放：开启';
                showNotification('🔊 语音播放已开启', 'success');
            } else {
                btn.classList.remove('active');
                btn.classList.add('disabled');
                btn.title = '语音播放：关闭';
                showNotification('🔇 语音播放已关闭', 'info');
                // 停止当前播放
                speechSynthesis.cancel();
            }
        }

        // 语音播放函数
        function speakText(text) {
            if (!voiceOutputEnabled || !speechSynthesis) return;

            // 停止当前播放
            speechSynthesis.cancel();

            // 清理文本（移除HTML标签和特殊字符）
            const cleanText = text
                .replace(/<[^>]*>/g, '') // 移除HTML标签
                .replace(/[🔍🌐💬🎤⚡✅❌🆕🗑️📊🎉💡]/g, '') // 移除emoji
                .replace(/\s+/g, ' ') // 合并空格
                .trim();

            if (cleanText.length === 0) return;

            const utterance = new SpeechSynthesisUtterance(cleanText);
            utterance.lang = 'zh-CN';
            utterance.rate = 0.9;
            utterance.pitch = 1;
            utterance.volume = 0.8;

            utterance.onstart = function() {
                showNotification('🔊 正在播放...', 'info');
            };

            utterance.onend = function() {
                console.log('语音播放完成');
            };

            utterance.onerror = function(event) {
                console.error('语音播放错误:', event.error);
                showNotification('❌ 语音播放失败', 'error');
            };

            speechSynthesis.speak(utterance);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 初始化语音识别
            initSpeechRecognition();

            // 初始化语音播放按钮状态
            const voiceOutputBtn = document.getElementById('voiceOutputBtn');
            if (voiceOutputBtn) {
                voiceOutputBtn.classList.add('active');
                voiceOutputBtn.title = '语音播放：开启';
            }

            // 初始化会话信息
            updateSessionInfo();

            // 延迟加载系统状态
            setTimeout(() => {
                loadSystemStatus();
            }, 1000);

            // 检查语音合成支持
            if (!speechSynthesis) {
                showNotification('⚠️ 您的浏览器不支持语音播放', 'warning');
                voiceOutputEnabled = false;
                if (voiceOutputBtn) {
                    voiceOutputBtn.classList.remove('active');
                    voiceOutputBtn.classList.add('disabled');
                    voiceOutputBtn.title = '语音播放：不支持';
                }
            }
        });
    </script>
</body>
</html>
