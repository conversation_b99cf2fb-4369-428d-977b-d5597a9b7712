#!/usr/bin/env python3
"""
设置ngrok隧道 - 让异地朋友访问中医RAG系统
"""
import subprocess
import sys
import time
import requests
import json
import os

def install_pyngrok():
    """安装pyngrok"""
    try:
        print("🔧 正在安装pyngrok...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyngrok'], check=True)
        print("✅ pyngrok安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ pyngrok安装失败")
        return False

def setup_ngrok_tunnel():
    """设置ngrok隧道"""
    try:
        from pyngrok import ngrok
        
        print("🚀 启动ngrok隧道...")
        print("🎯 目标端口: 8006")
        print("🔐 认证密码: MVP168918")
        
        # 启动隧道，指向本地8006端口
        public_url = ngrok.connect(8006)
        
        print(f"\n🎉 ngrok隧道启动成功！")
        print("=" * 60)
        print(f"🏥 中医RAG智能诊疗系统")
        print(f"🌐 公网访问地址: {public_url}")
        print(f"🔐 登录凭据:")
        print(f"   用户名: tcm_user")
        print(f"   密码: MVP168918")
        print("=" * 60)
        
        print(f"\n📱 分享给异地朋友的完整信息:")
        print("=" * 60)
        print(f"🏥 【中医RAG智能诊疗系统】")
        print(f"")
        print(f"🌐 访问地址: {public_url}")
        print(f"")
        print(f"🔐 登录信息:")
        print(f"   用户名: tcm_user")
        print(f"   密码: MVP168918")
        print(f"")
        print(f"💡 使用说明:")
        print(f"1. 点击上述网址打开系统")
        print(f"2. 输入用户名和密码登录")
        print(f"3. 即可使用中医智能诊疗功能")
        print(f"")
        print(f"🎯 系统功能:")
        print(f"• DeepSeek-R1智能诊疗分析")
        print(f"• 在线医学资源检索")
        print(f"• PDF文档上传分析")
        print(f"• 语音交互支持")
        print(f"• 会话历史管理")
        print("=" * 60)
        
        return public_url
        
    except ImportError:
        print("❌ pyngrok未安装，正在安装...")
        if install_pyngrok():
            return setup_ngrok_tunnel()
        else:
            return None
    except Exception as e:
        print(f"❌ ngrok隧道启动失败: {e}")
        print("💡 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试重新运行脚本")
        print("3. 使用其他隧道服务")
        return None

def test_tunnel_access(url):
    """测试隧道访问"""
    try:
        print("\n🧪 测试隧道访问...")
        
        # 测试健康检查接口
        health_url = f"{url}/api/health"
        response = requests.get(health_url, auth=('tcm_user', 'MVP168918'), timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 隧道访问测试成功！")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
            return True
        else:
            print(f"❌ 隧道访问测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 隧道访问测试异常: {e}")
        return False

def save_tunnel_info(url):
    """保存隧道信息到文件"""
    try:
        tunnel_info = {
            "url": url,
            "username": "tcm_user",
            "password": "MVP168918",
            "created_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "instructions": [
                "1. 点击网址打开系统",
                "2. 输入用户名和密码登录",
                "3. 即可使用中医智能诊疗功能"
            ]
        }
        
        with open("tunnel_info.json", "w", encoding="utf-8") as f:
            json.dump(tunnel_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 隧道信息已保存到 tunnel_info.json")
        
    except Exception as e:
        print(f"⚠️ 保存隧道信息失败: {e}")

def main():
    """主函数"""
    print("🚀 中医RAG系统 - ngrok隧道设置")
    print("=" * 50)
    
    # 检查本地服务器是否运行
    try:
        response = requests.get("http://localhost:8006/api/health", 
                              auth=('tcm_user', 'MVP168918'), 
                              timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 本地服务器运行正常")
            print(f"📊 系统状态: {data.get('status')}")
            print(f"📅 系统版本: {data.get('version')}")
            print(f"📄 文档数量: {data.get('documents', 0)}")
        else:
            print("❌ 本地服务器响应异常")
            return
    except Exception as e:
        print("❌ 本地服务器未运行，请先启动服务器")
        print("💡 运行命令: python simple_ultimate_tcm.py")
        print(f"错误详情: {e}")
        return
    
    # 设置ngrok隧道
    public_url = setup_ngrok_tunnel()
    
    if public_url:
        # 测试隧道访问
        if test_tunnel_access(public_url):
            print("\n🎉 ngrok隧道设置完成！")
            print("📱 现在可以将访问信息分享给异地朋友了")
            
            # 保存隧道信息
            save_tunnel_info(public_url)
            
            # 保持隧道运行
            try:
                print("\n⏳ 隧道保持运行中... (按Ctrl+C停止)")
                print("💡 请保持此窗口打开，关闭后隧道将断开")
                print("-" * 50)
                
                while True:
                    time.sleep(60)
                    print(f"🔄 隧道运行中: {public_url} ({time.strftime('%H:%M:%S')})")
                    
            except KeyboardInterrupt:
                print("\n🛑 隧道已停止")
                print("👋 感谢使用中医RAG智能诊疗系统！")
        else:
            print("❌ 隧道设置失败")
    else:
        print("❌ 无法设置ngrok隧道")
        print("\n💡 替代方案:")
        print("1. 使用局域网访问 (同一WiFi)")
        print("2. 尝试其他隧道服务")
        print("3. 部署到云服务器")

if __name__ == "__main__":
    main()
