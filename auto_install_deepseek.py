#!/usr/bin/env python3
"""
全自动安装llama-cpp-python和下载DeepSeek模型
解决编译问题，自动下载模型文件
"""

import subprocess
import sys
import os
import requests
import platform
from pathlib import Path
import time
import shutil
import re

def print_auto_install_banner():
    """打印自动安装横幅"""
    print("=" * 100)
    print("🤖 全自动DeepSeek模型安装器")
    print("=" * 100)
    print("🎯 将自动完成:")
    print("")
    print("✅ 安装Visual Studio Build Tools (Windows)")
    print("✅ 安装llama-cpp-python (预编译版本)")
    print("✅ 下载DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf模型")
    print("✅ 配置模型路径")
    print("✅ 验证安装结果")
    print("")
    print("⏰ 预计耗时: 10-30分钟 (取决于网络速度)")
    print("💾 所需空间: ~5GB")
    print("=" * 100)

def check_system_info():
    """检查系统信息"""
    print("🔍 检查系统信息...")
    
    system = platform.system()
    architecture = platform.architecture()[0]
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    print(f"💻 操作系统: {system}")
    print(f"🏗️ 架构: {architecture}")
    print(f"🐍 Python版本: {python_version}")
    
    return system, architecture, python_version

def install_build_tools_windows():
    """安装Windows构建工具"""
    print("\n🔧 安装Windows构建工具...")
    
    # 检查是否已安装Visual Studio Build Tools
    vs_paths = [
        r"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools",
        r"C:\Program Files\Microsoft Visual Studio\2022\Community",
        r"C:\Program Files\Microsoft Visual Studio\2019\Community"
    ]
    
    for path in vs_paths:
        if os.path.exists(path):
            print(f"✅ 发现Visual Studio: {path}")
            return True
    
    print("⚠️ 未发现Visual Studio Build Tools")
    print("💡 将尝试安装预编译版本的llama-cpp-python")
    return False

def install_llama_cpp_precompiled():
    """安装预编译版本的llama-cpp-python"""
    print("\n📦 安装预编译版本的llama-cpp-python...")
    
    # 尝试多种安装方式
    install_methods = [
        # 方法1: 使用pip安装预编译版本
        {
            "name": "pip预编译版本",
            "command": [sys.executable, "-m", "pip", "install", "llama-cpp-python", "--force-reinstall", "--no-cache-dir"]
        },
        # 方法2: 使用conda安装
        {
            "name": "conda安装",
            "command": ["conda", "install", "-c", "conda-forge", "llama-cpp-python", "-y"]
        },
        # 方法3: 从GitHub releases下载wheel
        {
            "name": "GitHub预编译wheel",
            "command": None,  # 特殊处理
            "action": "download_wheel"
        }
    ]
    
    for method in install_methods:
        print(f"🔄 尝试方法: {method['name']}")
        
        try:
            if method.get('action') == 'download_wheel':
                if install_from_github_wheel():
                    return True
            else:
                result = subprocess.run(
                    method['command'], 
                    capture_output=True, 
                    text=True, 
                    timeout=1800  # 30分钟超时
                )
                
                if result.returncode == 0:
                    print(f"✅ {method['name']} 安装成功")
                    return True
                else:
                    print(f"❌ {method['name']} 安装失败")
                    print(f"错误: {result.stderr[:200]}...")
        
        except subprocess.TimeoutExpired:
            print(f"⏰ {method['name']} 安装超时")
        except FileNotFoundError:
            print(f"⚠️ {method['name']} 命令不存在")
        except Exception as e:
            print(f"❌ {method['name']} 安装异常: {e}")
    
    return False

def install_from_github_wheel():
    """从GitHub下载预编译wheel文件"""
    print("📥 从GitHub下载预编译wheel文件...")
    
    try:
        # 获取Python版本信息
        python_version = f"{sys.version_info.major}{sys.version_info.minor}"
        
        # 构建可能的wheel文件URL
        wheel_urls = [
            f"https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.11/llama_cpp_python-0.2.11-cp{python_version}-cp{python_version}-win_amd64.whl",
            f"https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.10/llama_cpp_python-0.2.10-cp{python_version}-cp{python_version}-win_amd64.whl",
            "https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.11/llama_cpp_python-0.2.11-cp311-cp311-win_amd64.whl",
            "https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.11/llama_cpp_python-0.2.11-cp310-cp310-win_amd64.whl",
            "https://github.com/abetlen/llama-cpp-python/releases/download/v0.2.11/llama_cpp_python-0.2.11-cp39-cp39-win_amd64.whl"
        ]
        
        for url in wheel_urls:
            print(f"🔄 尝试下载: {url}")
            
            try:
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    wheel_filename = url.split('/')[-1]
                    
                    with open(wheel_filename, 'wb') as f:
                        f.write(response.content)
                    
                    print(f"✅ 下载成功: {wheel_filename}")
                    
                    # 安装wheel文件
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "install", wheel_filename
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        print("✅ wheel文件安装成功")
                        os.remove(wheel_filename)  # 清理文件
                        return True
                    else:
                        print(f"❌ wheel文件安装失败: {result.stderr}")
                        os.remove(wheel_filename)  # 清理文件
                
            except requests.exceptions.RequestException as e:
                print(f"⚠️ 下载失败: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"❌ GitHub wheel安装失败: {e}")
        return False

def create_model_directory():
    """创建模型目录"""
    print("\n📁 创建模型目录...")
    
    model_dir = Path("./models/deepseek")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 模型目录创建成功: {model_dir.absolute()}")
    return model_dir

def download_deepseek_model(model_dir):
    """下载DeepSeek模型"""
    print("\n📥 下载DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf模型...")
    
    model_filename = "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    model_path = model_dir / model_filename
    
    # 如果模型已存在，询问是否重新下载
    if model_path.exists():
        model_size = model_path.stat().st_size / (1024 * 1024 * 1024)  # GB
        print(f"✅ 模型文件已存在: {model_path}")
        print(f"📊 文件大小: {model_size:.2f} GB")
        
        choice = input("是否重新下载? (y/n): ").lower().strip()
        if choice != 'y':
            return model_path
    
    # 模型下载URL (多个镜像源)
    download_urls = [
        "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
        "https://hf-mirror.com/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf",
        "https://huggingface.co/lmstudio-community/DeepSeek-R1-0528-Qwen3-8B-GGUF/resolve/main/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf?download=true"
    ]
    
    for i, url in enumerate(download_urls, 1):
        print(f"🔄 尝试下载源 {i}/{len(download_urls)}: {url}")
        
        try:
            # 发送HEAD请求获取文件大小
            head_response = requests.head(url, timeout=30)
            if head_response.status_code == 200:
                file_size = int(head_response.headers.get('content-length', 0))
                file_size_gb = file_size / (1024 * 1024 * 1024)
                print(f"📊 文件大小: {file_size_gb:.2f} GB")
            
            # 开始下载
            response = requests.get(url, stream=True, timeout=60)
            response.raise_for_status()
            
            downloaded = 0
            chunk_size = 8192 * 1024  # 8MB chunks
            
            with open(model_path, 'wb') as f:
                print("⏳ 开始下载...")
                start_time = time.time()
                
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # 显示进度
                        if file_size > 0:
                            progress = (downloaded / file_size) * 100
                            speed = downloaded / (time.time() - start_time) / (1024 * 1024)  # MB/s
                            print(f"\r📥 下载进度: {progress:.1f}% ({downloaded/(1024*1024*1024):.2f}GB/{file_size_gb:.2f}GB) 速度: {speed:.1f}MB/s", end="")
                        else:
                            print(f"\r📥 已下载: {downloaded/(1024*1024*1024):.2f}GB", end="")
            
            print(f"\n✅ 模型下载成功: {model_path}")
            return model_path
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ 下载源 {i} 失败: {e}")
            if model_path.exists():
                model_path.unlink()  # 删除不完整的文件
            continue
        except Exception as e:
            print(f"\n❌ 下载过程出错: {e}")
            if model_path.exists():
                model_path.unlink()  # 删除不完整的文件
            continue
    
    print("❌ 所有下载源都失败了")
    return None

def update_config_file(model_path):
    """更新配置文件中的模型路径"""
    print("\n🔧 更新配置文件...")
    
    config_files = [
        "ultimate_working_tcm_system.py",
        "ultimate_rag_core.py"
    ]
    
    for config_file in config_files:
        if not Path(config_file).exists():
            continue
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新模型路径
            old_path_pattern = r"'DEEPSEEK_MODEL_PATH':\s*r?['\"][^'\"]*['\"]"
            new_path = f"'DEEPSEEK_MODEL_PATH': r'{model_path.absolute()}'"
            
            updated_content = re.sub(old_path_pattern, new_path, content)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print(f"✅ 已更新 {config_file}")
            
        except Exception as e:
            print(f"⚠️ 更新 {config_file} 失败: {e}")

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    # 测试llama-cpp-python
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python 导入成功")
        llama_available = True
    except ImportError as e:
        print(f"❌ llama-cpp-python 导入失败: {e}")
        llama_available = False
    
    # 测试模型文件
    model_dir = Path("./models/deepseek")
    model_file = model_dir / "DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"
    
    if model_file.exists():
        model_size = model_file.stat().st_size / (1024 * 1024 * 1024)
        print(f"✅ 模型文件存在: {model_file}")
        print(f"📊 文件大小: {model_size:.2f} GB")
        model_available = True
    else:
        print(f"❌ 模型文件不存在: {model_file}")
        model_available = False
    
    # 测试模型加载 (可选)
    if llama_available and model_available:
        test_model = input("\n是否测试模型加载? (可能需要几分钟) (y/n): ").lower().strip()
        if test_model == 'y':
            try:
                print("⏳ 正在测试模型加载...")
                llm = Llama(
                    model_path=str(model_file),
                    n_ctx=512,
                    n_threads=2,
                    verbose=False
                )
                print("✅ 模型加载测试成功")
                
                # 简单推理测试
                response = llm("Hello", max_tokens=10)
                print("✅ 模型推理测试成功")
                
            except Exception as e:
                print(f"⚠️ 模型加载测试失败: {e}")
    
    return llama_available and model_available

def main():
    """主函数"""
    print_auto_install_banner()
    
    # 确认开始安装
    confirm = input("\n是否开始自动安装? (y/n): ").lower().strip()
    if confirm != 'y':
        print("👋 安装已取消")
        return
    
    print("\n🚀 开始自动安装...")
    
    # 1. 检查系统信息
    system, architecture, python_version = check_system_info()
    
    # 2. 安装构建工具 (Windows)
    if system == "Windows":
        install_build_tools_windows()
    
    # 3. 安装llama-cpp-python
    print("\n" + "="*50)
    if not install_llama_cpp_precompiled():
        print("❌ llama-cpp-python安装失败")
        print("💡 请手动安装或检查网络连接")
        return
    
    # 4. 创建模型目录
    print("\n" + "="*50)
    model_dir = create_model_directory()
    
    # 5. 下载DeepSeek模型
    print("\n" + "="*50)
    model_path = download_deepseek_model(model_dir)
    if not model_path:
        print("❌ 模型下载失败")
        return
    
    # 6. 更新配置文件
    print("\n" + "="*50)
    update_config_file(model_path)
    
    # 7. 测试安装结果
    print("\n" + "="*50)
    success = test_installation()
    
    # 8. 总结
    print("\n" + "="*100)
    if success:
        print("🎉 自动安装完成！")
        print("")
        print("✅ llama-cpp-python: 已安装")
        print("✅ DeepSeek模型: 已下载")
        print("✅ 配置文件: 已更新")
        print("")
        print("💡 现在可以启动终极中医RAG系统:")
        print("   python ultimate_launcher.py")
    else:
        print("❌ 安装未完全成功")
        print("💡 请检查错误信息并重试")
    
    print("="*100)

if __name__ == "__main__":
    main()
