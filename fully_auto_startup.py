#!/usr/bin/env python3
"""
全自动启动MCP+API+RAG系统
等待模型下载完成后自动启动，无需手动操作
"""

import asyncio
import subprocess
import time
import requests
import logging
import sys
import os
from pathlib import Path
import threading
import signal

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FullyAutoStartup:
    """全自动启动系统"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
        # 配置
        self.config = {
            'required_models': [
                './models/qwen2-1.5b-instruct/config.json',  # Qwen2-1.5B模型
                './models/bge-m3'  # BGE-M3嵌入模型
            ],
            'api_port': 8002,
            'streamlit_port': 8509,
            'elasticsearch_port': 9200,
            'github_repo': 'https://github.com/BillHCM7777779/gudaiyishu',
            'check_interval': 30,  # 每30秒检查一次模型状态
            'max_wait_time': 1800  # 最大等待30分钟
        }
    
    def check_models_ready(self) -> bool:
        """检查模型是否准备就绪"""
        logger.info("🔍 检查模型下载状态...")
        
        ready_count = 0
        total_count = len(self.config['required_models'])
        
        for model_path in self.config['required_models']:
            if Path(model_path).exists():
                ready_count += 1
                logger.info(f"✅ 模型就绪: {model_path}")
            else:
                logger.info(f"⏳ 等待模型: {model_path}")
        
        progress = (ready_count / total_count) * 100
        logger.info(f"📊 模型准备进度: {ready_count}/{total_count} ({progress:.1f}%)")
        
        return ready_count == total_count
    
    def wait_for_models(self) -> bool:
        """等待模型下载完成"""
        logger.info("⏳ 等待模型下载完成...")
        
        start_time = time.time()
        
        while time.time() - start_time < self.config['max_wait_time']:
            if self.check_models_ready():
                logger.info("🎉 所有模型准备就绪！")
                return True
            
            logger.info(f"⏳ 继续等待... (已等待 {int(time.time() - start_time)} 秒)")
            time.sleep(self.config['check_interval'])
        
        logger.error("❌ 等待模型超时")
        return False
    
    def check_elasticsearch(self) -> bool:
        """检查并启动Elasticsearch"""
        try:
            response = requests.get(f"http://localhost:{self.config['elasticsearch_port']}", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Elasticsearch已运行")
                return True
        except:
            pass
        
        logger.info("🔄 启动Elasticsearch...")
        return self.start_elasticsearch()
    
    def start_elasticsearch(self) -> bool:
        """启动Elasticsearch"""
        try:
            # 尝试Docker方式
            cmd = [
                'docker', 'run', '-d',
                '--name', 'elasticsearch-auto',
                '-p', f'{self.config["elasticsearch_port"]}:9200',
                '-p', '9300:9300',
                '-e', 'discovery.type=single-node',
                '-e', 'xpack.security.enabled=false',
                'elasticsearch:8.11.0'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ Elasticsearch Docker启动成功")
                
                # 等待启动
                for i in range(30):
                    try:
                        response = requests.get(f"http://localhost:{self.config['elasticsearch_port']}", timeout=2)
                        if response.status_code == 200:
                            logger.info("✅ Elasticsearch启动完成")
                            return True
                    except:
                        pass
                    time.sleep(2)
                
                logger.error("❌ Elasticsearch启动超时")
                return False
            else:
                logger.warning(f"⚠️ Docker启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Elasticsearch启动失败: {e}")
            return False
    
    def start_api_server(self) -> bool:
        """启动API服务器"""
        try:
            logger.info("🚀 启动轻量级模型API服务器...")
            
            cmd = [sys.executable, 'lightweight_model_api_server.py']
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['api_server'] = process
            
            # 等待API服务器启动
            for i in range(60):  # 等待60秒
                try:
                    response = requests.get(f"http://localhost:{self.config['api_port']}/health", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ API服务器启动成功")
                        return True
                except:
                    pass
                time.sleep(2)
            
            logger.error("❌ API服务器启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ API服务器启动失败: {e}")
            return False
    
    def start_mcp_server(self) -> bool:
        """启动MCP服务器"""
        try:
            logger.info("🌐 启动Elasticsearch MCP服务器...")
            
            cmd = [sys.executable, 'elasticsearch_mcp_server.py']
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['mcp_server'] = process
            
            # MCP服务器没有HTTP接口，等待一段时间
            time.sleep(10)
            
            if process.poll() is None:
                logger.info("✅ MCP服务器启动成功")
                return True
            else:
                logger.error("❌ MCP服务器启动失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ MCP服务器启动失败: {e}")
            return False
    
    def start_streamlit_app(self) -> bool:
        """启动Streamlit应用"""
        try:
            logger.info("🎮 启动Streamlit应用...")
            
            cmd = [
                'streamlit', 'run', 'ultimate_final_tcm_system.py',
                '--server.port', str(self.config['streamlit_port']),
                '--server.address', '0.0.0.0',
                '--server.headless', 'true'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['streamlit'] = process
            
            # 等待Streamlit启动
            for i in range(60):  # 等待60秒
                try:
                    response = requests.get(f"http://localhost:{self.config['streamlit_port']}", timeout=2)
                    if response.status_code == 200:
                        logger.info("✅ Streamlit应用启动成功")
                        return True
                except:
                    pass
                time.sleep(2)
            
            logger.error("❌ Streamlit应用启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ Streamlit应用启动失败: {e}")
            return False
    
    def auto_initialize_system(self):
        """自动初始化系统"""
        try:
            logger.info("🤖 等待系统稳定后自动初始化...")
            time.sleep(20)  # 等待所有服务稳定
            
            # 这里可以添加自动初始化逻辑
            # 例如：调用系统的初始化API
            
            logger.info("✅ 系统自动初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 自动初始化失败: {e}")
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        logger.info("🚀 开始全自动启动MCP+API+RAG系统...")
        
        # 1. 等待模型下载完成
        if not self.wait_for_models():
            logger.error("❌ 模型未准备就绪，无法启动系统")
            return False
        
        # 2. 检查并启动Elasticsearch
        if not self.check_elasticsearch():
            logger.error("❌ Elasticsearch启动失败")
            return False
        
        # 3. 启动API服务器
        if not self.start_api_server():
            logger.error("❌ API服务器启动失败")
            return False
        
        # 4. 启动MCP服务器
        if not self.start_mcp_server():
            logger.error("❌ MCP服务器启动失败")
            return False
        
        # 5. 启动Streamlit应用
        if not self.start_streamlit_app():
            logger.error("❌ Streamlit应用启动失败")
            return False
        
        # 6. 自动初始化系统
        threading.Thread(target=self.auto_initialize_system, daemon=True).start()
        
        logger.info("🎉 全自动启动完成！")
        logger.info(f"🌐 访问地址: http://localhost:{self.config['streamlit_port']}")
        logger.info("💡 系统将自动输出 数据库+MCP+API 聚合的最佳答案")
        
        return True
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            try:
                # 检查各个服务的状态
                for service_name, process in self.processes.items():
                    if process.poll() is not None:
                        logger.warning(f"⚠️ 服务 {service_name} 已停止")
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"❌ 服务监控异常: {e}")
                time.sleep(30)
    
    def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理系统资源...")
        self.running = False
        
        for service_name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"✅ 服务 {service_name} 已停止")
            except:
                try:
                    process.kill()
                    logger.info(f"🔪 强制停止服务 {service_name}")
                except:
                    pass
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("📡 接收到停止信号，正在清理...")
        self.cleanup()
        sys.exit(0)

def main():
    """主函数"""
    startup = FullyAutoStartup()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, startup.signal_handler)
    signal.signal(signal.SIGTERM, startup.signal_handler)
    
    try:
        # 启动所有服务
        if startup.start_all_services():
            logger.info("🎊 MCP+API+RAG系统全自动启动完成！")
            logger.info("🎯 系统特点:")
            logger.info("  • 使用Qwen2-1.5B轻量级模型 (1.5GB)")
            logger.info("  • 真正的MCP协议通信")
            logger.info("  • BGE-M3中文嵌入模型")
            logger.info("  • GitHub古代医术检索")
            logger.info("  • 数据库+MCP+API聚合最佳答案")
            logger.info("  • 全自动运行，无需手动操作")
            
            # 启动服务监控
            startup.monitor_services()
        else:
            logger.error("❌ 系统启动失败")
            startup.cleanup()
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("👋 用户中断，正在停止系统...")
        startup.cleanup()
    except Exception as e:
        logger.error(f"❌ 系统运行异常: {e}")
        startup.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
