#!/usr/bin/env python3
"""
系统清理脚本 - 删除冗余文件，保留核心系统
"""

import os
import shutil
from pathlib import Path
import json

def cleanup_system():
    print('🧹 开始系统清理...')
    
    # 核心文件列表 - 这些文件需要保留
    core_files = {
        # 主系统文件
        'perfect_unified_tcm_system.py',
        'perfect_startup.py',
        'intelligent_rag_retriever.py',
        'intelligent_mcp_service.py',
        'deepseek_ollama_api.py',
        'config.py',
        
        # 文档和说明
        'README_PERFECT.md',
        'FINAL_DELIVERY_REPORT.md',
        
        # 配置文件
        'requirements.txt',
        'Dockerfile',
        'docker-compose.yml',
        
        # 测试文件
        'test_vector_db.py',
        'test_mcp_service.py',
        'test_system_integration.py',
        'test_deepseek_integration.py',
        'cleanup_system.py'
    }
    
    # 核心目录列表 - 这些目录需要保留
    core_directories = {
        'documents',
        'perfect_vector_db',
        'conversations',
        'uploads',
        'models',
        'static',
        'logs',
        'cache'
    }
    
    # 获取当前目录下的所有文件和目录
    current_dir = Path('.')
    all_items = list(current_dir.iterdir())
    
    deleted_files = []
    deleted_dirs = []
    
    print('📋 分析文件结构...')
    
    for item in all_items:
        if item.name.startswith('.'):
            continue  # 跳过隐藏文件
            
        if item.is_file():
            # 检查是否为核心文件
            if item.name not in core_files:
                # 检查是否为Python文件但不在核心列表中
                if (item.suffix == '.py' and 
                    'perfect_unified_tcm_system' not in item.name and
                    'test_' not in item.name and
                    'cleanup' not in item.name):
                    
                    try:
                        item.unlink()
                        deleted_files.append(item.name)
                        print(f'   🗑️ 删除文件: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
                
                # 删除其他冗余文件
                elif item.suffix in ['.bat', '.md'] and item.name not in core_files:
                    try:
                        item.unlink()
                        deleted_files.append(item.name)
                        print(f'   🗑️ 删除文件: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
        
        elif item.is_dir():
            # 检查是否为核心目录
            if item.name not in core_directories:
                # 删除非核心目录
                if item.name in ['__pycache__', 'temp', 'cache', 'logs'] and item.name != 'cache':
                    try:
                        shutil.rmtree(item)
                        deleted_dirs.append(item.name)
                        print(f'   🗑️ 删除目录: {item.name}')
                    except Exception as e:
                        print(f'   ❌ 删除失败: {item.name} - {e}')
    
    # 创建必要的目录
    print('\n📁 创建必要目录...')
    for dir_name in core_directories:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f'   ✅ 创建目录: {dir_name}')
    
    # 生成清理报告
    cleanup_report = {
        'timestamp': str(Path().cwd()),
        'deleted_files': deleted_files,
        'deleted_directories': deleted_dirs,
        'core_files_preserved': list(core_files),
        'core_directories_preserved': list(core_directories)
    }
    
    # 保存清理报告
    with open('cleanup_report.json', 'w', encoding='utf-8') as f:
        json.dump(cleanup_report, f, ensure_ascii=False, indent=2)
    
    print(f'\n📊 清理完成统计:')
    print(f'   - 删除文件: {len(deleted_files)} 个')
    print(f'   - 删除目录: {len(deleted_dirs)} 个')
    print(f'   - 保留核心文件: {len(core_files)} 个')
    print(f'   - 保留核心目录: {len(core_directories)} 个')
    
    if deleted_files:
        print(f'\n🗑️ 已删除的文件:')
        for file in deleted_files[:10]:  # 只显示前10个
            print(f'   - {file}')
        if len(deleted_files) > 10:
            print(f'   ... 还有 {len(deleted_files) - 10} 个文件')
    
    print('\n✅ 系统清理完成！')
    print('📋 清理报告已保存到 cleanup_report.json')
    
    return True

if __name__ == "__main__":
    print('🏥 完美统一中医智能助手 - 系统清理工具')
    print('🎯 删除冗余文件，保留核心系统')
    print('=' * 60)
    
    confirm = input('确认开始清理？这将删除非核心文件 (y/N): ')
    if confirm.lower() in ['y', 'yes']:
        cleanup_system()
    else:
        print('❌ 清理已取消')
