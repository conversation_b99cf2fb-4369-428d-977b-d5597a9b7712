<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试系统状态</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
        button { padding: 10px; margin: 5px; }
    </style>
</head>
<body>
    <h1>系统状态调试</h1>
    
    <div class="status">
        <h3>当前状态:</h3>
        <div id="currentStatus">未加载</div>
    </div>
    
    <div class="status">
        <h3>调试信息:</h3>
        <div id="debugInfo">等待测试...</div>
    </div>
    
    <button onclick="testAPI()">测试API</button>
    <button onclick="testFetch()">测试Fetch</button>
    <button onclick="clearLogs()">清除日志</button>
    
    <div class="status">
        <h3>控制台日志:</h3>
        <div id="logs" style="background: #f5f5f5; padding: 10px; height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `[${time}] ${type.toUpperCase()}: ${message}`;
            div.className = type;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
            
            // 也输出到原始控制台
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = (message) => addLog(message, 'info');
        console.error = (message) => addLog(message, 'error');
        
        async function testAPI() {
            addLog('开始测试API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8006/api/health');
                addLog(`响应状态: ${response.status}`, 'info');
                addLog(`响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addLog(`响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    document.getElementById('currentStatus').innerHTML = `
                        <div>状态: ${data.status}</div>
                        <div>版本: ${data.version}</div>
                        <div>文档: ${data.documents} 个</div>
                        <div>功能: ${data.features.length} 项</div>
                    `;
                } else {
                    addLog(`HTTP错误: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
                addLog(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        async function testFetch() {
            addLog('测试基本Fetch功能...', 'info');
            
            try {
                // 测试同源请求
                const response = await fetch('/api/health');
                addLog(`同源请求状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('同源请求成功', 'success');
                    addLog(`数据: ${JSON.stringify(data)}`, 'info');
                } else {
                    addLog(`同源请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                addLog(`Fetch错误: ${error.message}`, 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始自动测试...', 'info');
            
            // 检查基本功能
            addLog(`fetch函数存在: ${typeof fetch !== 'undefined'}`, 'info');
            addLog(`Promise支持: ${typeof Promise !== 'undefined'}`, 'info');
            addLog(`async/await支持: ${typeof (async function(){}) === 'function'}`, 'info');
            
            // 自动测试API
            setTimeout(testFetch, 1000);
        });
        
        // 检查错误
        window.addEventListener('error', function(e) {
            addLog(`JavaScript错误: ${e.message} 在 ${e.filename}:${e.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            addLog(`未处理的Promise拒绝: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
