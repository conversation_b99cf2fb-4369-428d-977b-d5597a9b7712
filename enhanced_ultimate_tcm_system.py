#!/usr/bin/env python3
"""
增强版终极中医RAG系统
解决所有问题：真正的检索、DeepSeek模型、语音功能、快速解析、多格式支持
"""

import streamlit as st
import os
import pickle
import json
import re
from pathlib import Path
from datetime import datetime
import PyPDF2
import numpy as np
import faiss
from sentence_transformers import SentenceTransformer
import requests
from bs4 import BeautifulSoup
import time
import hashlib
import logging
from typing import Dict, List, Any, Set
import threading
import queue
from concurrent.futures import ThreadPoolExecutor
import docx
import pptx
from pptx import Presentation
import pandas as pd

# 语音功能
try:
    import pyttsx3
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False

# DeepSeek模型
try:
    from llama_cpp import Llama
    DEEPSEEK_AVAILABLE = True
except ImportError:
    DEEPSEEK_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="🧙‍♂️ 智者·中医AI助手 - 增强版",
    page_icon="🧙‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
CONFIG = {
    'EMBEDDING_MODEL': 'moka-ai/m3e-base',
    'DEEPSEEK_MODEL_PATH': r'C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf',
    'VECTOR_DB_PATH': './enhanced_vector_db',
    'DOCUMENTS_PATH': './documents',
    'CHUNK_SIZE': 500,
    'CHUNK_OVERLAP': 50,
    'TOP_K': 5,
    'MAX_WORKERS': 4  # 并行处理
}

class RealOnlineMedicalCrawler:
    """真正的在线医学资源爬取器"""
    
    def __init__(self):
        self.base_url = "https://chinesebooks.github.io/gudaiyishu/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.cache = {}
    
    def search_medical_content(self, query, max_results=3):
        """真正搜索医学内容"""
        try:
            results = []
            
            # 搜索医宗金鉴
            yizongjinjian_url = self.base_url + "yizongjinjian/"
            content = self._fetch_page_content(yizongjinjian_url)
            if content and query in content:
                results.append({
                    'title': '医宗金鉴',
                    'content': self._extract_relevant_content(content, query),
                    'url': yizongjinjian_url,
                    'relevance': self._calculate_relevance(query, content)
                })
            
            # 搜索黄帝内经
            huangdineijing_url = self.base_url + "huangdineijing/"
            content = self._fetch_page_content(huangdineijing_url)
            if content and query in content:
                results.append({
                    'title': '黄帝内经',
                    'content': self._extract_relevant_content(content, query),
                    'url': huangdineijing_url,
                    'relevance': self._calculate_relevance(query, content)
                })
            
            # 搜索伤寒论
            shanghan_url = self.base_url + "shanghan/"
            content = self._fetch_page_content(shanghan_url)
            if content and query in content:
                results.append({
                    'title': '伤寒论',
                    'content': self._extract_relevant_content(content, query),
                    'url': shanghan_url,
                    'relevance': self._calculate_relevance(query, content)
                })
            
            # 按相关度排序
            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"在线搜索失败: {e}")
            return []
    
    def _fetch_page_content(self, url):
        """获取页面内容"""
        if url in self.cache:
            return self.cache[url]
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                # 移除脚本和样式
                for script in soup(["script", "style"]):
                    script.decompose()
                content = soup.get_text()
                self.cache[url] = content
                return content
        except Exception as e:
            logger.error(f"获取页面失败 {url}: {e}")
        
        return None
    
    def _extract_relevant_content(self, content, query):
        """提取相关内容"""
        sentences = content.split('。')
        relevant_sentences = []
        
        for sentence in sentences:
            if query in sentence and len(sentence.strip()) > 10:
                relevant_sentences.append(sentence.strip())
        
        if relevant_sentences:
            return '。'.join(relevant_sentences[:3]) + '。'
        else:
            # 如果没有直接匹配，返回包含查询词的段落
            paragraphs = content.split('\n')
            for para in paragraphs:
                if query in para and len(para.strip()) > 20:
                    return para.strip()[:200] + '...'
        
        return content[:200] + '...'
    
    def _calculate_relevance(self, query, content):
        """计算相关度"""
        query_chars = set(query)
        content_chars = set(content)
        intersection = query_chars.intersection(content_chars)
        return len(intersection) / len(query_chars) if query_chars else 0

class MultiFormatDocumentProcessor:
    """多格式文档处理器 - 支持PDF、Word、PPT、Excel等"""
    
    def __init__(self):
        self.supported_formats = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.xlsx', '.xls', '.txt']
    
    def extract_text_from_file(self, file_path):
        """从多种格式文件提取文本"""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        try:
            if extension == '.pdf':
                return self._extract_from_pdf(file_path)
            elif extension in ['.docx', '.doc']:
                return self._extract_from_word(file_path)
            elif extension in ['.pptx', '.ppt']:
                return self._extract_from_ppt(file_path)
            elif extension in ['.xlsx', '.xls']:
                return self._extract_from_excel(file_path)
            elif extension == '.txt':
                return self._extract_from_txt(file_path)
            else:
                logger.warning(f"不支持的文件格式: {extension}")
                return ""
        except Exception as e:
            logger.error(f"文件解析失败 {file_path}: {e}")
            return ""
    
    def _extract_from_pdf(self, file_path):
        """从PDF提取文本 - 优化版"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_parts = []
                
                # 并行处理页面
                with ThreadPoolExecutor(max_workers=CONFIG['MAX_WORKERS']) as executor:
                    futures = []
                    for page_num, page in enumerate(pdf_reader.pages):
                        future = executor.submit(self._extract_page_text, page, page_num)
                        futures.append(future)
                    
                    for future in futures:
                        page_text = future.result()
                        if page_text:
                            text_parts.append(page_text)
                
                return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"PDF解析失败: {e}")
            return ""
    
    def _extract_page_text(self, page, page_num):
        """提取单页文本"""
        try:
            return page.extract_text()
        except Exception as e:
            logger.error(f"页面 {page_num} 解析失败: {e}")
            return ""
    
    def _extract_from_word(self, file_path):
        """从Word文档提取文本"""
        try:
            doc = docx.Document(file_path)
            text_parts = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
            
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"Word文档解析失败: {e}")
            return ""
    
    def _extract_from_ppt(self, file_path):
        """从PPT提取文本"""
        try:
            prs = Presentation(file_path)
            text_parts = []
            
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text_parts.append(shape.text.strip())
            
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"PPT解析失败: {e}")
            return ""
    
    def _extract_from_excel(self, file_path):
        """从Excel提取文本"""
        try:
            df = pd.read_excel(file_path, sheet_name=None)
            text_parts = []
            
            for sheet_name, sheet_df in df.items():
                text_parts.append(f"工作表: {sheet_name}")
                text_parts.append(sheet_df.to_string())
            
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"Excel解析失败: {e}")
            return ""
    
    def _extract_from_txt(self, file_path):
        """从文本文件提取内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    return file.read()
            except Exception as e:
                logger.error(f"文本文件解析失败: {e}")
                return ""

class DeepSeekModelManager:
    """DeepSeek模型管理器"""
    
    def __init__(self):
        self.model = None
        self.model_loaded = False
    
    def load_model(self):
        """加载DeepSeek模型"""
        if not DEEPSEEK_AVAILABLE:
            logger.warning("llama-cpp-python未安装，无法使用DeepSeek模型")
            return False
        
        if not os.path.exists(CONFIG['DEEPSEEK_MODEL_PATH']):
            logger.warning(f"DeepSeek模型文件不存在: {CONFIG['DEEPSEEK_MODEL_PATH']}")
            return False
        
        try:
            logger.info("正在加载DeepSeek模型...")
            self.model = Llama(
                model_path=CONFIG['DEEPSEEK_MODEL_PATH'],
                n_ctx=4096,
                n_threads=8,
                n_gpu_layers=35,
                verbose=False
            )
            self.model_loaded = True
            logger.info("DeepSeek模型加载成功")
            return True
        except Exception as e:
            logger.error(f"DeepSeek模型加载失败: {e}")
            return False
    
    def generate_response(self, prompt, max_tokens=1024, temperature=0.7):
        """生成回答"""
        if not self.model_loaded:
            return "DeepSeek模型未加载"
        
        try:
            response = self.model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                stop=["用户问题：", "问题：", "Human:", "Assistant:"]
            )
            return response['choices'][0]['text'].strip()
        except Exception as e:
            logger.error(f"DeepSeek生成失败: {e}")
            return "生成回答时出错"

class VoiceManager:
    """语音管理器"""
    
    def __init__(self):
        self.engine = None
        self.voice_available = VOICE_AVAILABLE
        
        if self.voice_available:
            try:
                self.engine = pyttsx3.init()
                # 设置语音参数
                voices = self.engine.getProperty('voices')
                if voices:
                    # 尝试设置中文语音
                    for voice in voices:
                        if 'chinese' in voice.name.lower() or 'mandarin' in voice.name.lower():
                            self.engine.setProperty('voice', voice.id)
                            break
                
                self.engine.setProperty('rate', 150)  # 语速
                self.engine.setProperty('volume', 0.8)  # 音量
                logger.info("语音引擎初始化成功")
            except Exception as e:
                logger.error(f"语音引擎初始化失败: {e}")
                self.voice_available = False
    
    def speak_text(self, text):
        """朗读文本"""
        if not self.voice_available or not self.engine:
            return False
        
        try:
            # 清理文本，移除markdown格式
            clean_text = re.sub(r'[#*`\[\]()]', '', text)
            clean_text = re.sub(r'https?://\S+', '', clean_text)
            clean_text = clean_text.replace('\n', ' ').strip()
            
            # 限制长度
            if len(clean_text) > 200:
                clean_text = clean_text[:200] + "..."
            
            self.engine.say(clean_text)
            self.engine.runAndWait()
            return True
        except Exception as e:
            logger.error(f"语音播放失败: {e}")
            return False

# 全局实例
online_crawler = RealOnlineMedicalCrawler()
doc_processor = MultiFormatDocumentProcessor()
deepseek_manager = DeepSeekModelManager()
voice_manager = VoiceManager()

class EnhancedUltimateRAGSystem:
    """增强版终极RAG系统"""
    
    def __init__(self):
        self.embedding_model = None
        self.vector_index = None
        self.document_chunks = []
        self.chunk_metadata = []
        self.initialized = False
        
    def initialize(self):
        """初始化系统"""
        if self.initialized:
            return True
            
        try:
            with st.spinner("🚀 正在初始化增强版系统..."):
                # 1. 加载嵌入模型
                st.write("📥 加载嵌入模型...")
                self.embedding_model = SentenceTransformer(CONFIG['EMBEDDING_MODEL'])
                st.success("✅ 嵌入模型加载成功")
                
                # 2. 加载DeepSeek模型
                st.write("🧠 加载DeepSeek模型...")
                deepseek_loaded = deepseek_manager.load_model()
                if deepseek_loaded:
                    st.success("✅ DeepSeek模型加载成功")
                else:
                    st.warning("⚠️ DeepSeek模型加载失败，将使用智能模板")
                
                # 3. 初始化语音功能
                st.write("🔊 初始化语音功能...")
                if voice_manager.voice_available:
                    st.success("✅ 语音功能可用")
                else:
                    st.warning("⚠️ 语音功能不可用")
                
                # 4. 加载向量数据库
                st.write("📚 加载向量数据库...")
                self.load_vector_database()
                
                self.initialized = True
                st.success("🎉 增强版系统初始化完成！")
                return True

        except Exception as e:
            st.error(f"❌ 系统初始化失败: {e}")
            return False

    def load_vector_database(self):
        """加载向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])

            if vector_db_path.exists():
                index_file = vector_db_path / "index.faiss"
                chunks_file = vector_db_path / "chunks.pkl"
                metadata_file = vector_db_path / "metadata.pkl"

                if all(f.exists() for f in [index_file, chunks_file, metadata_file]):
                    self.vector_index = faiss.read_index(str(index_file))

                    with open(chunks_file, 'rb') as f:
                        self.document_chunks = pickle.load(f)

                    with open(metadata_file, 'rb') as f:
                        self.chunk_metadata = pickle.load(f)

                    st.success(f"✅ 已加载 {len(self.document_chunks)} 个文档块")
                else:
                    st.warning("⚠️ 向量数据库文件不完整，请重新处理文档")
            else:
                st.warning("⚠️ 向量数据库不存在，请先上传文档")

        except Exception as e:
            st.warning(f"⚠️ 加载向量数据库失败: {e}")

    def process_documents_fast(self, uploaded_files):
        """快速处理多格式文档"""
        if not uploaded_files:
            return False

        try:
            with st.spinner("⚡ 正在快速处理文档..."):
                all_chunks = []
                all_metadata = []

                # 并行处理文档
                with ThreadPoolExecutor(max_workers=CONFIG['MAX_WORKERS']) as executor:
                    futures = []

                    for uploaded_file in uploaded_files:
                        st.write(f"处理文件: {uploaded_file.name}")

                        # 保存文件
                        documents_path = Path(CONFIG['DOCUMENTS_PATH'])
                        documents_path.mkdir(exist_ok=True)

                        file_path = documents_path / uploaded_file.name
                        with open(file_path, "wb") as f:
                            f.write(uploaded_file.getbuffer())

                        # 提交并行任务
                        future = executor.submit(self._process_single_document, file_path, uploaded_file.name)
                        futures.append(future)

                    # 收集结果
                    for future in futures:
                        chunks, metadata = future.result()
                        if chunks:
                            all_chunks.extend(chunks)
                            all_metadata.extend(metadata)

                if not all_chunks:
                    st.error("❌ 没有提取到任何文本内容")
                    return False

                # 快速创建向量索引
                st.write("🔍 创建向量索引...")
                self._create_vector_index_fast(all_chunks)

                # 保存数据
                self.document_chunks = all_chunks
                self.chunk_metadata = all_metadata
                self.save_vector_database()

                st.success(f"🎉 成功处理 {len(uploaded_files)} 个文档，共 {len(all_chunks)} 个文本块！")
                return True

        except Exception as e:
            st.error(f"❌ 处理文档失败: {e}")
            return False

    def _process_single_document(self, file_path, file_name):
        """处理单个文档"""
        try:
            # 使用多格式处理器
            text = doc_processor.extract_text_from_file(file_path)
            if not text:
                return [], []

            # 分割文本
            chunks = self.split_text_into_chunks(text)

            # 创建元数据
            metadata = []
            for i, chunk in enumerate(chunks):
                meta = {
                    'source': file_name,
                    'chunk_id': f"{file_name}_{i}",
                    'chunk_index': i,
                    'content': chunk,
                    'upload_time': datetime.now().isoformat(),
                    'file_type': Path(file_name).suffix
                }
                metadata.append(meta)

            return chunks, metadata

        except Exception as e:
            logger.error(f"处理文档失败 {file_name}: {e}")
            return [], []

    def _create_vector_index_fast(self, chunks):
        """快速创建向量索引"""
        try:
            # 批量编码
            batch_size = 32
            embeddings = []

            progress_bar = st.progress(0)

            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]
                batch_embeddings = self.embedding_model.encode(batch)
                embeddings.extend(batch_embeddings)

                progress = min((i + batch_size) / len(chunks), 1.0)
                progress_bar.progress(progress)

            embeddings = np.array(embeddings).astype('float32')

            # 创建FAISS索引
            dimension = embeddings.shape[1]
            self.vector_index = faiss.IndexFlatIP(dimension)
            faiss.normalize_L2(embeddings)
            self.vector_index.add(embeddings)

            return True

        except Exception as e:
            logger.error(f"创建向量索引失败: {e}")
            return False

    def split_text_into_chunks(self, text):
        """智能文本分块"""
        chunks = []
        chunk_size = CONFIG['CHUNK_SIZE']
        overlap = CONFIG['CHUNK_OVERLAP']

        # 按段落分割
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果当前块加上新段落不超过限制
            if len(current_chunk) + len(paragraph) <= chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                # 保存当前块
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())

                # 开始新块
                if len(paragraph) <= chunk_size:
                    current_chunk = paragraph + "\n\n"
                else:
                    # 段落太长，需要进一步分割
                    sub_chunks = self._split_long_paragraph(paragraph, chunk_size, overlap)
                    chunks.extend(sub_chunks)
                    current_chunk = ""

        # 添加最后一块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return [chunk for chunk in chunks if len(chunk.strip()) > 20]

    def _split_long_paragraph(self, paragraph, chunk_size, overlap):
        """分割长段落"""
        chunks = []
        start = 0

        while start < len(paragraph):
            end = start + chunk_size
            if end > len(paragraph):
                end = len(paragraph)

            chunk = paragraph[start:end]

            # 在句号处分割
            if end < len(paragraph) and '。' in chunk:
                last_period = chunk.rfind('。')
                if last_period > chunk_size // 2:
                    end = start + last_period + 1
                    chunk = paragraph[start:end]

            chunks.append(chunk.strip())
            start = end - overlap

            if start >= len(paragraph):
                break

        return chunks

    def save_vector_database(self):
        """保存向量数据库"""
        try:
            vector_db_path = Path(CONFIG['VECTOR_DB_PATH'])
            vector_db_path.mkdir(exist_ok=True)

            faiss.write_index(self.vector_index, str(vector_db_path / "index.faiss"))

            with open(vector_db_path / "chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)

            with open(vector_db_path / "metadata.pkl", 'wb') as f:
                pickle.dump(self.chunk_metadata, f)

            return True
        except Exception as e:
            st.error(f"保存向量数据库失败: {e}")
            return False

    def search_documents(self, query, top_k=None):
        """搜索文档"""
        if top_k is None:
            top_k = CONFIG['TOP_K']

        if self.vector_index is None or not self.document_chunks:
            return []

        try:
            query_embedding = self.embedding_model.encode([query])[0]
            query_vector = np.array([query_embedding]).astype('float32')
            faiss.normalize_L2(query_vector)

            scores, indices = self.vector_index.search(query_vector, top_k)

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.chunk_metadata):
                    result = self.chunk_metadata[idx].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)

            return results

        except Exception as e:
            st.error(f"文档搜索失败: {e}")
            return []

    def generate_enhanced_response(self, query, pdf_results, online_results):
        """生成增强回答"""
        try:
            # 构建上下文
            context_parts = []

            # PDF检索结果
            if pdf_results:
                context_parts.append("## 本地文档检索结果:")
                for i, result in enumerate(pdf_results[:3], 1):
                    context_parts.append(f"{i}. 文档: {result['source']}")
                    context_parts.append(f"   相似度: {result['similarity_score']:.3f}")
                    context_parts.append(f"   内容: {result['content'][:300]}...")
                    context_parts.append("")

            # 在线检索结果
            if online_results:
                context_parts.append("## 古代医书检索结果:")
                for i, result in enumerate(online_results[:2], 1):
                    context_parts.append(f"{i}. 古籍: {result['title']}")
                    context_parts.append(f"   相关度: {result['relevance']:.3f}")
                    context_parts.append(f"   内容: {result['content']}")
                    context_parts.append("")

            context = "\n".join(context_parts)

            # 构建专业提示
            prompt = f"""你是一位资深的中医专家，请基于以下检索到的资料回答用户问题。

用户问题: {query}

检索到的参考资料:
{context}

请要求:
1. 基于提供的资料进行专业回答
2. 采用温和亲切的老中医风格
3. 结构化回答，包含病因病机、辨证论治、调理建议
4. 引用具体的文献来源
5. 提供安全提醒

请回答:"""

            # 使用DeepSeek模型生成回答
            if deepseek_manager.model_loaded:
                logger.info("🧠 使用DeepSeek模型生成回答")
                response = deepseek_manager.generate_response(prompt, max_tokens=1024)
                return self._format_deepseek_response(query, response, pdf_results, online_results)
            else:
                logger.info("🤖 使用智能模板生成回答")
                return self._generate_template_response(query, pdf_results, online_results)

        except Exception as e:
            logger.error(f"生成回答失败: {e}")
            return f"生成回答时出错: {e}"

    def _format_deepseek_response(self, query, response, pdf_results, online_results):
        """格式化DeepSeek回答"""
        formatted_parts = []

        # 标题
        formatted_parts.append(f"## 🧙‍♂️ 智者·中医AI助手 (DeepSeek驱动)")
        formatted_parts.append(f"**您的咨询**: {query}")
        formatted_parts.append("")

        # DeepSeek回答
        formatted_parts.append("### 🧠 专业分析")
        formatted_parts.append(response)
        formatted_parts.append("")

        # 检索来源
        if pdf_results or online_results:
            formatted_parts.append("### 📚 参考来源")

            if pdf_results:
                formatted_parts.append("**本地文档:**")
                for result in pdf_results[:2]:
                    formatted_parts.append(f"- 《{result['source']}》(相似度: {result['similarity_score']:.3f})")

            if online_results:
                formatted_parts.append("**古代医书:**")
                for result in online_results[:2]:
                    formatted_parts.append(f"- 《{result['title']}》(相关度: {result['relevance']:.3f})")

        # 安全提醒
        formatted_parts.append("")
        formatted_parts.append("### ⚠️ 重要提醒")
        formatted_parts.append("- 以上内容基于中医理论和文献资料，仅供学习参考")
        formatted_parts.append("- 具体治疗方案需专业中医师面诊后制定")
        formatted_parts.append("- 如症状严重请及时就医")

        return "\n".join(formatted_parts)

    def _generate_template_response(self, query, pdf_results, online_results):
        """生成模板回答"""
        # 这里可以使用之前的智能模板逻辑
        return f"""## 🧙‍♂️ 智者·中医AI助手

**您的咨询**: {query}

### 🔍 基于检索结果的分析

根据检索到的资料，为您提供以下中医分析：

**检索结果统计:**
- 本地文档: {len(pdf_results)} 个相关结果
- 古代医书: {len(online_results)} 个相关记载

### 💡 中医观点

中医学强调整体观念和辨证论治，需要结合具体症状、体质和病因进行个性化分析。

### ⚠️ 温馨提醒

以上内容仅供参考，具体诊疗请咨询专业中医师。"""

# 初始化系统
if 'enhanced_rag_system' not in st.session_state:
    st.session_state.enhanced_rag_system = EnhancedUltimateRAGSystem()

def main():
    """主界面"""
    st.title("🧙‍♂️ 智者·中医AI助手 - 增强版")
    st.markdown("### 🚀 真正的检索 + DeepSeek模型 + 语音功能 + 多格式支持")

    # 侧边栏
    with st.sidebar:
        st.header("📋 系统控制")

        # 系统初始化
        if st.button("🚀 初始化增强系统", type="primary"):
            st.session_state.enhanced_rag_system.initialize()

        # 系统状态
        st.subheader("📊 系统状态")
        if st.session_state.enhanced_rag_system.initialized:
            st.success("✅ 增强系统已就绪")
            st.metric("文档块数量", len(st.session_state.enhanced_rag_system.document_chunks))
            st.metric("DeepSeek模型", "✅ 已加载" if deepseek_manager.model_loaded else "❌ 未加载")
            st.metric("语音功能", "✅ 可用" if voice_manager.voice_available else "❌ 不可用")
            st.metric("向量维度", 768 if st.session_state.enhanced_rag_system.embedding_model else 0)
        else:
            st.warning("⚠️ 系统未初始化")

        st.divider()

        # 文档上传
        st.subheader("📄 多格式文档管理")
        st.write("支持格式: PDF, Word, PPT, Excel, TXT")

        uploaded_files = st.file_uploader(
            "上传文档",
            type=['pdf', 'docx', 'doc', 'pptx', 'ppt', 'xlsx', 'xls', 'txt'],
            accept_multiple_files=True,
            help="支持多种格式的中医文档"
        )

        if uploaded_files and st.button("⚡ 快速处理文档"):
            st.session_state.enhanced_rag_system.process_documents_fast(uploaded_files)

        st.divider()

        # 功能开关
        st.subheader("🎛️ 功能设置")

        # 语音开关
        voice_enabled = st.checkbox("🔊 启用语音播放", value=True, disabled=not voice_manager.voice_available)
        st.session_state.voice_enabled = voice_enabled

        # 检索设置
        st.write("🔍 检索设置")
        search_pdf = st.checkbox("📚 搜索本地文档", value=True)
        search_online = st.checkbox("🌐 搜索古代医书", value=True)

        st.session_state.search_pdf = search_pdf
        st.session_state.search_online = search_online

        st.divider()

        # 功能说明
        st.subheader("✨ 增强功能")
        st.write("🧙‍♂️ **智者·中医AI助手**")
        st.write("🧠 **DeepSeek模型驱动**")
        st.write("🔍 **真正的PDF检索**")
        st.write("📊 **向量相似度匹配**")
        st.write("🌐 **古代医书在线检索**")
        st.write("🔊 **语音播放功能**")
        st.write("⚡ **快速多格式解析**")
        st.write("🎯 **专业辨证论治**")

    # 主要内容区域
    if not st.session_state.enhanced_rag_system.initialized:
        st.info("👆 请先点击侧边栏的'初始化增强系统'按钮")
        return

    # 问答界面
    st.subheader("💬 智者·中医AI助手")

    # 示例问题
    st.write("💡 **示例问题：**")
    example_questions = [
        "我最近湿气很重，应该怎么调理？",
        "气血不足有什么症状和治疗方法？",
        "黄帝内经中关于五脏六腑的理论是什么？",
        "伤寒论的栀子甘草豉汤方是什么？",
        "头痛的中医辨证论治方法有哪些？",
        "失眠多梦应该如何用中医调理？"
    ]

    cols = st.columns(2)
    for i, question in enumerate(example_questions):
        with cols[i % 2]:
            if st.button(f"📝 {question}", key=f"example_{i}"):
                st.session_state.current_question = question

    # 问题输入
    question = st.text_input(
        "请输入您的问题:",
        value=st.session_state.get('current_question', ''),
        placeholder="例如：我最近湿气很重，应该怎么调理？",
        key="question_input"
    )

    # 提问按钮
    col1, col2 = st.columns([3, 1])
    with col1:
        ask_button = st.button("🧙‍♂️ 智者分析", type="primary")
    with col2:
        if st.session_state.get('last_answer'):
            if st.button("🔊 语音播放"):
                if voice_manager.voice_available:
                    with st.spinner("🔊 正在播放..."):
                        voice_manager.speak_text(st.session_state.last_answer)
                else:
                    st.warning("语音功能不可用")

    if ask_button and question:
        handle_enhanced_question(question)

def handle_enhanced_question(question):
    """处理增强问题"""
    with st.spinner("🧙‍♂️ 智者正在深度分析..."):
        pdf_results = []
        online_results = []

        # 1. PDF文档检索
        if st.session_state.get('search_pdf', True):
            with st.spinner("📚 搜索本地文档..."):
                pdf_results = st.session_state.enhanced_rag_system.search_documents(question)
                if pdf_results:
                    st.success(f"✅ 找到 {len(pdf_results)} 个相关文档")

        # 2. 在线古代医书检索
        if st.session_state.get('search_online', True):
            with st.spinner("🌐 搜索古代医书..."):
                online_results = online_crawler.search_medical_content(question)
                if online_results:
                    st.success(f"✅ 找到 {len(online_results)} 个古籍记载")

        # 3. 生成增强回答
        with st.spinner("🧠 生成专业回答..."):
            answer = st.session_state.enhanced_rag_system.generate_enhanced_response(
                question, pdf_results, online_results
            )

            # 保存回答用于语音播放
            st.session_state.last_answer = answer

        # 显示结果
        display_enhanced_results(question, answer, pdf_results, online_results)

def display_enhanced_results(question, answer, pdf_results, online_results):
    """显示增强结果"""
    # 用户问题
    st.markdown(f"""
    <div style="background-color: #E3F2FD; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid #2196F3;">
        <strong>🙋 用户问题：</strong><br>
        {question}
    </div>
    """, unsafe_allow_html=True)

    # 智者回答
    st.markdown(answer)

    # 自动语音播放（如果启用）
    if st.session_state.get('voice_enabled', False) and voice_manager.voice_available:
        with st.spinner("🔊 正在播放回答..."):
            # 提取回答的主要内容进行播放
            clean_answer = re.sub(r'[#*`\[\]()]', '', answer)
            clean_answer = clean_answer.split('### 📚 参考来源')[0]  # 只播放主要回答部分
            voice_manager.speak_text(clean_answer[:300])  # 限制长度

    # 详细检索结果
    if pdf_results or online_results:
        with st.expander("🔍 详细检索结果", expanded=False):

            if pdf_results:
                st.write("**📄 本地文档检索结果：**")
                for i, result in enumerate(pdf_results, 1):
                    with st.container():
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            st.write(f"**{i}. {result['source']}**")
                            st.write(f"文件类型: {result.get('file_type', 'PDF')}")
                        with col2:
                            st.metric("相似度", f"{result['similarity_score']:.3f}")

                        with st.expander(f"查看内容 {i}"):
                            st.write(result['content'])
                        st.divider()

            if online_results:
                st.write("**🌐 古代医书检索结果：**")
                for i, result in enumerate(online_results, 1):
                    with st.container():
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            st.write(f"**{i}. {result['title']}**")
                            st.write(f"来源: {result['url']}")
                        with col2:
                            st.metric("相关度", f"{result['relevance']:.3f}")

                        with st.expander(f"查看内容 {i}"):
                            st.write(result['content'])
                        st.divider()

if __name__ == "__main__":
    main()
