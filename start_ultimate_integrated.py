#!/usr/bin/env python3
"""
启动终极整合中医RAG系统
整合超级智能LLM + 商业级PDF检索功能
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_ultimate_banner():
    """打印终极横幅"""
    print("=" * 80)
    print("🧙‍♂️ 智者·中医AI助手 - 终极整合版")
    print("=" * 80)
    print("🚀 整合 ultra_intelligent_llm.py 的所有智能功能")
    print("📚 + 商业级PDF检索功能")
    print("🧠 = 最强大的中医智能助手")
    print("")
    print("✨ 核心特色:")
    print("   🧙‍♂️ 智者·中医AI助手 - 温和亲切的老中医")
    print("   🔍 真正的PDF文档检索")
    print("   📊 向量相似度匹配")
    print("   🌐 在线医学资源整合")
    print("   🎯 专业辨证论治分析")
    print("   ⚡ 3秒快速响应")
    print("   🏥 商业级用户体验")
    print("=" * 80)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        "streamlit",
        "sentence-transformers", 
        "faiss-cpu",
        "PyPDF2",
        "numpy",
        "requests",
        "beautifulsoup4",
        "torch"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("正在自动安装...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    print("✅ 所有依赖检查完成")
    return True

def check_system_files():
    """检查系统文件"""
    print("\n📁 检查系统文件...")
    
    required_files = [
        "ultimate_integrated_tcm_system.py",
        "ultra_intelligent_llm.py"
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少以下文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有系统文件检查完成")
    return True

def check_data_status():
    """检查数据状态"""
    print("\n📊 检查数据状态...")
    
    # 检查PDF文档
    documents_dir = Path("./documents")
    if documents_dir.exists():
        pdf_files = list(documents_dir.glob("*.pdf"))
        if pdf_files:
            print(f"📚 发现 {len(pdf_files)} 个PDF文档:")
            for pdf in pdf_files[:3]:  # 只显示前3个
                print(f"   - {pdf.name}")
            if len(pdf_files) > 3:
                print(f"   ... 还有 {len(pdf_files) - 3} 个文档")
        else:
            print("📄 documents目录为空")
    else:
        print("📁 documents目录不存在，将自动创建")
    
    # 检查向量数据库
    vector_db_dir = Path("./ultimate_integrated_vector_db")
    if vector_db_dir.exists():
        required_files = ["index.faiss", "chunks.pkl", "metadata.pkl"]
        existing_files = [f for f in required_files if (vector_db_dir / f).exists()]
        
        if len(existing_files) == len(required_files):
            print("🗄️ 向量数据库完整，可直接使用")
        elif existing_files:
            print(f"⚠️ 向量数据库不完整，缺少: {set(required_files) - set(existing_files)}")
        else:
            print("🆕 向量数据库为空")
    else:
        print("🆕 向量数据库目录不存在，将自动创建")

def create_directories():
    """创建必要目录"""
    print("\n📁 创建系统目录...")
    
    directories = [
        "./ultimate_integrated_vector_db",
        "./documents", 
        "./uploads",
        "./online_cache",
        "./logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}")

def test_core_components():
    """测试核心组件"""
    print("\n🧪 测试核心组件...")
    
    try:
        # 测试嵌入模型
        print("📊 测试嵌入模型...")
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('moka-ai/m3e-base')
        test_embedding = model.encode(["测试文本"])[0]
        print(f"✅ 嵌入模型正常 (维度: {len(test_embedding)})")
        
        # 测试FAISS
        print("🔍 测试FAISS...")
        import faiss
        import numpy as np
        test_vectors = np.random.random((5, len(test_embedding))).astype('float32')
        index = faiss.IndexFlatIP(len(test_embedding))
        index.add(test_vectors)
        print(f"✅ FAISS正常 (索引大小: {index.ntotal})")
        
        # 测试超级智能LLM
        print("🧙‍♂️ 测试智者·中医AI助手...")
        from ultra_intelligent_llm import UltraIntelligentLLM
        ultra_llm = UltraIntelligentLLM()
        print(f"✅ 智者·中医AI助手正常 (模型: {ultra_llm.model_name})")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False

def launch_ultimate_system():
    """启动终极系统"""
    print("\n🚀 启动终极整合系统...")
    
    try:
        app_file = "ultimate_integrated_tcm_system.py"
        if not Path(app_file).exists():
            print(f"❌ 应用文件不存在: {app_file}")
            return False
        
        print(f"📱 启动 {app_file}...")
        print("🌐 应用将在浏览器中打开: http://localhost:8502")
        print("⏹️ 按 Ctrl+C 停止应用")
        print("-" * 80)
        
        # 启动Streamlit（使用不同端口避免冲突）
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_file,
            "--server.port=8502",
            "--server.address=0.0.0.0",
            "--theme.base=light",
            "--server.headless=false"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 终极系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print_ultimate_banner()
    
    # 1. 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        input("按回车键退出...")
        return
    
    # 2. 检查系统文件
    if not check_system_files():
        print("\n❌ 系统文件检查失败")
        input("按回车键退出...")
        return
    
    # 3. 检查数据状态
    check_data_status()
    
    # 4. 创建目录
    create_directories()
    
    # 5. 测试核心组件
    if not test_core_components():
        print("\n⚠️ 核心组件测试失败，但可以尝试启动应用")
        response = input("是否继续启动？(y/N): ")
        if response.lower() != 'y':
            return
    
    print("\n" + "=" * 80)
    print("🎉 终极整合系统准备完成！")
    print("")
    print("🧙‍♂️ 智者·中医AI助手特色功能:")
    print("   ✅ 温和亲切的老中医风格回答")
    print("   ✅ 专业的辨证论治分析")
    print("   ✅ 真正的PDF文档检索")
    print("   ✅ 在线医学资源整合")
    print("   ✅ 超级智能回答生成")
    print("   ✅ 3秒快速响应")
    print("")
    print("💡 使用说明:")
    print("   1. 应用启动后，点击'初始化终极系统'")
    print("   2. 上传中医PDF文档到知识库")
    print("   3. 开始与智者·中医AI助手对话")
    print("=" * 80)
    
    # 6. 启动应用
    input("按回车键启动终极系统...")
    launch_ultimate_system()

if __name__ == "__main__":
    main()
