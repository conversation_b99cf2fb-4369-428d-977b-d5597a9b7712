#!/usr/bin/env python3
"""
测试智能检索系统 - 文档分类和多领域检索
"""

import sys
import time

def test_document_classification():
    """测试文档分类功能"""
    print("🔍 测试文档分类功能")
    print("=" * 40)
    
    try:
        from ultimate_final_tcm_system import UltraFastDocumentProcessor
        
        processor = UltraFastDocumentProcessor()
        
        # 测试不同领域的文本
        test_texts = [
            ("中医气血理论是中医学的基本概念。气是人体生命活动的动力，血是营养物质的载体。", "medical_tcm.txt"),
            ("根据《合同法》第一条规定，为了保护合同当事人的合法权益，维护社会经济秩序。", "legal_contract.txt"),
            ("Python是一种高级编程语言，具有简洁的语法和强大的功能。", "tech_python.txt"),
            ("教育是培养人才的重要途径，学校应该注重学生的全面发展。", "education_theory.txt"),
            ("企业管理需要科学的方法，包括人力资源管理、财务管理等方面。", "business_management.txt"),
            ("《红楼梦》是中国古典文学的巨著，描写了贾宝玉和林黛玉的爱情故事。", "literature_classic.txt")
        ]
        
        for text, filename in test_texts:
            print(f"\n📄 测试文档: {filename}")
            print(f"内容: {text[:50]}...")
            
            result = processor.classify_document_domain(text, filename)
            
            print(f"🎯 检测领域: {result['primary_domain']}")
            print(f"📊 置信度: {result['confidence']}")
            print(f"📈 所有评分: {result['all_scores']}")
            
            if result['medical_detail']:
                print(f"🏥 医学细分: {result['medical_detail']}")
            
            # 获取对应资源
            resources = processor.get_domain_resources(result['primary_domain'])
            print(f"🌐 推荐资源: {resources[:2]}")  # 显示前2个
            
        print("\n✅ 文档分类测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_elasticsearch_retriever():
    """测试Elasticsearch在线检索器"""
    print("\n🌐 测试Elasticsearch在线检索器")
    print("=" * 40)
    
    try:
        from elasticsearch_online_retriever import ElasticsearchOnlineRetriever
        
        retriever = ElasticsearchOnlineRetriever()
        
        print("初始化检索器...")
        if retriever.initialize():
            print("✅ 初始化成功")
            print(retriever.get_stats())
            
            # 测试不同领域的搜索
            test_queries = [
                ("气血不足怎么调理", "medical"),
                ("合同违约怎么处理", "legal"),
                ("Python编程入门", "technology"),
                ("如何提高教学质量", "education"),
                ("企业如何降低成本", "business"),
                ("红楼梦的主要人物", "literature")
            ]
            
            for query, domain in test_queries:
                print(f"\n🔍 搜索: {query} (领域: {domain})")
                results = retriever.search_by_domain(query, domain, max_results=2)
                print(f"找到 {len(results)} 个结果")
                
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result['title']}")
                    print(f"     领域: {result['domain']}")
                    print(f"     评分: {result['score']:.2f}")
                    print(f"     内容: {result['content'][:100]}...")
                    
        else:
            print("❌ 初始化失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_integrated_system():
    """测试集成系统"""
    print("\n🔗 测试集成系统")
    print("=" * 40)
    
    try:
        from ultimate_final_tcm_system import UltraFastDocumentProcessor
        from elasticsearch_online_retriever import ElasticsearchOnlineRetriever
        
        # 初始化组件
        processor = UltraFastDocumentProcessor()
        retriever = ElasticsearchOnlineRetriever()
        
        if retriever.initialize():
            print("✅ 组件初始化成功")
            
            # 测试智能查询流程
            test_query = "我最近失眠多梦，中医有什么好的调理方法？"
            print(f"\n🔍 用户查询: {test_query}")
            
            # 1. 分析查询领域
            print("1. 分析查询领域...")
            domain_analysis = processor.classify_document_domain(test_query)
            detected_domain = domain_analysis['primary_domain']
            confidence = domain_analysis['confidence']
            
            print(f"   检测到领域: {detected_domain} (置信度: {confidence})")
            
            # 2. 根据领域进行在线检索
            print("2. 进行在线检索...")
            online_results = retriever.search_by_domain(test_query, detected_domain, max_results=3)
            print(f"   找到 {len(online_results)} 个在线结果")
            
            # 3. 显示检索结果
            print("3. 检索结果:")
            for i, result in enumerate(online_results, 1):
                print(f"   {i}. {result['title']}")
                print(f"      领域: {result['domain']}")
                print(f"      评分: {result['score']:.2f}")
                print(f"      内容: {result['content'][:150]}...")
                print()
            
            # 4. 模拟回答生成
            print("4. 生成智能回答...")
            print("   基于检索到的资料，系统会生成专业的中医调理建议")
            print("   包括病机分析、治疗方案、调护建议等")
            
            print("\n🎉 集成测试完成!")
            
        else:
            print("❌ 组件初始化失败")
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

def test_multi_domain_scenarios():
    """测试多领域场景"""
    print("\n🌍 测试多领域场景")
    print("=" * 40)
    
    scenarios = [
        {
            "query": "医疗纠纷的法律处理程序",
            "expected_domains": ["medical", "legal"],
            "description": "医疗+法律交叉领域"
        },
        {
            "query": "中医药企业的经营管理",
            "expected_domains": ["medical", "business"],
            "description": "医疗+商业交叉领域"
        },
        {
            "query": "中医教育的现代化改革",
            "expected_domains": ["medical", "education"],
            "description": "医疗+教育交叉领域"
        },
        {
            "query": "人工智能在中医诊断中的应用",
            "expected_domains": ["medical", "technology"],
            "description": "医疗+技术交叉领域"
        }
    ]
    
    try:
        from ultimate_final_tcm_system import UltraFastDocumentProcessor
        
        processor = UltraFastDocumentProcessor()
        
        for scenario in scenarios:
            query = scenario["query"]
            expected = scenario["expected_domains"]
            desc = scenario["description"]
            
            print(f"\n📝 场景: {desc}")
            print(f"查询: {query}")
            
            result = processor.classify_document_domain(query)
            detected = result['primary_domain']
            scores = result['all_scores']
            
            print(f"🎯 检测结果: {detected}")
            print(f"📊 各领域评分: {scores}")
            
            # 检查是否检测到预期的领域
            if detected in expected:
                print("✅ 领域检测正确")
            else:
                print("⚠️ 领域检测可能需要优化")
                
            # 显示前3个高分领域
            sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            print(f"🏆 前3个领域: {[f'{k}({v})' for k, v in sorted_scores[:3]]}")
            
        print("\n✅ 多领域场景测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 智能检索系统测试套件")
    print("=" * 50)
    
    # 测试文档分类
    test_document_classification()
    
    time.sleep(1)
    
    # 测试Elasticsearch检索器
    test_elasticsearch_retriever()
    
    time.sleep(1)
    
    # 测试集成系统
    test_integrated_system()
    
    time.sleep(1)
    
    # 测试多领域场景
    test_multi_domain_scenarios()
    
    print("\n🎉 所有测试完成!")
    print("\n💡 系统功能:")
    print("✅ 智能文档分类 - 自动识别6大领域")
    print("✅ 多领域在线检索 - 根据领域匹配资源")
    print("✅ 原有向量数据库 - 保持PDF上传功能")
    print("✅ 智能回答生成 - 整合多源信息")

if __name__ == "__main__":
    main()
