#!/usr/bin/env python3
"""
测试DeepSeek集成功能
"""

import sys
sys.path.append('.')

def test_deepseek_integration():
    print('🧪 测试DeepSeek集成功能...')
    
    try:
        # 测试DeepSeek API
        from deepseek_ollama_api import DeepSeekOllamaAPI
        
        print('🔄 初始化DeepSeek API...')
        deepseek_api = DeepSeekOllamaAPI()
        
        if deepseek_api.available:
            print('✅ DeepSeek模型可用')
            
            # 测试简单查询
            test_query = "什么是中医的气血理论？"
            print(f'🔍 测试查询: {test_query}')
            
            try:
                response = deepseek_api.generate_response(test_query, max_tokens=200, temperature=0.7)
                if response and len(response) > 50:
                    print('✅ DeepSeek生成回答成功')
                    print(f'回答长度: {len(response)} 字符')
                    print(f'回答预览: {response[:150]}...')
                else:
                    print('❌ DeepSeek回答质量不佳')
            except Exception as e:
                print(f'❌ DeepSeek生成失败: {e}')
        else:
            print('❌ DeepSeek模型不可用')
            return False
        
        # 测试系统集成
        print('\n🔗 测试系统集成...')
        from perfect_unified_tcm_system import PerfectUnifiedTCMSystem
        
        app = PerfectUnifiedTCMSystem()
        print('✅ 主系统创建成功')
        
        # 检查DeepSeek集成状态
        deepseek_integrated = (app.response_generator.deepseek_api and 
                              app.response_generator.deepseek_api.available)
        print(f'🤖 DeepSeek集成状态: {"成功" if deepseek_integrated else "失败"}')
        
        if deepseek_integrated:
            # 测试完整的回答生成流程
            print('\n🧠 测试完整回答生成...')
            app.response_generator.initialize()
            
            test_query = "肾虚怎么治疗"
            print(f'🔍 测试查询: {test_query}')
            
            response = app.response_generator.generate_response(test_query)
            if response:
                print('✅ 完整回答生成成功')
                print(f'回答长度: {len(response)} 字符')
                print(f'回答预览: {response[:200]}...')
                
                # 检查是否包含DeepSeek生成的内容
                if "DeepSeek" in response or len(response) > 500:
                    print('✅ 疑似使用了DeepSeek模型生成')
                else:
                    print('⚠️ 可能使用了备用回答方案')
            else:
                print('❌ 完整回答生成失败')
        
        print('\n🎉 DeepSeek集成测试完成！')
        return True
        
    except Exception as e:
        print(f'❌ DeepSeek集成测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_deepseek_integration()
