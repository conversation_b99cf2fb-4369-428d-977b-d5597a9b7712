#!/usr/bin/env python3
"""
24小时本地部署脚本
专为本地电脑24小时不关机运行设计
"""
import subprocess
import sys
import json
import socket
import webbrowser
import time
import psutil
import threading
from pathlib import Path
from datetime import datetime

class LocalTCMServer:
    def __init__(self):
        self.process = None
        self.port = 8519
        self.host = "0.0.0.0"
        self.is_running = False
        
    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def find_available_port(self):
        """找到可用端口"""
        for port in range(8519, 8530):
            if self.check_port_available(port):
                self.port = port
                return port
        raise Exception("无法找到可用端口")
    
    def get_network_info(self):
        """获取网络信息"""
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # 获取所有网络接口
            interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                        interfaces.append({
                            'interface': interface,
                            'ip': addr.address
                        })
            
            return hostname, local_ip, interfaces
        except Exception as e:
            return "未知", "localhost", []
    
    def start_server(self):
        """启动服务器"""
        try:
            # 确保端口可用
            if not self.check_port_available(self.port):
                self.find_available_port()
            
            print(f"🚀 启动中医智能助手服务...")
            print(f"📡 端口: {self.port}")
            print(f"🌐 主机: {self.host}")
            
            # 启动命令
            cmd = [
                sys.executable, "-m", "streamlit", "run", 
                "commercial_tcm_system.py",
                "--server.port", str(self.port),
                "--server.address", self.host,
                "--server.headless", "true",
                "--server.maxUploadSize", "200",
                "--server.enableCORS", "false",
                "--server.enableXsrfProtection", "false"
            ]
            
            # 启动进程
            self.process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待启动
            print("⏳ 等待服务启动...")
            time.sleep(5)
            
            # 检查进程状态
            if self.process.poll() is None:
                self.is_running = True
                print("✅ 服务启动成功！")
                return True
            else:
                print("❌ 服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.process:
            print("🛑 正在停止服务...")
            self.process.terminate()
            self.process.wait()
            self.is_running = False
            print("✅ 服务已停止")
    
    def monitor_server(self):
        """监控服务器状态"""
        while self.is_running:
            if self.process and self.process.poll() is not None:
                print("⚠️ 检测到服务异常退出，正在重启...")
                self.start_server()
            time.sleep(30)  # 每30秒检查一次
    
    def generate_access_info(self):
        """生成访问信息"""
        hostname, local_ip, interfaces = self.get_network_info()
        
        access_info = {
            "service_name": "中医智能助手",
            "port": self.port,
            "local_urls": [
                f"http://localhost:{self.port}",
                f"http://127.0.0.1:{self.port}",
                f"http://{local_ip}:{self.port}"
            ],
            "network_urls": [f"http://{iface['ip']}:{self.port}" for iface in interfaces],
            "hostname": hostname,
            "start_time": datetime.now().isoformat()
        }
        
        return access_info
    
    def create_access_page(self, access_info):
        """创建访问页面"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中医智能助手 - 24小时服务</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .title {{
            color: #2E8B57;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }}
        .subtitle {{
            color: #666;
            font-size: 1.2rem;
        }}
        .status {{
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin: 10px;
        }}
        .access-section {{
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }}
        .url-list {{
            list-style: none;
            padding: 0;
        }}
        .url-item {{
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #2E8B57;
        }}
        .url-link {{
            color: #2E8B57;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
        }}
        .url-link:hover {{
            text-decoration: underline;
        }}
        .qr-section {{
            text-align: center;
            margin: 30px 0;
        }}
        .instructions {{
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }}
        .footer {{
            text-align: center;
            color: #666;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏥 中医智能助手</h1>
            <p class="subtitle">24小时智能服务 · 传承千年智慧</p>
            <div class="status">🟢 服务运行中</div>
            <div class="status">🔒 安全合规</div>
        </div>
        
        <div class="access-section">
            <h2>🌐 访问地址</h2>
            <ul class="url-list">
                <li class="url-item">
                    <strong>本机访问：</strong><br>
                    <a href="http://localhost:{access_info['port']}" class="url-link" target="_blank">
                        http://localhost:{access_info['port']}
                    </a>
                </li>
                <li class="url-item">
                    <strong>局域网访问：</strong><br>
                    <a href="{access_info['local_urls'][2]}" class="url-link" target="_blank">
                        {access_info['local_urls'][2]}
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="qr-section">
            <h3>📱 手机扫码访问</h3>
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={access_info['local_urls'][2]}" 
                 alt="二维码" style="border: 2px solid #ddd; border-radius: 10px;">
            <p>用手机扫描二维码即可访问</p>
        </div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>电脑访问：</strong>点击上方"本机访问"链接</li>
                <li><strong>手机访问：</strong>扫描二维码或输入局域网地址</li>
                <li><strong>添加到桌面：</strong>手机浏览器菜单 → "添加到主屏幕"</li>
                <li><strong>24小时服务：</strong>只要电脑不关机，随时可以访问</li>
            </ol>
        </div>
        
        <div class="access-section">
            <h3>🔧 系统信息</h3>
            <p><strong>主机名：</strong>{access_info['hostname']}</p>
            <p><strong>启动时间：</strong>{access_info['start_time']}</p>
            <p><strong>服务端口：</strong>{access_info['port']}</p>
        </div>
        
        <div class="footer">
            <p>⚠️ <strong>重要提醒：</strong>本系统仅供中医文化学习参考，不构成医疗建议</p>
            <p>如有健康问题，请咨询专业医疗机构</p>
            <hr>
            <p>🏥 中医智能助手 © 2024 | 传承文化 · 服务健康</p>
        </div>
    </div>
    
    <script>
        // 自动刷新状态
        setInterval(function() {{
            fetch('{access_info['local_urls'][2]}')
                .then(response => {{
                    if (response.ok) {{
                        document.querySelector('.status').innerHTML = '🟢 服务运行中';
                        document.querySelector('.status').style.background = '#28a745';
                    }}
                }})
                .catch(() => {{
                    document.querySelector('.status').innerHTML = '🔴 服务离线';
                    document.querySelector('.status').style.background = '#dc3545';
                }});
        }}, 30000); // 每30秒检查一次
    </script>
</body>
</html>
"""
        
        with open("中医助手访问页面.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        return "中医助手访问页面.html"

def main():
    """主函数"""
    print("🏥 中医智能助手 - 24小时本地部署")
    print("=" * 50)
    
    server = LocalTCMServer()
    
    try:
        # 启动服务
        if server.start_server():
            # 生成访问信息
            access_info = server.generate_access_info()
            
            # 创建访问页面
            access_page = server.create_access_page(access_info)
            
            print("\n✅ 部署成功！")
            print("=" * 50)
            print(f"🌐 本机访问: http://localhost:{server.port}")
            print(f"📱 局域网访问: {access_info['local_urls'][2]}")
            print(f"📄 访问页面: {access_page}")
            print("=" * 50)
            
            # 询问是否打开浏览器
            try:
                open_browser = input("\n是否打开浏览器查看访问页面？(y/n): ").lower()
                if open_browser != 'n':
                    webbrowser.open(access_page)
                    time.sleep(2)
                    webbrowser.open(f"http://localhost:{server.port}")
            except KeyboardInterrupt:
                pass
            
            print("\n🔄 服务监控已启动...")
            print("💡 提示：")
            print("   - 保持此窗口开启以维持服务运行")
            print("   - 按 Ctrl+C 可以停止服务")
            print("   - 服务会自动监控并重启异常进程")
            print("   - 家人朋友可通过局域网地址访问")
            
            # 启动监控线程
            monitor_thread = threading.Thread(target=server.monitor_server, daemon=True)
            monitor_thread.start()
            
            # 主循环
            try:
                while server.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n\n👋 收到停止信号...")
                server.stop_server()
                print("✅ 服务已安全停止")
        
        else:
            print("❌ 服务启动失败，请检查错误信息")
    
    except Exception as e:
        print(f"❌ 部署失败: {e}")
        if server.process:
            server.stop_server()

if __name__ == "__main__":
    main()
