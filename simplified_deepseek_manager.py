#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import streamlit as st
import logging

logger = logging.getLogger(__name__)

class SimplifiedDeepSeekManager:
    """简化的DeepSeek管理器 - 删除所有LM Studio代码"""

    def __init__(self):
        # 本地API服务
        self.api_url = "http://localhost:8002"
        self.api_available = False

        # 状态
        self.initialized = False
        self.engine_type = "local_api"
        
    def initialize(self) -> bool:
        """初始化DeepSeek模型 - 简化版"""
        if self.initialized:
            return True

        st.info("🧠 正在初始化AI模型...")

        # 尝试连接本地API
        if self._try_local_api():
            return True

        # 备选：使用内置知识
        st.warning("⚠️ 本地API不可用，使用内置中医知识库")
        self.initialized = True
        self.engine_type = "fallback"
        return True

    def _try_local_api(self) -> bool:
        """尝试本地API"""
        try:
            st.info("🔄 检测本地API服务...")
            
            # 测试API连接
            response = requests.get(f"{self.api_url}/health", timeout=3)
            
            if response.status_code == 200:
                self.api_available = True
                self.initialized = True
                self.engine_type = "local_api"
                
                st.success("✅ 本地API连接成功!")
                return True
            else:
                st.info("ℹ️ 本地API不可用")
                return False
                
        except requests.exceptions.ConnectionError:
            st.info("ℹ️ 本地API服务未启动")
            return False
        except Exception as e:
            st.warning(f"⚠️ 本地API检测失败: {e}")
            return False

    def generate_response(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
        """生成回答 - 简化版"""
        if not self.initialized:
            return "❌ 模型未初始化"

        # 使用本地API
        if self.api_available:
            return self._generate_local_api(prompt, max_tokens, temperature)
        
        # 备选：使用内置知识
        else:
            return self._generate_fallback_response(prompt)

    def _generate_local_api(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
        """使用本地API生成回答"""
        try:
            st.info("🧠 AI正在思考...")
            
            payload = {
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                st.success("✅ AI生成完成")
                return content
            else:
                return f"❌ API调用失败: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return "❌ API调用超时，请重试"
        except requests.exceptions.ConnectionError:
            return "❌ 无法连接到本地API服务"
        except Exception as e:
            return f"❌ API调用失败: {str(e)}"

    def _generate_fallback_response(self, prompt: str) -> str:
        """备用回答生成"""
        return f"""## 中医咨询回答

感谢您的咨询：「{prompt}」

由于AI模型暂时不可用，我基于中医基础理论为您提供参考：

### 🔍 中医分析思路
中医诊疗遵循"辨证论治"的原则，需要通过望、闻、问、切四诊合参来了解您的具体情况。

### 💊 一般建议
1. **饮食调理**：根据体质选择合适的食物
2. **起居调摄**：保持规律的作息时间
3. **情志调节**：保持心情舒畅
4. **适度运动**：选择适合的锻炼方式

### ⚠️ 重要提醒
- 以上建议仅供参考，不能替代专业医疗诊断
- 如有疾病症状，请及时就医
- 用药需要在专业中医师指导下进行

建议您到正规中医院进行详细的中医诊断和治疗。"""

    def get_status(self) -> dict:
        """获取管理器状态"""
        return {
            'initialized': self.initialized,
            'engine_type': self.engine_type,
            'api_available': self.api_available
        }
