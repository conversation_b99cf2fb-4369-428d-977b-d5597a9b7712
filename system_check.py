#!/usr/bin/env python3
"""
系统全面检查脚本
检查所有组件是否正常工作，严禁出现代码错误
"""

import sys
import os
import importlib
from pathlib import Path
import traceback

def print_check_banner():
    """打印检查横幅"""
    print("=" * 100)
    print("🔍 终极中医RAG系统 - 全面检查")
    print("=" * 100)
    print("🎯 检查项目:")
    print("   ✅ Python环境和版本")
    print("   ✅ 必要文件存在性")
    print("   ✅ 依赖包导入测试")
    print("   ✅ 配置文件语法检查")
    print("   ✅ 模型文件检查")
    print("   ✅ 功能模块测试")
    print("=" * 100)

def check_python_environment():
    """检查Python环境"""
    print("\n🐍 检查Python环境...")
    
    # Python版本
    version = sys.version_info
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    
    # Python路径
    print(f"✅ Python路径: {sys.executable}")
    
    return True

def check_required_files():
    """检查必要文件"""
    print("\n📁 检查必要文件...")
    
    required_files = [
        "ultimate_working_tcm_system.py",
        "ultimate_rag_core.py",
        "ultimate_launcher.py",
        "install_ultimate_dependencies.py",
        "ultimate_ngrok_launcher.py",
        "ultimate_docker_deploy.py",
        "auto_install_deepseek.py"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    dependencies = {
        'streamlit': '界面框架',
        'numpy': '数值计算',
        'pandas': '数据处理',
        'requests': 'HTTP请求',
        'beautifulsoup4': 'HTML解析',
        'sentence_transformers': '文本嵌入',
        'faiss': '向量搜索',
        'PyPDF2': 'PDF处理',
        'pyttsx3': '语音输出',
        'speech_recognition': '语音识别',
        'aiohttp': '异步HTTP'
    }
    
    success_count = 0
    failed_deps = []
    
    for module, description in dependencies.items():
        try:
            # 特殊处理一些模块名
            import_name = module
            if module == 'beautifulsoup4':
                import_name = 'bs4'
            elif module == 'speech_recognition':
                import_name = 'speech_recognition'
            
            importlib.import_module(import_name)
            print(f"✅ {description} ({module})")
            success_count += 1
        except ImportError:
            print(f"❌ {description} ({module}) - 未安装")
            failed_deps.append(module)
    
    # 检查llama-cpp-python
    try:
        import llama_cpp
        print("✅ DeepSeek模型支持 (llama-cpp-python)")
        success_count += 1
    except ImportError:
        print("❌ DeepSeek模型支持 (llama-cpp-python) - 未安装")
        failed_deps.append('llama-cpp-python')
    
    print(f"\n📊 依赖状态: {success_count}/{len(dependencies)+1} 已安装")
    
    if failed_deps:
        print(f"⚠️ 缺少依赖: {', '.join(failed_deps)}")
        return False
    
    return True

def check_syntax_errors():
    """检查语法错误"""
    print("\n🔍 检查语法错误...")
    
    python_files = [
        "ultimate_working_tcm_system.py",
        "ultimate_rag_core.py",
        "ultimate_launcher.py"
    ]
    
    syntax_errors = []
    
    for file in python_files:
        if not Path(file).exists():
            continue
        
        try:
            with open(file, 'r', encoding='utf-8') as f:
                source = f.read()
            
            compile(source, file, 'exec')
            print(f"✅ {file} - 语法正确")
            
        except SyntaxError as e:
            print(f"❌ {file} - 语法错误: 第{e.lineno}行: {e.msg}")
            syntax_errors.append(f"{file}:{e.lineno}")
        except Exception as e:
            print(f"⚠️ {file} - 检查失败: {e}")
    
    if syntax_errors:
        print(f"❌ 发现语法错误: {', '.join(syntax_errors)}")
        return False
    
    return True

def check_model_files():
    """检查模型文件"""
    print("\n🧠 检查模型文件...")
    
    # 检查多个可能的模型路径
    model_paths = [
        Path("./models/deepseek/DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf"),
        Path(r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\DeepSeek-R1-0528-Qwen3-8B-GGUF\DeepSeek-R1-0528-Qwen3-8B-Q4_K_M.gguf")
    ]
    
    model_found = False
    for model_path in model_paths:
        if model_path.exists():
            model_size = model_path.stat().st_size / (1024 * 1024 * 1024)  # GB
            print(f"✅ 发现模型文件: {model_path}")
            print(f"📊 文件大小: {model_size:.2f} GB")
            model_found = True
            break
    
    if not model_found:
        print("❌ 未发现DeepSeek模型文件")
        print("💡 请运行: python auto_install_deepseek.py")
        return False
    
    return True

def check_directories():
    """检查目录结构"""
    print("\n📂 检查目录结构...")
    
    required_dirs = [
        "ultimate_vector_db",
        "documents", 
        "conversations"
    ]
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {dir_name}")
        else:
            file_count = len(list(dir_path.glob("*")))
            print(f"✅ {dir_name}: {file_count} 个文件")
    
    return True

def test_core_imports():
    """测试核心模块导入"""
    print("\n🧪 测试核心模块导入...")
    
    try:
        # 测试streamlit
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        # 测试sentence_transformers
        from sentence_transformers import SentenceTransformer
        print("✅ SentenceTransformer导入成功")
        
        # 测试faiss
        import faiss
        print("✅ FAISS导入成功")
        
        # 测试语音功能
        import pyttsx3
        import speech_recognition as sr
        print("✅ 语音功能导入成功")
        
        # 测试文档处理
        import PyPDF2
        print("✅ PDF处理导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
        return False

def test_streamlit_config():
    """测试Streamlit配置"""
    print("\n🌐 测试Streamlit配置...")
    
    try:
        # 检查是否可以导入主系统文件
        import importlib.util
        
        spec = importlib.util.spec_from_file_location(
            "ultimate_system", 
            "ultimate_working_tcm_system.py"
        )
        
        if spec is None:
            print("❌ 无法加载主系统文件")
            return False
        
        print("✅ 主系统文件可以加载")
        return True
        
    except Exception as e:
        print(f"❌ Streamlit配置测试失败: {e}")
        return False

def generate_report(results):
    """生成检查报告"""
    print("\n" + "="*100)
    print("📊 系统检查报告")
    print("="*100)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {(passed_checks/total_checks)*100:.1f}%")
    
    print("\n详细结果:")
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查都通过！系统可以正常启动")
        print("💡 运行命令: python ultimate_launcher.py")
        return True
    else:
        print(f"\n⚠️ 有 {total_checks - passed_checks} 项检查失败")
        print("💡 请根据上述错误信息进行修复")
        return False

def main():
    """主函数"""
    print_check_banner()
    
    # 执行所有检查
    results = {}
    
    results["Python环境"] = check_python_environment()
    results["必要文件"] = check_required_files()
    results["依赖包"] = check_dependencies()
    results["语法检查"] = check_syntax_errors()
    results["模型文件"] = check_model_files()
    results["目录结构"] = check_directories()
    results["核心导入"] = test_core_imports()
    results["Streamlit配置"] = test_streamlit_config()
    
    # 生成报告
    success = generate_report(results)
    
    if not success:
        print("\n🔧 修复建议:")
        if not results["依赖包"]:
            print("  - 运行: python install_ultimate_dependencies.py")
        if not results["模型文件"]:
            print("  - 运行: python auto_install_deepseek.py")
        if not results["语法检查"]:
            print("  - 检查Python文件语法错误")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
